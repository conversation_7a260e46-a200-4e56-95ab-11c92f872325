(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[904],{19019:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});var A=a(95155),i=a(12115),r=["mousedown","touchstart"];let l=function(e,s,a){void 0===a&&(a=r);var A=(0,i.useRef)(s);(0,i.useEffect)(function(){A.current=s},[s]),(0,i.useEffect)(function(){for(var s=function(s){var a=e.current;a&&!a.contains(s.target)&&A.current(s)},i=0,r=a;i<r.length;i++)!function(e){for(var s=[],a=1;a<arguments.length;a++)s[a-1]=arguments[a];e&&e.addEventListener&&e.addEventListener.apply(e,s)}(document,r[i],s);return function(){for(var e=0,A=a;e<A.length;e++)!function(e){for(var s=[],a=1;a<arguments.length;a++)s[a-1]=arguments[a];e&&e.removeEventListener&&e.removeEventListener.apply(e,s)}(document,A[e],s)}},[a,e])},t=e=>{let{options:s,defaultCurrent:a,placeholder:r,className:t,onChange:n,name:c}=e,[d,o]=(0,i.useState)(!1),[m,g]=(0,i.useState)(s[a]),h=(0,i.useCallback)(()=>{o(!1)},[]),x=(0,i.useRef)(null);l(x,h);let p=e=>{g(e),n(e,c),h()};return(0,A.jsxs)("div",{className:"nice-select form-select-lg mb-3 ".concat(t||""," ").concat(d?"open":""),role:"button",tabIndex:0,onClick:()=>o(e=>!e),onKeyDown:e=>e,ref:x,children:[(0,A.jsx)("span",{className:"current",children:(null==m?void 0:m.text)||r}),(0,A.jsx)("ul",{className:"list",role:"menubar",onClick:e=>e.stopPropagation(),onKeyDown:e=>e.stopPropagation(),children:null==s?void 0:s.map((e,s)=>(0,A.jsx)("li",{"data-value":e.value,className:"option ".concat(e.value===(null==m?void 0:m.value)?"selected focus":""),style:{fontSize:"14px"},role:"menuitem",onClick:()=>p(e),onKeyDown:e=>e,children:e.text},s))})]})}},33479:(e,s,a)=>{Promise.resolve().then(a.bind(a,93539)),Promise.resolve().then(a.bind(a,38983)),Promise.resolve().then(a.bind(a,91727))},38983:(e,s,a)=>{"use strict";a.d(s,{default:()=>t});var A=a(95155),i=a(6874),r=a.n(i);a(12115);let l=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],t=()=>(0,A.jsx)(A.Fragment,{children:(0,A.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,A.jsx)("div",{className:"container px-0",children:(0,A.jsx)("div",{className:"footer-nav position-relative",children:(0,A.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:l.map((e,s)=>(0,A.jsx)("li",{children:(0,A.jsxs)(r(),{href:"/".concat(e.link),children:[(0,A.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,A.jsx)("span",{children:e.title})]})},s))})})})})})},56421:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});var A=a(95155),i=a(12115),r=a(11450);let l=e=>{let{images:s,setIsOpen:a,photoIndex:l,setPhotoIndex:t}=e;return(0,A.jsx)(i.Fragment,{children:(0,A.jsx)(r.A,{mainSrc:s[l],nextSrc:s[(l+1)%s.length],prevSrc:s[(l+s.length-1)%s.length],onCloseRequest:()=>a(!1),onMovePrevRequest:()=>t((l+s.length-1)%s.length),onMoveNextRequest:()=>t((l+1)%s.length)})})}},93539:(e,s,a)=>{"use strict";a.d(s,{default:()=>g});var A=a(95155),i=a(12115),r=a(66766),l=a(27677),t=a(67269),n=a(56421),c=a(19019),d=a(6874),o=a.n(d);let m=[{id:1,img:{src:"/_next/static/media/p1.4bf13620.jpg",height:800,width:800,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/2wBDAQoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/wgARCAAIAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAb/xAAUAQEAAAAAAAAAAAAAAAAAAAAE/9oADAMBAAIQAxAAAAChDAf/xAAYEAEBAQEBAAAAAAAAAAAAAAABAgMAYf/aAAgBAQABPwDE3sqWaD3v/8QAFxEAAwEAAAAAAAAAAAAAAAAAAAERIf/aAAgBAgEBPwBZT//EABcRAAMBAAAAAAAAAAAAAAAAAAABESH/2gAIAQMBAT8Aew//2Q==",blurWidth:8,blurHeight:8}},{id:2,img:{src:"/_next/static/media/p2.04a5007d.jpg",height:800,width:800,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/2wBDAQoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/wgARCAAIAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAb/xAAUAQEAAAAAAAAAAAAAAAAAAAAD/9oADAMBAAIQAxAAAACZCr//xAAYEAEAAwEAAAAAAAAAAAAAAAABAgMhAP/aAAgBAQABPwAnc3SHKzR7/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAgBAgEBPwB//8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAgBAwEBPwB//9k=",blurWidth:8,blurHeight:8}},{id:3,img:{src:"/_next/static/media/p3.e0359941.jpg",height:800,width:800,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/2wBDAQoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/wgARCAAIAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAb/xAAVAQEBAAAAAAAAAAAAAAAAAAABAv/aAAwDAQACEAMQAAAAnBD/AP/EAB0QAAICAQUAAAAAAAAAAAAAAAECAxEhAAUSFEH/2gAIAQEAAT8Am7abkFuYozk2Mx8KwK8Ov//EABURAQEAAAAAAAAAAAAAAAAAAABR/9oACAECAQE/AI//xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oACAEDAQE/AH//2Q==",blurWidth:8,blurHeight:8}}],g=()=>{let[e,s]=(0,i.useState)(null),[a,d]=(0,i.useState)(!1),g=e=>{s(e),d(!0)},h=m.map(e=>e.img.src);return(0,A.jsxs)(A.Fragment,{children:[a&&(0,A.jsx)(n.A,{images:h,setIsOpen:d,photoIndex:e,setPhotoIndex:s}),(0,A.jsx)("div",{className:"page-content-wrapper py-3",children:(0,A.jsxs)("div",{className:"container",children:[(0,A.jsxs)("div",{className:"card product-details-card mb-3",children:[(0,A.jsx)("span",{className:"badge bg-warning text-dark position-absolute product-badge",children:"Sale -10%"}),(0,A.jsx)("div",{className:"card-body",children:(0,A.jsxs)("div",{className:"product-gallery-wrapper",children:[(0,A.jsx)(l.RC,{loop:!0,slidesPerView:1,spaceBetween:0,pagination:{el:".tns-nav",clickable:!0},modules:[t.dK],className:"product-gallery gallery-img",children:m.map((e,s)=>(0,A.jsx)(l.qr,{children:(0,A.jsx)("a",{style:{cursor:"pointer"},onClick:()=>g(s),className:"image-zooming-in-out",title:"Product One","data-gall":"gallery2",children:(0,A.jsx)(r.default,{className:"rounded",src:e.img,alt:""})})},s))}),(0,A.jsx)("div",{className:"tns-nav","aria-label":"Carousel Pagination"})]})})]}),(0,A.jsx)("div",{className:"card product-details-card mb-3 direction-rtl",children:(0,A.jsxs)("div",{className:"card-body shop_style",children:[(0,A.jsx)("h3",{children:"Wooden Table"}),(0,A.jsx)("h1",{children:"$9.89"}),(0,A.jsx)("p",{children:"Lorem ipsum dolor sit amet, consectetur adipisicing elit. Ipsum dolores natus laboriosam accusantium."}),(0,A.jsxs)("form",{onSubmit:e=>e.preventDefault(),children:[(0,A.jsx)(c.A,{className:"form-select mb-3 d-flex align-items-center shop_grid_style",options:[{value:"01",text:"Choose Size"},{value:"02",text:"Small"},{value:"03",text:"Medium"},{value:"04",text:"Large"}],defaultCurrent:0,onChange:e=>{},placeholder:"Select an option",name:"myNiceSelect"}),(0,A.jsxs)("div",{className:"input-group",children:[(0,A.jsx)("input",{className:"input-group-text form-control",type:"number",min:"1",max:"99",defaultValue:"1"}),(0,A.jsx)("button",{className:"btn btn-primary w-50",type:"submit",children:"Add to Cart"})]})]})]})}),(0,A.jsx)("div",{className:"card product-details-card mb-3 direction-rtl",children:(0,A.jsxs)("div",{className:"card-body",children:[(0,A.jsx)("h5",{children:"Description"}),(0,A.jsx)("p",{children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Dolorum soluta tempore tenetur provident eligendi porro, eius nulla? Aliquam, blanditiis id. Corporis."}),(0,A.jsx)("p",{className:"mb-0",children:"Lorem ipsum dolor sit, amet consectetur adipisicing elit. At ut fugit accusantium quo quidem magni laboriosam!"}),(0,A.jsxs)("div",{className:"rating-card-two mt-4",children:[(0,A.jsxs)("div",{className:"d-flex align-items-center justify-content-between mb-3 border-bottom pb-2",children:[(0,A.jsxs)("div",{className:"rating",children:[(0,A.jsx)("a",{href:"#",children:(0,A.jsx)("i",{className:"bi bi-star-fill"})}),(0,A.jsx)("a",{href:"#",children:(0,A.jsx)("i",{className:"bi bi-star-fill"})}),(0,A.jsx)("a",{href:"#",children:(0,A.jsx)("i",{className:"bi bi-star-fill"})}),(0,A.jsx)("a",{href:"#",children:(0,A.jsx)("i",{className:"bi bi-star-fill"})}),(0,A.jsx)("a",{href:"#",children:(0,A.jsx)("i",{className:"bi bi-star-half"})})]}),(0,A.jsx)("span",{children:"4.44 out of 5 ratings"})]}),(0,A.jsxs)("div",{className:"rating-detail",children:[(0,A.jsxs)("div",{className:"d-flex align-items-center mt-2",children:[(0,A.jsx)("span",{style:{color:"#8480AE"},children:"5 star"}),(0,A.jsx)("div",{className:"progress-bar-wrapper",children:(0,A.jsx)("div",{className:"progress",children:(0,A.jsx)("div",{className:"progress-bar bg-warning",style:{width:"78%"},role:"progressbar","aria-valuenow":78,"aria-valuemin":0,"aria-valuemax":100})})}),(0,A.jsx)("span",{children:"78%"})]}),(0,A.jsxs)("div",{className:"d-flex align-items-center mt-2",children:[(0,A.jsx)("span",{style:{color:"#8480AE"},children:"4 star"}),(0,A.jsx)("div",{className:"progress-bar-wrapper",children:(0,A.jsx)("div",{className:"progress",children:(0,A.jsx)("div",{className:"progress-bar bg-warning",style:{width:"14%"},role:"progressbar","aria-valuenow":14,"aria-valuemin":0,"aria-valuemax":100})})}),(0,A.jsx)("span",{children:"14%"})]}),(0,A.jsxs)("div",{className:"d-flex align-items-center mt-2",children:[(0,A.jsx)("span",{style:{color:"#8480AE"},children:"3 star"}),(0,A.jsx)("div",{className:"progress-bar-wrapper",children:(0,A.jsx)("div",{className:"progress",children:(0,A.jsx)("div",{className:"progress-bar bg-warning",style:{width:"8%"},role:"progressbar","aria-valuenow":8,"aria-valuemin":0,"aria-valuemax":100})})}),(0,A.jsx)("span",{children:"8%"})]}),(0,A.jsxs)("div",{className:"d-flex align-items-center mt-2",children:[(0,A.jsx)("span",{style:{color:"#8480AE"},children:"2 star"}),(0,A.jsx)("div",{className:"progress-bar-wrapper",children:(0,A.jsx)("div",{className:"progress",children:(0,A.jsx)("div",{className:"progress-bar bg-warning",style:{width:"0%"},role:"progressbar","aria-valuenow":0,"aria-valuemin":0,"aria-valuemax":100})})}),(0,A.jsx)("span",{children:"0%"})]}),(0,A.jsxs)("div",{className:"d-flex align-items-center mt-2",children:[(0,A.jsx)("span",{style:{color:"#8480AE"},children:"1 star"}),(0,A.jsx)("div",{className:"progress-bar-wrapper",children:(0,A.jsx)("div",{className:"progress",children:(0,A.jsx)("div",{className:"progress-bar bg-warning",style:{width:"0%"},role:"progressbar","aria-valuenow":0,"aria-valuemin":0,"aria-valuemax":100})})}),(0,A.jsx)("span",{children:"0%"})]})]})]})]})}),(0,A.jsx)("div",{className:"card related-product-card direction-rtl",children:(0,A.jsxs)("div",{className:"card-body",children:[(0,A.jsx)("h5",{className:"mb-3",children:"Related Products"}),(0,A.jsxs)("div",{className:"row g-3",children:[(0,A.jsx)("div",{className:"col-6 col-sm-4 col-lg-3",children:(0,A.jsx)("div",{className:"card single-product-card border",children:(0,A.jsxs)("div",{className:"card-body p-3",children:[(0,A.jsxs)(o(),{className:"product-thumbnail d-block",href:"/shop-details",children:[(0,A.jsx)("img",{src:"/assets/img/bg-img/p1.jpg",alt:""}),(0,A.jsx)("span",{className:"badge bg-primary",children:"Sale"})]}),(0,A.jsx)(o(),{className:"product-title d-block text-truncate",href:"/shop-details",children:"Wooden Tool"}),(0,A.jsxs)("p",{className:"sale-price",children:["$9.89",(0,A.jsx)("span",{children:"$13.42"})]}),(0,A.jsx)("a",{className:"btn btn-danger btn-sm",href:"#",children:"Add to Cart"})]})})}),(0,A.jsx)("div",{className:"col-6 col-sm-4 col-lg-3",children:(0,A.jsx)("div",{className:"card single-product-card border",children:(0,A.jsxs)("div",{className:"card-body p-3",children:[(0,A.jsxs)(o(),{className:"product-thumbnail d-block",href:"/shop-details",children:[(0,A.jsx)("img",{src:"/assets/img/bg-img/p2.jpg",alt:""}),(0,A.jsx)("span",{className:"badge bg-primary",children:"Sale"})]}),(0,A.jsx)(o(),{className:"product-title d-block text-truncate",href:"/shop-details",children:"Atoms Musk"}),(0,A.jsxs)("p",{className:"sale-price",children:["$3.36",(0,A.jsx)("span",{children:"$5.99"})]}),(0,A.jsx)("a",{className:"btn btn-danger btn-sm",href:"#",children:"Add to Cart"})]})})})]})]})})]})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,1955,1531,7677,6766,7269,1450,1727,8441,1684,7358],()=>s(33479)),_N_E=e.O()}]);