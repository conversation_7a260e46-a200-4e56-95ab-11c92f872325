"use client";

import React from "react";
import { Card, Form, Button } from "react-bootstrap";
import SignatureCanvas from "react-signature-canvas";
import Swal from "sweetalert2";
import { ChecklistComponent, SignData, ErrorBuckets } from "../types/ChecklistTypes";
import ImageComponent from "@/services/FileDownlodS3";

interface SignatureComponentProps {
    component: ChecklistComponent;
    componentIndex: number;
    checklistData: ChecklistComponent[];
    setChecklistData: React.Dispatch<React.SetStateAction<ChecklistComponent[]>>;
    errorMap: ErrorBuckets;
    signRefs: React.MutableRefObject<{ [key: string]: SignatureCanvas | null }>;
    handleSaveSignature: (componentId: string, cIdx: number) => void;
    handleClearSignature: (componentId: string, cIdx: number) => void;
}

const SignatureComponent: React.FC<SignatureComponentProps> = ({
    component,
    componentIndex,
    checklistData,
    setChecklistData,
    errorMap,
    signRefs,
    handleSaveSignature,
    handleClearSignature,
}) => {
    const signData = component.data as SignData;
    const componentKey = `${signData.id}-${componentIndex}`;

    return (
        <Card className="mb-3 shadow-sm">
            <Card.Body className="p-3">
                <Form.Group>
                    <Form.Label className="fw-semibold d-flex align-items-center mb-2">
                        <i className="bi bi-pen me-2 text-primary"></i>
                        {signData.label}
                        {signData.required && <span className="text-danger ms-1"> *</span>}
                    </Form.Label>
                    <div className="border rounded p-3 bg-light">
                        {component.signature ? (
                            <div className="position-relative" style={{ height: 200 }}>
                                <ImageComponent fileName={component.signature} size={300} name={false} />
                                <Button
                                    size="sm"
                                    variant="outline-danger"
                                    className="position-absolute top-0 end-0"
                                    onClick={() => {
                                        const updated = [...checklistData];
                                        updated[componentIndex].signature = "";
                                        setChecklistData(updated);
                                        Swal.fire("Success", "Signature removed. You can sign again if needed.", "success");
                                    }}
                                >
                                    <i className="bi bi-trash me-1"></i>
                                    Clear
                                </Button>
                            </div>
                        ) : (
                            <div>
                                <div
                                    className="border rounded bg-white position-relative overflow-hidden"
                                    style={{
                                        width: '100%',
                                        height: '200px'
                                    }}
                                >
                                    <SignatureCanvas
                                        ref={(ref) => {
                                            signRefs.current[componentKey] = ref;
                                        }}
                                        canvasProps={{
                                            className: 'w-100 h-100',
                                            style: {
                                                width: '100%',
                                                height: '200px',
                                                display: 'block'
                                            }
                                        }}
                                        backgroundColor="white"
                                        penColor="black"
                                    />
                                </div>
                                <div className="mt-2 d-flex flex-column flex-sm-row gap-2">
                                    <Button
                                        variant="outline-secondary"
                                        size="sm"
                                        onClick={() => handleClearSignature(signData.id, componentIndex)}
                                    >
                                        <i className="bi bi-eraser me-1"></i>
                                        Clear
                                    </Button>
                                    <Button
                                        variant="primary"
                                        size="sm"
                                        onClick={() => handleSaveSignature(signData.id, componentIndex)}
                                    >
                                        <i className="bi bi-check-circle me-1"></i>
                                        Save Signature
                                    </Button>
                                </div>
                            </div>
                        )}
                    </div>
                    {errorMap.checklist[`${componentIndex}-sign`] && (
                        <div className="text-danger small mt-1">
                            {errorMap.checklist[`${componentIndex}-sign`]}
                        </div>
                    )}
                </Form.Group>
            </Card.Body>
        </Card>
    );
};

export default SignatureComponent;
