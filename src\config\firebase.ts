// Firebase configuration
import { initializeApp } from 'firebase/app';
import { getMessaging, getToken, onMessage, isSupported } from 'firebase/messaging';

// Your Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || "AIzaSyCvHAIHZPnzl9xMJu9NpPQ8J-iL8zKDh9k",
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || "pwa-superapp.firebaseapp.com",
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || "pwa-superapp",
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || "pwa-superapp.firebasestorage.app",
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || "596753047002",
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || "1:596753047002:web:e809373bd1190e6a8bf4e3"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Cloud Messaging and get a reference to the service
let messaging: any = null;

// Check if messaging is supported (only in browser environment)
const initializeMessaging = async () => {
  if (typeof window !== 'undefined') {
    try {
      console.log('🔄 Checking Firebase messaging support...');
      console.log('🔍 Firebase config check:');
      console.log('  - API Key:', firebaseConfig.apiKey ? 'Set' : 'Missing');
      console.log('  - Project ID:', firebaseConfig.projectId);
      console.log('  - Sender ID:', firebaseConfig.messagingSenderId);
      console.log('  - App ID:', firebaseConfig.appId ? 'Set' : 'Missing');

      const supported = await isSupported();
      console.log('🔍 Firebase messaging supported:', supported);

      if (supported) {
        console.log('🔄 Initializing Firebase messaging...');

        // Check if service worker is available and register Firebase messaging SW
        if ('serviceWorker' in navigator) {
          try {
            // Wait for service worker to be ready
            await navigator.serviceWorker.ready;
            console.log('✅ Service worker is ready for Firebase messaging');
          } catch (swError) {
            console.warn('⚠️ Service worker not ready:', swError);
          }
        }

        messaging = getMessaging(app);
        console.log('✅ Firebase messaging initialized successfully');
        console.log('  - Messaging instance:', !!messaging);
        return messaging;
      } else {
        console.log('❌ Firebase messaging is not supported in this browser');
        console.log('This could be due to:');
        console.log('  - Browser doesn\'t support service workers');
        console.log('  - Browser doesn\'t support push notifications');
        console.log('  - Running in incognito/private mode');
        return null;
      }
    } catch (error: any) {
      console.error('❌ Error initializing Firebase messaging:', error);
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        code: error.code
      });

      // Check for common Firebase initialization errors
      if (error.code) {
        switch (error.code) {
          case 'app/invalid-api-key':
            console.error('🔍 Invalid Firebase API key');
            break;
          case 'app/invalid-app-id':
            console.error('🔍 Invalid Firebase App ID');
            break;
          case 'messaging/unsupported-browser':
            console.error('🔍 Browser not supported for messaging');
            break;
          default:
            console.error('🔍 Unknown Firebase error:', error.code);
        }
      }

      return null;
    }
  }
  console.log('⚠️ Window not available (server-side rendering)');
  return null;
};

export { app, messaging, initializeMessaging, getToken, onMessage };
