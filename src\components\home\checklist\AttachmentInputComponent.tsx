"use client";

import React from "react";
import { ChecklistComponent, ErrorBuckets, AttachmentInputData } from "../types/ChecklistTypes";
import { Form, Alert } from "react-bootstrap";
import AttachmentUploader from "./AttachmentUploader";

interface AttachmentInputComponentProps {
    component: ChecklistComponent;
    componentIndex: number;
    checklistData: ChecklistComponent[];
    setChecklistData: React.Dispatch<React.SetStateAction<ChecklistComponent[]>>;
    errorMap: ErrorBuckets;
}

const AttachmentInputComponent: React.FC<AttachmentInputComponentProps> = ({
    component,
    componentIndex,
    checklistData,
    setChecklistData,
    errorMap,
}) => {
    const componentData = component.data as AttachmentInputData;

    const handleFilesSelected = (files: string[]) => {
        const updated = [...checklistData];
        // Store files in both locations for compatibility
        updated[componentIndex].uploads = files;
        (updated[componentIndex].data as AttachmentInputData).uploads = files;
        setChecklistData(updated);
    };

    const errorKey = `${componentIndex}-attachment`;
    const hasError = errorMap.checklist[errorKey];

    return (
        <div className="mb-3">
            <Form.Group>
                <Form.Label className="fw-bold">
                    {componentData.label}
                    {componentData.required && <span className="text-danger"> *</span>}
                </Form.Label>

                {hasError && (
                    <Alert variant="danger" className="py-1 px-2 small mb-2">
                        {hasError}
                    </Alert>
                )}

                <div style={{ maxWidth: '100%', overflow: 'hidden' }}>
                    <AttachmentUploader
                        attachmentConfig={componentData.attachmentConfig}
                        onFilesSelected={handleFilesSelected}
                        disabled={false}
                        files={component.uploads || componentData.uploads || []}
                    />
                </div>
            </Form.Group>
        </div>
    );
};

export default AttachmentInputComponent;
