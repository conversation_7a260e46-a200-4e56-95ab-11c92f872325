"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[386],{22070:(e,s,a)=>{a.d(s,{default:()=>t});var i=a(95155);a(12115);var l=a(6874),c=a.n(l),r=a(9e4);let t=e=>{let{home:s,elements:l,title:t,button_text:n}=e;a(81531);let{theme:d,handleDarkModeToggle:h}=(0,r.D)();return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"page-content-wrapper",children:[(0,i.jsx)("div",{className:"breadcrumb-wrapper breadcrumb-two mb-4",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("nav",{"aria-label":"breadcrumb",children:(0,i.jsxs)("ol",{className:"breadcrumb mb-0 px-1 py-4",children:[(0,i.jsx)("li",{className:"breadcrumb-item",children:(0,i.jsx)(c(),{href:"/home",children:s})}),(0,i.jsx)("li",{className:"breadcrumb-item",children:(0,i.jsx)(c(),{href:"/elements",children:l})}),(0,i.jsx)("li",{className:"breadcrumb-item active","aria-current":"page",children:t})]})})})}),(0,i.jsxs)("div",{className:"container",children:[(0,i.jsx)("a",{className:"btn btn-primary btn-lg w-100",id:"affanNavbarToggler",href:"#","data-bs-toggle":"offcanvas","data-bs-target":"#affanOffcanvas","aria-controls":"affanOffcanvas",children:n}),(0,i.jsx)("div",{className:"text-center",children:(0,i.jsx)("img",{className:"w-50 mt-4 mb-3",src:"/assets/img/bg-img/left-sidebar.png",alt:""})})]})]}),(0,i.jsxs)("div",{className:"offcanvas ".concat("Left Sidebar"===t?"offcanvas-start":"offcanvas-end"),id:"affanOffcanvas","data-bs-scroll":"true",tabIndex:-1,"aria-labelledby":"affanOffcanvsLabel",children:[(0,i.jsx)("button",{className:"btn-close btn-close-white text-reset",type:"button","data-bs-dismiss":"offcanvas","aria-label":"Close"}),(0,i.jsx)("div",{className:"offcanvas-body p-0",children:(0,i.jsxs)("div",{className:"sidenav-wrapper",children:[(0,i.jsxs)("div",{className:"sidenav-profile bg-gradient",children:[(0,i.jsx)("div",{className:"sidenav-style1"}),(0,i.jsx)("div",{className:"user-profile",children:(0,i.jsx)("img",{src:"/assets/img/bg-img/2.jpg",alt:""})}),(0,i.jsxs)("div",{className:"user-info",children:[(0,i.jsx)("h6",{className:"user-name mb-0",children:"Affan Islam"}),(0,i.jsx)("span",{children:"CEO, Designing World"})]})]}),(0,i.jsxs)("ul",{className:"sidenav-nav ps-0",children:[(0,i.jsx)("li",{children:(0,i.jsxs)(c(),{href:"/home",children:[(0,i.jsx)("i",{className:"bi bi-house-door"})," Home"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)(c(),{href:"/elements",children:[(0,i.jsx)("i",{className:"bi bi-folder2-open"})," Elements",(0,i.jsx)("span",{className:"badge bg-danger rounded-pill ms-2",children:"220+"})]})}),(0,i.jsx)("li",{children:(0,i.jsxs)(c(),{href:"/pages",children:[(0,i.jsx)("i",{className:"bi bi-collection"})," Pages",(0,i.jsx)("span",{className:"badge bg-success rounded-pill ms-2",children:"100+"})]})}),(0,i.jsxs)("li",{children:[(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-cart-check"})," Shop"]}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:(0,i.jsx)(c(),{href:"/shop-grid",children:" Shop Grid"})}),(0,i.jsx)("li",{children:(0,i.jsx)(c(),{href:"/shop-list",children:" Shop List"})}),(0,i.jsx)("li",{children:(0,i.jsx)(c(),{href:"/shop-details",children:" Shop Details"})}),(0,i.jsx)("li",{children:(0,i.jsx)(c(),{href:"/cart",children:" Cart"})}),(0,i.jsx)("li",{children:(0,i.jsx)(c(),{href:"/checkout",children:" Checkout"})})]})]}),(0,i.jsx)("li",{children:(0,i.jsxs)(c(),{href:"/settings",children:[(0,i.jsx)("i",{className:"bi bi-gear"})," Settings"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("div",{className:"night-mode-nav",children:[(0,i.jsx)("i",{className:"bi bi-moon"}),"dark"===d?"Light":"Dark"," Mode",(0,i.jsx)("div",{className:"form-check form-switch",children:(0,i.jsx)("input",{className:"form-check-input form-check-success",id:"darkSwitch",type:"checkbox",checked:"dark"===d,onChange:h})})]})}),(0,i.jsx)("li",{children:(0,i.jsxs)(c(),{href:"/login",children:[(0,i.jsx)("i",{className:"bi bi-box-arrow-right"})," Logout"]})})]}),(0,i.jsxs)("div",{className:"social-info-wrap",children:[(0,i.jsx)("a",{href:"#",children:(0,i.jsx)("i",{className:"bi bi-facebook"})}),(0,i.jsx)("a",{href:"#",children:(0,i.jsx)("i",{className:"bi bi-twitter"})}),(0,i.jsx)("a",{href:"#",children:(0,i.jsx)("i",{className:"bi bi-linkedin"})})]}),(0,i.jsx)("div",{className:"copyright-info",children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{id:"copyrightYear"}),new Date().getFullYear()," \xa9 Made by ",(0,i.jsx)("a",{target:"_blank",href:"https://themeforest.net/user/rk_theme/portfolio",children:"rk theme"})]})})]})})]})]})}},38983:(e,s,a)=>{a.d(s,{default:()=>t});var i=a(95155),l=a(6874),c=a.n(l);a(12115);let r=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],t=()=>(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,i.jsx)("div",{className:"container px-0",children:(0,i.jsx)("div",{className:"footer-nav position-relative",children:(0,i.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:r.map((e,s)=>(0,i.jsx)("li",{children:(0,i.jsxs)(c(),{href:"/".concat(e.link),children:[(0,i.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,i.jsx)("span",{children:e.title})]})},s))})})})})})},9e4:(e,s,a)=>{a.d(s,{D:()=>l});var i=a(12115);let l=()=>{let[e,s]=(0,i.useState)("light"),[a,l]=(0,i.useState)(!1);(0,i.useEffect)(()=>{s(localStorage.getItem("theme")||"light"),l(!0)},[]),(0,i.useEffect)(()=>{a&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,a]);let c=(0,i.useCallback)(()=>{s(e=>"dark"===e?"light":"dark")},[]),r=(0,i.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let s=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(s),e.style.display="none")},20)},1e3))}c()},[c]);return{theme:e,toggleTheme:c,handleDarkModeToggle:r}}}}]);