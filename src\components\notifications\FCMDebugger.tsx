"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Badge } from 'react-bootstrap';
import fcmService from '@/services/fcmService';

const FCMDebugger: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  useEffect(() => {
    checkEnvironment();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(message);
  };

  const checkEnvironment = () => {
    const info = {
      isHttps: window.location.protocol === 'https:',
      isLocalhost: window.location.hostname === 'localhost',
      hasNotificationAPI: 'Notification' in window,
      hasServiceWorker: 'serviceWorker' in navigator,
      notificationPermission: Notification.permission,
      userAgent: navigator.userAgent,
      vapidKey: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY || 'Not set',
      firebaseConfig: {
        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || 'Not set',
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'Not set',
        messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || 'Not set',
      }
    };

    setDebugInfo(info);
    addLog('Environment check completed');
  };

  const testFCMInitialization = async () => {
    setIsLoading(true);
    addLog('🔄 Testing FCM initialization...');

    try {
      await fcmService.initialize();
      addLog('✅ FCM initialization test completed');
    } catch (error: any) {
      addLog(`❌ FCM initialization failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testTokenGeneration = async () => {
    setIsLoading(true);
    addLog('🔄 Testing FCM token generation...');

    try {
      const token = await fcmService.generateFCMToken();
      if (token) {
        addLog(`✅ Token generated successfully: ${token.substring(0, 50)}...`);
      } else {
        addLog('❌ Token generation failed');
      }
    } catch (error: any) {
      addLog(`❌ Token generation error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const getStatusBadge = (condition: boolean, trueText: string, falseText: string) => {
    return (
      <Badge bg={condition ? 'success' : 'danger'}>
        {condition ? trueText : falseText}
      </Badge>
    );
  };

  return (
    <Card className="mb-3">
      <Card.Header>
        <h6 className="mb-0">🔧 FCM Debug Tool</h6>
      </Card.Header>
      <Card.Body>
        {/* Environment Status */}
        <div className="mb-3">
          <h6>Environment Status:</h6>
          <div className="d-flex flex-wrap gap-2 mb-2">
            {getStatusBadge(debugInfo.isHttps || debugInfo.isLocalhost, 'HTTPS/Localhost ✓', 'HTTPS Required ✗')}
            {getStatusBadge(debugInfo.hasNotificationAPI, 'Notification API ✓', 'Notification API ✗')}
            {getStatusBadge(debugInfo.hasServiceWorker, 'Service Worker ✓', 'Service Worker ✗')}
            {getStatusBadge(debugInfo.notificationPermission === 'granted', 'Permission Granted', 'Permission Needed')}
          </div>
        </div>

        {/* Configuration Status */}
        <div className="mb-3">
          <h6>Firebase Configuration:</h6>
          <div className="small">
            <div>API Key: {debugInfo.firebaseConfig?.apiKey?.substring(0, 20)}...</div>
            <div>Project ID: {debugInfo.firebaseConfig?.projectId}</div>
            <div>Sender ID: {debugInfo.firebaseConfig?.messagingSenderId}</div>
            <div>VAPID Key: {debugInfo.vapidKey?.substring(0, 20)}...</div>
          </div>
        </div>

        {/* Test Buttons */}
        <div className="d-flex gap-2 mb-3">
          <Button
            variant="outline-primary"
            size="sm"
            onClick={testFCMInitialization}
            disabled={isLoading}
          >
            Test FCM Init
          </Button>
          <Button
            variant="primary"
            size="sm"
            onClick={testTokenGeneration}
            disabled={isLoading}
          >
            Test Token Generation
          </Button>
          <Button
            variant="outline-secondary"
            size="sm"
            onClick={clearLogs}
          >
            Clear Logs
          </Button>
        </div>

        {/* Logs */}
        {logs.length > 0 && (
          <div>
            <h6>Debug Logs:</h6>
            <div
              className="bg-dark text-light p-2 rounded small"
              style={{ maxHeight: '200px', overflowY: 'auto', fontFamily: 'monospace' }}
            >
              {logs.map((log, index) => (
                <div key={index}>{log}</div>
              ))}
            </div>
          </div>
        )}

        {/* Warnings */}
        {!debugInfo.isHttps && !debugInfo.isLocalhost && (
          <Alert variant="warning" className="mt-3">
            <i className="bi bi-exclamation-triangle me-2"></i>
            FCM requires HTTPS in production. Use localhost for development.
          </Alert>
        )}

        {debugInfo.notificationPermission === 'denied' && (
          <Alert variant="danger" className="mt-3">
            <i className="bi bi-x-circle me-2"></i>
            Notifications are blocked. Please enable them in browser settings.
          </Alert>
        )}
      </Card.Body>
    </Card>
  );
};

export default FCMDebugger;
