"use client";

import React from "react";
import { Card, Form, Button } from "react-bootstrap";
import DatePicker from "react-datepicker";
import { PostAction, ErrorBuckets, OptionType } from "../types/ChecklistTypes";
import FileUploader from "@/services/FileUploader";
import ModalSelect from "@/services/ModalSelect";

interface PostActionsComponentProps {
    postActions: PostAction[];
    setPostActions: React.Dispatch<React.SetStateAction<PostAction[]>>;
    showPostActions: boolean;
    setShowPostActions: React.Dispatch<React.SetStateAction<boolean>>;
    errorMap: ErrorBuckets;
    assessor: OptionType[];
    handlePostActionChange: (index: number, field: keyof PostAction, value: string | Date | null) => void;
    handlePostFileUpload: (files: string[], index: number) => void;
    handleRemovePostAction: (idx: number) => void;
    addNewPostAction: () => void;
}

const PostActionsComponent: React.FC<PostActionsComponentProps> = ({
    postActions,
    setPostActions,
    showPostActions,
    setShowPostActions,
    errorMap,
    assessor,
    handlePostActionChange,
    handlePostFileUpload,
    handleRemovePostAction,
    addNewPostAction,
}) => {
    return (
        <div className="mt-4">
            {/* Add Post Action Button */}
            {!showPostActions && (
                <div className="text-center">
                    <Button
                        variant="outline-primary"
                        onClick={() => {
                            setShowPostActions(true);
                            setPostActions([
                                {
                                    actionToBeTaken: "",
                                    dueDate: null,
                                    uploads: [],
                                    assignee: "",
                                },
                            ]);
                        }}
                        className="w-100 w-sm-auto"
                    >
                        <i className="bi bi-plus-circle me-2"></i>
                        Additional Actions
                    </Button>
                </div>
            )}

            {/* Post Actions Section */}
            {showPostActions && (
                <div className="border-top pt-4">
                    <div className="mb-4">
                        <div className="d-flex align-items-center justify-content-between mb-2">
                            <h5 className="mb-0 fw-bold text-primary d-flex align-items-center">
                                <i className="bi bi-clipboard-check me-2"></i>
                                Additional Actions
                            </h5>
                            <Button
                                variant="outline-secondary"
                                size="sm"
                                onClick={() => {
                                    setShowPostActions(false);
                                    setPostActions([]);
                                }}
                            >
                                <i className="bi bi-x-circle me-1"></i>
                                Cancel All
                            </Button>
                        </div>
                        <p className="mb-0 text-muted">Please list any additional recommended actions identified during your QC Check</p>
                    </div>

                    {postActions.map((pa, idx) => (
                        <Card key={idx} className="mb-3 position-relative shadow-sm">
                            {/* Action Number Badge */}
                            <div
                                className="position-absolute bg-primary text-white rounded-circle d-flex justify-content-center align-items-center"
                                style={{
                                    width: 30,
                                    height: 30,
                                    top: -10,
                                    left: 15,
                                    fontWeight: "bold",
                                    fontSize: "14px",
                                    zIndex: 2
                                }}
                            >
                                {idx + 1}
                            </div>

                            {/* Remove Action Button */}
                            <Button
                                variant="outline-danger"
                                size="sm"
                                className="position-absolute"
                                style={{ top: 10, right: 10, zIndex: 2 }}
                                onClick={() => handleRemovePostAction(idx)}
                                title="Remove this action"
                            >
                                <i className="bi bi-trash"></i>
                            </Button>

                            <Card.Body className="pt-4 p-3">
                                <div className="row">
                                    {/* Action Description */}
                                    <div className="col-12 mb-3">
                                        <Form.Group>
                                            <Form.Label className="fw-semibold">
                                                <i className="bi bi-pencil-square me-2 text-primary"></i>
                                                Action to be Taken *
                                            </Form.Label>
                                            <Form.Control
                                                as="textarea"
                                                rows={3}
                                                placeholder="Describe the action that needs to be taken..."
                                                value={pa.actionToBeTaken}
                                                onChange={(e) =>
                                                    handlePostActionChange(idx, "actionToBeTaken", e.target.value)
                                                }
                                                isInvalid={!!errorMap.post[`${idx}-action`]}
                                            />
                                            <Form.Control.Feedback type="invalid">
                                                {errorMap.post[`${idx}-action`]}
                                            </Form.Control.Feedback>
                                        </Form.Group>
                                    </div>

                                    {/* Due Date */}
                                    <div className="col-12 col-md-6 mb-3">
                                        <Form.Group>
                                            <Form.Label className="fw-semibold">
                                                <i className="bi bi-calendar-event me-2 text-primary"></i>
                                                Due Date *
                                            </Form.Label>
                                            <div
                                                className={
                                                    errorMap.post[`${idx}-due`] ? "border border-danger rounded" : ""
                                                }
                                            >
                                                <DatePicker
                                                    selected={pa.dueDate}
                                                    onChange={(d: Date | null) =>
                                                        handlePostActionChange(idx, "dueDate", d ? d.toISOString() : null)
                                                    }
                                                    minDate={new Date()}
                                                    placeholderText="Select due date"
                                                    dateFormat="dd-MM-yyyy"
                                                    className="form-control"
                                                />
                                            </div>
                                            {errorMap.post[`${idx}-due`] && (
                                                <div className="text-danger small mt-1">
                                                    {errorMap.post[`${idx}-due`]}
                                                </div>
                                            )}
                                        </Form.Group>
                                    </div>

                                    {/* Assignee */}
                                    <div className="col-12 col-md-6 mb-3">
                                        <Form.Group>
                                            <Form.Label className="fw-semibold">
                                                <i className="bi bi-person-check me-2 text-primary"></i>
                                                Assign To *
                                            </Form.Label>
                                            <ModalSelect
                                                title={"Action Owner"}
                                                options={assessor}
                                                selectedValue={pa.assignee}
                                                onChange={(newVal: any) => handlePostActionChange(idx, "assignee", newVal || "")}
                                                placeholder="Select Action Owner"
                                                clearable
                                                disabled={false}
                                            />
                                            {errorMap.post[`${idx}-own`] && (
                                                <div className="text-danger small mt-1">
                                                    {errorMap.post[`${idx}-own`]}
                                                </div>
                                            )}
                                        </Form.Group>
                                    </div>

                                    {/* Upload Evidence */}
                                    {/* <div className="col-12 mb-3">
                                        <Form.Group>
                                            <Form.Label className="fw-semibold">
                                                <i className="bi bi-paperclip me-2 text-primary"></i>
                                                Upload Evidence (Optional)
                                            </Form.Label>
                                            <FileUploader
                                                onFilesSelected={(updatedList: string[]) => {
                                                    handlePostFileUpload(updatedList, idx);
                                                }}
                                                disabled={false}
                                                files={pa.uploads}
                                            />
                                        </Form.Group>
                                    </div> */}
                                </div>
                            </Card.Body>
                        </Card>
                    ))}

                    {/* Add Another Action Button */}
                    <div className="text-center">
                        <Button
                            variant="outline-primary"
                            onClick={addNewPostAction}
                            className="px-4"
                        >
                            <i className="bi bi-plus-circle me-2"></i>
                            Add More
                        </Button>
                    </div>
                </div>
            )}
        </div>
    );
};

export default PostActionsComponent;
