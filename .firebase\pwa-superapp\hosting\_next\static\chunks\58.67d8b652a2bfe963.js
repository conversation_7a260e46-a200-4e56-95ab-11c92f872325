"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[58],{60058:(e,t,s)=>{s.d(t,{offlineStorage:()=>r});class a{async init(){return new Promise((e,t)=>{let s=indexedDB.open(this.dbName,this.version);s.onerror=()=>{console.error("❌ Failed to open IndexedDB:",s.error),t(s.error)},s.onsuccess=()=>{this.db=s.result,console.log("✅ IndexedDB initialized successfully"),e()},s.onupgradeneeded=e=>{let t=e.target.result;if(console.log("\uD83D\uDD04 Upgrading IndexedDB schema"),!t.objectStoreNames.contains(this.stores.services)){let e=t.createObjectStore(this.stores.services,{keyPath:"id"});e.createIndex("maskName","maskName",{unique:!1}),e.createIndex("lastUpdated","lastUpdated",{unique:!1})}if(!t.objectStoreNames.contains(this.stores.actions)){let e=t.createObjectStore(this.stores.actions,{keyPath:"id"});e.createIndex("application","application",{unique:!1}),e.createIndex("status","status",{unique:!1}),e.createIndex("dueDate","dueDate",{unique:!1}),e.createIndex("lastUpdated","lastUpdated",{unique:!1})}if(!t.objectStoreNames.contains(this.stores.queue)){let e=t.createObjectStore(this.stores.queue,{keyPath:"id"});e.createIndex("timestamp","timestamp",{unique:!1}),e.createIndex("retryCount","retryCount",{unique:!1})}t.objectStoreNames.contains(this.stores.metadata)||t.createObjectStore(this.stores.metadata,{keyPath:"key"})}})}async saveServices(e){if(!this.db)throw Error("Database not initialized");let t=this.db.transaction([this.stores.services],"readwrite").objectStore(this.stores.services),s=Date.now();for(let a of e)await t.put({...a,lastUpdated:s});await this.setMetadata("servicesLastSync",s),console.log("\uD83D\uDCBE Saved ".concat(e.length," services to offline storage"))}async getServices(){if(!this.db)throw Error("Database not initialized");return new Promise((e,t)=>{let s=this.db.transaction([this.stores.services],"readonly").objectStore(this.stores.services).getAll();s.onsuccess=()=>{console.log("\uD83D\uDCF1 Retrieved ".concat(s.result.length," services from offline storage")),e(s.result)},s.onerror=()=>t(s.error)})}async saveActions(e){if(!this.db)throw Error("Database not initialized");let t=this.db.transaction([this.stores.actions],"readwrite").objectStore(this.stores.actions),s=Date.now();for(let a of e)await t.put({...a,lastUpdated:s});await this.setMetadata("actionsLastSync",s),console.log("\uD83D\uDCBE Saved ".concat(e.length," actions to offline storage"))}async getActions(e){if(!this.db)throw Error("Database not initialized");return new Promise((t,s)=>{let a=this.db.transaction([this.stores.actions],"readonly").objectStore(this.stores.actions).getAll();a.onsuccess=()=>{let s=a.result;e&&"All"!==e&&(s=s.filter(t=>t.application===e)),console.log("\uD83D\uDCF1 Retrieved ".concat(s.length," actions from offline storage")),t(s)},a.onerror=()=>s(a.error)})}async addToQueue(e){if(!this.db)throw Error("Database not initialized");let t={...e,id:"queue_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),timestamp:Date.now(),retryCount:0},s=this.db.transaction([this.stores.queue],"readwrite").objectStore(this.stores.queue);await s.add(t),console.log("\uD83D\uDCE4 Added item to offline queue:",t.url)}async getQueueItems(){if(!this.db)throw Error("Database not initialized");return new Promise((e,t)=>{let s=this.db.transaction([this.stores.queue],"readonly").objectStore(this.stores.queue).getAll();s.onsuccess=()=>e(s.result),s.onerror=()=>t(s.error)})}async removeFromQueue(e){if(!this.db)throw Error("Database not initialized");let t=this.db.transaction([this.stores.queue],"readwrite").objectStore(this.stores.queue);await t.delete(e),console.log("✅ Removed item from offline queue:",e)}async updateQueueItemRetryCount(e,t){if(!this.db)throw Error("Database not initialized");let s=this.db.transaction([this.stores.queue],"readwrite").objectStore(this.stores.queue),a=s.get(e);a.onsuccess=()=>{let e=a.result;e&&(e.retryCount=t,s.put(e))}}async setMetadata(e,t){if(!this.db)throw Error("Database not initialized");let s=this.db.transaction([this.stores.metadata],"readwrite").objectStore(this.stores.metadata);await s.put({key:e,value:t,timestamp:Date.now()})}async getMetadata(e){if(!this.db)throw Error("Database not initialized");return new Promise((t,s)=>{let a=this.db.transaction([this.stores.metadata],"readonly").objectStore(this.stores.metadata).get(e);a.onsuccess=()=>{var e;t((null==(e=a.result)?void 0:e.value)||null)},a.onerror=()=>s(a.error)})}async clearAllData(){if(!this.db)throw Error("Database not initialized");let e=Object.values(this.stores),t=this.db.transaction(e,"readwrite");for(let s of e){let e=t.objectStore(s);await e.clear()}console.log("\uD83D\uDDD1️ Cleared all offline data")}async getStorageInfo(){if(!this.db)throw Error("Database not initialized");let e=await this.getServices(),t=await this.getActions(),s=await this.getQueueItems();return{services:e.length,actions:t.length,queue:s.length}}isOnline(){return navigator.onLine}constructor(){this.dbName="AcuiZenOfflineDB",this.version=1,this.db=null,this.stores={services:"services",actions:"actions",queue:"offlineQueue",metadata:"metadata"}}}let r=new a;r.init().catch(console.error)}}]);