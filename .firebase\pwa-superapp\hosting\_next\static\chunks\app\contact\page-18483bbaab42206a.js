(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{19019:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(95155),l=s(12115),i=["mousedown","touchstart"];let c=function(e,t,s){void 0===s&&(s=i);var a=(0,l.useRef)(t);(0,l.useEffect)(function(){a.current=t},[t]),(0,l.useEffect)(function(){for(var t=function(t){var s=e.current;s&&!s.contains(t.target)&&a.current(t)},l=0,i=s;l<i.length;l++)!function(e){for(var t=[],s=1;s<arguments.length;s++)t[s-1]=arguments[s];e&&e.addEventListener&&e.addEventListener.apply(e,t)}(document,i[l],t);return function(){for(var e=0,a=s;e<a.length;e++)!function(e){for(var t=[],s=1;s<arguments.length;s++)t[s-1]=arguments[s];e&&e.removeEventListener&&e.removeEventListener.apply(e,t)}(document,a[e],t)}},[s,e])},n=e=>{let{options:t,defaultCurrent:s,placeholder:i,className:n,onChange:r,name:o}=e,[d,m]=(0,l.useState)(!1),[h,u]=(0,l.useState)(t[s]),x=(0,l.useCallback)(()=>{m(!1)},[]),p=(0,l.useRef)(null);c(p,x);let f=e=>{u(e),r(e,o),x()};return(0,a.jsxs)("div",{className:"nice-select form-select-lg mb-3 ".concat(n||""," ").concat(d?"open":""),role:"button",tabIndex:0,onClick:()=>m(e=>!e),onKeyDown:e=>e,ref:p,children:[(0,a.jsx)("span",{className:"current",children:(null==h?void 0:h.text)||i}),(0,a.jsx)("ul",{className:"list",role:"menubar",onClick:e=>e.stopPropagation(),onKeyDown:e=>e.stopPropagation(),children:null==t?void 0:t.map((e,t)=>(0,a.jsx)("li",{"data-value":e.value,className:"option ".concat(e.value===(null==h?void 0:h.value)?"selected focus":""),style:{fontSize:"14px"},role:"menuitem",onClick:()=>f(e),onKeyDown:e=>e,children:e.text},t))})]})}},21217:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var a=s(95155),l=s(9e4),i=s(38808),c=s(12115);let n=e=>{let{handleShowSetting:t,showSetting:s}=e,{theme:c,handleDarkModeToggle:n}=(0,l.D)(),{viewMode:r,handleRTLToggling:o}=(0,i.L)();return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{id:"setting-popup-overlay",className:s?"active":"",onClick:t}),(0,a.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(s?"active":""),id:"settingCard",children:(0,a.jsx)("div",{className:"card-body",children:(0,a.jsxs)("div",{className:"container",children:[(0,a.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,a.jsx)("p",{className:"mb-0",children:"Settings"}),(0,a.jsx)("div",{onClick:t,className:"btn-close",id:"settingCardClose"})]}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,a.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,a.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===c,onChange:n}),(0,a.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===c?"Light":"Dark"," mode"]})]})}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===r,onChange:o}),(0,a.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===r?"LTR":"RTL"," mode"]})]})})]})})})]})};var r=s(6874),o=s.n(r);let d=e=>{let{links:t,title:s}=e,[l,i]=(0,c.useState)(!1),r=()=>i(!l);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"header-area",id:"headerArea",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,a.jsx)("div",{className:"back-button",children:(0,a.jsx)(o(),{href:"/".concat(t),children:(0,a.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,a.jsx)("div",{className:"page-heading",children:(0,a.jsx)("h6",{className:"mb-0",children:s})}),(0,a.jsx)("div",{className:"setting-wrapper",onClick:r,children:(0,a.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,a.jsx)("i",{className:"bi bi-gear"}),(0,a.jsx)("span",{})]})})]})})}),(0,a.jsx)(n,{showSetting:l,handleShowSetting:r})]})}},38808:(e,t,s)=>{"use strict";s.d(t,{L:()=>l});var a=s(12115);let l=()=>{let[e,t]=(0,a.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,a.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let s=()=>{t(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:s,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}s()}}}},38983:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var a=s(95155),l=s(6874),i=s.n(l);s(12115);let c=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],n=()=>(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,a.jsx)("div",{className:"container px-0",children:(0,a.jsx)("div",{className:"footer-nav position-relative",children:(0,a.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:c.map((e,t)=>(0,a.jsx)("li",{children:(0,a.jsxs)(i(),{href:"/".concat(e.link),children:[(0,a.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,a.jsx)("span",{children:e.title})]})},t))})})})})})},47562:(e,t,s)=>{Promise.resolve().then(s.bind(s,53874)),Promise.resolve().then(s.bind(s,38983)),Promise.resolve().then(s.bind(s,21217))},53874:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var a=s(95155);s(12115);var l=s(19019);let i=()=>(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"page-content-wrapper py-3",children:[(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"card mb-3",children:(0,a.jsxs)("div",{className:"card-body",children:[(0,a.jsx)("h5",{className:"mb-3",children:"Write to us"}),(0,a.jsx)("div",{className:"contact-form",children:(0,a.jsxs)("form",{onSubmit:e=>e.preventDefault(),children:[(0,a.jsx)("div",{className:"form-group mb-3",children:(0,a.jsx)("input",{className:"form-control",type:"text",placeholder:"Your name"})}),(0,a.jsx)("div",{className:"form-group mb-3",children:(0,a.jsx)("input",{className:"form-control",type:"email",placeholder:"Enter email"})}),(0,a.jsx)("div",{className:"form-group mb-3 rk_select",children:(0,a.jsx)(l.A,{className:"form-select d-flex align-items-center",options:[{value:"01",text:"Authors Help"},{value:"02",text:"Buyer Help"},{value:"03",text:"Licenses"}],defaultCurrent:0,onChange:e=>{},placeholder:"Select an option",name:"myNiceSelect"})}),(0,a.jsx)("div",{className:"form-group mb-3",children:(0,a.jsx)("textarea",{className:"form-control",name:"textarea",cols:30,rows:10,placeholder:"Write details"})}),(0,a.jsx)("button",{className:"btn btn-primary w-100",children:"Send Now"})]})})]})})}),(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"card",children:(0,a.jsx)("div",{className:"card-body",children:(0,a.jsxs)("div",{className:"google-maps",children:[(0,a.jsx)("h5",{className:"mb-3",children:"Our office location"}),(0,a.jsx)("iframe",{className:"w-100",src:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d37902.096510377676!2d101.6393079588335!3d3.103387873464772!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31cc49c701efeae7%3A0xf4d98e5b2f1c287d!2sKuala%20Lumpur%2C%20Federal%20Territory%20of%20Kuala%20Lumpur%2C%20Malaysia!5e0!3m2!1sen!2sbd!4v1591684973931!5m2!1sen!2sbd",allowFullScreen:!0,"aria-hidden":"false",tabIndex:0})]})})})})]})})},9e4:(e,t,s)=>{"use strict";s.d(t,{D:()=>l});var a=s(12115);let l=()=>{let[e,t]=(0,a.useState)("light"),[s,l]=(0,a.useState)(!1);(0,a.useEffect)(()=>{t(localStorage.getItem("theme")||"light"),l(!0)},[]),(0,a.useEffect)(()=>{s&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,s]);let i=(0,a.useCallback)(()=>{t(e=>"dark"===e?"light":"dark")},[]),c=(0,a.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}i()},[i]);return{theme:e,toggleTheme:i,handleDarkModeToggle:c}}}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,8441,1684,7358],()=>t(47562)),_N_E=e.O()}]);