(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2072],{46554:(e,s,a)=>{"use strict";a.d(s,{default:()=>o});var c=a(95155),i=a(12115),l=a(35695),t=a(38336),d=a(26957),r=a(34540),n=a(81359);let o=e=>{let{heading:s}=e,a=(0,l.useRouter)(),[o,x]=(0,i.useState)(""),m=(0,r.wA)();i.useEffect(()=>{b()},[]);let b=async()=>{try{let e=await t.A.get(d.AM);200===e.status?(x(e.data.firstName),m(n.l.setUser(e.data))):a.push("/")}catch(e){console.log(e)}};return(0,c.jsx)(c.Fragment,{children:(0,c.jsx)("div",{className:"header-area",id:"headerArea",children:(0,c.jsx)("div",{className:"container",children:(0,c.jsxs)("div",{className:"header-content header-style-four position-relative d-flex align-items-center justify-content-between",children:[(0,c.jsx)("div",{className:"back-button",children:(0,c.jsx)("button",{onClick:()=>a.back(),className:"border-0 bg-transparent p-0",style:{cursor:"pointer"},children:(0,c.jsx)("i",{className:"bi bi-arrow-left-short fs-3"})})}),(0,c.jsx)("div",{className:"page-heading",children:(0,c.jsx)("h6",{className:"mb-0",children:s})}),(0,c.jsx)("div",{className:"user-profile-wrapper"})]})})})})}},52702:(e,s,a)=>{"use strict";a.d(s,{A:()=>d});var c=a(95155),i=a(12115),l=a(38336),t=a(26957);let d=e=>{let{logo:s}=e,[a,d]=(0,i.useState)("");return(0,i.useEffect)(()=>{let e=async()=>{try{let e=await l.A.post(t.J9,{presignedUrl:s},{responseType:"blob"}),a=new FileReader;a.onloadend=()=>{"string"==typeof a.result&&d(a.result)},a.readAsDataURL(e.data)}catch(e){console.error("Error fetching logo blob:",e)}};s&&e()},[s]),(0,c.jsx)("img",{src:a||"/default-logo.png",alt:"Logo",style:{maxWidth:"125px",height:"auto"}})}},53686:(e,s,a)=>{"use strict";a.d(s,{A:()=>d});var c=a(95155);a(12115);var i=a(82940),l=a.n(i),t=a(65677);let d=e=>{var s,a;let{reportData:i}=e;return(0,c.jsxs)("div",{className:"observation-report",style:{background:"transparent",padding:"0"},children:["High-Risk Hazard"!==i.type?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("div",{className:"card border-0 mb-3",style:{backgroundColor:"#f8fafc"},children:(0,c.jsxs)("div",{className:"card-body p-3",children:[(0,c.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-2",children:[(0,c.jsx)("i",{className:"bi bi-briefcase text-primary"}),(0,c.jsx)("h6",{className:"section-title mb-0",children:"Work Activity"})]}),(0,c.jsx)("p",{className:"obs-dec text-muted mb-0",children:"Routine"===i.type?null==(s=i.workActivity)?void 0:s.name:i.description})]})}),(0,c.jsx)("div",{className:"card border-0 mb-3",style:{backgroundColor:"#f8fafc"},children:(0,c.jsxs)("div",{className:"card-body p-3",children:[(0,c.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-2",children:[(0,c.jsx)("i",{className:"bi bi-building text-primary"}),(0,c.jsx)("h6",{className:"section-title mb-0",children:"Department"})]}),(0,c.jsx)("p",{className:"obs-dec text-muted mb-0",children:"Routine"===i.type?null==(a=i.department)?void 0:a.name:i.description})]})})]}):(0,c.jsx)("div",{className:"card border-0 mb-3",style:{backgroundColor:"#fef2f2"},children:(0,c.jsxs)("div",{className:"card-body p-3",children:[(0,c.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-2",children:[(0,c.jsx)("i",{className:"bi bi-exclamation-triangle text-danger"}),(0,c.jsx)("h6",{className:"section-title mb-0",children:"Hazard Name"})]}),(0,c.jsx)("p",{className:"obs-dec text-muted mb-0",children:i.hazardName})]})}),(0,c.jsxs)("div",{className:"row g-3 mb-3",children:[(0,c.jsx)("div",{className:"col-md-6",children:(0,c.jsx)("div",{className:"card border-0",style:{backgroundColor:"#f0f9ff"},children:(0,c.jsxs)("div",{className:"card-body p-3",children:[(0,c.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-2",children:[(0,c.jsx)("i",{className:"bi bi-tag text-info"}),(0,c.jsx)("h6",{className:"obs-title mb-0",children:"Type"})]}),(0,c.jsx)("p",{className:"obs-content mb-0",children:i.type})]})})}),(0,c.jsx)("div",{className:"col-md-6",children:(0,c.jsx)("div",{className:"card border-0",style:{backgroundColor:"#f0f9ff"},children:(0,c.jsxs)("div",{className:"card-body p-3",children:[(0,c.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-2",children:[(0,c.jsx)("i",{className:"bi bi-calendar3 text-info"}),(0,c.jsx)("h6",{className:"obs-title mb-0",children:"Reported Date"})]}),(0,c.jsx)("p",{className:"obs-content mb-0",children:i.created?l()(i.created).format("Do MMM YYYY, hh:mm:ss a"):"N/A"})]})})})]}),"High-Risk Hazard"!==i.type?(0,c.jsxs)("div",{className:"",children:[(0,c.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-3",children:[(0,c.jsx)("i",{className:"bi bi-list-check text-primary"}),(0,c.jsx)("h5",{className:"mb-0 fw-semibold",children:"Risk Assessment Tasks"})]}),(0,c.jsx)(t.A,{children:i.tasks.map((e,s)=>(0,c.jsxs)(t.A.Item,{eventKey:s.toString(),className:"mb-3 border-0 shadow-sm",style:{borderRadius:"12px",overflow:"hidden"},children:[(0,c.jsx)(t.A.Header,{style:{borderRadius:"12px 12px 0 0"},children:(0,c.jsxs)("div",{className:"d-flex align-items-center gap-2",children:[(0,c.jsx)("span",{className:"badge bg-primary text-white",style:{fontSize:"10px"},children:s+1}),e[0].name||"Sub Activity ".concat(s+1)]})}),(0,c.jsxs)(t.A.Body,{style:{backgroundColor:"#fafbfc"},children:[(0,c.jsx)("div",{className:"mb-4",children:(0,c.jsx)("div",{className:"card border-0 mb-3",style:{backgroundColor:"#fff7ed"},children:(0,c.jsxs)("div",{className:"card-body p-3",children:[(0,c.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-3",children:[(0,c.jsx)("i",{className:"bi bi-exclamation-triangle text-warning"}),(0,c.jsx)("h6",{className:"obs-title mb-0",children:"Identified Hazards"})]}),e[1].selected.map((e,s)=>(0,c.jsx)("div",{className:"card border-0 mb-2 shadow-sm",children:(0,c.jsx)("div",{className:"card-body p-3",children:(0,c.jsxs)("div",{className:"d-flex align-items-center gap-3",children:[(0,c.jsx)("div",{className:"p-2 rounded",style:{backgroundColor:"#f3f4f6"},children:(0,c.jsx)("img",{src:"https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/".concat(e.image),style:{height:32,width:32,objectFit:"contain"},alt:"hazard"})}),(0,c.jsx)("div",{className:"flex-grow-1",children:(0,c.jsx)("p",{className:"mb-0 fw-medium",children:e.name})})]})})},s))]})})}),(0,c.jsxs)("div",{className:"row g-3",children:[(0,c.jsx)("div",{className:"col-md-6",children:(0,c.jsx)("div",{className:"card border-0",style:{backgroundColor:"#fef2f2"},children:(0,c.jsxs)("div",{className:"card-body p-3",children:[(0,c.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-3",children:[(0,c.jsx)("i",{className:"bi bi-exclamation-circle text-danger"}),(0,c.jsx)("h6",{className:"obs-title mb-0",children:"Consequences"})]}),e[2].option.map((e,s)=>(0,c.jsx)("div",{className:"card border-0 mb-2",style:{backgroundColor:"rgba(255,255,255,0.7)"},children:(0,c.jsx)("div",{className:"card-body p-2",children:(0,c.jsxs)("div",{className:"d-flex align-items-center gap-2",children:[(0,c.jsx)("span",{className:"badge bg-danger text-white",style:{fontSize:"9px"},children:e.current_type}),(0,c.jsx)("span",{className:"small",children:e.value})]})})},s))]})})}),(0,c.jsx)("div",{className:"col-md-6",children:(0,c.jsx)("div",{className:"card border-0",style:{backgroundColor:"#f0fdf4"},children:(0,c.jsxs)("div",{className:"card-body p-3",children:[(0,c.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-3",children:[(0,c.jsx)("i",{className:"bi bi-shield-check text-success"}),(0,c.jsx)("h6",{className:"obs-title mb-0",children:"Controls"})]}),e[3].option.map((e,s)=>(0,c.jsx)("div",{className:"card border-0 mb-2",style:{backgroundColor:"rgba(255,255,255,0.7)"},children:(0,c.jsx)("div",{className:"card-body p-2",children:(0,c.jsxs)("div",{className:"d-flex align-items-center gap-2",children:[(0,c.jsx)("span",{className:"badge bg-success text-white",style:{fontSize:"9px"},children:e.current_type}),(0,c.jsx)("span",{className:"small",children:e.value})]})})},s))]})})})]})]})]},s))})]}):(0,c.jsxs)("div",{className:"",children:[(0,c.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-3",children:[(0,c.jsx)("i",{className:"bi bi-exclamation-triangle-fill text-danger"}),(0,c.jsx)("h5",{className:"mb-0 fw-semibold text-danger",children:"High-Risk Assessment"})]}),i.tasks.map((e,s)=>(0,c.jsx)("div",{className:"mb-4",children:(0,c.jsxs)("div",{className:"row g-3",children:[(0,c.jsx)("div",{className:"col-md-6",children:(0,c.jsx)("div",{className:"card border-0",style:{backgroundColor:"#fef2f2"},children:(0,c.jsxs)("div",{className:"card-body p-3",children:[(0,c.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-3",children:[(0,c.jsx)("i",{className:"bi bi-exclamation-circle text-danger"}),(0,c.jsx)("h6",{className:"obs-title mb-0",children:"Consequences"})]}),e[0].option.map((e,s)=>(0,c.jsx)("div",{className:"card border-0 mb-2",style:{backgroundColor:"rgba(255,255,255,0.7)"},children:(0,c.jsx)("div",{className:"card-body p-2",children:(0,c.jsxs)("div",{className:"d-flex align-items-center gap-2",children:[(0,c.jsx)("span",{className:"badge bg-danger text-white",style:{fontSize:"9px"},children:e.current_type}),(0,c.jsx)("span",{className:"small",children:e.value})]})})},s))]})})}),(0,c.jsx)("div",{className:"col-md-6",children:(0,c.jsx)("div",{className:"card border-0",style:{backgroundColor:"#f0fdf4"},children:(0,c.jsxs)("div",{className:"card-body p-3",children:[(0,c.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-3",children:[(0,c.jsx)("i",{className:"bi bi-shield-check text-success"}),(0,c.jsx)("h6",{className:"obs-title mb-0",children:"Controls"})]}),e[1].option.map((e,s)=>(0,c.jsx)("div",{className:"card border-0 mb-2",style:{backgroundColor:"rgba(255,255,255,0.7)"},children:(0,c.jsx)("div",{className:"card-body p-2",children:(0,c.jsxs)("div",{className:"d-flex align-items-center gap-2",children:[(0,c.jsx)("span",{className:"badge bg-success text-white",style:{fontSize:"9px"},children:e.current_type}),(0,c.jsx)("span",{className:"small",children:e.value})]})})},s))]})})})]})},s))]})]})}},60306:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>g});var c=a(95155),i=a(11518),l=a.n(i),t=a(46554),d=a(12115),r=a(35695),n=a(38336),o=a(26957),x=a(56160),m=a(60902),b=a(52702),h=a(53686),j=a(90371);let g=()=>{(0,r.useRouter)();let[e,s]=(0,d.useState)("activity"),[a,i]=(0,d.useState)([]),[g,p]=(0,d.useState)([]),[f,u]=(0,d.useState)(0),[N,y]=(0,d.useState)(0),[v,k]=(0,d.useState)(null),[w,z]=(0,d.useState)(!1),[S,A]=(0,d.useState)({risk:!1,highRisk:!1}),[C,R]=(0,d.useState)({risk:null,highRisk:null}),[H,F]=(0,d.useState)(""),L=async()=>{try{A(e=>({...e,risk:!0})),R(e=>({...e,risk:null}));let e={where:{$and:[{$or:[{status:"Pending"},{status:"Published"}]},{$or:[{type:"Routine"},{type:"Non Routine"}]}]},include:[{relation:"department"},{relation:"teamLeader"},{relation:"workActivity"},{relation:"raTeamMembers",scope:{include:[{relation:"user"}]}}]},s="".concat(o.dm,"?filter=").concat(encodeURIComponent(JSON.stringify(e))),a=await n.A.get(s);200===a.status&&(i(a.data),u(a.data.length))}catch(e){R(e=>({...e,risk:"Failed to fetch risk assessments."})),console.error(e)}finally{A(e=>({...e,risk:!1}))}},E=async()=>{try{A(e=>({...e,highRisk:!0})),R(e=>({...e,highRisk:null}));let e={where:{$and:[{$or:[{status:"Pending"},{status:"Published"}]},{type:"High-Risk Hazard"}]},include:[{relation:"department"},{relation:"teamLeader"},{relation:"workActivity"},{relation:"raTeamMembers",scope:{include:[{relation:"user"}]}}]},s="".concat(o.dm,"?filter=").concat(encodeURIComponent(JSON.stringify(e))),a=await n.A.get(s);200===a.status&&(p(a.data),y(a.data.length))}catch(e){R(e=>({...e,highRisk:"Failed to fetch high-risk hazards."})),console.error(e)}finally{A(e=>({...e,highRisk:!1}))}};(0,d.useEffect)(()=>{T()},[]);let T=async()=>{try{let e=(await n.A.get((0,o._i)(localStorage.getItem("logo")),{headers:{"Content-Type":"application/json"}})).data;F(e)}catch(e){console.error("Error fetching logo:",e)}};(0,d.useEffect)(()=>{"activity"===e?L():"hazard"===e&&E()},[e]);let P=e=>{k(e),z(!0)};return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(t.default,{heading:"Risk Awareness"}),(0,c.jsxs)(j.default,{pageTitle:"Risk Awareness",requiresOnline:!0,children:[(0,c.jsx)("div",{style:{backgroundColor:"#f8fafc",minHeight:"100vh"},className:"jsx-d072c8c01a0755b6 page-content-wrapper",children:(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 container-fluid px-3 py-4",children:[(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 mb-4",children:(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 card border-0 shadow-sm",children:(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 card-body p-2",children:(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 d-flex gap-1",children:[{id:"activity",label:"Activity Based",icon:"bi-list-task",count:f},{id:"hazard",label:"Hazard Based",icon:"bi-exclamation-triangle",count:N}].map(a=>(0,c.jsxs)("button",{style:{border:"none",fontWeight:"600",fontSize:"14px",transition:"all 0.3s ease",position:"relative"},onClick:()=>s(a.id),className:"jsx-d072c8c01a0755b6 "+"btn flex-fill py-3  rounded-3 d-flex align-items-center justify-content-center gap-2 ".concat(e===a.id?"btn-primary text-white shadow-sm":"btn-light text-muted"),children:[(0,c.jsx)("i",{className:"jsx-d072c8c01a0755b6 "+"".concat(a.icon," ").concat(e===a.id?"text-white":"text-primary")}),(0,c.jsx)("span",{className:"jsx-d072c8c01a0755b6",children:a.label}),a.count>0&&(0,c.jsx)("span",{style:{fontSize:"10px"},className:"jsx-d072c8c01a0755b6 "+"badge ".concat(e===a.id?"bg-white text-primary":"bg-primary text-white"," ms-1"),children:a.count})]},a.id))})})})}),(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6",children:["activity"===e&&(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6",children:[(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 card border-0 shadow-sm mb-4",children:(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 card-body py-3",children:(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex justify-content-between align-items-center",children:[(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex align-items-center gap-3",children:[(0,c.jsx)("div",{style:{backgroundColor:"#e0f2fe"},className:"jsx-d072c8c01a0755b6 p-2 rounded-circle",children:(0,c.jsx)("i",{style:{fontSize:"1.2rem"},className:"jsx-d072c8c01a0755b6 bi bi-list-task text-primary"})}),(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6",children:[(0,c.jsx)("h6",{style:{color:"#1f2937",fontWeight:"600"},className:"jsx-d072c8c01a0755b6 mb-0",children:"Activity Based Risk Assessments"}),(0,c.jsx)("small",{className:"jsx-d072c8c01a0755b6 text-muted",children:"Routine and non-routine activities"})]})]}),(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 text-end",children:(0,c.jsxs)("span",{style:{fontSize:"12px"},className:"jsx-d072c8c01a0755b6 badge bg-primary px-3 py-2",children:[f," items"]})})]})})}),(0,c.jsxs)("div",{style:{maxHeight:"calc(100vh - 300px)",overflowY:"auto"},className:"jsx-d072c8c01a0755b6 custom-scrollbar",children:[S.risk&&(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 text-center py-5",children:(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex flex-column align-items-center",children:[(0,c.jsx)("div",{role:"status",className:"jsx-d072c8c01a0755b6 spinner-border text-primary mb-3",children:(0,c.jsx)("span",{className:"jsx-d072c8c01a0755b6 visually-hidden",children:"Loading..."})}),(0,c.jsx)("p",{className:"jsx-d072c8c01a0755b6 text-muted mb-0",children:"Loading risk assessments..."})]})}),C.risk&&(0,c.jsx)("div",{role:"alert",className:"jsx-d072c8c01a0755b6 alert alert-danger border-0 shadow-sm",children:(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex align-items-center",children:[(0,c.jsx)("i",{className:"jsx-d072c8c01a0755b6 bi bi-exclamation-triangle-fill me-2"}),C.risk]})}),a.length>0?(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 row g-3",children:a.map((e,s)=>{var a;return(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 col-12",children:(0,c.jsx)("div",{style:{cursor:"pointer",transition:"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",boxShadow:"0 4px 12px rgba(0,0,0,0.08)",borderRadius:"16px",backgroundColor:"#FFFFFF",borderLeft:"4px solid #3b82f6"},onClick:()=>P(e),onMouseEnter:e=>{e.currentTarget.style.transform="translateY(-4px)",e.currentTarget.style.boxShadow="0 12px 32px rgba(59, 130, 246, 0.15)"},onMouseLeave:e=>{e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0,0,0,0.08)"},className:"jsx-d072c8c01a0755b6 card border-0 h-100",children:(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 card-body p-4",children:[(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex justify-content-between align-items-start mb-3",children:[(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex align-items-center gap-2",children:[(0,c.jsx)("div",{style:{backgroundColor:"#eff6ff"},className:"jsx-d072c8c01a0755b6 p-2 rounded-circle",children:(0,c.jsx)("i",{style:{fontSize:"0.9rem"},className:"jsx-d072c8c01a0755b6 bi bi-clipboard-check text-primary"})}),(0,c.jsxs)("span",{style:{fontSize:"13px"},className:"jsx-d072c8c01a0755b6 text-muted fw-medium",children:["#",e.maskId]})]}),(0,c.jsx)("span",{style:{fontSize:"11px",fontWeight:"600"},className:"jsx-d072c8c01a0755b6 "+"badge px-3 py-1 ".concat("Published"===e.status?"bg-success":"Pending"===e.status?"bg-warning text-dark":"bg-secondary"),children:e.status})]}),(0,c.jsx)("h6",{style:{color:"#1f2937",lineHeight:"1.4",fontSize:"15px"},className:"jsx-d072c8c01a0755b6 mb-3 fw-semibold",children:"Routine"===e.type?null==(a=e.workActivity)?void 0:a.name:e.description}),(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex justify-content-between align-items-center",children:[(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex align-items-center gap-1 text-muted",children:[(0,c.jsx)("i",{style:{fontSize:"12px"},className:"jsx-d072c8c01a0755b6 bi bi-calendar3"}),(0,c.jsx)("small",{style:{fontSize:"12px"},className:"jsx-d072c8c01a0755b6",children:new Date(e.created).toLocaleDateString()})]}),(0,c.jsx)("span",{style:{fontSize:"10px",fontWeight:"600"},className:"jsx-d072c8c01a0755b6 "+"badge ".concat("Routine"===e.type?"bg-primary":"bg-info"," text-white px-2 py-1"),children:e.type||"N/A"})]})]})})},s)})}):!S.risk&&(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 text-center py-5",children:(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex flex-column align-items-center",children:[(0,c.jsx)("div",{style:{backgroundColor:"#f3f4f6"},className:"jsx-d072c8c01a0755b6 p-4 rounded-circle mb-3",children:(0,c.jsx)("i",{style:{fontSize:"2.5rem"},className:"jsx-d072c8c01a0755b6 bi bi-clipboard-x text-muted"})}),(0,c.jsx)("h6",{className:"jsx-d072c8c01a0755b6 text-muted mb-2",children:"No Risk Assessments Found"}),(0,c.jsx)("p",{className:"jsx-d072c8c01a0755b6 text-muted small mb-0",children:"There are no activity-based risk assessments available at the moment."})]})})]})]}),"hazard"===e&&(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6",children:[(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 card border-0 shadow-sm mb-4",children:(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 card-body py-3",children:(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex justify-content-between align-items-center",children:[(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex align-items-center gap-3",children:[(0,c.jsx)("div",{style:{backgroundColor:"#fef2f2"},className:"jsx-d072c8c01a0755b6 p-2 rounded-circle",children:(0,c.jsx)("i",{style:{fontSize:"1.2rem"},className:"jsx-d072c8c01a0755b6 bi bi-exclamation-triangle text-danger"})}),(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6",children:[(0,c.jsx)("h6",{style:{color:"#1f2937",fontWeight:"600"},className:"jsx-d072c8c01a0755b6 mb-0",children:"High-Risk Hazards"}),(0,c.jsx)("small",{className:"jsx-d072c8c01a0755b6 text-muted",children:"Critical hazards requiring immediate attention"})]})]}),(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 text-end",children:(0,c.jsxs)("span",{style:{fontSize:"12px"},className:"jsx-d072c8c01a0755b6 badge bg-danger px-3 py-2",children:[N," items"]})})]})})}),(0,c.jsxs)("div",{style:{maxHeight:"calc(100vh - 300px)",overflowY:"auto"},className:"jsx-d072c8c01a0755b6 custom-scrollbar",children:[S.highRisk&&(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 text-center py-5",children:(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex flex-column align-items-center",children:[(0,c.jsx)("div",{role:"status",className:"jsx-d072c8c01a0755b6 spinner-border text-danger mb-3",children:(0,c.jsx)("span",{className:"jsx-d072c8c01a0755b6 visually-hidden",children:"Loading..."})}),(0,c.jsx)("p",{className:"jsx-d072c8c01a0755b6 text-muted mb-0",children:"Loading high-risk hazards..."})]})}),C.highRisk&&(0,c.jsx)("div",{role:"alert",className:"jsx-d072c8c01a0755b6 alert alert-danger border-0 shadow-sm",children:(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex align-items-center",children:[(0,c.jsx)("i",{className:"jsx-d072c8c01a0755b6 bi bi-exclamation-triangle-fill me-2"}),C.highRisk]})}),g.length>0?(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 row g-3",children:g.map((e,s)=>(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 col-12",children:(0,c.jsx)("div",{style:{cursor:"pointer",transition:"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",borderLeft:"4px solid #dc2626",boxShadow:"0 4px 12px rgba(220, 38, 38, 0.12)",borderRadius:"16px",backgroundColor:"#FFFFFF"},onClick:()=>P(e),onMouseEnter:e=>{e.currentTarget.style.transform="translateY(-4px)",e.currentTarget.style.boxShadow="0 12px 32px rgba(220, 38, 38, 0.25)"},onMouseLeave:e=>{e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="0 4px 12px rgba(220, 38, 38, 0.12)"},className:"jsx-d072c8c01a0755b6 card border-0 h-100",children:(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 card-body p-4",children:[(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex justify-content-between align-items-start mb-3",children:[(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex align-items-center gap-2",children:[(0,c.jsx)("div",{style:{backgroundColor:"#fef2f2"},className:"jsx-d072c8c01a0755b6 p-2 rounded-circle",children:(0,c.jsx)("i",{style:{fontSize:"0.9rem"},className:"jsx-d072c8c01a0755b6 bi bi-exclamation-triangle-fill text-danger"})}),(0,c.jsxs)("span",{style:{fontSize:"13px"},className:"jsx-d072c8c01a0755b6 text-muted fw-medium",children:["#",e.maskId]})]}),(0,c.jsx)("span",{style:{fontSize:"11px",fontWeight:"600"},className:"jsx-d072c8c01a0755b6 "+"badge px-3 py-1 ".concat("Published"===e.status?"bg-success":"Pending"===e.status?"bg-warning text-dark":"bg-secondary"),children:e.status})]}),(0,c.jsx)("h6",{style:{color:"#1f2937",lineHeight:"1.4",fontSize:"15px"},className:"jsx-d072c8c01a0755b6 mb-3 fw-semibold",children:e.hazardName||"No Description"}),(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex justify-content-between align-items-center",children:[(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex align-items-center gap-1 text-muted",children:[(0,c.jsx)("i",{style:{fontSize:"12px"},className:"jsx-d072c8c01a0755b6 bi bi-calendar3"}),(0,c.jsx)("small",{style:{fontSize:"12px"},className:"jsx-d072c8c01a0755b6",children:new Date(e.created).toLocaleDateString()})]}),(0,c.jsxs)("span",{style:{fontSize:"10px",fontWeight:"600"},className:"jsx-d072c8c01a0755b6 badge bg-danger text-white px-2 py-1",children:[(0,c.jsx)("i",{style:{fontSize:"8px"},className:"jsx-d072c8c01a0755b6 bi bi-exclamation-triangle-fill me-1"}),"High Risk"]})]})]})})},s))}):!S.highRisk&&(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 text-center py-5",children:(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex flex-column align-items-center",children:[(0,c.jsx)("div",{style:{backgroundColor:"#f0fdf4"},className:"jsx-d072c8c01a0755b6 p-4 rounded-circle mb-3",children:(0,c.jsx)("i",{style:{fontSize:"2.5rem"},className:"jsx-d072c8c01a0755b6 bi bi-shield-check text-success"})}),(0,c.jsx)("h6",{className:"jsx-d072c8c01a0755b6 text-success mb-2",children:"All Clear!"}),(0,c.jsx)("p",{className:"jsx-d072c8c01a0755b6 text-muted small mb-0",children:"No high-risk hazards found. Your workplace is currently safe."})]})})]})]})]})]})}),(0,c.jsxs)(x.A,{show:w,onHide:()=>z(!1),centered:!0,size:"lg",children:[(0,c.jsx)(x.A.Header,{closeButton:!0,className:"border-0 pb-0",children:v&&(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 w-100",children:(0,c.jsx)("div",{className:"jsx-d072c8c01a0755b6 d-flex align-items-center justify-content-between",children:(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex align-items-center gap-3",children:[(0,c.jsx)(b.A,{logo:H}),(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6",children:[(0,c.jsx)("h5",{className:"jsx-d072c8c01a0755b6 mb-1 fw-semibold",children:"Risk Assessment Details"}),(0,c.jsxs)("div",{className:"jsx-d072c8c01a0755b6 d-flex align-items-center gap-2",children:[(0,c.jsxs)("span",{className:"jsx-d072c8c01a0755b6 text-muted small",children:["#",v.maskId||""]}),(0,c.jsx)("span",{style:{fontSize:"11px"},className:"jsx-d072c8c01a0755b6 "+"badge px-2 py-1 ".concat("Published"===v.status?"bg-success":"Pending"===v.status?"bg-warning text-dark":"bg-secondary"),children:v.status})]})]})]})})})}),(0,c.jsx)(x.A.Body,{className:"pt-2",children:(0,c.jsx)(h.A,{reportData:v})}),(0,c.jsx)(x.A.Footer,{className:"border-0 pt-0",children:(0,c.jsxs)(m.A,{variant:"outline-secondary",onClick:()=>z(!1),className:"px-4",children:[(0,c.jsx)("i",{className:"jsx-d072c8c01a0755b6 bi bi-x-lg me-2"}),"Close"]})})]}),(0,c.jsx)(l(),{id:"d072c8c01a0755b6",children:".observation-report.jsx-d072c8c01a0755b6{background:#fff;-webkit-border-radius:10px;-moz-border-radius:10px;border-radius:10px;padding:20px}.section-title.jsx-d072c8c01a0755b6{font-size:1.1rem;font-weight:bold;color:#333}.obs-title.jsx-d072c8c01a0755b6{font-size:.9rem;font-weight:bold;color:#555;margin-bottom:5px}.obs-content.jsx-d072c8c01a0755b6{font-size:.9rem;color:#777}.image-box.jsx-d072c8c01a0755b6{border:1px solid#ddd;background:#f8f9fa;height:100px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.custom-scrollbar.jsx-d072c8c01a0755b6{scrollbar-width:thin;scrollbar-color:#cbd5e1#f1f5f9}.custom-scrollbar.jsx-d072c8c01a0755b6::-webkit-scrollbar{width:6px}.custom-scrollbar.jsx-d072c8c01a0755b6::-webkit-scrollbar-track{background:#f1f5f9;-webkit-border-radius:3px;-moz-border-radius:3px;border-radius:3px}.custom-scrollbar.jsx-d072c8c01a0755b6::-webkit-scrollbar-thumb{background:#cbd5e1;-webkit-border-radius:3px;-moz-border-radius:3px;border-radius:3px}.custom-scrollbar.jsx-d072c8c01a0755b6::-webkit-scrollbar-thumb:hover{background:#94a3b8}@media(max-width:768px){.card-body.jsx-d072c8c01a0755b6{padding:1rem!important}}"})]})]})}},90371:(e,s,a)=>{"use strict";a.d(s,{default:()=>d});var c=a(95155),i=a(12115),l=a(79459),t=a(36651);let d=e=>{let{children:s,pageTitle:a="Page",requiresOnline:d=!1,customFallback:r,className:n=""}=e,[o,x]=(0,i.useState)(!0);if((0,i.useEffect)(()=>{x(navigator.onLine);let e=()=>x(!0),s=()=>x(!1);return window.addEventListener("online",e),window.addEventListener("offline",s),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}},[]),d&&!o){let e=r||(0,c.jsx)(t.A,{title:"".concat(a," Unavailable Offline"),message:"".concat(a," requires an internet connection. Please check your connection and try again."),showHomeLink:!0});return(0,c.jsx)("div",{className:n,children:e})}return(0,c.jsx)(l.A,{className:n,showOfflineIndicator:!0,children:s})}},90927:(e,s,a)=>{Promise.resolve().then(a.bind(a,60306))}},e=>{var s=s=>e(e.s=s);e.O(0,[586,6874,6078,635,4816,5898,7746,381,1434,8441,1684,7358],()=>s(90927)),_N_E=e.O()}]);