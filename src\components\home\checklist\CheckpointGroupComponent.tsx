"use client";

import React from "react";
import { Card, Form } from "react-bootstrap";
import DatePicker from "react-datepicker";
import { ChecklistComponent, CheckpointGroupData, ErrorBuckets, OptionType } from "../types/ChecklistTypes";
import FileUploader from "@/services/FileUploader";
import ModalSelect from "@/services/ModalSelect";

interface CheckpointGroupComponentProps {
    component: ChecklistComponent;
    componentIndex: number;
    checklistData: ChecklistComponent[];
    setChecklistData: React.Dispatch<React.SetStateAction<ChecklistComponent[]>>;
    errorMap: ErrorBuckets;
    assessor: OptionType[];
    handleFileUpload: (files: string[], componentIndex: number, checkpointIndex?: number) => void;
}

const CheckpointGroupComponent: React.FC<CheckpointGroupComponentProps> = ({
    component,
    componentIndex,
    checklistData,
    setChecklistData,
    errorMap,
    assessor,
    handleFileUpload,
}) => {
    const groupData = component.data as CheckpointGroupData;
    const ansErr = errorMap.group[`${componentIndex}-answer`];

    return (
        <Card className="mb-3 shadow-sm" style={{
            border: "1px solid #e5e7eb",
            borderRadius: "8px",
            overflow: "hidden"
        }}>
            <Card.Body className="p-0">
                {/* Group header */}
                <div className="p-3 border-bottom" style={{
                    backgroundColor: "#3b82f6",
                    color: "white"
                }}>
                    <h6 className="fw-semibold mb-0 d-flex align-items-center" style={{ fontSize: "0.95rem" }}>
                        <i className="bi bi-list-ul me-2"></i>
                        {groupData.title}
                    </h6>
                </div>

                <div className="p-3">
                    {/* Group checkbox instead of Yes/No buttons */}
                    <div className="d-flex align-items-center mb-3 p-3 border rounded" style={{
                        backgroundColor: component.groupAnswer === "Yes" ? "#f0f9ff" : "#f9fafb",
                        borderColor: component.groupAnswer === "Yes" ? "#3b82f6" : "#d1d5db"
                    }}>
                        <div className="form-check form-switch me-3">
                            <input
                                className="form-check-input"
                                type="checkbox"
                                id={`group-check-${componentIndex}`}
                                style={{
                                    width: "3em",
                                    height: "1.5em"
                                }}
                                checked={component.groupAnswer === "Yes"}
                                onChange={(e) => {
                                    const updated = [...checklistData];
                                    updated[componentIndex].groupAnswer = e.target.checked ? "Yes" : "No";
                                    if (!e.target.checked) {
                                        // If unchecked (No), clear checkpoints
                                        if (updated[componentIndex].checkpoints) {
                                            updated[componentIndex].checkpoints!.forEach((cp) => {
                                                cp.selected = "";
                                                cp.remarks = "";
                                                cp.actionToBeTaken = "";
                                                cp.dueDate = null;
                                                cp.assignee = "";
                                                cp.uploads = [];
                                            });
                                        }
                                    }
                                    setChecklistData(updated);
                                }}
                            />
                        </div>
                        <div>
                            <span className="fw-semibold" style={{
                                color: component.groupAnswer === "Yes" ? "#3b82f6" : "#6b7280",
                                fontSize: "0.95rem"
                            }}>
                                {component.groupAnswer === "Yes" ? "✓ Section is applicable" : "Section not applicable"}
                            </span>
                            <br />
                            <span className="small text-muted">
                                {component.groupAnswer === "Yes"
                                    ? "Complete the checkpoints below"
                                    : "Toggle to show section checkpoints"}
                            </span>
                        </div>
                    </div>
                    {ansErr && (
                        <div className="mb-3 small p-2 rounded text-danger bg-light border border-danger">
                            {ansErr}
                        </div>
                    )}

                    {/* Reason field when group answer is No */}
                    {component.groupAnswer === "No" && (
                        <div className="mb-3">
                            <Form.Group>
                                <Form.Label>Reason for not applicable</Form.Label>
                                <Form.Control
                                    as="textarea"
                                    rows={3}
                                    placeholder="Please provide a reason why this section is not applicable..."
                                    value={component.reason || ""}
                                    onChange={(e) => {
                                        const updated = [...checklistData];
                                        updated[componentIndex].reason = e.target.value;
                                        setChecklistData(updated);
                                    }}
                                    style={{
                                        borderRadius: "8px",
                                        borderWidth: "2px",
                                        padding: "0.75rem"
                                    }}
                                />
                            </Form.Group>
                        </div>
                    )}

                    {/* Group Checkpoints when YES */}
                    {component.groupAnswer === "Yes" && component.checkpoints && (
                        <div className="ps-3 pt-2" style={{
                            borderLeft: "3px solid #3b82f6",
                            backgroundColor: "#f8fafc"
                        }}>
                            {component.checkpoints.map((checkpoint, cpIdx) => {
                                const sel = checkpoint.selected || "";
                                const base = `${componentIndex}-${cpIdx}`;
                                return (
                                    <div className="mb-3 p-3 rounded border" key={cpIdx} style={{
                                        backgroundColor: sel === "Yes" ? "#f0fdf4" : sel === "No" ? "#fef2f2" : "#ffffff",
                                        borderColor: sel === "Yes" ? "#10b981" : sel === "No" ? "#ef4444" : "#e5e7eb"
                                    }}>
                                        <div className="mb-3 fw-semibold d-flex align-items-start">
                                            <i className="bi bi-check-circle me-2 mt-1" style={{
                                                color: sel === "Yes" ? "#10b981" : sel === "No" ? "#ef4444" : "#6b7280",
                                                fontSize: "1rem"
                                            }}></i>
                                            <span style={{ color: "#374151", fontSize: "0.9rem" }}>{checkpoint.text}</span>
                                        </div>

                                        {/* radio buttons - mobile friendly */}
                                        <div className="d-flex flex-column flex-sm-row w-100 mb-2 gap-2">
                                            {["Yes", "No", "N/A"].map((opt) => {
                                                const id = `cp-${componentIndex}-${cpIdx}-${opt}`;
                                                const isSelected = sel === opt;

                                                return (
                                                    <div key={opt} className="flex-grow-1">
                                                        <input
                                                            type="radio"
                                                            className="btn-check"
                                                            id={id}
                                                            name={`cp-${componentIndex}-${cpIdx}`}
                                                            autoComplete="off"
                                                            checked={sel === opt}
                                                            onChange={() => {
                                                                const upd = [...checklistData];
                                                                if (upd[componentIndex].checkpoints) {
                                                                    upd[componentIndex].checkpoints![cpIdx].selected = opt as any;
                                                                    if (opt !== "No") {
                                                                        upd[componentIndex].checkpoints![cpIdx].actionToBeTaken = "";
                                                                        upd[componentIndex].checkpoints![cpIdx].dueDate = null;
                                                                        upd[componentIndex].checkpoints![cpIdx].assignee = "";
                                                                        upd[componentIndex].checkpoints![cpIdx].uploads = [];
                                                                    }
                                                                    upd[componentIndex].checkpoints![cpIdx].remarks = "";
                                                                }
                                                                setChecklistData(upd);
                                                            }}
                                                        />
                                                        <label
                                                            className={`btn w-100 ${
                                                                opt === "Yes"
                                                                    ? isSelected ? "btn-success" : "btn-outline-success"
                                                                    : opt === "No"
                                                                    ? isSelected ? "btn-danger" : "btn-outline-danger"
                                                                    : isSelected ? "btn-warning" : "btn-outline-warning"
                                                            }`}
                                                            htmlFor={id}
                                                            style={{ fontSize: "0.9rem", padding: "0.5rem" }}
                                                        >
                                                            {opt}
                                                        </label>
                                                    </div>
                                                );
                                            })}
                                        </div>
                                        {errorMap.checklist[`${base}-sel`] && (
                                            <div className="small p-2 rounded mb-2" style={{
                                                color: "#dc2626",
                                                backgroundColor: "#fef2f2",
                                                border: "1px solid #fecaca"
                                            }}>
                                                {errorMap.checklist[`${base}-sel`]}
                                            </div>
                                        )}

                                        {/* Remarks for Yes/N/A */}
                                        {["Yes", "N/A"].includes(sel) && (
                                            <Form.Group className="mb-3">
                                                <Form.Control
                                                    placeholder="Remarks (mandatory)..."
                                                    value={checkpoint.remarks || ""}
                                                    onChange={(e) => {
                                                        const upd = [...checklistData];
                                                        if (upd[componentIndex].checkpoints) {
                                                            upd[componentIndex].checkpoints![cpIdx].remarks = e.target.value;
                                                        }
                                                        setChecklistData(upd);
                                                    }}
                                                    isInvalid={!!errorMap.checklist[`${base}-remarks`]}
                                                    style={{
                                                        borderRadius: "8px",
                                                        borderWidth: "2px",
                                                        padding: "0.75rem"
                                                    }}
                                                />
                                                <Form.Control.Feedback type="invalid">
                                                    {errorMap.checklist[`${base}-remarks`]}
                                                </Form.Control.Feedback>
                                            </Form.Group>
                                        )}

                                        {/* Extra fields when answer is NO */}
                                        {sel === "No" && (
                                            <div className="p-3 rounded border border-danger bg-light">
                                                <h6 className="mb-3 d-flex align-items-center fw-semibold text-danger">
                                                    <i className="bi bi-exclamation-triangle me-2"></i>
                                                    Action Required
                                                </h6>

                                                {/* Remarks */}
                                                <Form.Group className="mb-2">
                                                    <Form.Label>Remarks</Form.Label>
                                                    <Form.Control
                                                        placeholder="Remarks (mandatory)..."
                                                        value={checkpoint.remarks || ""}
                                                        onChange={(e) => {
                                                            const upd = [...checklistData];
                                                            if (upd[componentIndex].checkpoints) {
                                                                upd[componentIndex].checkpoints![cpIdx].remarks = e.target.value;
                                                            }
                                                            setChecklistData(upd);
                                                        }}
                                                        isInvalid={!!errorMap.checklist[`${base}-remarks`]}
                                                    />
                                                    <Form.Control.Feedback type="invalid">
                                                        {errorMap.checklist[`${base}-remarks`]}
                                                    </Form.Control.Feedback>
                                                </Form.Group>

                                                {/* Action */}
                                                <Form.Group className="mb-2">
                                                    <Form.Label>Actions to be taken</Form.Label>
                                                    <Form.Control
                                                        placeholder="Actions to be taken *"
                                                        value={checkpoint.actionToBeTaken || ""}
                                                        onChange={(e) => {
                                                            const upd = [...checklistData];
                                                            if (upd[componentIndex].checkpoints) {
                                                                upd[componentIndex].checkpoints![cpIdx].actionToBeTaken = e.target.value;
                                                            }
                                                            setChecklistData(upd);
                                                        }}
                                                        isInvalid={!!errorMap.checklist[`${base}-action`]}
                                                    />
                                                    <Form.Control.Feedback type="invalid">
                                                        {errorMap.checklist[`${base}-action`]}
                                                    </Form.Control.Feedback>
                                                </Form.Group>

                                                {/* Upload */}
                                                {/* <Form.Group className="mb-2">
                                                    <Form.Label>Upload media (optional)</Form.Label>
                                                    <FileUploader
                                                        onFilesSelected={(updatedList: string[]) => {
                                                            handleFileUpload(updatedList, componentIndex, cpIdx);
                                                        }}
                                                        disabled={false}
                                                        files={checkpoint.uploads || []}
                                                    />
                                                </Form.Group> */}

                                                {/* Due Date */}
                                                <Form.Group className="mb-2">
                                                    <Form.Label>Due Date</Form.Label>
                                                    <div
                                                        className={
                                                            errorMap.checklist[`${base}-due`]
                                                                ? "border border-danger rounded"
                                                                : ""
                                                        }
                                                    >
                                                        <DatePicker
                                                            selected={checkpoint.dueDate ? new Date(checkpoint.dueDate) : null}
                                                            onChange={(d: Date | null | undefined) => {
                                                                const upd = [...checklistData];
                                                                if (upd[componentIndex].checkpoints) {
                                                                    upd[componentIndex].checkpoints![cpIdx].dueDate = d ? d.toISOString() : null;
                                                                }
                                                                setChecklistData(upd);
                                                            }}
                                                            minDate={new Date()}
                                                            placeholderText="Select due date"
                                                            dateFormat="dd-MM-yyyy"
                                                            className="form-control"
                                                            popperClassName="datepicker-high-zindex"
                                                        />
                                                    </div>
                                                    {errorMap.checklist[`${base}-due`] && (
                                                        <div className="text-danger small mt-1">
                                                            {errorMap.checklist[`${base}-due`]}
                                                        </div>
                                                    )}
                                                </Form.Group>

                                                {/* Assignee */}
                                                <Form.Group className="mb-2">
                                                    <Form.Label>Assign Action to *</Form.Label>
                                                    <ModalSelect
                                                        title={""}
                                                        options={assessor}
                                                        selectedValue={checkpoint.assignee}
                                                        onChange={(newVal: any) => {
                                                            const upd = [...checklistData];
                                                            if (upd[componentIndex].checkpoints) {
                                                                upd[componentIndex].checkpoints![cpIdx].assignee = newVal;
                                                            }
                                                            setChecklistData(upd);
                                                        }}
                                                        placeholder="Select Action Owner"
                                                        clearable
                                                        disabled={false}
                                                    />
                                                    {errorMap.checklist[`${base}-own`] && (
                                                        <div className="text-danger small mt-1">
                                                            {errorMap.checklist[`${base}-own`]}
                                                        </div>
                                                    )}
                                                </Form.Group>
                                            </div>
                                        )}
                                    </div>
                                );
                            })}
                        </div>
                    )}
                </div>
            </Card.Body>
        </Card>
    );
};

export default CheckpointGroupComponent;
