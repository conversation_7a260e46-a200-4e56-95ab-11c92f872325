{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next export", "build:firebase": "next build && next export"}, "dependencies": {"@capacitor/camera": "^7.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^7.0.2", "@mui/x-date-pickers": "^8.0.0", "@reduxjs/toolkit": "^2.5.1", "apexcharts": "^3.50.0", "axios": "^1.11.0", "bootstrap": "^5.3.3", "exifr": "^7.1.3", "firebase": "^11.8.1", "firebase-admin": "^13.2.0", "firebase-functions": "^6.3.2", "moment": "^2.30.1", "moment-timezone": "^0.5.47", "next": "^15.3.5", "next-pwa": "^5.6.0", "piexifjs": "^1.0.6", "react": "^18", "react-18-image-lightbox": "^5.1.4", "react-apexcharts": "^1.4.1", "react-bootstrap": "^2.10.9", "react-countup": "^6.5.3", "react-datepicker": "^8.3.0", "react-dom": "^18", "react-intersection-observer": "^9.10.3", "react-modal-video": "^2.0.2", "react-password-checklist": "^1.8.0", "react-redux": "^9.2.0", "react-responsive-masonry": "^2.2.1", "react-select": "^5.10.0", "react-signature-canvas": "^1.1.0-alpha.1", "react-timer-hook": "^3.0.7", "react-use": "^17.5.0", "sass": "^1.77.6", "sharp": "^0.33.4", "sweetalert2": "^11.22.2", "swiper": "^11.1.4", "yet-another-react-lightbox": "^3.21.7"}, "devDependencies": {"@types/bootstrap": "^5.2.10", "@types/node": "^20", "@types/piexifjs": "^1.0.0", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-modal-video": "^1.2.3", "@types/react-responsive-masonry": "^2.1.3", "@types/react-signature-canvas": "^1.0.7", "esbuild": "^0.25.5", "eslint": "^8", "eslint-config-next": "^15.3.5", "typescript": "^5"}}