import React from 'react';
import moment from 'moment';
import ImageComponent from '@/services/FileDownlodS3';

interface OptionType {
    value: string;
    label: string;
}

interface Hazard {
    id: number;
    name: string;
    image: string;
    toolbox_value?: string;
    toolbox_remarks?: string;
}

interface Control {
    value: string;
    toolbox_value?: string;
    toolbox_remarks?: string;
}

interface Task {
    type: 'activity' | 'hazards' | 'current_control';
    name?: string;
    selected?: Hazard[];
    option?: Control[];
}

interface SignatureStatus {
    signedBy: {
        firstName: string;
    };
    sign: string;
}

interface Challenge {
    unexpectedChallenges: string;
    remarks: string;
}

interface ViewTBTProps {
    formData: {
        commenceDate?: string;
        riskAssessment?: { workActivity?: { name?: string } };
        conductedBy?: { firstName?: string };
        locationOne?: { name?: string };
        locationTwo?: { name?: string };
        locationThree?: { name?: string };
        locationFour?: { name?: string };
        noOfPersonsParticipated?: number;
        tasks?: Task[][];
        controls: {
            isAdditionalControlsIdentified: boolean;
            describeAdditionalControls?: string;
            teamBrief?: string;
            teamBriefRemarks?: string;
        };
        toolboxSignStatuses: SignatureStatus[];
        isCloseOutChallenges?: boolean;
        closeOutChallenges?: Challenge[];
    };
}

const ViewTBT: React.FC<ViewTBTProps> = ({ formData }) => {
    return (
        <div style={{ background: 'transparent' }}>
            {/* Basic Information Cards */}
            <div className="row g-3 mb-4">
                <div className="col-md-6">
                    <div className="card border-0" style={{ backgroundColor: "#f0f9ff" }}>
                        <div className="card-body p-3">
                            <div className="d-flex align-items-center gap-2 mb-2">
                                <i className="bi bi-calendar-event text-info"></i>
                                <h6 className="mb-0 fw-semibold">Commence Date</h6>
                            </div>
                            <p className="mb-0 text-muted">
                                {formData.commenceDate
                                    ? moment(formData.commenceDate).format("DD-MM-YYYY HH:mm")
                                    : "Not started"}
                            </p>
                        </div>
                    </div>
                </div>
                <div className="col-md-6">
                    <div className="card border-0" style={{ backgroundColor: "#f0f9ff" }}>
                        <div className="card-body p-3">
                            <div className="d-flex align-items-center gap-2 mb-2">
                                <i className="bi bi-person-badge text-info"></i>
                                <h6 className="mb-0 fw-semibold">Conducted By</h6>
                            </div>
                            <p className="mb-0 text-muted">{formData.conductedBy?.firstName || "-"}</p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Work Activity */}
            <div className="card border-0 mb-3" style={{ backgroundColor: "#f8fafc" }}>
                <div className="card-body p-3">
                    <div className="d-flex align-items-center gap-2 mb-2">
                        <i className="bi bi-briefcase text-primary"></i>
                        <h6 className="mb-0 fw-semibold">Work Activity / Situational Hazard</h6>
                    </div>
                    <p className="mb-0 text-muted">{formData.riskAssessment?.workActivity?.name || "-"}</p>
                </div>
            </div>

            {/* Location */}
            <div className="card border-0 mb-3" style={{ backgroundColor: "#f8fafc" }}>
                <div className="card-body p-3">
                    <div className="d-flex align-items-center gap-2 mb-2">
                        <i className="bi bi-geo-alt text-primary"></i>
                        <h6 className="mb-0 fw-semibold">Location</h6>
                    </div>
                    <p className="mb-0 text-muted">
                        {[formData?.locationOne?.name, formData?.locationTwo?.name, formData?.locationThree?.name, formData?.locationFour?.name]
                            .filter(Boolean)
                            .join(' > ') || "-"}
                    </p>
                </div>
            </div>

            {/* Participants */}
            <div className="card border-0 mb-4" style={{ backgroundColor: "#fef2f2" }}>
                <div className="card-body p-3">
                    <div className="d-flex align-items-center gap-2 mb-2">
                        <i className="bi bi-people text-danger"></i>
                        <h6 className="mb-0 fw-semibold">Participants</h6>
                    </div>
                    <div className="d-flex align-items-center gap-2">
                        <span className="badge bg-danger text-white px-3 py-2">
                            {formData.noOfPersonsParticipated ?? 0} people participated
                        </span>
                    </div>
                </div>
            </div>

            {/* Enhanced Hazards and Controls */}
            <div className="mb-4">
                <div className="d-flex align-items-center gap-2 mb-3">
                    <i className="bi bi-list-check text-primary"></i>
                    <h5 className="mb-0 fw-semibold">Safety Assessment Tasks</h5>
                </div>

                {formData?.tasks?.map((group: any, groupIndex: number) => (
                    <div key={groupIndex} className="card border-0 shadow-sm mb-4" style={{ borderRadius: "12px", overflow: "hidden" }}>
                        <div className="card-header bg-light border-0 py-3">
                            <div className="d-flex align-items-center gap-2">
                                <span className="badge bg-danger text-white" style={{ fontSize: "10px" }}>
                                    {groupIndex + 1}
                                </span>
                                <h6 className="mb-0 fw-semibold">Sub-Activity {groupIndex + 1}</h6>
                            </div>
                            {group.activity?.type === "activity" && (
                                <p className="mb-0 mt-2 text-muted small">
                                    <strong>Activity:</strong> {group.activity.name}
                                </p>
                            )}
                        </div>
                        <div className="card-body p-4" style={{ backgroundColor: "#fafbfc" }}>
                            {/* Hazards Section */}
                            {group.hazards?.selected?.length > 0 && (
                                <div className="mb-4">
                                    <div className="card border-0 mb-3" style={{ backgroundColor: "#fff7ed" }}>
                                        <div className="card-body p-3">
                                            <div className="d-flex align-items-center gap-2 mb-3">
                                                <i className="bi bi-exclamation-triangle text-warning"></i>
                                                <h6 className="mb-0 fw-semibold">Identified Hazards</h6>
                                            </div>
                                            {group.hazards.selected.map((hazard: any, hazardIndex: number) => (
                                                <div key={hazard.id} className="card border-0 mb-3 shadow-sm">
                                                    <div className="card-body p-3">
                                                        <div className="d-flex align-items-start gap-3 mb-3">
                                                            <div className="p-2 rounded" style={{ backgroundColor: "#f3f4f6" }}>
                                                                <img
                                                                    src={`https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/${hazard.image}`}
                                                                    alt={hazard.name}
                                                                    style={{ width: 40, height: 40, objectFit: "contain" }}
                                                                />
                                                            </div>
                                                            <div className="flex-grow-1">
                                                                <h6 className="mb-2 fw-medium">{hazard.name}</h6>
                                                                <div className="d-flex align-items-center gap-2 mb-2">
                                                                    <span className="small fw-medium">Team Briefed:</span>
                                                                    <span className={`badge ${
                                                                        hazard.toolbox_value === "Yes" ? "bg-success" :
                                                                        hazard.toolbox_value === "No" ? "bg-danger" : "bg-secondary"
                                                                    } text-white`} style={{ fontSize: "10px" }}>
                                                                        {hazard.toolbox_value}
                                                                    </span>
                                                                </div>
                                                                {(hazard.toolbox_value === "No" || hazard.toolbox_value === "Not Applicable") && (
                                                                    <div className="mt-2 p-2 rounded" style={{ backgroundColor: "#fef2f2" }}>
                                                                        <small className="text-muted">
                                                                            <strong>Remarks:</strong> {hazard.toolbox_remarks}
                                                                        </small>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Controls Section */}
                            {group.currentControl?.option?.length > 0 && (
                                <div className="card border-0" style={{ backgroundColor: "#f0fdf4" }}>
                                    <div className="card-body p-3">
                                        <div className="d-flex align-items-center gap-2 mb-3">
                                            <i className="bi bi-shield-check text-success"></i>
                                            <h6 className="mb-0 fw-semibold">Safety Controls</h6>
                                        </div>
                                        {group.currentControl.option.map((control: any, controlIndex: number) => (
                                            <div key={controlIndex} className="card border-0 mb-2 shadow-sm">
                                                <div className="card-body p-3">
                                                    <p className="mb-2 fw-medium">{control.value || "No control description provided"}</p>
                                                    <div className="d-flex align-items-center gap-2 mb-2">
                                                        <span className="small fw-medium">Implemented:</span>
                                                        <span className={`badge ${
                                                            control.toolbox_value === "Yes" ? "bg-success" :
                                                            control.toolbox_value === "No" ? "bg-danger" : "bg-secondary"
                                                        } text-white`} style={{ fontSize: "10px" }}>
                                                            {control.toolbox_value}
                                                        </span>
                                                    </div>
                                                    {(control.toolbox_value === "No" || control.toolbox_value === "Not Applicable") && (
                                                        <div className="mt-2 p-2 rounded" style={{ backgroundColor: "#fef2f2" }}>
                                                            <small className="text-muted">
                                                                <strong>Remarks:</strong> {control.toolbox_remarks}
                                                            </small>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>

            {/* Additional Controls Section */}
            <div className="card border-0 shadow-sm mb-4" style={{ borderRadius: "12px" }}>
                <div className="card-header bg-light border-0 py-3">
                    <div className="d-flex align-items-center gap-2">
                        <i className="bi bi-plus-circle text-info"></i>
                        <h6 className="mb-0 fw-semibold">Additional Controls</h6>
                    </div>
                </div>
                <div className="card-body p-4">
                    <div className="row g-3">
                        <div className="col-md-6">
                            <div className="card border-0" style={{ backgroundColor: "#f0f9ff" }}>
                                <div className="card-body p-3">
                                    <h6 className="mb-2 fw-medium">Additional Controls Identified?</h6>
                                    <span className={`badge ${
                                        formData.controls.isAdditionalControlsIdentified ? "bg-success" : "bg-secondary"
                                    } text-white px-3 py-2`}>
                                        {formData.controls.isAdditionalControlsIdentified ? "Yes" : "No"}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div className="col-md-6">
                            <div className="card border-0" style={{ backgroundColor: "#f0f9ff" }}>
                                <div className="card-body p-3">
                                    <h6 className="mb-2 fw-medium">Team Briefed</h6>
                                    <div className="d-flex align-items-center gap-2">
                                        <span className={`badge ${
                                            formData.controls.teamBrief === "Yes" ? "bg-success" :
                                            formData.controls.teamBrief === "No" ? "bg-danger" : "bg-secondary"
                                        } text-white`} style={{ fontSize: "10px" }}>
                                            {formData.controls.teamBrief}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {formData.controls.isAdditionalControlsIdentified && (
                        <div className="mt-3">
                            <div className="card border-0" style={{ backgroundColor: "#f8fafc" }}>
                                <div className="card-body p-3">
                                    <h6 className="mb-2 fw-medium">Description</h6>
                                    <p className="mb-0 text-muted">{formData.controls.describeAdditionalControls}</p>
                                </div>
                            </div>
                        </div>
                    )}

                    {(formData.controls.teamBrief === "No" || formData.controls.teamBrief === "Not Applicable") && (
                        <div className="mt-3">
                            <div className="card border-0" style={{ backgroundColor: "#fef2f2" }}>
                                <div className="card-body p-3">
                                    <h6 className="mb-2 fw-medium">Team Brief Remarks</h6>
                                    <p className="mb-0 text-muted">{formData.controls.teamBriefRemarks}</p>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Signatures Section */}
            <div className="card border-0 shadow-sm mb-4" style={{ borderRadius: "12px" }}>
                <div className="card-header bg-light border-0 py-3">
                    <div className="d-flex align-items-center gap-2">
                        <i className="bi bi-pen text-success"></i>
                        <h6 className="mb-0 fw-semibold">Team Member Acknowledgement</h6>
                    </div>
                </div>
                <div className="card-body p-4">
                    <div className="row g-3">
                        {formData.toolboxSignStatuses.map((item, index) => (
                            <div key={index} className="col-md-6">
                                <div className="card border-0" style={{ backgroundColor: "#f0fdf4" }}>
                                    <div className="card-body p-3">
                                        <div className="d-flex align-items-center gap-2 mb-3">
                                            <i className="bi bi-person-check text-success"></i>
                                            <h6 className="mb-0 fw-medium">{item.signedBy?.firstName || "-"}</h6>
                                        </div>
                                        <div className="text-center">
                                            <div className="p-3 rounded border" style={{ backgroundColor: "#ffffff", display: "inline-block" }}>
                                                <ImageComponent size={120} fileName={item.sign} />
                                            </div>
                                            <p className="small text-muted mt-2 mb-0">Digital Signature</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* Close Out Section */}
            <div className="card border-0 shadow-sm" style={{ borderRadius: "12px" }}>
                <div className="card-header bg-light border-0 py-3">
                    <div className="d-flex align-items-center gap-2">
                        <i className="bi bi-check-circle text-success"></i>
                        <h6 className="mb-0 fw-semibold">Session Close Out</h6>
                    </div>
                </div>
                <div className="card-body p-4">
                    <div className="card border-0 mb-3" style={{ backgroundColor: "#f0f9ff" }}>
                        <div className="card-body p-3">
                            <div className="d-flex align-items-center gap-2 mb-2">
                                <i className="bi bi-question-circle text-info"></i>
                                <h6 className="mb-0 fw-medium">Unexpected Challenges Encountered?</h6>
                            </div>
                            <span className={`badge ${
                                formData.isCloseOutChallenges ? "bg-warning text-dark" : "bg-success"
                            } text-white px-3 py-2`}>
                                {formData.isCloseOutChallenges ? "Yes" : "No"}
                            </span>
                        </div>
                    </div>

                    {formData.isCloseOutChallenges && formData.closeOutChallenges?.map((item, index) => (
                        <div key={index} className="card border-0 mb-3" style={{ backgroundColor: "#fff7ed" }}>
                            <div className="card-body p-3">
                                <div className="mb-3">
                                    <h6 className="fw-medium mb-2">Challenge {index + 1}</h6>
                                    <p className="mb-0 text-muted">{item.unexpectedChallenges}</p>
                                </div>
                                <div>
                                    <h6 className="fw-medium mb-2">Resolution/Remarks</h6>
                                    <p className="mb-0 text-muted">{item.remarks}</p>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default ViewTBT;
