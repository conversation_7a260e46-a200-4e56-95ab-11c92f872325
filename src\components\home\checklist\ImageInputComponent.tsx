"use client";

import React from "react";
import { ChecklistComponent, ErrorBuckets, ImageInputData } from "../types/ChecklistTypes";
import { Form, Alert } from "react-bootstrap";
import FileUploader from "@/services/FileUploader";

interface ImageInputComponentProps {
    component: ChecklistComponent;
    componentIndex: number;
    checklistData: ChecklistComponent[];
    setChecklistData: React.Dispatch<React.SetStateAction<ChecklistComponent[]>>;
    errorMap: ErrorBuckets;
}

const ImageInputComponent: React.FC<ImageInputComponentProps> = ({
    component,
    componentIndex,
    checklistData,
    setChecklistData,
    errorMap,
}) => {
    const componentData = component.data as ImageInputData;

    const handleFilesSelected = (files: string[]) => {
        const updated = [...checklistData];
        // Store files in both locations for compatibility
        updated[componentIndex].imageFiles = files;
        (updated[componentIndex].data as ImageInputData).uploads = files;
        setChecklistData(updated);
    };

    const errorKey = `${componentIndex}-image`;
    const hasError = errorMap.checklist[errorKey];

    return (
        <div className="mb-3">
            <Form.Group>
                <Form.Label className="fw-bold">
                    {componentData.label}
                    {componentData.required && <span className="text-danger"> *</span>}
                </Form.Label>

                {hasError && (
                    <Alert variant="danger" className="py-1 px-2 small mb-2">
                        {hasError}
                    </Alert>
                )}

                <div style={{ maxWidth: '100%', overflow: 'hidden' }}>
                    <FileUploader
                        onFilesSelected={handleFilesSelected}
                        disabled={false}
                        files={component.imageFiles || componentData.uploads || []}
                    />
                </div>
            </Form.Group>
        </div>
    );
};

export default ImageInputComponent;
