"use client";

import React, { useEffect, useState, useCallback, useRef } from "react";
import AllFilterLocation from "../../../services/AllFilterLocation";
import { Button, Form } from "react-bootstrap";
import FileUploader from "@/services/FileUploader";
import Select from 'react-select'
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import API from "@/services/API";
import { FILE_URL, GET_USER_ROLE_BY_MODE, OBSERVATION_REPORT_URL, PERMIT_REPORT_WITH_ID, PERMIT_REPORTS } from "@/constant";
import HeaderSeven from "@/layouts/headers/HeaderSeven";
import Swal from "sweetalert2";
import { useRouter } from "next/navigation";
import SignatureCanvas from 'react-signature-canvas'
import ModalSelect, { PickerOption } from "@/services/ModalSelect";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import {
    addHours,
    isSameDay,
    setHours,
    setMinutes,
} from 'date-fns';


interface PermitRiskControl {
    remarks: string;
    evidence: string[];
    description: string;
    currentType: string;
    files: string[];
    method: string;
    permitType: string;
    value: "Yes" | "No" | "Not Applicable" | "";
}

interface FormErrors {
    workDescription?: string;
    [key: string]: string | undefined;
}

interface PermitFormData {
    permitStartDate: string;
    permitEndDate: string | null;
    workDescription: string;
    nameOfSiteSupervisor: string;
    applicantContactNo: string;
    supervisorContactNo: string;
    workOrderNo: string;
    noOfWorkers: number;
    permitWorkType: string; // High‑Risk Hazard | Routine | Non Routine
    permitType: string[];
    supportingDocuments: string[];
    locationOneId: string;
    locationTwoId: string;
    locationThreeId: string;
    locationFourId: string;
    locationFiveId: string;
    locationSixId: string;
    reviewerId: string;
    assessorId: string;
    uploads: string[];
    permitTag: string;
    status: string;
    permitRiskControl: any[];
    riskAssessmentId: string;
    approverId: string;
    applicantStatus: {
        signature: string;
    };
}


const initialFormData: PermitFormData = {
    "uploads": [],
    "supportingDocuments": [],
    "permitStartDate": "",
    "permitEndDate": "",
    "workDescription": "",
    "nameOfSiteSupervisor": "",
    "applicantContactNo": "",
    "supervisorContactNo": "",
    "workOrderNo": "",
    "noOfWorkers": 0,
    "permitWorkType": "",
    "permitType": [],
    "permitTag": "",
    "status": "",
    "permitRiskControl": [],
    "riskAssessmentId": "",
    "assessorId": "",
    "approverId": "",
    "locationFiveId": "",
    "locationFourId": "",
    "locationOneId": "",
    "locationSixId": "",
    "locationThreeId": "",
    "locationTwoId": "",
    "reviewerId": "",
    "applicantStatus": {
        "signature": ""
    },
};

export default function Page() {
    const [formData, setFormData] = useState(initialFormData);
    const [actionOwner, setActionOwner] = useState([])
    const [reviewer, setReviewer] = useState([])
    const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
    const router = useRouter();
    const user = useSelector((state: RootState) => state.login.user);
    // Current time
    const now = new Date();

    // 24 hours from now
    const maxStartDate = addHours(now, 48);
    console.log(user)
    const [errors, setErrors] = useState<FormErrors>({});;
    const signRef = useRef<SignatureCanvas>(null);
    const [assessor, setAssessor] = useState([])
    const [permitTypeOptions, setPermitTypeOptions] = useState([]);

    const handleStartDateChange = (date: Date | null) => {
        // If the user clears the date (or the date is null), handle that case:
        const isoDate = date ? date.toISOString() : "";
        setFormData({
            ...formData,
            permitStartDate: isoDate,
            permitEndDate: "", // Clear end date when start date changes
        });
    };

    const handleEndDateChange = (date: Date | null) => {
        const isoDate = date ? date.toISOString() : "";
        setFormData({
            ...formData,
            permitEndDate: isoDate,
        });
    };

    const handleFilter = (
        locationOneId: string,
        locationTwoId: string,
        locationThreeId: string,
        locationFourId: string,
        locationFiveId: string,
        locationSixId: string
    ) => {
        setFormData((prev) => ({
            ...prev,
            locationOneId,
            locationTwoId,
            locationThreeId,
            locationFourId,
            locationFiveId,
            locationSixId
        }));
    };


    const handleApplicantChange = (selectedOption: any) => {
        setFormData({
            ...formData,
            reviewerId: selectedOption ? selectedOption.value : '',
        });
    };

    const fetchUsersByMode = useCallback(
        async (mode: "eptwReviewer" | "eptwAssessor", setter: Function) => {
            if (!formData.locationOneId) return; // wait for location
            try {
                const res = await API.post(GET_USER_ROLE_BY_MODE, {
                    locationOneId: formData.locationOneId,
                    locationTwoId: formData.locationTwoId,
                    locationThreeId: formData.locationThreeId,
                    locationFourId: formData.locationFourId,
                    mode,
                });
                if (res.status === 200) {
                    const options = res.data.map((u: any) => ({
                        label: `${u.firstName} ${u.lastName ?? ""}`.trim(),
                        value: u.id,
                    }));
                    setter(options);
                }
            } catch (err) {
                console.error(`Error fetching ${mode}:`, err);
            }
        },
        [formData.locationOneId, formData.locationTwoId, formData.locationThreeId, formData.locationFourId]
    );

    useEffect(() => {
        fetchUsersByMode("eptwReviewer", setReviewer);
        fetchUsersByMode("eptwAssessor", setAssessor);
    }, [formData]);




    const dataURItoFile = (dataURI: any, filename: any) => {
        var byteString = atob(dataURI.split(",")[1]);
        // separate out the mime component
        var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
        // write the bytes of the string to an ArrayBuffer
        var ab = new ArrayBuffer(byteString.length);
        var dw = new DataView(ab);
        for (var i = 0; i < byteString.length; i++) {
            dw.setUint8(i, byteString.charCodeAt(i));
        }
        return new File([ab], filename, { type: mimeString });
    };

    const uploadSignature = async () => {
        const filename = `${new Date().getTime()}_captin_sign.png`;
        const formData1 = new FormData();
        const signatureFile = dataURItoFile(
            signRef.current?.toDataURL("image/png"),
            filename
        );
        formData1.append('file', signatureFile);

        try {
            const response = await API.post(FILE_URL, formData1, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                }
            });

            if (response && response.status === 200) {
                return response.data.files[0].originalname;
            } else {
                throw new Error("File upload failed.");
            }
        } catch (error) {
            console.error("File upload error:", error);
            throw error;
        }
    };
    const validateFields = () => {
        const newErrors: any = {};

        if (!formData.permitStartDate) newErrors.permitStartDate = "Start date is required.";
        if (!formData.permitEndDate) newErrors.permitEndDate = "End date is required.";
        if (!formData.workDescription) newErrors.workDescription = "Work description is required.";
        if (!formData.nameOfSiteSupervisor) newErrors.nameOfSiteSupervisor = "Site supervisor name is required.";
        if (!formData.applicantContactNo) newErrors.applicantContactNo = "Applicant Contact number is required.";
        if (!formData.supervisorContactNo) newErrors.supervisorContactNo = "Supervisor Contact number is required.";
        if (!formData.noOfWorkers) newErrors.noOfWorkers = "Number of workers is required.";
        if (!formData.permitWorkType) newErrors.permitWorkType = "Permit Type is required.";
        if (!formData.permitType || formData.permitType.length === 0) {
            newErrors.permitType = "Checkpoint selection is required.";
        }
        if (!formData.supportingDocuments || formData.supportingDocuments.length < 2) {
            newErrors.supportingDocuments = "Minimum 2 files must be uploaded.";
        }


        if (user?.type === 'External') {
            if (!formData.reviewerId) newErrors.reviewerId = "Reviewer is required.";
        }
        if (user?.type === 'Internal') {
            if (!formData.assessorId) newErrors.assessorId = "Assessor is required.";
        }

        if (signRef.current && signRef.current.isEmpty()) {
            newErrors.signature = "Applicant signature is required.";
        }

        formData.permitRiskControl?.forEach((control, index) => {
            if (!control.value) {
                newErrors[`control_value_${index}`] = "Select Yes, No, or Not Applicable.";
            }
            if ((control.value === "No" || control.value === "Not Applicable") && !control.remarks?.trim()) {
                newErrors[`control_remarks_${index}`] = "Remarks required for No or Not Applicable.";
            }
            // if (!control.evidence || control.evidence.length < 1) {
            //     newErrors[`control_evidence_${index}`] = "Upload at least one evidence file.";
            // }
        });
        console.log(newErrors)
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async () => {
        if (validateFields()) {
            try {
                // Upload signature if provided
                const uploadedSignature = await uploadSignature();

                // Update formData with uploaded signature
                const updatedFormData = {
                    ...formData,
                    applicantStatus: {
                        ...formData.applicantStatus,
                        signature: uploadedSignature,
                        status: true,
                        comments: '',
                        signedDate: new Date().toISOString()
                    },
                    noOfWorkers: Number(formData.noOfWorkers)
                };


                console.log(updatedFormData)
                // Determine API call based on mode
                const response = await API.post(PERMIT_REPORTS, updatedFormData);

                if (response.status === 200) {
                    Swal.fire(
                        "Permit",
                        "Submitted Successfully",
                        "success"
                    );
                    router.back()
                    // window.location.reload();
                }
            } catch (error) {
                console.error('Error saving permit:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Save Error',
                    text:
                        (error as { response?: { data?: { message?: string } } })?.response?.data?.message ||
                        'An error occurred while saving the permit. Please try again.',
                });
            }
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Save Error',
                text: 'Please fill all required fields and try again.',
            });
        }
    };
    const fetchPermitTypeOptions = async (endpoint: any) => {
        try {
            const response = await API.get(`/${endpoint}`);
            const options = response.data.map((item: any) => ({
                value: item.hazardName,
                label: item.hazardName,
                id: item.riskAssessmentId,
                controls: item.controls
            }));
            setPermitTypeOptions(options);
        } catch (error) {
            console.error("Error fetching permit type options:", error);
        }
    };
    useEffect(() => {
        if (formData.permitWorkType === 'High-Risk Hazard') {
            fetchPermitTypeOptions('high-risk-hazard-list');
        } else if (formData.permitWorkType === 'Routine') {
            fetchPermitTypeOptions('routine-list');
        } else if (formData.permitWorkType === 'Non Routine') {
            fetchPermitTypeOptions('non-routine-list');
        }
    }, [formData.permitWorkType]);
    useEffect(() => {
        setFormData({
            ...formData,
            permitWorkType: "High-Risk Hazard",
            permitType: [],
            permitRiskControl: []
        });
    }, [])

    const handlePermitTypeChange = (selectedOptions: any) => {
        if (!selectedOptions) {
            setFormData((prev) => ({
                ...prev,
                permitType: [],
                permitRiskControl: [],
                riskAssessmentId: "",
            }));
            return;
        }

        // only one selection allowed for every work‑type right now
        if (selectedOptions.length > 1) {
            Swal.fire({
                icon: "error",
                title: "Selection Error",
                text: "Only one checkpoint can be selected for this Permit Work Type.",
            });
            selectedOptions = selectedOptions.slice(0, 1);
        }

        const selectedHazardNames = selectedOptions.map((o: any) => o.value);
        const selectedControls: PermitRiskControl[] = selectedOptions.flatMap(
            (o: any) =>
                o.controls.map((c: any) => ({
                    remarks: "",
                    evidence: [],
                    description: c.value,
                    currentType: c.current_type,
                    files: c.files,
                    method: c.method,
                    permitType: o.label,
                    value: "",
                }))
        );

        setFormData((prev) => ({
            ...prev,
            permitType: selectedHazardNames,
            permitRiskControl: selectedControls,
            riskAssessmentId: selectedOptions[0]?.id || "",
        }));
    };


    const groupedControls = formData.permitRiskControl.reduce((acc, control, index) => {
        (acc[control.permitType] = acc[control.permitType] || []).push({
            ...control,
            controlIndex: index
        });
        return acc;
    }, {});

    const handleRiskControlChange = (
        index: number,
        field: keyof PermitRiskControl,
        value: any
    ) => {
        setFormData((prev) => {
            const updated = [...prev.permitRiskControl];
            (updated[index] as any)[field] = value;
            return { ...prev, permitRiskControl: updated };
        });
    };


    const getBoxStyle = (option: string, selected: string) => ({
        cursor: "pointer",
        fontSize: '12px',
        padding: "12px 20px",
        margin: "0 8px",
        borderRadius: "8px",
        border: selected === option ? "none" : "2px solid #e0e0e0",
        backgroundColor:
            selected === option
                ? option === "Yes"
                    ? "#4CAF50"
                    : option === "No"
                        ? "#F44336"
                        : "#FFC107"
                : "#ffffff",
        color: selected === option ? "#ffffff" : "#424242",
        textAlign: "center" as const,
        width: "160px",
        fontWeight: selected === option ? "600" : "normal",
        boxShadow: selected === option
            ? "0 4px 8px rgba(0,0,0,0.1)"
            : "none",
        transition: "all 0.3s ease",
        className: "option-box"  // Add this class
    });

    const getBoxClass = (option: string, selected: string) => {
        const baseClass = "btn rounded-pill px-4 me-2 mb-2";
        const isSelected = selected === option;

        if (option === "Yes") {
            return `${baseClass} ${isSelected ? "btn-success text-white" : "btn-outline-success"}`;
        }
        if (option === "No") {
            return `${baseClass} ${isSelected ? "btn-danger text-white" : "btn-outline-danger"}`;
        }
        if (option === "Not Applicable") {
            return `${baseClass} ${isSelected ? "btn-warning text-white" : "btn-outline-warning"}`;
        }
        return baseClass;
    };

    const handleBoxClick = (controlIndex: number, selectedValue: string) => {
        handleRiskControlChange(
            controlIndex,
            "value",
            selectedValue as PermitRiskControl["value"]
        );
    };

    const handleEvidenceUpload = async (controlIndex: number, files: string[]) => {

        setFormData((prev) => {
            const updatedControls = [...prev.permitRiskControl];

            // Ensure the evidence field is initialized as an array
            if (!Array.isArray(updatedControls[controlIndex].evidence)) {
                updatedControls[controlIndex].evidence = [];
            }

            // Add all files to the evidence array
            updatedControls[controlIndex].evidence = files;

            return {
                ...prev,
                permitRiskControl: updatedControls,
            };
        });
    };



    function getStartDateMaxTime() {
        // The latest the start date/time can be is 24 hours from `now`.
        if (isSameDay(maxStartDate, formData.permitStartDate || now)) {
            return maxStartDate;
        } else {
            // If the user picks a date other than `now`’s date, allow full day
            return setHours(setMinutes(new Date(), 59), 23);
        }
    }

    function getEndDateMinTime() {
        // The end date/time must always be >= start date/time
        if (!formData.permitStartDate) return now;
        const start = formData.permitStartDate;
        const end = formData.permitEndDate || formData.permitStartDate;

        if (isSameDay(start, end)) {
            // If end date is the same day as start date
            return start;
        } else {
            // If end date is a different day, let them choose from midnight onward
            return setHours(setMinutes(new Date(), 0), 0);
        }
    }

    function getEndDateMaxTime() {
        if (!formData.permitStartDate) {
            return addHours(now, 48);
        }
        const maxDateTime = addHours(formData.permitStartDate, 48);
        const end = formData.permitEndDate || formData.permitStartDate;
    
        if (isSameDay(maxDateTime, end)) {
            return maxDateTime;
        } else {
            return setHours(setMinutes(new Date(maxDateTime), 59), 23);
        }
    }
    
    

    return (<>



        <HeaderSeven heading={'New Permit'} />
        <div className="page-content-wrapper py-3">
            <div className="container">
                <div className="card">
                    <div className="card-body p-3">
                        {/* Permit Work Type Selection */}
                        <Form.Group className="mb-3">
                            <Form.Label>Permit Work Type *</Form.Label>
                            <Form.Select
                                value={formData.permitWorkType}
                                onChange={(e) =>
                                    setFormData((prev) => ({
                                        ...prev,
                                        permitWorkType: e.target.value,
                                        permitType: [], // Reset permit type when work type changes
                                        permitRiskControl: [] // Reset risk controls
                                    }))
                                }
                            >
                                <option value="">Select Permit Work Type</option>
                                <option value="High-Risk Hazard">High-Risk Hazard Permit</option>
                                <option value="Routine">Routine Work Activity</option>
                                <option value="Non Routine">Non Routine Work Activity</option>
                            </Form.Select>
                            {errors.permitWorkType && (
                                <p className="text-danger">{errors.permitWorkType}</p>
                            )}
                        </Form.Group>

                        {/* Checkpoint Selection */}
                        {formData.permitWorkType && (
                            <Form.Group className="mb-3">
                                <Form.Label>Checkpoints *</Form.Label>
                                <Select
                                    options={permitTypeOptions}
                                    value={permitTypeOptions.filter((option) =>
                                        formData.permitType?.includes((option as { value: string }).value)
                                    )}
                                    onChange={handlePermitTypeChange}
                                    placeholder="Select Check Point(s)"
                                    isMulti
                                    isClearable
                                    isDisabled={!formData.permitWorkType}
                                />
                                {errors.permitType && (
                                    <p className="text-danger">{errors.permitType}</p>
                                )}
                            </Form.Group>
                        )}

                    
                        <Form.Group className="row mb-3">
                            <div className="col-md-12">
                                <Form.Label className='d-flex'>
                                    Permit Start Date and Time *
                                </Form.Label>
                                <Form.Control
                                    type="datetime-local"
                                    value={
                                        formData.permitStartDate
                                            ? new Date(formData.permitStartDate).toISOString().slice(0, 16)
                                            : ""
                                    }
                                    onChange={(e) => {
                                        if (e.target.value) {
                                            const date = new Date(e.target.value);
                                            // Basic validation - check if it's not in the past and within 48 hours
                                            const isNotInPast = date >= now;
                                            const isWithin48Hours = date <= maxStartDate;

                                            if (isNotInPast && isWithin48Hours) {
                                                handleStartDateChange(date);
                                            } else {
                                                let errorMessage = "Please select a valid date and time.";
                                                if (!isNotInPast) {
                                                    errorMessage = "Start date/time cannot be in the past.";
                                                } else if (!isWithin48Hours) {
                                                    errorMessage = "Start date/time must be within 48 hours from now.";
                                                }
                                                Swal.fire({
                                                    icon: 'error',
                                                    title: 'Invalid Date/Time',
                                                    text: errorMessage,
                                                });
                                            }
                                        } else {
                                            handleStartDateChange(null);
                                        }
                                    }}
                                    min={now.toISOString().slice(0, 16)}
                                    max={maxStartDate.toISOString().slice(0, 16)}
                                />
                                {errors.permitStartDate && (
                                    <p className="text-danger">{errors.permitStartDate}</p>
                                )}
                            </div>
                            <div className="col-md-12">
                                <Form.Label className='d-flex'>
                                    Permit End Date and Time *
                                </Form.Label>
                                <Form.Control
                                    type="datetime-local"
                                    value={
                                        formData.permitEndDate
                                            ? new Date(formData.permitEndDate).toISOString().slice(0, 16)
                                            : ""
                                    }
                                    onChange={(e) => {
                                        if (e.target.value) {
                                            const date = new Date(e.target.value);
                                            // Basic validation for end date
                                            if (formData.permitStartDate) {
                                                const startDate = new Date(formData.permitStartDate);
                                                const isAfterStart = date >= startDate;
                                                const isWithin48Hours = date <= addHours(startDate, 48);

                                                if (isAfterStart && isWithin48Hours) {
                                                    handleEndDateChange(date);
                                                } else {
                                                    let errorMessage = "Please select a valid end date/time.";
                                                    if (!isAfterStart) {
                                                        errorMessage = "End date/time must be after start date/time.";
                                                    } else if (!isWithin48Hours) {
                                                        errorMessage = "End date/time must be within 48 hours of start date/time.";
                                                    }
                                                    Swal.fire({
                                                        icon: 'error',
                                                        title: 'Invalid Date/Time',
                                                        text: errorMessage,
                                                    });
                                                }
                                            } else {
                                                handleEndDateChange(date);
                                            }
                                        } else {
                                            handleEndDateChange(null);
                                        }
                                    }}
                                    disabled={!formData.permitStartDate}
                                    min={
                                        formData.permitStartDate
                                            ? new Date(formData.permitStartDate).toISOString().slice(0, 16)
                                            : now.toISOString().slice(0, 16)
                                    }
                                    max={
                                        formData.permitStartDate
                                            ? addHours(new Date(formData.permitStartDate), 48).toISOString().slice(0, 16)
                                            : addHours(now, 48).toISOString().slice(0, 16)
                                    }
                                />
                                {errors.permitEndDate && (
                                    <p className="text-danger">{errors.permitEndDate}</p>
                                )}
                            </div>
                        </Form.Group>


                        <Form.Group controlId="description" className="mt-3">
                            <Form.Label>
                                Description <span className="text-danger">*</span>
                            </Form.Label>
                            <Form.Control
                                as="textarea"
                                rows={3}
                                value={formData.workDescription || ''}
                                onChange={(e) =>
                                    setFormData((prev) => ({
                                        ...prev,
                                        workDescription: e.target.value,
                                    }))
                                }
                            />
                            {errors.workDescription && <div className="text-danger">{errors.workDescription}</div>}
                        </Form.Group>

                        <div className="bg-light shadow-sm p-2 mt-3 mb-3">
                            <AllFilterLocation handleFilter={handleFilter} getLocation={formData} disabled={false} />
                            {fieldErrors.locationOneId && (
                                <div className="text-danger mt-1">{fieldErrors.locationOneId}</div>
                            )}
                        </div>


                        <Form.Group className="mb-3">
                            <Form.Label>Responsible Site / Job Supervisor</Form.Label>
                            <Form.Control
                                type="text"
                                name="nameOfSiteSupervisor"
                                value={formData.nameOfSiteSupervisor || ""}
                                onChange={(e) =>
                                    setFormData((prev) => ({
                                        ...prev,
                                        nameOfSiteSupervisor: e.target.value,
                                    }))
                                }
                            />
                            {errors.nameOfSiteSupervisor && (
                                <p className="text-danger">
                                    {errors.nameOfSiteSupervisor}
                                </p>
                            )}
                        </Form.Group>

                        <Form.Group className="mb-3">
                            <Form.Label>Number of Workers</Form.Label>
                            <Form.Control
                                type="text"
                                name="nameOfSiteSupervisor"
                                value={formData.noOfWorkers || ""}
                                onChange={(e) =>
                                    setFormData((prev) => ({
                                        ...prev,
                                        noOfWorkers: Number(e.target.value),
                                    }))
                                }
                            />
                            {errors.noOfWorkers && (
                                <p className="text-danger">{errors.noOfWorkers}</p>
                            )}
                        </Form.Group>

                        <Form.Group className="mb-3">
                            <Form.Label>Work Order / Job Number</Form.Label>
                            <Form.Control
                                type="text"
                                name="nameOfSiteSupervisor"
                                value={formData.workOrderNo || ""}
                                onChange={(e) =>
                                    setFormData((prev) => ({
                                        ...prev,
                                        workOrderNo: e.target.value,
                                    }))
                                }
                            />

                        </Form.Group>

                        <Form.Group className="mb-3">
                            <Form.Label>Applicant Contact Number</Form.Label>
                            <Form.Control
                                type="text"
                                name="nameOfSiteSupervisor"
                                value={formData.applicantContactNo || ""}
                                onChange={(e) =>
                                    setFormData((prev) => ({
                                        ...prev,
                                        applicantContactNo: e.target.value,
                                    }))
                                }
                            />
                            {errors.applicantContactNo && (
                                <p className="text-danger">
                                    {errors.applicantContactNo}
                                </p>
                            )}
                        </Form.Group>

                        <Form.Group className="mb-3">
                            <Form.Label>Supervisor Contact Number</Form.Label>
                            <Form.Control
                                type="text"
                                name="nameOfSiteSupervisor"
                                value={formData.supervisorContactNo || ""}
                                onChange={(e) =>
                                    setFormData((prev) => ({
                                        ...prev,
                                        supervisorContactNo: e.target.value,
                                    }))
                                }
                            />
                            {errors.supervisorContactNo && (
                                <p className="text-danger">
                                    {errors.supervisorContactNo}
                                </p>
                            )}
                        </Form.Group>
                        <label htmlFor="incidentImages" className="mb-2">
                            Add/Upload images of the work location and authorized workers list (Min 2 attachment is mandatory) *
                        </label>
                        <FileUploader
                           
                            onFilesSelected={(files) => setFormData((prev) => ({
                                ...prev,
                                supportingDocuments: files, // Assign exactly what the child gives
                            }))}
                            files={formData.supportingDocuments}
                            disabled={false}
                        />

                        {errors.supportingDocuments && (
                            <p className="text-danger">{errors.supportingDocuments}</p>
                        )}

                        {Object.entries(groupedControls).map(([permitType, controls]) => (
                            <div key={permitType} className="mb-4">
                                <h5 className="mt-4 mb-3 permit-head">{permitType}</h5>
                                {(controls as Array<{ controlIndex: number; description: string; currentType: string; value: string; remarks: string }>).map((control: any) => (
                                    <div
                                        key={control.controlIndex}
                                        className="border p-3 mb-3 rounded"
                                    >
                                        <p>
                                            <strong>{control.controlIndex + 1}.</strong>{' '}
                                            {control.description}
                                        </p>
                                        <p>
                                            <strong>Control Type:</strong> {control.currentType}
                                        </p>
                                        <Form.Group className="mb-3">
                                            <div className="btn-group d-flex flex-wrap" role="group" aria-label="Risk Control Options">
                                                {["Yes", "No", "Not Applicable"].map((option) => (
                                                    <button
                                                        key={`${option}-${control.controlIndex}`}
                                                        type="button"
                                                        className={getBoxClass(option, control.value)}
                                                        onClick={() => handleBoxClick(control.controlIndex, option)}
                                                    >
                                                        {option}
                                                    </button>
                                                ))}
                                            </div>
                                            {errors[`control_value_${control.controlIndex}`] && (
                                                <div className="text-danger mt-1">{errors[`control_value_${control.controlIndex}`]}</div>
                                            )}
                                        </Form.Group>

                                        <Form.Group className="mb-3">
                                            <Form.Label>Remarks</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={control.remarks}
                                                onChange={(e) =>
                                                    handleRiskControlChange(
                                                        control.controlIndex,
                                                        "remarks",
                                                        e.target.value
                                                    )
                                                }
                                            />
                                            {errors[`control_remarks_${control.controlIndex}`] && (
                                                <div className="text-danger">{errors[`control_remarks_${control.controlIndex}`]}</div>
                                            )}
                                        </Form.Group>

                                        {/* EVIDENCE UPLOAD */}
                                        <div className="col-12 mt-3 mb-2">
                                            <label htmlFor="incidentImages" className="mb-2">
                                                Add/Upload Evidence *
                                            </label>
                                            <FileUploader
                                               
                                                onFilesSelected={(files) => handleEvidenceUpload(control.controlIndex, files)}
                                                files={control.evidence}
                                                disabled={false}
                                            />
                                            {errors[`control_evidence_${control.controlIndex}`] && (
                                                <div className="text-danger">{errors[`control_evidence_${control.controlIndex}`]}</div>
                                            )}
                                        </div>

                                    </div>
                                ))}
                            </div>
                        ))}

                        <div className="row mb-4 text-center">
                            <div className="d-flex flex-column col-12">
                                <Form.Label className="fw-bold">Applicant Sign</Form.Label>
                                <i>
                                    I confirm that all required fields are accurately completed, and I acknowledge responsibility for adhering to the specified safety controls for this work activity.
                                </i>
                                <div className="row mt-2">
                                    <div className="col-12">

                                        <SignatureCanvas
                                            penColor="#1F3BB3"
                                            backgroundColor="white"
                                            canvasProps={{
                                                width: 300,
                                                height: 120,
                                                className: "sigCanvas",
                                                style: {
                                                    boxShadow: "0px 0px 10px 3px rgb(189 189 189)"
                                                }
                                            }}
                                            ref={signRef as React.RefObject<SignatureCanvas>}
                                        />
                                        <i
                                            className="fa fa-undo undo"
                                            onClick={() => signRef.current?.clear()}
                                        ></i>
                                        <p>{user?.firstName}</p>
                                    </div>

                                    {errors.signature && <div className="text-danger">{errors.signature}</div>}
                                </div>
                            </div>
                        </div>

                        {user?.type === 'External' ? (<>
                            <ModalSelect
                                title="Reviewer"
                                options={reviewer}                // same array of { value, label }
                                selectedValue={formData.reviewerId}
                                onChange={(newVal) => {
                                    setFormData((prev) => ({
                                        ...prev,
                                        reviewerId: newVal,
                                    }));
                                }}
                                placeholder="Select Reviewer"
                                clearable
                                disabled={false}
                            />
                            {errors.reviewerId && (
                                <p className="text-danger">{errors.reviewerId}</p>
                            )}
                        </>) : (<>
                            <ModalSelect
                                title="Assessor"
                                options={assessor}                // same array of { value, label }
                                selectedValue={formData.assessorId}
                                onChange={(newVal) => {
                                    setFormData((prev) => ({
                                        ...prev,
                                        assessorId: newVal,
                                    }));
                                }}
                                placeholder="Select Assessor"
                                clearable
                                disabled={false}
                            />

                            {errors.assessorId && (
                                <p className="text-danger">{errors.assessorId}</p>
                            )}
                        </>
                        )}

                    </div>
                </div>
            </div>
        </div>
        <div className="footer-nav-area" id="footerNav">
            <div className="container px-0">
                <div className="footer-nav position-relative">
                    <div className="h-100 d-flex align-items-center justify-content-between ps-0 w-100">
                        <Button variant="primary" className=" w-100" onClick={handleSubmit}>
                            Submit
                        </Button>
                    </div>
                </div>
            </div>
        </div>

        

    </>);
}
