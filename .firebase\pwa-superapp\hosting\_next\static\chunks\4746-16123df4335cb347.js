"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4746],{23718:e=>{e.exports=function(e,t,r,n,a,o,l,i){if(!e){var s;if(void 0===t)s=Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[r,n,a,o,l,i],f=0;(s=Error(t.replace(/%s/g,function(){return c[f++]}))).name="Invariant Violation"}throw s.framesToPop=1,s}}},39746:(e,t,r)=>{r.d(t,{Zw:()=>s});var n=r(79630),a=r(93495),o=r(12115);r(23718);function l(e){return"default"+e.charAt(0).toUpperCase()+e.substr(1)}function i(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}function s(e,t){return Object.keys(t).reduce(function(r,s){var c,f,p,d,u,m,y,h,v=r[l(s)],g=r[s],N=(0,a.A)(r,[l(s),s].map(i)),w=t[s],U=(c=e[w],f=(0,o.useRef)(void 0!==g),d=(p=(0,o.useState)(v))[0],u=p[1],m=void 0!==g,y=f.current,f.current=m,!m&&y&&d!==v&&u(v),[m?g:d,(0,o.useCallback)(function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];c&&c.apply(void 0,[e].concat(r)),u(e)},[c])]),b=U[0],x=U[1];return(0,n.A)({},N,((h={})[s]=b,h[w]=x,h))},e)}r(87760)},57111:(e,t,r)=>{r.d(t,{A:()=>U});var n=r(29300),a=r.n(n),o=r(12115),l=r(39746),i=r(37150),s=r(97390),c=r(58724),f=r(95155);let p=(0,c.A)("h4");p.displayName="DivStyledAsH4";let d=o.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:o=p,...l}=e;return n=(0,s.oU)(n,"alert-heading"),(0,f.jsx)(o,{ref:t,className:a()(r,n),...l})});d.displayName="AlertHeading",r(84956);var u=r(41730);r(68141),r(18),r(94583),new WeakMap;var m=r(32960);let y=["onKeyDown"],h=o.forwardRef((e,t)=>{let{onKeyDown:r}=e,n=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,y),[a]=(0,m.Am)(Object.assign({tagName:"a"},n)),o=(0,u.A)(e=>{a.onKeyDown(e),null==r||r(e)});return function(e){return!e||"#"===e.trim()}(n.href)||"button"===n.role?(0,f.jsx)("a",Object.assign({ref:t},n,a,{onKeyDown:o})):(0,f.jsx)("a",Object.assign({ref:t},n,{onKeyDown:r}))});h.displayName="Anchor";let v=o.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:o=h,...l}=e;return n=(0,s.oU)(n,"alert-link"),(0,f.jsx)(o,{ref:t,className:a()(r,n),...l})});v.displayName="AlertLink";var g=r(34748),N=r(77706);let w=o.forwardRef((e,t)=>{let{bsPrefix:r,show:n=!0,closeLabel:o="Close alert",closeVariant:c,className:p,children:d,variant:u="primary",onClose:m,dismissible:y,transition:h=g.A,...v}=(0,l.Zw)(e,{show:"onClose"}),w=(0,s.oU)(r,"alert"),U=(0,i.A)(e=>{m&&m(!1,e)}),b=!0===h?g.A:h,x=(0,f.jsxs)("div",{role:"alert",...!b?v:void 0,ref:t,className:a()(p,w,u&&"".concat(w,"-").concat(u),y&&"".concat(w,"-dismissible")),children:[y&&(0,f.jsx)(N.A,{onClick:U,"aria-label":o,variant:c}),d]});return b?(0,f.jsx)(b,{unmountOnExit:!0,...v,ref:void 0,in:n,children:x}):n?x:null});w.displayName="Alert";let U=Object.assign(w,{Link:v,Heading:d})},87760:(e,t,r)=>{function n(){var e=this.constructor.getDerivedStateFromProps(this.props,this.state);null!=e&&this.setState(e)}function a(e){this.setState((function(t){var r=this.constructor.getDerivedStateFromProps(e,t);return null!=r?r:null}).bind(this))}function o(e,t){try{var r=this.props,n=this.state;this.props=e,this.state=t,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(r,n)}finally{this.props=r,this.state=n}}function l(e){var t=e.prototype;if(!t||!t.isReactComponent)throw Error("Can only polyfill class components");if("function"!=typeof e.getDerivedStateFromProps&&"function"!=typeof t.getSnapshotBeforeUpdate)return e;var r=null,l=null,i=null;if("function"==typeof t.componentWillMount?r="componentWillMount":"function"==typeof t.UNSAFE_componentWillMount&&(r="UNSAFE_componentWillMount"),"function"==typeof t.componentWillReceiveProps?l="componentWillReceiveProps":"function"==typeof t.UNSAFE_componentWillReceiveProps&&(l="UNSAFE_componentWillReceiveProps"),"function"==typeof t.componentWillUpdate?i="componentWillUpdate":"function"==typeof t.UNSAFE_componentWillUpdate&&(i="UNSAFE_componentWillUpdate"),null!==r||null!==l||null!==i)throw Error("Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n"+(e.displayName||e.name)+" uses "+("function"==typeof e.getDerivedStateFromProps?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()")+" but also contains the following legacy lifecycles:"+(null!==r?"\n  "+r:"")+(null!==l?"\n  "+l:"")+(null!==i?"\n  "+i:"")+"\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://fb.me/react-async-component-lifecycle-hooks");if("function"==typeof e.getDerivedStateFromProps&&(t.componentWillMount=n,t.componentWillReceiveProps=a),"function"==typeof t.getSnapshotBeforeUpdate){if("function"!=typeof t.componentDidUpdate)throw Error("Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype");t.componentWillUpdate=o;var s=t.componentDidUpdate;t.componentDidUpdate=function(e,t,r){var n=this.__reactInternalSnapshotFlag?this.__reactInternalSnapshot:r;s.call(this,e,t,n)}}return e}r.r(t),r.d(t,{polyfill:()=>l}),n.__suppressDeprecationWarning=!0,a.__suppressDeprecationWarning=!0,o.__suppressDeprecationWarning=!0},94016:(e,t,r)=>{r.d(t,{A:()=>b});var n=r(29300),a=r.n(n),o=r(12115),l=r(97390),i=r(95155);let s=o.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:o="div",...s}=e;return n=(0,l.oU)(n,"card-body"),(0,i.jsx)(o,{ref:t,className:a()(r,n),...s})});s.displayName="CardBody";let c=o.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:o="div",...s}=e;return n=(0,l.oU)(n,"card-footer"),(0,i.jsx)(o,{ref:t,className:a()(r,n),...s})});c.displayName="CardFooter";let f=o.createContext(null);f.displayName="CardHeaderContext";let p=o.forwardRef((e,t)=>{let{bsPrefix:r,className:n,as:s="div",...c}=e,p=(0,l.oU)(r,"card-header"),d=(0,o.useMemo)(()=>({cardHeaderBsPrefix:p}),[p]);return(0,i.jsx)(f.Provider,{value:d,children:(0,i.jsx)(s,{ref:t,...c,className:a()(n,p)})})});p.displayName="CardHeader";let d=o.forwardRef((e,t)=>{let{bsPrefix:r,className:n,variant:o,as:s="img",...c}=e,f=(0,l.oU)(r,"card-img");return(0,i.jsx)(s,{ref:t,className:a()(o?"".concat(f,"-").concat(o):f,n),...c})});d.displayName="CardImg";let u=o.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:o="div",...s}=e;return n=(0,l.oU)(n,"card-img-overlay"),(0,i.jsx)(o,{ref:t,className:a()(r,n),...s})});u.displayName="CardImgOverlay";let m=o.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:o="a",...s}=e;return n=(0,l.oU)(n,"card-link"),(0,i.jsx)(o,{ref:t,className:a()(r,n),...s})});m.displayName="CardLink";var y=r(58724);let h=(0,y.A)("h6"),v=o.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:o=h,...s}=e;return n=(0,l.oU)(n,"card-subtitle"),(0,i.jsx)(o,{ref:t,className:a()(r,n),...s})});v.displayName="CardSubtitle";let g=o.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:o="p",...s}=e;return n=(0,l.oU)(n,"card-text"),(0,i.jsx)(o,{ref:t,className:a()(r,n),...s})});g.displayName="CardText";let N=(0,y.A)("h5"),w=o.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:o=N,...s}=e;return n=(0,l.oU)(n,"card-title"),(0,i.jsx)(o,{ref:t,className:a()(r,n),...s})});w.displayName="CardTitle";let U=o.forwardRef((e,t)=>{let{bsPrefix:r,className:n,bg:o,text:c,border:f,body:p=!1,children:d,as:u="div",...m}=e,y=(0,l.oU)(r,"card");return(0,i.jsx)(u,{ref:t,...m,className:a()(n,y,o&&"bg-".concat(o),c&&"text-".concat(c),f&&"border-".concat(f)),children:p?(0,i.jsx)(s,{children:d}):d})});U.displayName="Card";let b=Object.assign(U,{Img:d,Title:w,Subtitle:v,Body:s,Link:m,Text:g,Header:p,Footer:c,ImgOverlay:u})}}]);