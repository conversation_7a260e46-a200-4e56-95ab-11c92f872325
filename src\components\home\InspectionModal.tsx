"use client";

import React, { useState, useRef, useEffect, useCallback } from "react";
import { <PERSON>dal, Button, Form, Card, Row, Col, Container } from "react-bootstrap";
import { FILE_URL, GET_USER_ROLE_BY_MODE, OBS_ACTION_SUBMIT, SUBMIT_INSPECTION_ACTION } from "@/constant";
import Swal from "sweetalert2";
import API from "@/services/API";
import Select, { SingleValue } from "react-select";
import ImageComponent from "../../services/FileDownlodS3";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import ViewOBS from "../observation/ViewOBS";
import FileUploader from "@/services/FileUploader";
import ModalSelect from "@/services/ModalSelect";
import ViewInspection from "../inspection/ViewInspection";
import moment from "moment";
// If using a dropzone library, import it below, e.g.:
// import { DropzoneArea } from "material-ui-dropzone";

// import ViewObs from "./ViewObs"; // Adjust import path to your actual file

// Define the object shape for applicationDetails
interface ApplicationDetail {
  maskId?: string;
  status?: string;
  // Add more fields as necessary
}

// Define the shape for showItem
interface ShowItem {
  id: string;
  actionType: "take_action" | "reperform_action" | "review" | "verify_task" | "perform_task" | "reperform_task";
  prefix?: string;
  description?: string;
  actionToBeTaken?: string;
  dueDate?: string;
  status?: string;
  assignedToId?: (string | number)[];
  actionTaken?: string;
  uploads?: string[] | string | null | any;
  created?: string;
  comments?: string;
  evidence?: string[] | string[] | null | any;
  submittedBy?: { firstName?: string };
  // Add other fields if needed
}

interface OptionType {
  value: string;
  label: string;
}

interface ActionModalProps {
  show: boolean;                          // Whether the modal is visible
  applicationDetails?: ApplicationDetail; // Single object used for .maskId, .status
  showItem?: ShowItem;                    // Contains .id, .actionType, etc.
  closeModal: (flag?: boolean) => void;   // Callback to close modal
}



const InspectionModal: React.FC<ActionModalProps> = ({
  show,
  applicationDetails,
  showItem,
  closeModal,
}) => {
  // If modal should not be visible, return null



  // Component states
  const [apiStatus, setApiStatus] = useState<string>("");
  const [signs, setSign] = useState<string>("");
  const [signModal, setSignModal] = useState<boolean>(false);
  const [comments, setComments] = useState<string>("");
  const [showErrors, setShowErrors] = useState<boolean>(false);
  const [assessor, setAssessor] = useState<OptionType[]>([]);
  const [assessorId, setAssessorId] = useState<string>("");
  const [actionTaken, setActionTaken] = useState<string>("");
  const [actionToBeTaken, setActionToBeTaken] = useState<string>("");
  const [evidence, setEvidence] = useState<string[]>([]);
  const [dueDate, setDueDate] = useState<string | null>(null);

  // Decide which crewList to fetch based on showItem.actionType
  useEffect(() => {
    if (!showItem) return;
    if (showItem.actionType === "review") {
      getCrewList("obsactionowner");
    } else if (
      showItem.actionType === "perform_task" ||
      showItem.actionType === "reperform_task"
    ) {
      getCrewList("ins_action_reviewer");
    }
  }, [showItem]);

  // Fetch the crew list
  const getCrewList = useCallback(async (type: string) => {
    try {
      const response = await API.post(GET_USER_ROLE_BY_MODE, {
        locationOneId: "",
        locationTwoId: "",
        locationThreeId: "",
        locationFourId: "",
        mode: type,
      });

      if (response.status === 200) {
        const data: OptionType[] = response.data.map((item: any) => ({
          label: item.firstName,
          value: item.id,
        }));
        setAssessor(data);
      }
    } catch (error) {
      console.error("Error fetching crew list:", error);
    }
  }, []);

  // Validation & Submission
  const handleSubmit = async () => {
    setShowErrors(true);
    if (!showItem) return;

    let hasError = false;

    // Validate based on actionType
    if (showItem.actionType === "perform_task" || showItem.actionType === "reperform_task") {
      if (!actionTaken) hasError = true;
      if (!assessorId) hasError = true;
      if (evidence.length === 0) hasError = true;
    } else if (showItem.actionType === "review") {
      if (!actionToBeTaken) hasError = true;
      if (!dueDate) hasError = true;
      if (!assessorId) hasError = true;
    } else if (showItem.actionType === "verify_task") {
      if (!apiStatus) hasError = true;
      if (apiStatus === "Return" && !comments) hasError = true;
    }

    if (hasError) {
      Swal.fire({
        icon: "error",
        title: "Validation Error",
        text: "Please fill all required fields before submitting!",
      });
      return;
    }

    // Build the formData
    let formData: Record<string, any> = {};
    if (showItem.actionType === "perform_task" || showItem.actionType === "reperform_task") {
      formData = {
        actionTaken,
        reviewerId: assessorId,
        evidence,
      };
    } else if (showItem.actionType === "review") {
      formData = {
        actionToBeTaken,
        dueDate,
        actionOwnerId: assessorId,
      };
    } else if (showItem.actionType === "verify_task") {
      formData = {
        status: apiStatus === "Approve" ? "Completed" : "Returned",
        comments,
      };
    }

    try {
      const response = await API.patch(SUBMIT_INSPECTION_ACTION(showItem.id), formData);
      if (response.status === 204) {
        Swal.fire({
          icon: "success",
          title: "Success",
          text: "Action submitted successfully!",
        });
        closeModal(false);
      } else {
        throw new Error("Something went wrong. Please try again.");
      }
    } catch (error: any) {
      Swal.fire({
        icon: "error",
        title: "Submission Failed",
        text: error.message || "Failed to submit action. Please try again.",
      });
    }
  };

  // Handle react-select change
  const handleApplicantChange = (selectedOption: SingleValue<OptionType>) => {
    setAssessorId(selectedOption ? selectedOption.value : "");
  };

  // Handle evidence uploads
  const handleEvidenceUpload = async (files: File[]) => {
    if (files.length > 0) {
      const latestFile = files[files.length - 1];
      const formData1 = new FormData();
      formData1.append("file", latestFile);

      try {
        const response = await API.post(FILE_URL, formData1, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });

        if (response && response.status === 200) {
          setEvidence((prev) => [...prev, response.data.files[0].originalname]);
        }
      } catch (error) {
        console.error("File upload error: ", error);
      }
    }
  };

  // Remove evidence by index
  const handleRemoveMainImage = (index: number) => {
    setEvidence((prev) => prev.filter((_, i) => i !== index));
  };

  return (
    <Modal
      show={show}
      size="lg"
      onHide={() => closeModal(false)}
      aria-labelledby="example-modal-sizes-title-md"
      id="pdf-content"
    >
      <Modal.Header closeButton>
        {applicationDetails && (
          <div className="row" style={{ width: "100%" }}>
            <div className="col-9">
              <div className="row">
                <div className="col-12">
                  <h4>Inspection</h4>
                  <div className="d-flex align-items-center">
                    <p className="me-2">#{applicationDetails.maskId || ""}</p>
                    <p className="badge bg-primary text-white">{applicationDetails.status}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </Modal.Header>

      <Modal.Body>
        {/* If you pass applicationDetails as reportData to ViewObs */}
        {applicationDetails && <ViewInspection reportData={applicationDetails} type ={"action"}/>}


        {/* TAKE_ACTION / REPERFORM_ACTION */}
        {showItem?.actionType === "perform_task" || showItem?.actionType === "reperform_task" ? (
          <>
            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
              <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                <Card.Body>
                  <h6 className="fw-bold mb-3">{showItem.prefix === "INS-CL" && showItem.description}</h6>
                  <h6 className="fw-bold mb-3">Action to be Taken</h6>
                  <p>{showItem.actionToBeTaken}</p>
                  <div className="row">

                    {showItem?.uploads?.length > 0 && (<>
                      <h5 className="fw-bold mb-3 mt-3">Images</h5>
                      <div className="col-6 mt-2">
                        {showItem?.uploads.map((img: any, i: number) => (
                          <div key={i} className="m-2 position-relative">
                            <ImageComponent fileName={img} size={100} name={false} />

                          </div>
                        ))}
                      </div>
                    </>)}
                  </div>
                  {showItem?.actionType === 'reperform_task' && <>
                    <h5 className="fw-bold mb-3">Action Taken</h5>
                    <p>{showItem.actionTaken}</p>

                    {showItem.evidence?.length > 0 && (<>
                      <h5 className="fw-bold mb-3 mt-3">Evidence</h5>
                      <div className="col-6 mt-2">
                        {showItem.evidence.map((img: any, i: number) => (
                          <div key={i} className="m-2 position-relative">
                            <ImageComponent fileName={img} size={100} name={false} />

                          </div>
                        ))}
                      </div>
                    </>)}

                    <h5 className="fw-bold mb-3">Reviewer Comments</h5>
                    <p>{showItem.comments}</p>
                  </>}
                </Card.Body>
              </Card>
            </Col>
            {/* Action Taken */}
            <Col className="m-auto mb-3" xs={12}>
              <Card style={{ boxShadow: "rgba(0, 0, 0, 0.24) 0px 3px 8px" }}>
                <Card.Body>
                  <h5 className=" mb-3">Action Taken</h5>
                  <Row className="justify-content-center">
                    <Col xs={12} className="d-flex text-center">
                      <textarea
                        rows={4}
                        cols={50}
                        className="form-control"
                        placeholder="Enter your action here..."
                        onChange={(e) => setActionTaken(e.target.value)}
                      />
                    </Col>
                  </Row>
                  {showErrors && !actionTaken && (
                    <p className="text-danger mt-2">Action Taken is required.</p>
                  )}
                </Card.Body>
              </Card>
            </Col>

            {/* Evidence Upload */}
            <Col className="m-auto mb-3" xs={12}>
              <Card style={{ boxShadow: "rgba(0, 0, 0, 0.24) 0px 3px 8px" }}>
                <Card.Body>
                  <h5 className="mb-3">Evidence</h5>
                  {/* Dropzone example: 
                  <DropzoneArea
                    onChange={(files: File[]) => handleEvidenceUpload(files)}
                  /> */}
                  <FileUploader


                    onFilesSelected={(updatedList) => {
                      // The child passes back the entire updated array
                      // Parent just replaces its state with that updated list
                      setEvidence(updatedList);
                    }}
                    disabled={false}
                    files={evidence}
                  />
                  {showErrors && evidence.length === 0 && (
                    <p className="text-danger mt-2">
                      At least one evidence file is required.
                    </p>
                  )}

                  {/* {evidence.length > 0 && (
                    <div className="col-12 mt-3 mb-4">
                      <Form.Label>Uploaded Evidence</Form.Label>
                      <div className="row">
                        {evidence.map((file, idx) => (
                          <div
                            key={idx}
                            className="col-3"
                            style={{ position: "relative" }}
                          >
                            <div className="boxShadow d-flex align-items-center">
                              <ImageComponent fileName={file} size={100} name={true} />
                              <i
                                className="pi pi-trash"
                                onClick={() => handleRemoveMainImage(idx)}
                                style={{
                                  position: "absolute",
                                  top: "5px",
                                  right: "5px",
                                  cursor: "pointer",
                                }}
                              />
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )} */}
                </Card.Body>
              </Card>
            </Col>

            {/* Reviewer / Assessor */}
            <Col className="m-auto mb-3" xs={12}>
              <Card style={{ boxShadow: "rgba(0, 0, 0, 0.24) 0px 3px 8px" }}>
                <Card.Body>
                  <Form.Group className="mb-3">
                    {/* <Form.Label>
                      {["review"].includes(showItem.actionType) ? "Assessor" : "Reviewer"}
                    </Form.Label> */}
                    {/* <Select
                      options={assessor}
                      value={assessor.find((o) => o.value === assessorId)}
                      onChange={handleApplicantChange}
                      placeholder={
                        ["review"].includes(showItem.actionType) ? "Select Assessor" : "Select Reviewer"
                      }
                      isClearable
                    /> */}

                    <ModalSelect
                      title={["review"].includes(showItem.actionType) ? "Assessor" : "Reviewer"}
                      options={assessor}                // same array of { value, label }
                      selectedValue={assessorId}
                      onChange={(newVal) => setAssessorId(newVal)}
                      placeholder={
                        ["review"].includes(showItem.actionType) ? "Select Assessor" : "Select Reviewer"
                      }
                      clearable
                      disabled={false}
                    />
                  </Form.Group>
                  {showErrors && !assessorId && (
                    <p className="text-danger mt-2">
                      {["review"].includes(showItem.actionType) ? "Assessor" : "Reviewer"} required.
                    </p>
                  )}
                </Card.Body>
              </Card>
            </Col>
          </>
        ) : showItem?.actionType === "review" ? (
          <>
            {/* Action to be Taken */}
            <Col className="m-auto mb-3" xs={12}>
              <Card style={{ boxShadow: "rgba(0, 0, 0, 0.24) 0px 3px 8px" }}>
                <Card.Body>
                  <h5 className="mb-3">Action to be Taken</h5>
                  <Row className="justify-content-center">
                    <Col xs={12} className="d-flex text-center">
                      <textarea
                        rows={4}
                        cols={50}
                        className="form-control"
                        placeholder="Enter your action here..."
                        onChange={(e) => setActionToBeTaken(e.target.value)}
                      />
                    </Col>
                  </Row>
                  {showErrors && !actionToBeTaken && (
                    <p className="text-danger mt-2">
                      Action to be Taken is required.
                    </p>
                  )}
                </Card.Body>
              </Card>
            </Col>

            {/* Due Date */}
            <Col className="m-auto mb-3" xs={12}>
              <Card style={{ boxShadow: "rgba(0, 0, 0, 0.24) 0px 3px 8px" }}>
                <Card.Body>
                  <Form.Group controlId="dueDate" className="mb-3">
                    <Form.Label className="mb-3">Due Date</Form.Label>
                    <DatePicker
                      selected={dueDate ? new Date(dueDate) : null}
                      onChange={(date) => setDueDate(date ? date.toISOString() : null)}
                      dateFormat="yyyy-MM-dd"
                      className="form-control"
                      placeholderText="Select a date"
                      minDate={new Date()}
                    />
                  </Form.Group>
                  {showErrors && dueDate === null && (
                    <p className="text-danger mt-2">Due Date is required.</p>
                  )}
                </Card.Body>
              </Card>
            </Col>

            {/* Action Owner Select */}
            <Col className="m-auto mb-3" xs={12}>
              <Card style={{ boxShadow: "rgba(0, 0, 0, 0.24) 0px 3px 8px" }}>
                <Card.Body>
                  <Form.Group className="mb-3">
                    {/* <Form.Label>
                      {showItem.actionType === "review"
                        ? "Action Owner"
                        : "Reviewer"}
                    </Form.Label> */}
                    {/* <Select
                      options={assessor}
                      value={assessor.find((o) => o.value === assessorId)}
                      onChange={handleApplicantChange}
                      placeholder={
                        showItem.actionType === "review"
                          ? "Select Action Owner"
                          : "Select Reviewer"
                      }
                      isClearable
                    /> */}

                    <ModalSelect
                      title={showItem.actionType === "review"
                        ? "Action Owner"
                        : "Reviewer"}
                      options={assessor}                // same array of { value, label }
                      selectedValue={assessorId}
                      onChange={(newVal) => setAssessorId(newVal)}
                      placeholder={
                        showItem.actionType === "review"
                          ? "Select Action Owner"
                          : "Select Reviewer"
                      }
                      clearable
                      disabled={false}
                    />
                  </Form.Group>


                  {showErrors && !assessorId && (
                    <p className="text-danger mt-2">
                      {showItem.actionType === "review" ? "Assessor" : "Reviewer"} required.
                    </p>
                  )}
                </Card.Body>
              </Card>
            </Col>
          </>
        ) : showItem?.actionType === "verify_task" ? (
          <>
            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
              <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                <Card.Body>
                  <h4 className="fw-bold mb-3">{showItem.prefix === "INS-CL" && showItem.description}</h4>
                  <h5 className="fw-bold mb-3">Action to be Taken</h5>
                  <p>{showItem.actionToBeTaken}</p>
                  <div className="row">

                    {showItem.uploads?.length > 0 && (<>
                      <h5 className="fw-bold mb-3 mt-3">Images</h5>
                      <div className="col-6 mt-2">
                        {showItem.uploads.map((img  : any , i : number) => (
                          <div key={i} className="m-2 position-relative">
                            <ImageComponent fileName={img} size={100} name={false} />

                          </div>
                        ))}
                      </div>
                    </>)}
                  </div>

                  <h5 className="fw-bold mb-3">Action Taken by {showItem.submittedBy?.firstName} - {moment(showItem.created).format('DD-MM-YYYY')}</h5>
                  <p>{showItem.actionTaken}</p>

                  <div className="row">

                    {showItem.evidence?.length > 0 && (<>
                      <h5 className="fw-bold mb-3 mt-3">Evidence</h5>
                      <div className="col-6 mt-2">
                        {showItem.evidence.map((img : any, i  : number) => (
                          <div key={i} className="m-2 position-relative">
                            <ImageComponent fileName={img} size={100} name={false} />

                          </div>
                        ))}
                      </div>
                    </>)}
                  </div>
                </Card.Body>
              </Card>
            </Col>
            {/* Approve / Return */}
            <Col className="m-auto mb-3" xs={12}>
              <Card style={{ boxShadow: "rgba(0, 0, 0, 0.24) 0px 3px 8px" }}>
                <Card.Body>
                  <Row className="justify-content-center">
                    <Col xs={12} className="d-flex text-center">
                      <Container
                        fluid
                        className="col-6 p-2"
                        style={{
                          border: "1px solid #dee2e6",
                          borderRadius: 10,
                          color: "#000000",
                          cursor: "pointer",
                        }}
                        onClick={() => setApiStatus("Approve")}
                      >
                        <Row>
                          <Col xs={4}>
                            <div
                              style={
                                apiStatus === "Approve"
                                  ? {
                                    width: 24,
                                    height: 24,
                                    borderRadius: 12,
                                    background: "green",
                                  }
                                  : {
                                    width: 24,
                                    height: 24,
                                    borderRadius: 12,
                                    background: "lightgray",
                                  }
                              }
                            >
                              {apiStatus === "Approve" && (
                                <i className="bi bi-check text-white"></i>

                              )}
                            </div>
                          </Col>
                          <Col xs={8} style={apiStatus === "Approve" ? { color: "green" } : {}}>
                            Approve
                          </Col>
                        </Row>
                      </Container>

                      <Container
                        fluid
                        className="col-5 p-2"
                        style={{
                          border: "1px solid #dee2e6",
                          borderRadius: 10,
                          color: "#000000",
                          cursor: "pointer",
                        }}
                        onClick={() => setApiStatus("Return")}
                      >
                        <Row>
                          <Col xs={4}>
                            <div
                              style={
                                apiStatus === "Return"
                                  ? {
                                    width: 24,
                                    height: 24,
                                    borderRadius: 12,
                                    background: "red",
                                  }
                                  : {
                                    width: 24,
                                    height: 24,
                                    borderRadius: 12,
                                    background: "lightgray",
                                  }
                              }
                            >
                              {apiStatus === "Return" && (
                                <i className="bi bi-check text-white"></i>
                              )}
                            </div>
                          </Col>
                          <Col xs={8} style={apiStatus === "Return" ? { color: "red" } : {}}>
                            Return
                          </Col>
                        </Row>
                      </Container>
                    </Col>
                  </Row>
                  {showErrors && !apiStatus && (
                    <p className="text-danger mt-2">Please select Approve or Return.</p>
                  )}
                </Card.Body>
              </Card>
            </Col>

            {/* Comments */}
            <Col className="m-auto mb-3" xs={12}>
              <Card style={{ boxShadow: "rgba(0, 0, 0, 0.24) 0px 3px 8px" }}>
                <Card.Body>
                  <h5 className="mb-3">Comments</h5>
                  <Row className="justify-content-center">
                    <Col xs={12} className="d-flex text-center">
                      <textarea
                        rows={4}
                        cols={50}
                        className="form-control"
                        placeholder="Enter your comments here..."
                        onChange={(e) => setComments(e.target.value)}
                      />
                    </Col>
                  </Row>
                  {showErrors && apiStatus === "Return" && !comments && (
                    <p className="text-danger mt-2">
                      Comments are required when returning.
                    </p>
                  )}
                </Card.Body>
              </Card>
            </Col>
          </>
        ) : null}
      </Modal.Body>

      <Modal.Footer>
        {showItem?.actionType === "verify_task" ? (
          <Button variant="primary" onClick={handleSubmit}>
            {apiStatus === "Return" ? "Return to Action Owner" : "Approve"}
          </Button>
        ) : (
          <Button variant="primary" onClick={handleSubmit}>
            Submit
          </Button>
        )}
      </Modal.Footer>
    </Modal>
  );
};

export default InspectionModal;
