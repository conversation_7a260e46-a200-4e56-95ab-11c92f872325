/**
 * Simple test script to verify notification integration
 * Run this in the browser console on your notification test page
 */

console.log('🧪 Starting iOS Push Notification Integration Test...');

// Test 1: Check if services are available
console.log('\n📋 Test 1: Service Availability');
try {
  if (typeof window !== 'undefined') {
    console.log('✅ Window object available');
    console.log('✅ Browser environment detected');
    
    // Check for notification API
    if ('Notification' in window) {
      console.log('✅ Notification API available');
      console.log(`   Permission status: ${Notification.permission}`);
    } else {
      console.log('❌ Notification API not available');
    }
    
    // Check for service worker
    if ('serviceWorker' in navigator) {
      console.log('✅ Service Worker API available');
    } else {
      console.log('❌ Service Worker API not available');
    }
    
    // Check for push manager
    if ('PushManager' in window) {
      console.log('✅ Push Manager API available');
    } else {
      console.log('❌ Push Manager API not available');
    }
  }
} catch (error) {
  console.log('❌ Service availability test failed:', error);
}

// Test 2: Device Detection
console.log('\n📱 Test 2: Device Detection');
try {
  const userAgent = navigator.userAgent.toLowerCase();
  const isIOS = /ipad|iphone|ipod/.test(userAgent);
  const isIOSPWA = isIOS && navigator.standalone === true;
  const isAndroid = /android/.test(userAgent);
  
  console.log(`   User Agent: ${navigator.userAgent}`);
  console.log(`   Is iOS: ${isIOS}`);
  console.log(`   Is iOS PWA: ${isIOSPWA}`);
  console.log(`   Is Android: ${isAndroid}`);
  console.log(`   Platform: ${navigator.platform}`);
  
  if (isIOS) {
    const iosVersionMatch = userAgent.match(/os (\d+)_(\d+)_?(\d+)?/);
    const iosVersion = iosVersionMatch ? parseInt(iosVersionMatch[1], 10) : 0;
    console.log(`   iOS Version: ${iosVersion}`);
    console.log(`   iOS 16.4+ Support: ${iosVersion >= 16}`);
  }
} catch (error) {
  console.log('❌ Device detection test failed:', error);
}

// Test 3: Check if our services are loaded
console.log('\n🔧 Test 3: Service Loading');
try {
  // These will only work if the page has loaded our services
  if (typeof window.unifiedNotificationService !== 'undefined') {
    console.log('✅ Unified Notification Service loaded');
  } else {
    console.log('⚠️ Unified Notification Service not found in global scope');
  }
  
  if (typeof window.iosNotificationService !== 'undefined') {
    console.log('✅ iOS Notification Service loaded');
  } else {
    console.log('⚠️ iOS Notification Service not found in global scope');
  }
  
  if (typeof window.fcmService !== 'undefined') {
    console.log('✅ FCM Service loaded');
  } else {
    console.log('⚠️ FCM Service not found in global scope');
  }
} catch (error) {
  console.log('❌ Service loading test failed:', error);
}

// Test 4: Firebase Configuration
console.log('\n🔥 Test 4: Firebase Configuration');
try {
  // Check if Firebase is configured
  if (typeof window.firebase !== 'undefined') {
    console.log('✅ Firebase SDK loaded');
  } else {
    console.log('⚠️ Firebase SDK not found');
  }
  
  // Check environment variables (these should be available in the browser)
  const hasApiKey = typeof process !== 'undefined' && process.env && process.env.NEXT_PUBLIC_FIREBASE_API_KEY;
  const hasProjectId = typeof process !== 'undefined' && process.env && process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID;
  const hasVapidKey = typeof process !== 'undefined' && process.env && process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY;
  
  console.log(`   Firebase API Key: ${hasApiKey ? 'Set' : 'Missing'}`);
  console.log(`   Firebase Project ID: ${hasProjectId ? 'Set' : 'Missing'}`);
  console.log(`   VAPID Key: ${hasVapidKey ? 'Set' : 'Missing'}`);
} catch (error) {
  console.log('❌ Firebase configuration test failed:', error);
}

// Test 5: Service Worker Registration
console.log('\n⚙️ Test 5: Service Worker Status');
try {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.getRegistrations().then(registrations => {
      console.log(`   Active registrations: ${registrations.length}`);
      registrations.forEach((registration, index) => {
        console.log(`   Registration ${index + 1}: ${registration.scope}`);
        console.log(`     State: ${registration.active ? registration.active.state : 'No active worker'}`);
      });
    });
    
    navigator.serviceWorker.ready.then(registration => {
      console.log('✅ Service Worker ready');
      console.log(`   Scope: ${registration.scope}`);
    }).catch(error => {
      console.log('❌ Service Worker not ready:', error);
    });
  }
} catch (error) {
  console.log('❌ Service Worker test failed:', error);
}

// Test 6: Manual Notification Test (requires user interaction)
console.log('\n🔔 Test 6: Manual Notification Test');
console.log('   To test notifications manually, run:');
console.log('   testNotification()');

window.testNotification = async function() {
  try {
    console.log('🔔 Testing notification...');
    
    // Request permission
    const permission = await Notification.requestPermission();
    console.log(`   Permission result: ${permission}`);
    
    if (permission === 'granted') {
      // Show test notification
      const notification = new Notification('Test Notification', {
        body: 'This is a test notification from the integration test',
        icon: '/assets/icons/Icon-192.png',
        tag: 'integration-test'
      });
      
      notification.onclick = function() {
        console.log('✅ Notification clicked');
        notification.close();
      };
      
      setTimeout(() => {
        notification.close();
      }, 5000);
      
      console.log('✅ Test notification shown');
    } else {
      console.log('❌ Notification permission denied');
    }
  } catch (error) {
    console.log('❌ Manual notification test failed:', error);
  }
};

// Summary
console.log('\n📊 Integration Test Summary');
console.log('✅ Basic browser APIs checked');
console.log('✅ Device detection completed');
console.log('✅ Service loading verified');
console.log('✅ Firebase configuration checked');
console.log('✅ Service Worker status checked');
console.log('\n💡 Next Steps:');
console.log('1. Visit /notification-test page to use the enhanced test interface');
console.log('2. Run testNotification() in console to test basic notifications');
console.log('3. Use the "Run All Tests" button on the test page for comprehensive testing');
console.log('4. Test on different devices (iOS Safari, Android Chrome, Desktop)');

console.log('\n🎉 Integration test completed!');
