(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2454],{36450:(e,s,t)=>{"use strict";t.d(s,{default:()=>i});var a=t(95155),c=t(78093),r=t(6874),l=t.n(r),n=t(12115);let i=()=>{let[e,s]=(0,n.useState)(""),[t,r]=(0,n.useState)(!1),[i,o]=(0,n.useState)(null),m=e=>{o(e)},d=()=>{o(null)};return(0,a.jsxs)(a.Frag<PERSON>,{children:[(0,a.jsx)(c.A,{links:"login"}),(0,a.jsx)("div",{className:"login-wrapper d-flex align-items-center justify-content-center",children:(0,a.jsxs)("div",{className:"custom-container",children:[(0,a.jsx)("div",{className:"text-center px-4",children:(0,a.jsx)("img",{className:"login-intro-img",src:"/assets/img/bg-img/36.png",alt:""})}),(0,a.jsxs)("div",{className:"register-form mt-4",children:[(0,a.jsx)("h6",{className:"mb-3 text-center",children:"Register to continue to the Affan"}),(0,a.jsxs)("form",{action:"/otp",onSubmit:e=>e.preventDefault(),children:[(0,a.jsx)("div",{className:"form-group text-start mb-3",children:(0,a.jsx)("input",{className:"form-control ".concat("text"===i?"form-control-clicked":""),type:"text",placeholder:"Email address",onFocus:()=>m("text"),onBlur:d})}),(0,a.jsx)("div",{className:"form-group text-start mb-3",children:(0,a.jsx)("input",{className:"form-control ".concat("username"===i?"form-control-clicked":""),type:"text",placeholder:"Username",onFocus:()=>m("username"),onBlur:d})}),(0,a.jsxs)("div",{className:"form-group text-start mb-3 position-relative",children:[(0,a.jsx)("input",{className:"form-control ".concat("password"===i?"form-control-clicked":""),onFocus:()=>m("password"),onBlur:d,type:t?"text":"password",value:e,onChange:e=>s(e.target.value),id:"psw-input",placeholder:"New password"}),(0,a.jsxs)("div",{className:"position-absolute ".concat(t?"active":""),id:"password-visibility",onClick:()=>{r(!t)},style:{cursor:"pointer",top:"50%",right:"10px",transform:"translateY(-50%)"},children:[(0,a.jsx)("i",{className:"bi bi-eye"}),(0,a.jsx)("i",{className:"bi bi-eye-slash"})]})]}),(0,a.jsx)("div",{className:"mb-3",id:"pswmeter"}),(0,a.jsxs)("div",{className:"form-check mb-3",children:[(0,a.jsx)("input",{className:"form-check-input",id:"checkedCheckbox",type:"checkbox",value:"",defaultChecked:!0}),(0,a.jsx)("label",{className:"form-check-label text-muted fw-normal",htmlFor:"checkedCheckbox",children:"I agree with the terms & policy."})]}),(0,a.jsx)("button",{className:"btn btn-primary w-100",type:"submit",children:"Sign Up"})]})]}),(0,a.jsx)("div",{className:"login-meta-data text-center",children:(0,a.jsxs)("p",{className:"mt-3 mb-0",children:["Already have an account?",(0,a.jsxs)(l(),{className:"stretched-link",href:"/login",children:[" "," ","Login"]})]})})]})})]})}},50982:(e,s,t)=>{Promise.resolve().then(t.bind(t,36450))},78093:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var a=t(95155),c=t(6874),r=t.n(c);t(12115);let l=e=>{let{links:s}=e;return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"login-back-button",children:(0,a.jsx)(r(),{href:"/".concat(s),children:(0,a.jsx)("i",{className:"bi bi-arrow-left-short"})})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,8441,1684,7358],()=>s(50982)),_N_E=e.O()}]);