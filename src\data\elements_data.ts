const elements_data = [
	{
		category: "Navigation",
		icon: "bi bi-list",
		description: "Modern header, footer, and sidebar nav.",
		items: [
			{ name: "Header Variations", href: "/header-menu" },
			{ name: "Footer Variations", href: "/footer-menu" },
			{ name: "Left Sidebar Nav", href: "/sidebar-left-menu" },
			{ name: "Right Sidebar Nav", href: "/sidebar-right-menu" },
		],
	},
	{
		category: "Notifications",
		icon: "bi bi-bell-fill",
		description: "Display alerts & notifications creatively.",
		items: [
			{ name: "<PERSON><PERSON><PERSON>", href: "/alerts" },
			{ name: "Toast<PERSON>", href: "/toasts" },
			{ name: "Online / Offline Detection", href: "/online-offline-detection" },
		],
	},
	{
		category: "Form Layouts",
		icon: "bi bi-ui-checks",
		description: "All form input components.",
		items: [
			{ name: "Input", href: "/form-input" },
			{ name: "Textarea", href: "/form-textarea" },
			{ name: "Select", href: "/form-select" },
			{ name: "Input Group", href: "/form-input-group" },
			{ name: "Checkbox", href: "/form-check" },
			{ name: "Radio", href: "/form-radio" },
			{ name: "File Upload", href: "/form-file-upload" },
			{ name: "Range", href: "/form-range" },
			{ name: "Auto Complete", href: "/form-auto-complete" },
			{ name: "Switches", href: "/form-switches" },
			{ name: "Form Validation", href: "/form-validation" },
		],
	},
	{
		category: "UI Elements",
		icon: "bi bi-columns-gap",
		description: "Beautifully designed lots of UI elements.",
		items: [
			{ name: "Accordion", href: "/accordion" },
			{ name: "Badge", href: "/badge" },
			{ name: "Button", href: "/button" },
			{ name: "Breadcrumb", href: "/breadcrumb" },
			{ name: "Timeline", href: "/timeline" },
			{ name: "Card", href: "/card" },
			{ name: "Image Gallery", href: "/image-gallery" },
			{ name: "Hero Blocks", href: "/hero-blocks" },
			{ name: "Tab", href: "/tab" },
			{ name: "Offcanvas", href: "/offcanvas" },
			{ name: "User Ratings", href: "/user-ratings" },
			{ name: "Testimonials", href: "/testimonial" },
			{ name: "Call to Action", href: "/call-to-action" },
			{ name: "Partner Logo", href: "/partner-logo" },
		],
	},
	{
		category: "Helpers",
		icon: "bi bi-tools",
		description: "Quickly create any blocks with helpers.",
		items: [
			{ name: "Borders", href: "/borders" },
			{ name: "Colors", href: "/colors" },
			{ name: "Dividers", href: "/dividers" },
			{ name: "Embeds Video", href: "/embed-video" },
			{ name: "Images", href: "/images" },
			{ name: "List Group", href: "/list-group" },
			{ name: "Modal", href: "/modal" },
			{ name: "Pagination", href: "/pagination" },
			{ name: "Progress Bar", href: "/progress-bar" },
			{ name: "Scrollspy", href: "/scrollspy" },
			{ name: "Spinners", href: "/spinners" },
			{ name: "Stretched link", href: "/stretched-link" },
			{ name: "Shadows", href: "/shadows" },
			{ name: "Sizing", href: "/sizing" },
			{ name: "Tooltips", href: "/tooltips" },
			{ name: "Text truncation", href: "/text-truncation" },
			{ name: "Typography", href: "/typography" },
			{ name: "Text", href: "/text" },
		],
	},
	{
		category: "Carousels",
		icon: "bi bi-sliders",
		description: "Create a variety of carousels.",
		items: [
			{ name: "Bootstrap Carousel", href: "/bootstrap-carousel" },
			// { name: "Tiny Slider", href: "/tiny-slider" },
		],
	},
	{
		category: "Tables",
		icon: "bi bi-table",
		description: "Make responsive table layouts.",
		items: [
			{ name: "Basic Table", href: "/basic-table" },
			{ name: "Data Table", href: "/data-table" },
			{ name: "Price Table", href: "/price-table" },
			{ name: "Comparison Table", href: "/comparison-table" },
		],
	},
	{
		category: "Timer",
		icon: "bi bi-clock-history",
		description: "Countdown or countup your milestones.",
		items: [
			{ name: "Count Down", href: "/countdown" },
			{ name: "Counter Up", href: "/counterup" },
		],
	},
	{
		category: "Charts",
		icon: "bi bi-bar-chart",
		description: "Lots of charts for showing data.",
		items: [
			{ name: "Area Chart", href: "/area-charts" },
			{ name: "Column Chart", href: "/column-charts" },
			{ name: "Line Chart", href: "/line-charts" },
			{ name: "Pie Chart", href: "/pie-charts" },
		],
	},
];

export default elements_data;
