"use client";

import React from "react";
import { ChecklistComponent, ErrorBuckets, TextInputData } from "../types/ChecklistTypes";
import { Card, Form } from "react-bootstrap";

interface TextInputComponentProps {
    component: ChecklistComponent;
    componentIndex: number;
    checklistData: ChecklistComponent[];
    setChecklistData: React.Dispatch<React.SetStateAction<ChecklistComponent[]>>;
    errorMap: ErrorBuckets;
}

const TextInputComponent: React.FC<TextInputComponentProps> = ({
    component,
    componentIndex,
    checklistData,
    setChecklistData,
    errorMap,
}) => {
    const componentData = component.data as TextInputData;
    const textInputError = errorMap.checklist[`${componentIndex}-text`];

    const handleTextChange = (value: string) => {
        const updated = [...checklistData];
        updated[componentIndex].textValue = value;
        setChecklistData(updated);
    };

    return (
        <Card className="mb-3 shadow-sm" style={{
            border: "1px solid #e5e7eb",
            borderRadius: "8px",
            overflow: "hidden"
        }}>
            <Card.Body className="p-2">
                <Form.Group>
                    <Form.Label className="fw-bold">
                        {componentData.label}
                        {componentData.required && <span className="text-danger ms-1">*</span>}
                    </Form.Label>
                    <Form.Control
                        type="text"
                        value={component.textValue || ''}
                        onChange={(e) => handleTextChange(e.target.value)}
                        placeholder={componentData.placeholder || "Enter text here..."}
                        className={textInputError ? "border-danger" : ""}
                    />
                    {textInputError && (
                        <div className="text-danger small mt-1">{textInputError}</div>
                    )}
                </Form.Group>
            </Card.Body>
        </Card>
    );
};

export default TextInputComponent;
