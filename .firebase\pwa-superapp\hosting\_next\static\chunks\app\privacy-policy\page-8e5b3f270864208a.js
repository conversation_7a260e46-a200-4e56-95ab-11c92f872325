(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2245,2902,8898],{38719:(e,i,n)=>{Promise.resolve().then(n.bind(n,38983)),Promise.resolve().then(n.bind(n,91727))},38983:(e,i,n)=>{"use strict";n.d(i,{default:()=>c});var s=n(95155),t=n(6874),l=n.n(t);n(12115);let a=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],c=()=>(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,s.jsx)("div",{className:"container px-0",children:(0,s.jsx)("div",{className:"footer-nav position-relative",children:(0,s.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:a.map((e,i)=>(0,s.jsx)("li",{children:(0,s.jsxs)(l(),{href:"/".concat(e.link),children:[(0,s.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,s.jsx)("span",{children:e.title})]})},i))})})})})})}},e=>{var i=i=>e(e.s=i);e.O(0,[6874,1955,1531,1727,8441,1684,7358],()=>i(38719)),_N_E=e.O()}]);