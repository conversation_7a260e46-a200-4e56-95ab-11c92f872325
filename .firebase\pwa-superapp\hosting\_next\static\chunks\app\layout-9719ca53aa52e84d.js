(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{26957:(e,t,r)=>{"use strict";r.d(t,{AM:()=>a,Dp:()=>d,F4:()=>E,FI:()=>A,H$:()=>n,J9:()=>g,Jo:()=>m,K9:()=>l,M6:()=>f,MO:()=>u,OT:()=>L,P4:()=>I,UR:()=>O,WD:()=>C,WH:()=>y,WU:()=>k,_i:()=>s,bW:()=>S,dG:()=>i,dm:()=>x,iJ:()=>j,mh:()=>M,oo:()=>p,pZ:()=>b,u3:()=>c,x2:()=>_,xE:()=>o,xo:()=>w,yo:()=>v,zP:()=>h});let n="https://client-api.acuizen.com",i=n+"/login-configs",o=n+"/services",s=e=>n+"/files/"+e+"/presigned-url",a=n+"/users/me",l=n+"/dynamic-titles",c=n+"/users/get_users",d=n+"/files",u=n+"/observation-reports",f=n+"/my-observation-reports",p=n+"/dropdowns",g=n+"/get-blob",h=n+"/permit-reports",m=n+"/users",v=n+"/toolbox-talks",b=n+"/my-toolbox-talks",y=e=>n+"/my-assigned-actions/"+e,_=e=>n+"/inspection-checklist-submit/"+e,I=e=>n+"/observation-reports/"+e,S=e=>n+"/inspection-task-submit/"+e,w=e=>n+"/inspections/"+e,k=e=>n+"/permit-report-submit/"+e,C=e=>n+"/permit-reports-acknowledge/"+e,O=e=>n+"/permit-reports-update-status/"+e,A=e=>n+"/observation-action-submit/"+e,x=n+"/risk-assessments",j=e=>n+"/risk-assessments/"+e,E=e=>n+"/ra-team-member-submit-signature/"+e,L=n+"/permit-reports",M=e=>n+"/permit-reports/"+e},37759:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,X:()=>i});let n=(0,r(51990).Z0)({name:"service",initialState:{loading:!1,service:[]},reducers:{setService(e,t){e.service=t.payload}}}),i=n.actions,o=n},38336:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(96078),i=r(26957);let o=n.A.create({baseURL:i.H$,headers:{Accept:"application/json","Content-Type":"application/json"},mode:"no-cors"});o.interceptors.request.use(async e=>(e.metadata={requestTime:Date.now()},e),e=>Promise.reject(e)),o.interceptors.response.use(e=>{var t;return e.headers["x-request-time"]=null==(t=e.config.metadata)?void 0:t.requestTime,e},async e=>{let{offlineQueue:t}=await r.e(8836).then(r.bind(r,48836)),{offlineStorage:n}=await r.e(58).then(r.bind(r,60058));if(t.shouldQueue(e)){var i,o,s,a;let r=e.config;if(await t.addRequest(r.url,(null==(i=r.method)?void 0:i.toUpperCase())||"GET",r.data,r.headers),(null==(o=r.method)?void 0:o.toLowerCase())==="get")try{if(null==(s=r.url)?void 0:s.includes("/services")){let e=await n.getServices();if(e.length>0)return console.log("\uD83D\uDCF1 Serving services from offline cache"),{data:e,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:r}}if(null==(a=r.url)?void 0:a.includes("assigned-actions")){let e=new URLSearchParams(r.url.split("?")[1]).get("filter"),t="All";if(e)try{JSON.parse(decodeURIComponent(e))}catch(i){let e=r.url.split("/"),n=e.findIndex(e=>"assigned-actions"===e);-1!==n&&e[n+1]&&(t=e[n+1])}let i=await n.getActions(t);if(i.length>0)return console.log("\uD83D\uDCF1 Serving actions from offline cache"),{data:i,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:r}}}catch(e){console.error("❌ Error serving from cache:",e)}}return Promise.reject(e)});let s=o},42714:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return o}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},n=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function i(e){return["async","defer","noModule"].includes(e)}function o(e,t){for(let[o,s]of Object.entries(t)){if(!t.hasOwnProperty(o)||n.includes(o)||void 0===s)continue;let a=r[o]||o.toLowerCase();"SCRIPT"===e.tagName&&i(a)?e[a]=!!s:e.setAttribute(a,String(s)),(!1===s||"SCRIPT"===e.tagName&&i(a)&&(!s||"false"===s))&&(e.setAttribute(a,""),e.removeAttribute(a))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51306:()=>{},69243:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return b},handleClientScriptLoad:function(){return h},initScriptLoader:function(){return m}});let n=r(88229),i=r(6966),o=r(95155),s=n._(r(47650)),a=i._(r(12115)),l=r(82830),c=r(42714),d=r(92374),u=new Map,f=new Set,p=e=>{if(s.default.preinit)return void e.forEach(e=>{s.default.preinit(e,{as:"style"})});{let t=document.head;e.forEach(e=>{let r=document.createElement("link");r.type="text/css",r.rel="stylesheet",r.href=e,t.appendChild(r)})}},g=e=>{let{src:t,id:r,onLoad:n=()=>{},onReady:i=null,dangerouslySetInnerHTML:o,children:s="",strategy:a="afterInteractive",onError:l,stylesheets:d}=e,g=r||t;if(g&&f.has(g))return;if(u.has(t)){f.add(g),u.get(t).then(n,l);return}let h=()=>{i&&i(),f.add(g)},m=document.createElement("script"),v=new Promise((e,t)=>{m.addEventListener("load",function(t){e(),n&&n.call(this,t),h()}),m.addEventListener("error",function(e){t(e)})}).catch(function(e){l&&l(e)});o?(m.innerHTML=o.__html||"",h()):s?(m.textContent="string"==typeof s?s:Array.isArray(s)?s.join(""):"",h()):t&&(m.src=t,u.set(t,v)),(0,c.setAttributesFromProps)(m,e),"worker"===a&&m.setAttribute("type","text/partytown"),m.setAttribute("data-nscript",a),d&&p(d),document.body.appendChild(m)};function h(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,d.requestIdleCallback)(()=>g(e))}):g(e)}function m(e){e.forEach(h),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");f.add(t)})}function v(e){let{id:t,src:r="",onLoad:n=()=>{},onReady:i=null,strategy:c="afterInteractive",onError:u,stylesheets:p,...h}=e,{updateScripts:m,scripts:v,getIsSsr:b,appDir:y,nonce:_}=(0,a.useContext)(l.HeadManagerContext),I=(0,a.useRef)(!1);(0,a.useEffect)(()=>{let e=t||r;I.current||(i&&e&&f.has(e)&&i(),I.current=!0)},[i,t,r]);let S=(0,a.useRef)(!1);if((0,a.useEffect)(()=>{if(!S.current){if("afterInteractive"===c)g(e);else"lazyOnload"===c&&("complete"===document.readyState?(0,d.requestIdleCallback)(()=>g(e)):window.addEventListener("load",()=>{(0,d.requestIdleCallback)(()=>g(e))}));S.current=!0}},[e,c]),("beforeInteractive"===c||"worker"===c)&&(m?(v[c]=(v[c]||[]).concat([{id:t,src:r,onLoad:n,onReady:i,onError:u,...h}]),m(v)):b&&b()?f.add(t||r):b&&!b()&&g(e)),y){if(p&&p.forEach(e=>{s.default.preinit(e,{as:"style"})}),"beforeInteractive"===c)if(!r)return h.dangerouslySetInnerHTML&&(h.children=h.dangerouslySetInnerHTML.__html,delete h.dangerouslySetInnerHTML),(0,o.jsx)("script",{nonce:_,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...h,id:t}])+")"}});else return s.default.preload(r,h.integrity?{as:"script",integrity:h.integrity,nonce:_,crossOrigin:h.crossOrigin}:{as:"script",nonce:_,crossOrigin:h.crossOrigin}),(0,o.jsx)("script",{nonce:_,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...h,id:t}])+")"}});"afterInteractive"===c&&r&&s.default.preload(r,h.integrity?{as:"script",integrity:h.integrity,nonce:_,crossOrigin:h.crossOrigin}:{as:"script",nonce:_,crossOrigin:h.crossOrigin})}return null}Object.defineProperty(v,"__nextScript",{value:!0});let b=v;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73166:()=>{},76230:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,69243,23)),Promise.resolve().then(r.bind(r,79705)),Promise.resolve().then(r.t.bind(r,73166,23)),Promise.resolve().then(r.t.bind(r,51306,23))},79705:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var n=r(95155),i=r(12115),o=r(34540),s=r(51990),a=r(81359),l=r(37759);let c=(0,s.U1)({reducer:{login:a.A.reducer,service:l.A.reducer}});var d=r(96078),u=r(38336);let f=()=>{let[e,t]=(0,i.useState)(null),[r,o]=(0,i.useState)(!1),[s,a]=(0,i.useState)(!1),[l,c]=(0,i.useState)(!1),[d,u]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=window.navigator.userAgent.toLowerCase();/iphone|ipad|ipod/.test(e)&&(a(!0),window.navigator.standalone&&c(!0));let r=e=>{e.preventDefault(),t(e),o(!0)};return window.addEventListener("beforeinstallprompt",r),window.addEventListener("appinstalled",()=>{t(null),o(!1)}),()=>{window.removeEventListener("beforeinstallprompt",r)}},[]),(0,i.useEffect)(()=>{s&&!l&&u(!0)},[s,l]);let f=async()=>{e&&(e.prompt(),"accepted"===(await e.userChoice).outcome&&console.log("User accepted the PWA installation"),t(null),o(!1))};return(0,n.jsxs)(n.Fragment,{children:[r&&!s&&(0,n.jsx)("button",{className:"install-btn",onClick:f,children:"Install App"}),d&&(0,n.jsx)("div",{className:"ios-popup-overlay",children:(0,n.jsxs)("div",{className:"ios-popup-content",children:[(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Acuizen"}),(0,n.jsx)("br",{}),"To install the app, tap the"," ",(0,n.jsx)("strong",{children:"Share"})," ","icon"," ",(0,n.jsx)("svg",{viewBox:"0 0 24 24",width:"16",height:"16",style:{verticalAlign:"middle",margin:"0 4px"},"aria-hidden":"true",children:(0,n.jsx)("path",{fill:"currentColor",d:"M12 2.5l-3.5 3.5h2.5v6h2v-6h2.5L12 2.5zM6 14v6h12v-6h2v6c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2v-6h2z"})}),"in Safari and select ",(0,n.jsx)("strong",{children:"Add to Home Screen"}),"."]}),(0,n.jsx)("button",{onClick:()=>{u(!1)},className:"ios-popup-close",children:"Close"})]})})]})};var p=r(15123),g=r(46222);function h(e){let{children:t}=e;(0,i.useEffect)(()=>{"serviceWorker"in navigator?navigator.serviceWorker.register("/sw.js").then(()=>{console.log("✅ Service Worker registered"),r()}).catch(e=>console.log("❌ Service Worker registration failed:",e)):r()},[]);let r=async()=>{try{if(console.log("\uD83D\uDD04 Initializing unified notification service..."),await g.A.initialize()){console.log("✅ Unified Notification Service initialized");let e=g.A.getDeviceInfo();console.log("\uD83D\uDCF1 Device info:",e)}else console.log("⚠️ Unified notification service failed, falling back to FCM only"),await p.A.initialize(),console.log("✅ FCM Service initialized (fallback)");"serviceWorker"in navigator&&navigator.serviceWorker.addEventListener("message",e=>{var t;(null==(t=e.data)?void 0:t.type)==="NOTIFICATION_CLICKED"&&console.log("\uD83D\uDCF1 Notification clicked, data:",e.data.data)})}catch(e){console.error("❌ Notification service initialization failed:",e);try{await p.A.initialize(),console.log("✅ FCM Service initialized (error fallback)")}catch(e){console.error("❌ FCM fallback also failed:",e)}}};return(0,n.jsxs)(o.Kq,{store:c,children:[t,(0,n.jsx)(f,{})]})}(e=>{u.A.interceptors.request.use(e=>(e.headers.Authorization="Bearer ".concat(localStorage.getItem("access_token")),e),e=>Promise.reject(e)),u.A.interceptors.response.use(e=>e,async t=>{let r=t.config;if(401===t.response.status&&!r._retry){r._retry=!0;let t=localStorage.getItem("refresh_token");if(t)try{let e=localStorage.getItem("COGNITO_USER_DOMAIN"),n=localStorage.getItem("COGNITO_USER_APP_CLIENT_ID"),i="grant_type=refresh_token&client_id=".concat(n,"&refresh_token=").concat(t),o=await d.A.post("".concat(e,"/oauth2/token"),i,{headers:{"Content-Type":"application/x-www-form-urlencoded"}});if(200===o.status)return localStorage.setItem("access_token",o.data.access_token),u.A.defaults.headers.common.Authorization="Bearer ".concat(o.data.access_token),r.headers.Authorization="Bearer ".concat(o.data.access_token),(0,u.A)(r)}catch(t){localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("COGNITO_USER_DOMAIN"),localStorage.removeItem("COGNITO_USER_APP_CLIENT_ID"),e.dispatch(a.l.setLogout())}else localStorage.removeItem("access_token"),e.dispatch(a.l.setLogout())}return Promise.reject(t)})})(c)},81359:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,l:()=>i});let n=(0,r(51990).Z0)({name:"login",initialState:{isLogin:!1,user:null},reducers:{setLogin(e){e.isLogin=!0},setLogout(e){e.isLogin=!1,e.user=null},setUser(e,t){e.user=t.payload}}}),i=n.actions,o=n},92374:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}},e=>{var t=t=>e(e.s=t);e.O(0,[1911,229,7982,8781,3497,8570,5403,6078,635,7666,6222,8441,1684,7358],()=>t(76230)),_N_E=e.O()}]);