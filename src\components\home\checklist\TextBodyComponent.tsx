"use client";

import React from "react";
import { ChecklistComponent, TextBodyData } from "../types/ChecklistTypes";

interface TextBodyComponentProps {
    component: ChecklistComponent;
    componentIndex: number;
}

const TextBodyComponent: React.FC<TextBodyComponentProps> = ({ component }) => {
    const textBodyData = component.data as TextBodyData;
    
    return (
        <div className="mb-3 p-3 rounded border bg-light">
            <p className="mb-0 text-muted" style={{
                whiteSpace: 'pre-wrap',
                fontSize: "0.9rem"
            }}>
                {textBodyData.content}
            </p>
        </div>
    );
};

export default TextBodyComponent;
