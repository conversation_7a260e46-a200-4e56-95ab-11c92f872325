"use client";

import { initializeMessaging, getToken, onMessage } from '@/config/firebase';

// VAPID key for Firebase Cloud Messaging
const VAPID_KEY = process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY || 'BDukpatijXC4YB_g4W7FvW-UjBfYtMAM7epw7MJFkcb4ICU8udWq7UYAQlk-hFJ9WmZ-EutlgybC5-HIAkaMSnU';

class FCMService {
  private messaging: any = null;
  private isInitialized = false;

  async initialize() {
    if (this.isInitialized) {
      console.log('✅ FCM Service already initialized');
      return this.messaging;
    }

    try {
      console.log('🔄 Initializing FCM Service...');
      console.log('Browser environment check:', typeof window !== 'undefined');
      console.log('Notification support:', 'Notification' in window);
      console.log('Service Worker support:', 'serviceWorker' in navigator);

      this.messaging = await initializeMessaging();
      this.isInitialized = true;

      if (this.messaging) {
        // Set up foreground message listener
        this.setupForegroundMessageListener();
        console.log('✅ FCM Service initialized successfully');
        console.log('Messaging instance:', !!this.messaging);
      } else {
        console.log('⚠️ FCM messaging not supported or failed to initialize');
      }

      return this.messaging;
    } catch (error: any) {
      console.error('❌ Error initializing FCM service:', error);
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        code: error.code
      });
      return null;
    }
  }

  async requestNotificationPermission(): Promise<boolean> {
    if (typeof window === 'undefined') return false;

    try {
      const permission = await Notification.requestPermission();

      if (permission === 'granted') {
        console.log('✅ Notification permission granted');
        return true;
      } else {
        console.log('❌ Notification permission denied');
        return false;
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  }

  async generateFCMToken(): Promise<string | null> {
    console.log('🔄 Starting FCM token generation...');
    console.log('🔍 Environment check:');
    console.log('  - Window available:', typeof window !== 'undefined');
    console.log('  - HTTPS/Localhost:', window.location.protocol === 'https:' || window.location.hostname === 'localhost');
    console.log('  - Current URL:', window.location.href);

    // Debug environment variables
    console.log('🔍 Environment variables:');
    console.log('  - API Key:', process.env.NEXT_PUBLIC_FIREBASE_API_KEY ? 'Set' : 'Missing');
    console.log('  - Project ID:', process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'Missing');
    console.log('  - Sender ID:', process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || 'Missing');
    console.log('  - VAPID Key:', process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY ? 'Set' : 'Missing');

    try {
      // Step 1: Ensure messaging is initialized
      if (!this.messaging) {
        console.log('🔄 Step 1: Initializing FCM messaging...');
        await this.initialize();
      }

      if (!this.messaging) {
        console.error('❌ Step 1 FAILED: FCM messaging not available');
        console.error('This usually means:');
        console.error('  - Firebase configuration is incorrect');
        console.error('  - Browser doesn\'t support FCM');
        console.error('  - Network connectivity issues');
        return null;
      }
      console.log('✅ Step 1: FCM messaging initialized');

      // Step 2: Check service worker
      console.log('🔄 Step 2: Checking service worker...');
      if ('serviceWorker' in navigator) {
        try {
          const registration = await navigator.serviceWorker.ready;
          console.log('✅ Step 2: Service Worker is ready');
          console.log('  - Scope:', registration.scope);
          console.log('  - Active:', !!registration.active);
        } catch (swError) {
          console.warn('⚠️ Step 2: Service Worker not ready:', swError);
          // Continue anyway, some browsers might work without SW ready
        }
      } else {
        console.warn('⚠️ Step 2: Service Worker not supported');
      }

      // Step 3: Check notification permission
      console.log('🔄 Step 3: Checking notification permission...');
      console.log('  - Current permission:', Notification.permission);

      const hasPermission = await this.requestNotificationPermission();
      if (!hasPermission) {
        console.log('❌ Step 3 FAILED: Cannot generate token without notification permission');
        return null;
      }
      console.log('✅ Step 3: Notification permission granted');

      // Step 4: Generate FCM token
      console.log('🔄 Step 4: Generating FCM token...');
      console.log('  - Using VAPID key:', VAPID_KEY.substring(0, 20) + '...');
      console.log('  - Messaging instance type:', typeof this.messaging);

      const token = await getToken(this.messaging, {
        vapidKey: VAPID_KEY
      });

      if (token) {
        console.log('✅ Step 4: FCM Token generated successfully!');
        console.log('  - Token length:', token.length);
        console.log('  - Token preview:', token.substring(0, 50) + '...');
        console.log('  - Full token:', token);

        // Store token in localStorage for future use
        localStorage.setItem('fcm_token', token);
        console.log('✅ Token stored in localStorage');

        // Send token to your backend server
        console.log('🔄 Step 5: Sending token to server...');
        await this.sendTokenToServer(token);

        return token;
      } else {
        console.log('❌ Step 4 FAILED: No registration token available');
        console.log('This could be due to:');
        console.log('  - Browser blocking notifications');
        console.log('  - Invalid VAPID key');
        console.log('  - Firebase configuration issues');
        console.log('  - Network connectivity issues');
        console.log('  - Service worker not properly configured');
        return null;
      }
    } catch (error: any) {
      console.error('❌ CRITICAL ERROR in FCM token generation:', error);
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        code: error.code,
        stack: error.stack
      });

      // Additional debugging for common Firebase errors
      if (error.code) {
        switch (error.code) {
          case 'messaging/unsupported-browser':
            console.error('🔍 Browser not supported for FCM');
            break;
          case 'messaging/permission-blocked':
            console.error('🔍 Notification permission blocked');
            break;
          case 'messaging/vapid-key-required':
            console.error('🔍 VAPID key is required but missing');
            break;
          case 'messaging/invalid-vapid-key':
            console.error('🔍 VAPID key is invalid');
            break;
          default:
            console.error('🔍 Unknown Firebase error code:', error.code);
        }
      }

      return null;
    }
  }

  async sendTokenToServer(token: string): Promise<void> {
    try {
      // Get authorization token from localStorage
      const authToken = localStorage.getItem('access_token') || localStorage.getItem('token') || localStorage.getItem('authToken');

      if (!authToken) {
        console.log('⚠️ No authorization token found, skipping token registration');
        return;
      }

      // Get base URL from environment or use current origin
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://client-api.acuizen.com';
      const apiUrl = `${baseUrl}/users/me`;

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
        },
        body: JSON.stringify({
          deviceToken: token
        }),
      });

      if (response.ok) {
        console.log('✅ FCM token sent to server successfully');
        const responseData = await response.json();
        console.log('Server response:', responseData);
      } else {
        const errorText = await response.text();
        console.log('⚠️ Failed to send FCM token to server:', response.status, errorText);
      }
    } catch (error) {
      console.error('❌ Error sending token to server:', error);
    }
  }

  setupForegroundMessageListener(): void {
    if (!this.messaging) return;

    onMessage(this.messaging, (payload) => {
      console.log('📱 Foreground message received:', payload);

      // Handle foreground notifications
      this.showNotification(payload);
    });
  }

  showNotification(payload: any): void {
    const { notification, data } = payload;

    if (notification) {
      // Create a custom notification
      const notificationTitle = notification.title || 'New Notification';
      const notificationOptions = {
        body: notification.body || '',
        icon: notification.icon || '/assets/icons/Icon-192.png',
        badge: '/assets/icons/Icon-72.png',
        tag: data?.tag || 'default',
        data: data || {},
        requireInteraction: true,
        actions: [
          {
            action: 'view',
            title: 'View',
            icon: '/assets/icons/Icon-72.png'
          },
          {
            action: 'dismiss',
            title: 'Dismiss'
          }
        ]
      };

      if ('serviceWorker' in navigator && 'showNotification' in ServiceWorkerRegistration.prototype) {
        navigator.serviceWorker.ready.then((registration) => {
          registration.showNotification(notificationTitle, notificationOptions);
        });
      } else {
        // Fallback for browsers that don't support service worker notifications
        new Notification(notificationTitle, notificationOptions);
      }
    }
  }

  async getStoredToken(): Promise<string | null> {
    return localStorage.getItem('fcm_token');
  }

  async refreshToken(): Promise<string | null> {
    // Clear stored token and generate new one
    localStorage.removeItem('fcm_token');
    return await this.generateFCMToken();
  }

  async deleteToken(): Promise<void> {
    try {
      if (this.messaging) {
        // Note: deleteToken is not available in v9+ SDK
        // Token will be automatically refreshed when needed
        localStorage.removeItem('fcm_token');
        console.log('✅ FCM token cleared from local storage');
      }
    } catch (error) {
      console.error('Error deleting FCM token:', error);
    }
  }
}

// Export singleton instance
const fcmService = new FCMService();
export default fcmService;
