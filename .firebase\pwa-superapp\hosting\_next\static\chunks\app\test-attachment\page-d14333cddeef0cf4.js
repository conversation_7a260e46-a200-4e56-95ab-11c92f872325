(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3859],{26957:(e,t,s)=>{"use strict";s.d(t,{AM:()=>l,Dp:()=>c,F4:()=>O,FI:()=>C,H$:()=>a,J9:()=>h,Jo:()=>x,K9:()=>o,M6:()=>p,MO:()=>m,OT:()=>_,P4:()=>j,UR:()=>U,WD:()=>A,WH:()=>y,WU:()=>k,_i:()=>i,bW:()=>w,dG:()=>r,dm:()=>S,iJ:()=>T,mh:()=>R,oo:()=>u,pZ:()=>g,u3:()=>d,x2:()=>v,xE:()=>n,xo:()=>N,yo:()=>f,zP:()=>b});let a="https://client-api.acuizen.com",r=a+"/login-configs",n=a+"/services",i=e=>a+"/files/"+e+"/presigned-url",l=a+"/users/me",o=a+"/dynamic-titles",d=a+"/users/get_users",c=a+"/files",m=a+"/observation-reports",p=a+"/my-observation-reports",u=a+"/dropdowns",h=a+"/get-blob",b=a+"/permit-reports",x=a+"/users",f=a+"/toolbox-talks",g=a+"/my-toolbox-talks",y=e=>a+"/my-assigned-actions/"+e,v=e=>a+"/inspection-checklist-submit/"+e,j=e=>a+"/observation-reports/"+e,w=e=>a+"/inspection-task-submit/"+e,N=e=>a+"/inspections/"+e,k=e=>a+"/permit-report-submit/"+e,A=e=>a+"/permit-reports-acknowledge/"+e,U=e=>a+"/permit-reports-update-status/"+e,C=e=>a+"/observation-action-submit/"+e,S=a+"/risk-assessments",T=e=>a+"/risk-assessments/"+e,O=e=>a+"/ra-team-member-submit-signature/"+e,_=a+"/permit-reports",R=e=>a+"/permit-reports/"+e},29300:(e,t)=>{var s;!function(){"use strict";var a={}.hasOwnProperty;function r(){for(var e="",t=0;t<arguments.length;t++){var s=arguments[t];s&&(e=n(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return r.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var s in e)a.call(e,s)&&e[s]&&(t=n(t,s));return t}(s)))}return e}function n(e,t){return t?e?e+" "+t:e+t:e}e.exports?(r.default=r,e.exports=r):void 0===(s=(function(){return r}).apply(t,[]))||(e.exports=s)}()},34761:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(95155),r=s(12115),n=s(97726);let i=()=>{let[e,t]=(0,r.useState)([]);return(0,a.jsxs)("div",{className:"container py-5",children:[(0,a.jsx)("h1",{className:"mb-4",children:"Attachment Uploader Test"}),(0,a.jsxs)("div",{className:"card mb-4",children:[(0,a.jsx)("div",{className:"card-header bg-primary text-white",children:(0,a.jsx)("h5",{className:"mb-0",children:"Images: Camera + Gallery"})}),(0,a.jsx)("div",{className:"card-body",children:(0,a.jsx)(n.A,{attachmentConfig:{image:{enabled:!0,galleryUploads:!0},video:{enabled:!1,galleryUploads:!1},documents:{enabled:!1}},onFilesSelected:t,files:e,label:"Take photos or upload from gallery"})})]}),(0,a.jsxs)("div",{className:"card mb-4",children:[(0,a.jsx)("div",{className:"card-header bg-secondary text-white",children:(0,a.jsx)("h5",{className:"mb-0",children:"Images: Camera Only"})}),(0,a.jsx)("div",{className:"card-body",children:(0,a.jsx)(n.A,{attachmentConfig:{image:{enabled:!0,galleryUploads:!1},video:{enabled:!1,galleryUploads:!1},documents:{enabled:!1}},onFilesSelected:t,files:e,label:"Take photos only (no gallery)"})})]}),(0,a.jsxs)("div",{className:"card mb-4",children:[(0,a.jsx)("div",{className:"card-header bg-info text-white",children:(0,a.jsx)("h5",{className:"mb-0",children:"Images: Gallery Only"})}),(0,a.jsx)("div",{className:"card-body",children:(0,a.jsx)(n.A,{attachmentConfig:{image:{enabled:!1,galleryUploads:!0},video:{enabled:!1,galleryUploads:!1},documents:{enabled:!1}},onFilesSelected:t,files:e,label:"Upload images from gallery only"})})]}),(0,a.jsxs)("div",{className:"card mb-4",children:[(0,a.jsx)("div",{className:"card-header bg-success text-white",children:(0,a.jsx)("h5",{className:"mb-0",children:"Documents Only"})}),(0,a.jsx)("div",{className:"card-body",children:(0,a.jsx)(n.A,{attachmentConfig:{image:{enabled:!1,galleryUploads:!1},video:{enabled:!1,galleryUploads:!1},documents:{enabled:!0}},onFilesSelected:t,files:e,label:"Upload Documents Only"})})]}),(0,a.jsxs)("div",{className:"card mb-4",children:[(0,a.jsx)("div",{className:"card-header bg-warning text-dark",children:(0,a.jsx)("h5",{className:"mb-0",children:"Video: Camera + Gallery"})}),(0,a.jsx)("div",{className:"card-body",children:(0,a.jsx)(n.A,{attachmentConfig:{image:{enabled:!1,galleryUploads:!1},video:{enabled:!0,galleryUploads:!0},documents:{enabled:!1}},onFilesSelected:t,files:e,label:"Record videos or upload from gallery"})})]}),(0,a.jsxs)("div",{className:"card mb-4",children:[(0,a.jsx)("div",{className:"card-header bg-dark text-white",children:(0,a.jsx)("h5",{className:"mb-0",children:"All Types Allowed"})}),(0,a.jsx)("div",{className:"card-body",children:(0,a.jsx)(n.A,{attachmentConfig:{image:{enabled:!0,galleryUploads:!0},video:{enabled:!0,galleryUploads:!0},documents:{enabled:!0}},onFilesSelected:t,files:e,label:"All options available"})})]}),(0,a.jsxs)("div",{className:"card mb-4",children:[(0,a.jsx)("div",{className:"card-header bg-warning text-dark",children:(0,a.jsx)("h5",{className:"mb-0",children:"No Types Allowed"})}),(0,a.jsx)("div",{className:"card-body",children:(0,a.jsx)(n.A,{attachmentConfig:{image:{enabled:!1,galleryUploads:!1},video:{enabled:!1,galleryUploads:!1},documents:{enabled:!1}},onFilesSelected:t,files:e,label:"No Uploads Allowed"})})]})]})}},38336:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(96078),r=s(26957);let n=a.A.create({baseURL:r.H$,headers:{Accept:"application/json","Content-Type":"application/json"},mode:"no-cors"});n.interceptors.request.use(async e=>(e.metadata={requestTime:Date.now()},e),e=>Promise.reject(e)),n.interceptors.response.use(e=>{var t;return e.headers["x-request-time"]=null==(t=e.config.metadata)?void 0:t.requestTime,e},async e=>{let{offlineQueue:t}=await s.e(8836).then(s.bind(s,48836)),{offlineStorage:a}=await s.e(58).then(s.bind(s,60058));if(t.shouldQueue(e)){var r,n,i,l;let s=e.config;if(await t.addRequest(s.url,(null==(r=s.method)?void 0:r.toUpperCase())||"GET",s.data,s.headers),(null==(n=s.method)?void 0:n.toLowerCase())==="get")try{if(null==(i=s.url)?void 0:i.includes("/services")){let e=await a.getServices();if(e.length>0)return console.log("\uD83D\uDCF1 Serving services from offline cache"),{data:e,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:s}}if(null==(l=s.url)?void 0:l.includes("assigned-actions")){let e=new URLSearchParams(s.url.split("?")[1]).get("filter"),t="All";if(e)try{JSON.parse(decodeURIComponent(e))}catch(r){let e=s.url.split("/"),a=e.findIndex(e=>"assigned-actions"===e);-1!==a&&e[a+1]&&(t=e[a+1])}let r=await a.getActions(t);if(r.length>0)return console.log("\uD83D\uDCF1 Serving actions from offline cache"),{data:r,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:s}}}catch(e){console.error("❌ Error serving from cache:",e)}}return Promise.reject(e)});let i=n},38637:(e,t,s)=>{e.exports=s(79399)()},43864:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var a=s(95155),r=s(12115),n=s(26957),i=s(38336);let l=async e=>{try{return(await i.A.get((0,n._i)(e),{headers:{"Content-Type":"application/json"}})).data}catch(e){return console.error("Failed to fetch image URL:",e),null}},o=async e=>{try{let t=(await i.A.post(n.J9,{presignedUrl:e},{responseType:"blob"})).data;return new Promise((e,s)=>{let a=new FileReader;a.onloadend=()=>e(a.result),a.onerror=s,a.readAsDataURL(t)})}catch(e){throw console.error("Error fetching Data URL:",e),e}};var d=s(11518),c=s.n(d),m=s(36209);s(58561);var p=s(4178);let u=e=>{let{imageSrc:t}=e,[s,n]=(0,r.useState)(!1);return(0,a.jsxs)("div",{className:"jsx-15b99a83659358da container",children:[(0,a.jsx)("div",{className:"jsx-15b99a83659358da card",children:(0,a.jsxs)("div",{className:"jsx-15b99a83659358da body-blue text-center",children:[(0,a.jsx)("img",{src:t,alt:"Displayed",onClick:e=>{e.preventDefault(),e.stopPropagation(),n(!0)},style:{cursor:"pointer"},className:"jsx-15b99a83659358da display-image"}),s&&(0,a.jsx)(m.Ay,{open:s,close:()=>n(!1),slides:[{src:t}],plugins:[p.A],carousel:{finite:!0}})]})}),(0,a.jsx)(c(),{id:"15b99a83659358da",children:".display-image.jsx-15b99a83659358da{max-width:80px;max-height:80px;width:auto;height:auto;-o-object-fit:cover;object-fit:cover;cursor:pointer!important;-webkit-transition:-webkit-transform.3s ease-in-out;-moz-transition:-moz-transform.3s ease-in-out;-o-transition:-o-transform.3s ease-in-out;transition:-webkit-transform.3s ease-in-out;transition:-moz-transform.3s ease-in-out;transition:-o-transform.3s ease-in-out;transition:transform.3s ease-in-out;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;border:1px solid#dee2e6;pointer-events:auto;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.display-image.jsx-15b99a83659358da:hover{-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-ms-transform:scale(1.05);-o-transform:scale(1.05);transform:scale(1.05);-webkit-box-shadow:0 2px 8px rgba(0,0,0,.15);-moz-box-shadow:0 2px 8px rgba(0,0,0,.15);box-shadow:0 2px 8px rgba(0,0,0,.15)}.container.jsx-15b99a83659358da{padding:0;margin:0;max-width:none;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.card.jsx-15b99a83659358da{border:none;background:none;margin:0;padding:0;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.body-blue.jsx-15b99a83659358da{padding:0;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}"})]})},h=e=>{let{fileName:t,size:s=100,name:n=!1}=e,[i,d]=(0,r.useState)(null),[c,m]=(0,r.useState)(null);if((0,r.useEffect)(()=>{(async()=>{try{var e;let s=await l(t);d(s);let a=null==(e=t.split(".").pop())?void 0:e.toLowerCase();if(a&&["jpg","jpeg","png","gif","bmp","webp"].includes(a)){let e=await o(s);m(e)}}catch(e){console.error("Error fetching file or data URL:",e)}})()},[t]),!i)return(0,a.jsx)("p",{children:"Loading..."});let p=(e=>{var t;let s=null==(t=e.split(".").pop())?void 0:t.toLowerCase();return s?["jpg","jpeg","png","gif","bmp","webp"].includes(s)?"image":["pdf"].includes(s)?"pdf":["xls","xlsx"].includes(s)?"xls":"other":"other"})(t),h=t.replace(/^\d+[\s-_]*/,"");switch(p){case"image":return(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center",style:{padding:"4px"},children:[c?(0,a.jsx)(u,{imageSrc:c}):(0,a.jsx)("div",{className:"d-flex align-items-center justify-content-center bg-light border rounded",style:{width:s,height:s},children:(0,a.jsx)("div",{className:"spinner-border spinner-border-sm text-primary",role:"status",children:(0,a.jsx)("span",{className:"visually-hidden",children:"Loading..."})})}),n&&(0,a.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center",fontSize:"10px",margin:"2px 0 0 0"},children:h})]});case"pdf":return(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center p-2",children:[(0,a.jsx)("a",{href:i,target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)("i",{className:"bi bi-file-pdf-fill fs-1 text-danger"})}),n&&(0,a.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center"},children:h})]});case"xls":return(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center p-2",children:[(0,a.jsx)("a",{href:i,target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)("i",{className:"bi bi-file-excel-fill fs-1 text-success"})}),n&&(0,a.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center"},children:h})]});default:return(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center p-2",children:[(0,a.jsx)("a",{href:i,target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)("i",{className:"bi bi-file-earmark-fill fs-1 text-secondary"})}),n&&(0,a.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center"},children:h})]})}}},72948:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},79399:(e,t,s)=>{"use strict";var a=s(72948);function r(){}function n(){}n.resetWarningCache=r,e.exports=function(){function e(e,t,s,r,n,i){if(i!==a){var l=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function t(){return e}e.isRequired=e;var s={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:n,resetWarningCache:r};return s.PropTypes=s,s}},84052:(e,t,s)=>{Promise.resolve().then(s.bind(s,34761))},97390:(e,t,s)=>{"use strict";s.d(t,{Jm:()=>d,Wz:()=>c,gy:()=>o,oU:()=>l});var a=s(12115);s(95155);let r=a.createContext({prefixes:{},breakpoints:["xxl","xl","lg","md","sm","xs"],minBreakpoint:"xs"}),{Consumer:n,Provider:i}=r;function l(e,t){let{prefixes:s}=(0,a.useContext)(r);return e||s[t]||t}function o(){let{breakpoints:e}=(0,a.useContext)(r);return e}function d(){let{minBreakpoint:e}=(0,a.useContext)(r);return e}function c(){let{dir:e}=(0,a.useContext)(r);return"rtl"===e}}},e=>{var t=t=>e(e.s=t);e.O(0,[3496,6078,1205,6639,9697,7726,8441,1684,7358],()=>t(84052)),_N_E=e.O()}]);