import nextPWA from "next-pwa";

const isDev = process.env.NODE_ENV === "development";

const nextConfig = nextPWA({
  dest: "public",
  register: true,
  skipWaiting: true,
  disable: isDev, // Disable PWA in development
});

export default {
  ...nextConfig,
  reactStrictMode: false,
  // Enable static export for Firebase deployment
  output: "export",
  trailingSlash: true,
  images: {
    unoptimized: true,
  },
};
