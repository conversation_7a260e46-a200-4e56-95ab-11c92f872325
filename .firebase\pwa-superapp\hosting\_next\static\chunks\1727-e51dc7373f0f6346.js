"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1727],{9e4:(e,s,a)=>{a.d(s,{D:()=>l});var i=a(12115);let l=()=>{let[e,s]=(0,i.useState)("light"),[a,l]=(0,i.useState)(!1);(0,i.useEffect)(()=>{s(localStorage.getItem("theme")||"light"),l(!0)},[]),(0,i.useEffect)(()=>{a&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,a]);let c=(0,i.useCallback)(()=>{s(e=>"dark"===e?"light":"dark")},[]),r=(0,i.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let s=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(s),e.style.display="none")},20)},1e3))}c()},[c]);return{theme:e,toggleTheme:c,handleDarkModeToggle:r}}},91727:(e,s,a)=>{a.d(s,{default:()=>n});var i=a(95155);a(12115);var l=a(6874),c=a.n(l),r=a(9e4);let n=e=>{let{links:s,title:l}=e;a(81531);let{theme:n,handleDarkModeToggle:t}=(0,r.D)();return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"header-area",id:"headerArea",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"header-content header-style-five position-relative d-flex align-items-center justify-content-between",children:[(0,i.jsx)("div",{className:"back-button",children:(0,i.jsx)(c(),{href:"/".concat(s),children:(0,i.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,i.jsx)("div",{className:"page-heading",children:(0,i.jsx)("h6",{className:"mb-0",children:l})}),(0,i.jsxs)("div",{className:"navbar--toggler",id:"affanNavbarToggler","data-bs-toggle":"offcanvas","data-bs-target":"#affanOffcanvas","aria-controls":"affanOffcanvas",children:[(0,i.jsx)("span",{className:"d-block"}),(0,i.jsx)("span",{className:"d-block"}),(0,i.jsx)("span",{className:"d-block"})]})]})})}),(0,i.jsxs)("div",{className:"offcanvas offcanvas-start",id:"affanOffcanvas","data-bs-scroll":"true",tabIndex:-1,"aria-labelledby":"affanOffcanvsLabel",children:[(0,i.jsx)("button",{className:"btn-close btn-close-white text-reset",type:"button","data-bs-dismiss":"offcanvas","aria-label":"Close"}),(0,i.jsx)("div",{className:"offcanvas-body p-0",children:(0,i.jsxs)("div",{className:"sidenav-wrapper",children:[(0,i.jsxs)("div",{className:"sidenav-profile bg-gradient",children:[(0,i.jsx)("div",{className:"sidenav-style1"}),(0,i.jsx)("div",{className:"user-profile",children:(0,i.jsx)("img",{src:"/assets/img/bg-img/2.jpg",alt:""})}),(0,i.jsxs)("div",{className:"user-info",children:[(0,i.jsx)("h6",{className:"user-name mb-0",children:"Affan Islam"}),(0,i.jsx)("span",{children:"CEO, Designing World"})]})]}),(0,i.jsxs)("ul",{className:"sidenav-nav ps-0",children:[(0,i.jsx)("li",{children:(0,i.jsxs)(c(),{href:"/home",children:[(0,i.jsx)("i",{className:"bi bi-house-door"})," Home"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)(c(),{href:"/elements",children:[(0,i.jsx)("i",{className:"bi bi-folder2-open"})," Elements",(0,i.jsx)("span",{className:"badge bg-danger rounded-pill ms-2",children:"220+"})]})}),(0,i.jsx)("li",{children:(0,i.jsxs)(c(),{href:"/pages",children:[(0,i.jsx)("i",{className:"bi bi-collection"})," Pages",(0,i.jsx)("span",{className:"badge bg-success rounded-pill ms-2",children:"100+"})]})}),(0,i.jsxs)("li",{children:[(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-cart-check"})," Shop"]}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:(0,i.jsx)(c(),{href:"/shop-grid",children:" Shop Grid"})}),(0,i.jsx)("li",{children:(0,i.jsx)(c(),{href:"/shop-list",children:" Shop List"})}),(0,i.jsx)("li",{children:(0,i.jsx)(c(),{href:"/shop-details",children:" Shop Details"})}),(0,i.jsx)("li",{children:(0,i.jsx)(c(),{href:"/cart",children:" Cart"})}),(0,i.jsx)("li",{children:(0,i.jsx)(c(),{href:"/checkout",children:" Checkout"})})]})]}),(0,i.jsx)("li",{children:(0,i.jsxs)(c(),{href:"/settings",children:[(0,i.jsx)("i",{className:"bi bi-gear"})," Settings"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("div",{className:"night-mode-nav",children:[(0,i.jsx)("i",{className:"bi bi-moon"}),"dark"===n?"Light":"Dark"," Mode",(0,i.jsx)("div",{className:"form-check form-switch",children:(0,i.jsx)("input",{className:"form-check-input form-check-success",id:"darkSwitch",type:"checkbox",checked:"dark"===n,onChange:t})})]})}),(0,i.jsx)("li",{children:(0,i.jsxs)(c(),{href:"/login",children:[(0,i.jsx)("i",{className:"bi bi-box-arrow-right"})," Logout"]})})]}),(0,i.jsxs)("div",{className:"social-info-wrap",children:[(0,i.jsx)("a",{href:"#",children:(0,i.jsx)("i",{className:"bi bi-facebook"})}),(0,i.jsx)("a",{href:"#",children:(0,i.jsx)("i",{className:"bi bi-twitter"})}),(0,i.jsx)("a",{href:"#",children:(0,i.jsx)("i",{className:"bi bi-linkedin"})})]}),(0,i.jsx)("div",{className:"copyright-info",children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{id:"copyrightYear"}),"c@ Made by ",(0,i.jsx)("a",{href:"#",children:"Designing World"})]})})]})})]})]})}}}]);