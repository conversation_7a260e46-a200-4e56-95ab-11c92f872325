(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3984],{36065:(e,s,a)=>{"use strict";a.d(s,{default:()=>l});var i=a(95155);a(12115);let l=()=>(a(40844),(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("div",{className:"page-content-wrapper py-3",children:[(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("div",{className:"element-heading",children:(0,i.jsx)("h6",{children:"Price Table 01"})})}),(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("div",{className:"card",children:(0,i.jsx)("div",{className:"card-body",children:(0,i.jsxs)("div",{className:"price-table-one",children:[(0,i.jsxs)("ul",{className:"nav nav-tabs border-bottom-0 mb-3 align-items-center justify-content-center",id:"priceTab",role:"tablist",children:[(0,i.jsx)("li",{className:"nav-item",role:"presentation",children:(0,i.jsx)("a",{className:"nav-link shadow",id:"priceTabOne","data-bs-toggle":"tab",href:"#priceTab_One",role:"tab","aria-controls":"priceTab_One","aria-selected":"false",children:(0,i.jsx)("i",{className:"bi bi-egg"})})}),(0,i.jsx)("li",{className:"nav-item",role:"presentation",children:(0,i.jsx)("a",{className:"nav-link active shadow",id:"priceTabTwo","data-bs-toggle":"tab",href:"#priceTab_Two",role:"tab","aria-controls":"priceTab_Two","aria-selected":"true",children:(0,i.jsx)("i",{className:"bi bi-lightning"})})}),(0,i.jsx)("li",{className:"nav-item",role:"presentation",children:(0,i.jsx)("a",{className:"nav-link shadow",id:"priceTabThree","data-bs-toggle":"tab",href:"#priceTab_Three",role:"tab","aria-controls":"priceTab_Three","aria-selected":"false",children:(0,i.jsx)("i",{className:"bi bi-cpu"})})})]}),(0,i.jsxs)("div",{className:"tab-content",id:"priceTabContent",children:[(0,i.jsx)("div",{className:"tab-pane fade",id:"priceTab_One",role:"tabpanel","aria-labelledby":"priceTabOne",children:(0,i.jsxs)("div",{className:"single-price-content shadow-sm",children:[(0,i.jsxs)("div",{className:"price",children:[(0,i.jsx)("span",{className:"text-white mb-2",children:"Intro"}),(0,i.jsx)("h2",{className:"display-3",children:"$3.19"}),(0,i.jsx)("span",{className:"badge bg-light text-dark rounded-pill",children:"Save -29%"})]}),(0,i.jsx)("div",{className:"pricing-desc",children:(0,i.jsxs)("ul",{className:"ps-0",children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("i",{className:"bi bi-check-lg me-2"}),"3 Month Usage"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("i",{className:"bi bi-check-lg me-2"}),"Lifetime Updates"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("i",{className:"bi bi-check-lg me-2"}),"2 Website License"]}),(0,i.jsxs)("li",{className:"times",children:[(0,i.jsx)("i",{className:"bi bi-x-lg me-2"}),"Free Support"]}),(0,i.jsxs)("li",{className:"times",children:[(0,i.jsx)("i",{className:"bi bi-x-lg me-2"}),"Download New Release"]})]})}),(0,i.jsxs)("div",{className:"purchase",children:[(0,i.jsx)("a",{className:"btn btn-warning btn-lg btn-creative w-100",href:"#",children:"Choose Plan"}),(0,i.jsx)("small",{className:"d-block text-white mt-2 ms-1",children:"No credit card required*"})]})]})}),(0,i.jsx)("div",{className:"tab-pane fade show active",id:"priceTab_Two",role:"tabpanel","aria-labelledby":"priceTabTwo",children:(0,i.jsxs)("div",{className:"single-price-content shadow-sm",children:[(0,i.jsxs)("div",{className:"price",children:[(0,i.jsx)("span",{className:"text-white mb-2",children:"Popular"}),(0,i.jsx)("h2",{className:"display-3",children:"$19.19"}),(0,i.jsx)("span",{className:"badge bg-light text-dark rounded-pill",children:"Save -29%"})]}),(0,i.jsx)("div",{className:"pricing-desc",children:(0,i.jsxs)("ul",{className:"ps-0",children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("i",{className:"bi bi-check-lg me-2"}),"6 Month Usage"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("i",{className:"bi bi-check-lg me-2"}),"Lifetime Updates"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("i",{className:"bi bi-check-lg me-2"}),"10 Website License"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("i",{className:"bi bi-check-lg me-2"}),"Free Support"]}),(0,i.jsxs)("li",{className:"times",children:[(0,i.jsx)("i",{className:"bi bi-x-lg me-2"}),"Download New Release"]})]})}),(0,i.jsx)("div",{className:"purchase",children:(0,i.jsx)("a",{className:"btn btn-light btn-lg btn-creative",href:"#",children:"Choose Plan"})})]})}),(0,i.jsx)("div",{className:"tab-pane fade",id:"priceTab_Three",role:"tabpanel","aria-labelledby":"priceTabThree",children:(0,i.jsxs)("div",{className:"single-price-content shadow-sm",children:[(0,i.jsxs)("div",{className:"price",children:[(0,i.jsx)("span",{className:"text-white mb-2",children:"Pro"}),(0,i.jsx)("h2",{className:"display-3",children:"$49.99"}),(0,i.jsx)("span",{className:"badge bg-light text-dark rounded-pill",children:"Save -29%"})]}),(0,i.jsx)("div",{className:"pricing-desc",children:(0,i.jsxs)("ul",{className:"ps-0",children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("i",{className:"bi bi-check-lg me-2"}),"12 Month Usage"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("i",{className:"bi bi-check-lg me-2"}),"Lifetime Updates"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("i",{className:"bi bi-check-lg me-2"}),"Unlimited Website License"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("i",{className:"bi bi-check-lg me-2"}),"Free Support"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("i",{className:"bi bi-check-lg me-2"}),"Download New Release"]})]})}),(0,i.jsx)("div",{className:"purchase",children:(0,i.jsx)("a",{className:"btn btn-primary btn-lg btn-creative",href:"#",children:"Choose Plan"})})]})})]})]})})})}),(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("div",{className:"element-heading mt-3",children:(0,i.jsx)("h6",{children:"Price Table 02"})})}),(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("div",{className:"card",children:(0,i.jsx)("div",{className:"card-body",children:(0,i.jsxs)("div",{className:"price-table-two d-flex align-items-center",children:[(0,i.jsxs)("div",{className:"single-price-table active-effect",children:[(0,i.jsxs)("div",{className:"text",children:[(0,i.jsx)("h6",{className:"fz-14",children:"Basic"}),(0,i.jsx)("span",{className:"badge bg-primary rounded-pill",children:"Save 7%"})]}),(0,i.jsxs)("div",{className:"price",children:[(0,i.jsx)("h3",{children:"$9"}),(0,i.jsx)("span",{className:"fz-12",style:{color:"#8480AE"},children:"per month"})]}),(0,i.jsx)("div",{className:"purchase",children:(0,i.jsxs)("div",{className:"form-check mb-0",children:[(0,i.jsx)("input",{className:"form-check-input form-check-warning mx-0 shadow",type:"radio",name:"exampleRadio",id:"choosePlan1"}),(0,i.jsx)("label",{className:"form-check-label",htmlFor:"choosePlan1"})]})})]}),(0,i.jsxs)("div",{className:"single-price-table active-effect active",children:[(0,i.jsxs)("div",{className:"text",children:[(0,i.jsx)("h6",{className:"fz-14",children:"Standard"}),(0,i.jsx)("span",{className:"badge bg-primary rounded-pill",children:"Save 16%"})]}),(0,i.jsxs)("div",{className:"price",children:[(0,i.jsx)("h3",{children:"$59"}),(0,i.jsx)("span",{className:"fz-12",children:"per month"})]}),(0,i.jsx)("div",{className:"purchase",children:(0,i.jsxs)("div",{className:"form-check",children:[(0,i.jsx)("input",{className:"form-check-input form-check-warning mx-0 shadow",type:"radio",name:"exampleRadio",id:"choosePlan2",defaultChecked:!0}),(0,i.jsx)("label",{className:"form-check-label",htmlFor:"choosePlan2"})]})})]}),(0,i.jsxs)("div",{className:"single-price-table active-effect",children:[(0,i.jsxs)("div",{className:"text",children:[(0,i.jsx)("h6",{className:"fz-14",children:"Premium"}),(0,i.jsx)("span",{className:"badge bg-primary rounded-pill",children:"Save 23%"})]}),(0,i.jsxs)("div",{className:"price",children:[(0,i.jsx)("h3",{children:"$99"}),(0,i.jsx)("span",{className:"fz-12",style:{color:"#8480AE"},children:"per month"})]}),(0,i.jsx)("div",{className:"purchase",children:(0,i.jsxs)("div",{className:"form-check",children:[(0,i.jsx)("input",{className:"form-check-input form-check-warning mx-0 shadow",type:"radio",name:"exampleRadio",id:"choosePlan3"}),(0,i.jsx)("label",{className:"form-check-label",htmlFor:"choosePlan3"})]})})]})]})})})})]})}))},73067:(e,s,a)=>{Promise.resolve().then(a.bind(a,36065)),Promise.resolve().then(a.bind(a,38983)),Promise.resolve().then(a.bind(a,21217))}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,2703,3008,8441,1684,7358],()=>s(73067)),_N_E=e.O()}]);