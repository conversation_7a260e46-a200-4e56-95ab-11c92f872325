"use client";

import React, { useState, useEffect } from 'react';
import { Contain<PERSON>, <PERSON>, <PERSON>, Card, Button, Alert, Badge, Table } from 'react-bootstrap';
import EnhancedNotificationSetup from './EnhancedNotificationSetup';
import unifiedNotificationService from '@/services/unifiedNotificationService';
import { notificationTester, TestResult } from '@/utils/notificationTestUtils';

const NotificationTestPage: React.FC = () => {
  const [deviceInfo, setDeviceInfo] = useState<any>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);

  useEffect(() => {
    initializeAndGetInfo();
  }, []);

  const initializeAndGetInfo = async () => {
    try {
      await unifiedNotificationService.initialize();
      const info = unifiedNotificationService.getDeviceInfo();
      setDeviceInfo(info);
      addLog(`Device detected: ${JSON.stringify(info, null, 2)}`);
    } catch (error) {
      addLog(`Error initializing: ${error}`);
    }
  };

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)]);
  };

  const handleTestNotification = async () => {
    try {
      addLog('Testing notification...');
      const success = await unifiedNotificationService.showTestNotification();
      addLog(`Test notification result: ${success ? 'Success' : 'Failed'}`);
    } catch (error) {
      addLog(`Test notification error: ${error}`);
    }
  };

  const handleCustomNotification = async () => {
    try {
      addLog('Showing custom notification...');
      const success = await unifiedNotificationService.showLocalNotification({
        title: 'Custom Notification',
        body: 'This is a custom notification with data',
        tag: 'custom-test',
        data: { customData: 'test-value' },
        url: '/notifications'
      });
      addLog(`Custom notification result: ${success ? 'Success' : 'Failed'}`);
    } catch (error) {
      addLog(`Custom notification error: ${error}`);
    }
  };

  const handleCheckPermission = () => {
    const permission = unifiedNotificationService.getPermissionStatus();
    const status = unifiedNotificationService.getStatusMessage();
    addLog(`Permission status: ${permission}`);
    addLog(`Status message: ${status}`);
  };

  const handleGetInstructions = () => {
    const instructions = unifiedNotificationService.getSetupInstructions();
    addLog('Setup instructions:');
    instructions.forEach((instruction, index) => {
      addLog(`  ${index + 1}. ${instruction}`);
    });
  };

  const handleRunAllTests = async () => {
    setIsRunningTests(true);
    addLog('🧪 Starting comprehensive notification tests...');

    try {
      const results = await notificationTester.runAllTests();
      setTestResults(results);

      addLog(`✅ Tests completed: ${results.filter(r => r.success).length}/${results.length} passed`);
      results.forEach(result => {
        addLog(`${result.success ? '✅' : '❌'} ${result.test}: ${result.message}`);
      });
    } catch (error) {
      addLog(`❌ Test error: ${error}`);
    } finally {
      setIsRunningTests(false);
    }
  };

  const handleTestPermission = async () => {
    addLog('🔔 Testing permission request...');
    try {
      const result = await notificationTester.testPermissionRequest();
      addLog(`${result.success ? '✅' : '❌'} ${result.test}: ${result.message}`);
    } catch (error) {
      addLog(`❌ Permission test error: ${error}`);
    }
  };

  const handleTestRegistration = async () => {
    addLog('📝 Testing notification registration...');
    try {
      const result = await notificationTester.testNotificationRegistration();
      addLog(`${result.success ? '✅' : '❌'} ${result.test}: ${result.message}`);
    } catch (error) {
      addLog(`❌ Registration test error: ${error}`);
    }
  };

  const clearLogs = () => {
    setLogs([]);
    setTestResults([]);
  };

  const getDeviceIcon = () => {
    if (!deviceInfo) return '📱';
    if (deviceInfo.isIOS) return '🍎';
    if (deviceInfo.isAndroid) return '🤖';
    return '💻';
  };

  const getMethodColor = () => {
    if (!deviceInfo) return 'secondary';
    switch (deviceInfo.recommendedMethod) {
      case 'fcm': return 'success';
      case 'ios-web-push': return 'info';
      case 'local-only': return 'warning';
      case 'unsupported': return 'danger';
      default: return 'secondary';
    }
  };

  return (
    <Container className="py-4">
      <Row>
        <Col md={8} className="mx-auto">
          <h2 className="text-center mb-4">
            {getDeviceIcon()} Notification Test Page
          </h2>

          {/* Device Information */}
          {deviceInfo && (
            <Card className="mb-4">
              <Card.Header>
                <h5 className="mb-0">Device Information</h5>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col sm={6}>
                    <strong>Platform:</strong> {deviceInfo.isIOS ? 'iOS' : deviceInfo.isAndroid ? 'Android' : 'Desktop'}
                  </Col>
                  <Col sm={6}>
                    <strong>Browser:</strong> {deviceInfo.browser}
                  </Col>
                  <Col sm={6}>
                    <strong>PWA Mode:</strong> {deviceInfo.isIOSPWA ? 'Yes' : 'No'}
                  </Col>
                  <Col sm={6}>
                    <strong>Web Push Support:</strong> {deviceInfo.supportsWebPush ? 'Yes' : 'No'}
                  </Col>
                  <Col sm={12} className="mt-2">
                    <strong>Recommended Method:</strong>{' '}
                    <Badge bg={getMethodColor()}>
                      {deviceInfo.recommendedMethod.replace('-', ' ').toUpperCase()}
                    </Badge>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          )}

          {/* Enhanced Notification Setup */}
          <div className="mb-4">
            <EnhancedNotificationSetup 
              onTokenGenerated={(token) => addLog(`Token generated: ${token.substring(0, 50)}...`)}
              showTestButton={true}
              autoInitialize={false}
            />
          </div>

          {/* Test Controls */}
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">Test Controls</h5>
            </Card.Header>
            <Card.Body>
              <div className="d-flex flex-wrap gap-2 mb-3">
                <Button variant="primary" onClick={handleTestNotification}>
                  Test Notification
                </Button>
                <Button variant="secondary" onClick={handleCustomNotification}>
                  Custom Notification
                </Button>
                <Button variant="info" onClick={handleCheckPermission}>
                  Check Permission
                </Button>
                <Button variant="warning" onClick={handleGetInstructions}>
                  Get Instructions
                </Button>
              </div>

              <div className="d-flex flex-wrap gap-2 mb-3">
                <Button
                  variant="success"
                  onClick={handleRunAllTests}
                  disabled={isRunningTests}
                >
                  {isRunningTests ? 'Running Tests...' : 'Run All Tests'}
                </Button>
                <Button variant="outline-primary" onClick={handleTestPermission}>
                  Test Permission
                </Button>
                <Button variant="outline-secondary" onClick={handleTestRegistration}>
                  Test Registration
                </Button>
                <Button variant="outline-danger" onClick={clearLogs}>
                  Clear Logs
                </Button>
              </div>
            </Card.Body>
          </Card>

          {/* iOS Specific Guidance */}
          {deviceInfo?.isIOS && (
            <Alert variant="info" className="mb-4">
              <h6>
                <i className="bi bi-apple me-2"></i>
                iOS Specific Notes
              </h6>
              <ul className="mb-0">
                <li>Web push notifications require iOS 16.4 or later</li>
                <li>Must use Safari browser (not Chrome or others)</li>
                <li>For PWA: Add to home screen and open from there</li>
                <li>If notifications don&apos;t work, check Safari Settings → Websites → Notifications</li>
                {deviceInfo.isIOSPWA && (
                  <li><strong>PWA Mode Detected:</strong> You&apos;re running in PWA mode - great for notifications!</li>
                )}
              </ul>
            </Alert>
          )}

          {/* Test Results */}
          {testResults.length > 0 && (
            <Card className="mb-4">
              <Card.Header>
                <h5 className="mb-0">Test Results</h5>
              </Card.Header>
              <Card.Body>
                <Table striped bordered hover size="sm">
                  <thead>
                    <tr>
                      <th>Test</th>
                      <th>Status</th>
                      <th>Message</th>
                    </tr>
                  </thead>
                  <tbody>
                    {testResults.map((result, index) => (
                      <tr key={index}>
                        <td>{result.test}</td>
                        <td>
                          <Badge bg={result.success ? 'success' : 'danger'}>
                            {result.success ? '✅ Pass' : '❌ Fail'}
                          </Badge>
                        </td>
                        <td className="small">{result.message}</td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </Card.Body>
            </Card>
          )}

          {/* Logs */}
          <Card>
            <Card.Header className="d-flex justify-content-between align-items-center">
              <h5 className="mb-0">Debug Logs</h5>
              <Badge bg="secondary">{logs.length} entries</Badge>
            </Card.Header>
            <Card.Body>
              {logs.length === 0 ? (
                <p className="text-muted mb-0">No logs yet. Try testing some notifications!</p>
              ) : (
                <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                  {logs.map((log, index) => (
                    <div key={index} className="mb-1">
                      <code className="small text-break">{log}</code>
                    </div>
                  ))}
                </div>
              )}
            </Card.Body>
          </Card>

          {/* Instructions */}
          <Card className="mt-4">
            <Card.Header>
              <h5 className="mb-0">How to Test</h5>
            </Card.Header>
            <Card.Body>
              <ol>
                <li><strong>Enable Notifications:</strong> Click &quot;Enable Notifications&quot; above</li>
                <li><strong>Test Basic:</strong> Click &quot;Test Notification&quot; to see if it works</li>
                <li><strong>Test Custom:</strong> Click &quot;Custom Notification&quot; to test with custom data</li>
                <li><strong>Check Status:</strong> Use &quot;Check Permission&quot; to see current state</li>
                <li><strong>Debug:</strong> Watch the logs for detailed information</li>
              </ol>
              
              <Alert variant="warning" className="mt-3">
                <strong>Note:</strong> If you&apos;re testing on iOS and notifications don&apos;t work:
                <ul className="mb-0 mt-2">
                  <li>Make sure you&apos;re using Safari (not Chrome)</li>
                  <li>Check your iOS version (needs 16.4+)</li>
                  <li>Try adding the app to home screen first</li>
                  <li>Check Safari Settings → Websites → Notifications</li>
                </ul>
              </Alert>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default NotificationTestPage;
