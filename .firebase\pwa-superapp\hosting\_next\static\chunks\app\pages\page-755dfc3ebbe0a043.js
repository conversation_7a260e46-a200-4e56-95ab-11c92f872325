(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5253],{11518:(e,t,s)=>{"use strict";e.exports=s(82269).style},17451:(e,t,s)=>{"use strict";s.d(t,{default:()=>f});var n=s(95155),i=s(26957),r=s(9e4),o=s(6874),a=s.n(o),l=s(12115),c=s(38336),d=s(34540),u=s(81359),h=s(35695);let f=()=>{s(81531);let[e,t]=(0,l.useState)(""),[o,f]=(0,l.useState)(""),m=(0,d.wA)(),p=(0,h.useRouter)(),{theme:v,handleDarkModeToggle:_}=(0,r.D)();(0,l.useEffect)(()=>{g(),y()},[]);let g=async()=>{try{if(window.localStorage){let e=localStorage.getItem("logo");if(!e)return void console.warn("No logo key found in localStorage");let s=(await c.A.get((0,i._i)(e),{headers:{"Content-Type":"application/json"}})).data;t(s)}else console.warn("localStorage is not available")}catch(e){console.error("Error fetching logo:",e)}},y=async()=>{try{let e=await c.A.get(i.AM);200===e.status?(f(e.data.firstName),m(u.l.setUser(e.data))):p.push("/")}catch(e){console.log(e)}};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"header-area",id:"headerArea",children:(0,n.jsx)("div",{className:"container",children:(0,n.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,n.jsxs)("div",{className:"navbar--toggler",id:"affanNavbarToggler","data-bs-toggle":"offcanvas","data-bs-target":"#affanOffcanvas","aria-controls":"affanOffcanvas",children:[(0,n.jsx)("span",{className:"d-block"}),(0,n.jsx)("span",{className:"d-block"}),(0,n.jsx)("span",{className:"d-block"})]}),(0,n.jsxs)("div",{className:"logo-wrapper text-center",children:[(0,n.jsx)(a(),{href:"/home",children:(0,n.jsx)("img",{src:e,alt:""})}),(0,n.jsx)("div",{className:"element-heading",children:(0,n.jsx)("h6",{children:"AcuiZen WorkHub"})})]}),(0,n.jsx)("div",{className:"setting-wrapper",children:(0,n.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn2",children:[(0,n.jsx)("i",{className:"bi bi-bell"}),(0,n.jsx)("span",{})]})})]})})}),(0,n.jsxs)("div",{className:"offcanvas offcanvas-start",id:"affanOffcanvas","data-bs-scroll":"true",tabIndex:-1,"aria-labelledby":"affanOffcanvsLabel",children:[(0,n.jsx)("button",{className:"btn-close btn-close-white text-reset",type:"button","data-bs-dismiss":"offcanvas","aria-label":"Close"}),(0,n.jsx)("div",{className:"offcanvas-body p-0",children:(0,n.jsxs)("div",{className:"sidenav-wrapper",children:[(0,n.jsxs)("div",{className:"sidenav-profile bg-gradient",children:[(0,n.jsx)("div",{className:"sidenav-style1"}),(0,n.jsx)("div",{className:"user-profile"}),(0,n.jsx)("div",{className:"user-info",children:(0,n.jsx)("h6",{className:"user-name mb-0",children:o})})]}),(0,n.jsxs)("ul",{className:"sidenav-nav ps-0",children:[(0,n.jsx)("li",{children:(0,n.jsxs)(a(),{href:"/home",children:[(0,n.jsx)("i",{className:"bi bi-house-door"})," Home"]})}),(0,n.jsx)("li",{children:(0,n.jsxs)("div",{className:"night-mode-nav",children:[(0,n.jsx)("i",{className:"bi bi-moon"}),"dark"===v?"Light":"Dark"," Mode",(0,n.jsx)("div",{className:"form-check form-switch",children:(0,n.jsx)("input",{className:"form-check-input form-check-success",id:"darkSwitch",type:"checkbox",checked:"dark"===v,onChange:_})})]})}),(0,n.jsx)("li",{children:(0,n.jsxs)("button",{className:"btn w-100 text-start",onClick:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("COGNITO_USER_DOMAIN"),localStorage.removeItem("enterprise_id"),p.push("/")},style:{border:"none",background:"none",padding:"10px 22px "},children:[(0,n.jsx)("i",{className:"bi bi-box-arrow-right",style:{fontSize:"20px",paddingRight:10}})," Logout"]})})]}),(0,n.jsx)("div",{className:"copyright-info",children:(0,n.jsxs)("p",{children:[(0,n.jsx)("span",{id:"copyrightYear"}),new Date().getFullYear()," \xa9 Made by ",(0,n.jsx)("a",{target:"_blank",href:"https://www.acuizen.com",children:"AcuiZen"})]})})]})})]})]})}},35695:(e,t,s)=>{"use strict";var n=s(18999);s.o(n,"useRouter")&&s.d(t,{useRouter:function(){return n.useRouter}}),s.o(n,"useSearchParams")&&s.d(t,{useSearchParams:function(){return n.useSearchParams}})},38983:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var n=s(95155),i=s(6874),r=s.n(i);s(12115);let o=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],a=()=>(0,n.jsx)(n.Fragment,{children:(0,n.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,n.jsx)("div",{className:"container px-0",children:(0,n.jsx)("div",{className:"footer-nav position-relative",children:(0,n.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:o.map((e,t)=>(0,n.jsx)("li",{children:(0,n.jsxs)(r(),{href:"/".concat(e.link),children:[(0,n.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,n.jsx)("span",{children:e.title})]})},t))})})})})})},58742:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.bind(s,90371)),Promise.resolve().then(s.bind(s,38983)),Promise.resolve().then(s.bind(s,17451))},68375:()=>{},82269:(e,t,s)=>{"use strict";var n=s(49509);s(68375);var i=s(12115),r=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(i),o=void 0!==n&&n.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,s=t.name,n=void 0===s?"stylesheet":s,i=t.optimizeForSpeed,r=void 0===i?o:i;c(a(n),"`name` must be a string"),this._name=n,this._deletedRulePlaceholder="#"+n+"-deleted-rule____{}",c("boolean"==typeof r,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=r,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var l="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=l?l.getAttribute("content"):null}var t,s=e.prototype;return s.setOptimizeForSpeed=function(e){c("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),c(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},s.isOptimizeForSpeed=function(){return this._optimizeForSpeed},s.inject=function(){var e=this;if(c(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(o||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,s){return"number"==typeof s?e._serverSheet.cssRules[s]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),s},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},s.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},s.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},s.insertRule=function(e,t){if(c(a(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var s=this.getSheet();"number"!=typeof t&&(t=s.cssRules.length);try{s.insertRule(e,t)}catch(t){return o||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var n=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,n))}return this._rulesCount++},s.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var s="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!s.cssRules[e])return e;s.deleteRule(e);try{s.insertRule(t,e)}catch(n){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),s.insertRule(this._deletedRulePlaceholder,e)}}else{var n=this._tags[e];c(n,"old rule at index `"+e+"` not found"),n.textContent=t}return e},s.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];c(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},s.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},s.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,s){return s?t=t.concat(Array.prototype.map.call(e.getSheetForTag(s).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},s.makeStyleTag=function(e,t,s){t&&c(a(t),"makeStyleTag accepts only strings as second parameter");var n=document.createElement("style");this._nonce&&n.setAttribute("nonce",this._nonce),n.type="text/css",n.setAttribute("data-"+e,""),t&&n.appendChild(document.createTextNode(t));var i=document.head||document.getElementsByTagName("head")[0];return s?i.insertBefore(n,s):i.appendChild(n),n},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var s=0;s<t.length;s++){var n=t[s];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),e}();function c(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,s=e.length;s;)t=33*t^e.charCodeAt(--s);return t>>>0},u={};function h(e,t){if(!t)return"jsx-"+e;var s=String(t),n=e+s;return u[n]||(u[n]="jsx-"+d(e+"-"+s)),u[n]}function f(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var s=e+t;return u[s]||(u[s]=t.replace(/__jsx-style-dynamic-selector/g,e)),u[s]}var m=function(){function e(e){var t=void 0===e?{}:e,s=t.styleSheet,n=void 0===s?null:s,i=t.optimizeForSpeed,r=void 0!==i&&i;this._sheet=n||new l({name:"styled-jsx",optimizeForSpeed:r}),this._sheet.inject(),n&&"boolean"==typeof r&&(this._sheet.setOptimizeForSpeed(r),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var s=this.getIdAndRules(e),n=s.styleId,i=s.rules;if(n in this._instancesCounts){this._instancesCounts[n]+=1;return}var r=i.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[n]=r,this._instancesCounts[n]=1},t.remove=function(e){var t=this,s=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(s in this._instancesCounts,"styleId: `"+s+"` not found"),this._instancesCounts[s]-=1,this._instancesCounts[s]<1){var n=this._fromServer&&this._fromServer[s];n?(n.parentNode.removeChild(n),delete this._fromServer[s]):(this._indices[s].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[s]),delete this._instancesCounts[s]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],s=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return s[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,s;return t=this.cssRules(),void 0===(s=e)&&(s={}),t.map(function(e){var t=e[0],n=e[1];return r.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:s.nonce?s.nonce:void 0,dangerouslySetInnerHTML:{__html:n}})})},t.getIdAndRules=function(e){var t=e.children,s=e.dynamic,n=e.id;if(s){var i=h(n,s);return{styleId:i,rules:Array.isArray(t)?t.map(function(e){return f(i,e)}):[f(i,t)]}}return{styleId:h(n),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),p=i.createContext(null);p.displayName="StyleSheetContext";var v=r.default.useInsertionEffect||r.default.useLayoutEffect,_="undefined"!=typeof window?new m:void 0;function g(e){var t=_||i.useContext(p);return t&&("undefined"==typeof window?t.add(e):v(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}g.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=g},9e4:(e,t,s)=>{"use strict";s.d(t,{D:()=>i});var n=s(12115);let i=()=>{let[e,t]=(0,n.useState)("light"),[s,i]=(0,n.useState)(!1);(0,n.useEffect)(()=>{t(localStorage.getItem("theme")||"light"),i(!0)},[]),(0,n.useEffect)(()=>{s&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,s]);let r=(0,n.useCallback)(()=>{t(e=>"dark"===e?"light":"dark")},[]),o=(0,n.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}r()},[r]);return{theme:e,toggleTheme:r,handleDarkModeToggle:o}}},90371:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var n=s(95155),i=s(12115),r=s(79459),o=s(36651);let a=e=>{let{children:t,pageTitle:s="Page",requiresOnline:a=!1,customFallback:l,className:c=""}=e,[d,u]=(0,i.useState)(!0);if((0,i.useEffect)(()=>{u(navigator.onLine);let e=()=>u(!0),t=()=>u(!1);return window.addEventListener("online",e),window.addEventListener("offline",t),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",t)}},[]),a&&!d){let e=l||(0,n.jsx)(o.A,{title:"".concat(s," Unavailable Offline"),message:"".concat(s," requires an internet connection. Please check your connection and try again."),showHomeLink:!0});return(0,n.jsx)("div",{className:c,children:e})}return(0,n.jsx)(r.A,{className:c,showOfflineIndicator:!0,children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,6078,1955,635,1531,381,1434,8441,1684,7358],()=>t(58742)),_N_E=e.O()}]);