(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3126],{14832:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var a=s(95155),l=s(12115),n=s(23327),i=s(60902),o=s(56160),r=s(16344),d=s(38336),c=s(26957),m=s(46554),u=s(24752),h=s.n(u),g=s(35695),b=s(27347),x=s(24952),p=s(82940),f=s.n(p);let j={commenceDate:"",noOfPersonsParticipated:0,uploads:[],permitRiskControl:[],controls:{isAdditionalControlsIdentified:null,describeAdditionalControls:"",teamBrief:"",teamBriefRemarks:""},isCloseOutChallenges:null,closeOutChallenges:[{unexpectedChallenges:"",remarks:""}],toolboxSignStatuses:[{signedDate:"",sign:"",signedById:""}],locationOneId:"",locationTwoId:"",locationThreeId:"",locationFourId:"",locationFiveId:"",locationSixId:"",conductedById:"",riskAssessmentId:"",tasks:""};function N(){var e;let[t,s]=(0,l.useState)(j),[u,p]=(0,l.useState)([]),[N,v]=(0,l.useState)([]),[y,C]=(0,l.useState)({}),[S,w]=(0,l.useState)(!1),k=(0,g.useRouter)(),[A,O]=(0,l.useState)(null),[I,_]=(0,l.useState)([]),[B,P]=(0,l.useState)(!1),[D,R]=(0,l.useState)(null),Y=(0,l.useRef)(null),[E,F]=(0,l.useState)(!1),T=!!t.commenceDate,[z,W]=(0,l.useState)(!0);(0,l.useEffect)(()=>{(async()=>{W(!0),await Promise.all([q(),H()]),W(!1)})()},[]);let q=async()=>{try{let e={where:{$and:[{$or:[{status:"Published"}]},{$or:[{type:"Routine"},{type:"Non Routine"},{type:"High-Risk Hazard"}]}]},include:[{relation:"department"},{relation:"teamLeader"},{relation:"workActivity"},{relation:"raTeamMembers",scope:{include:[{relation:"user"}]}}]},t="".concat(c.dm,"?filter=").concat(encodeURIComponent(JSON.stringify(e))),s=await d.A.get(t);if(200===s.status){p(s.data);let e=s.data.map(e=>({label:"Routine"===e.type?e.workActivity.name:"Non Routine"===e.type?e.nonRoutineWorkActivity:e.hazardName,value:e.id}));v(e)}}catch(e){console.error(e)}},H=async()=>{try{let e=await d.A.get(c.Jo);if(200===e.status){let t=e.data.map(e=>({label:e.firstName,value:e.id}));_(t)}}catch(e){console.error("Error fetching crew list:",e)}},M=()=>{let e={};return t.commenceDate||(e.commenceDate="Commence Date is required."),t.riskAssessmentId||(e.riskAssessmentId="Work Activity is required."),t.conductedById||(e.conductedById="Conducted by is required."),t.locationOneId||(e.locationOneId="Location is required."),(!t.noOfPersonsParticipated||t.noOfPersonsParticipated<=0)&&(e.noOfPersonsParticipated="Number of Persons Participated must be greater than 0."),t.uploads&&0!==t.uploads.length||(e.uploads="At least one file is required."),null===t.controls.isAdditionalControlsIdentified?e.isAdditionalControlsIdentified="Please select if additional controls are identified (Yes/No).":(t.controls.isAdditionalControlsIdentified&&(t.controls.describeAdditionalControls.trim()||(e.describeAdditionalControls="Please describe the additional controls.")),t.controls.teamBrief?"No"!==t.controls.teamBrief&&"Not Applicable"!==t.controls.teamBrief||t.controls.teamBriefRemarks.trim()||(e.teamBriefRemarks="Remarks are required if team not briefed / not applicable."):e.teamBrief="Please select if the team is briefed."),null===t.isCloseOutChallenges?e.isCloseOutChallenges="Please select if there were any unexpected challenges (Yes/No).":t.isCloseOutChallenges&&(t.closeOutChallenges.length?t.closeOutChallenges.forEach((t,s)=>{t.unexpectedChallenges.trim()||(e["closeOutChallenges_".concat(s,"_unexpectedChallenges")]="Unexpected challenges cannot be empty."),t.remarks.trim()||(e["closeOutChallenges_".concat(s,"_remarks")]="Remarks cannot be empty for the challenge.")}):e.closeOutChallenges="At least one close-out challenge must be added."),t.toolboxSignStatuses.length?t.toolboxSignStatuses.forEach((t,s)=>{t.signedById||(e["toolboxSignStatuses_".concat(s,"_signedById")]="Please select a team member."),t.sign||(e["toolboxSignStatuses_".concat(s,"_sign")]="Signature is required."),t.signedDate||(e["toolboxSignStatuses_".concat(s,"_signedDate")]="Signed date is required.")}):e.toolboxSignStatuses="At least one signature is required.",C(e),0===Object.keys(e).length},L=(e,t)=>{for(var s=atob(e.split(",")[1]),a=e.split(",")[0].split(":")[1].split(";")[0],l=new ArrayBuffer(s.length),n=new DataView(l),i=0;i<s.length;i++)n.setUint8(i,s.charCodeAt(i));return new File([l],t,{type:a})},U=async()=>{if(M()){w(!0);try{let e=await Promise.all((t.toolboxSignStatuses||[]).map(async e=>{if(e.sign&&e.sign.startsWith("data:image")){let t="".concat(new Date().getTime(),"_captin_sign.png"),s=L(e.sign,t),a=new FormData;a.append("file",s);let l=await d.A.post(c.Dp,a,{headers:{"Content-Type":"multipart/form-data"}});if(l&&200===l.status)return{...e,sign:l.data.files[0].originalname,signedDate:e.signedDate||new Date().toISOString()};throw Error("File upload failed")}return e}));t.toolboxSignStatuses=e;let s=A.tasks.map(e=>{let t=e.find(e=>"activity"===e.type),s=e.find(e=>"hazards"===e.type),a=e.find(e=>"current_control"===e.type);return{activity:t,hazards:s,currentControl:a}});t.tasks=JSON.stringify(s);let{toolboxSignStatuses:a,...l}=t,n={toolboxTalk:l,toolboxSignStatuses:a||[]};console.log("Submitting this payload:",n);let i=await d.A.post(c.yo,n);if(200===i.status||201===i.status)h().fire("Success","Form submitted successfully","success"),k.back();else throw Error("Submission failed")}catch(e){console.error("Submit error:",e),h().fire("Error","Something went wrong","error")}finally{w(!1)}}},V=(e,t,s,a)=>{let l={...A};l.tasks[e][t].selected[s].toolbox_value=a,O(l)},J=(e,t,s,a)=>{let l={...A};l.tasks[e][t].selected[s].toolbox_remarks=a,O(l)},$=(e,t,s,a)=>{let l={...A};l.tasks[e][t].option[s].toolbox_value=a,O(l)},G=(e,t,s,a)=>{let l={...A};l.tasks[e][t].option[s].toolbox_remarks=a,O(l)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.default,{heading:"Toolbox Talk"}),z?(0,a.jsx)("div",{className:"d-flex justify-content-center align-items-center",style:{height:"60vh"},children:(0,a.jsx)("div",{className:"spinner-border text-primary",role:"status",children:(0,a.jsx)("span",{className:"visually-hidden",children:"Loading..."})})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"page-content-wrapper py-3",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"card",children:(0,a.jsxs)("div",{className:"card-body p-3",children:[(0,a.jsxs)("div",{className:"form-check mb-3",children:[(0,a.jsx)("input",{type:"checkbox",className:"form-check-input",id:"commenceDateCheck",checked:!!t.commenceDate,onChange:e=>{s(t=>({...t,commenceDate:e.target.checked?new Date().toISOString():""}))}}),(0,a.jsxs)("label",{className:"form-check-label",htmlFor:"commenceDateCheck",children:["Commence TBT *"," ",t.commenceDate?"- ".concat(f()(t.commenceDate).format("DD-MM-YYYY HH:mm")):""]})]}),y.commenceDate&&(0,a.jsx)("div",{className:"text-danger",children:y.commenceDate}),(0,a.jsxs)("div",{className:"mt-4 p-3 bg-white shadow-sm border rounded-lg",children:[(0,a.jsx)(b.A,{title:"WorkActivity / Situational Hazard *",options:N,selectedValue:t.riskAssessmentId,disabled:!1,onChange:e=>{s(t=>({...t,riskAssessmentId:e})),O(u.find(t=>t.id===e)||null)},placeholder:"Select WorkActivity",clearable:!0}),y.riskAssessmentId&&(0,a.jsx)("div",{className:"text-danger",children:y.riskAssessmentId})]}),(0,a.jsxs)("div",{className:"mt-4 p-3 bg-white shadow-sm border rounded-lg",children:[(0,a.jsx)(b.A,{title:"Conducted by *",options:I,selectedValue:t.conductedById,disabled:!T,onChange:e=>{s(t=>({...t,conductedById:e}))},placeholder:"Select Conducted by",clearable:!0}),y.conductedById&&(0,a.jsx)("div",{className:"text-danger",children:y.conductedById})]}),(0,a.jsxs)("div",{className:"mt-4 p-3 bg-white shadow-sm border rounded-lg",children:[(0,a.jsx)(n.A,{handleFilter:(e,t,a,l,n,i)=>{s(s=>({...s,locationOneId:e,locationTwoId:t,locationThreeId:a,locationFourId:l,locationFiveId:n,locationSixId:i}))},getLocation:t,disabled:!T}),y.locationOneId&&(0,a.jsx)("div",{className:"text-danger",children:y.locationOneId})]}),(0,a.jsxs)("div",{className:"mt-4 p-3 bg-white shadow-sm border rounded-lg",children:[(0,a.jsx)("label",{htmlFor:"noOfPersonsParticipated",className:"form-label",children:"Number of Persons Participated *"}),(0,a.jsx)("input",{type:"number",className:"form-control",id:"noOfPersonsParticipated",value:t.noOfPersonsParticipated,disabled:!T,onChange:e=>s(t=>({...t,noOfPersonsParticipated:Number(e.target.value)}))}),y.noOfPersonsParticipated&&(0,a.jsx)("div",{className:"text-danger",children:y.noOfPersonsParticipated})]}),(0,a.jsxs)("div",{className:"mt-4 p-3 bg-white shadow-sm border rounded-lg",children:[(0,a.jsx)(r.A,{disabled:!T,onFilesSelected:e=>s(t=>({...t,uploads:e})),files:t.uploads}),y.uploads&&(0,a.jsx)("div",{className:"text-danger",children:y.uploads})]}),null==A||null==(e=A.tasks)?void 0:e.map((e,t)=>(0,a.jsxs)("div",{className:"mt-4 p-3 bg-white shadow-sm border rounded-lg",children:[(0,a.jsxs)("h6",{className:"mb-3",children:["Sub-Activity ",t+1]}),e.map((e,s)=>"activity"===e.type?(0,a.jsx)("div",{className:"mb-3 p-3 bg-light rounded",children:e.name},s):"hazards"===e.type?(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"mb-3",children:"Have you briefed the team about below hazards and control measure?"}),e.selected.map((e,l)=>{let n=e.toolbox_value||"",i=e.toolbox_remarks||"";return(0,a.jsxs)("div",{className:"mb-3 p-3 border rounded shadow-sm bg-light",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center mb-2",children:[(0,a.jsx)("img",{src:"https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/".concat(e.image),alt:e.name,style:{width:"60px",height:"60px",marginRight:"1rem",borderRadius:"10px"}}),(0,a.jsx)("p",{children:e.name})]}),(0,a.jsx)("ul",{className:"nav nav-pills mb-2 p-2 d-flex justify-content-around bg-white align-items-center shadow-sm",style:{borderRadius:"9999px",gap:"0.5rem"},children:["Yes","No","Not Applicable"].map(e=>{let i=n===e;return(0,a.jsx)("li",{className:"nav-item flex-grow-1 text-center",children:(0,a.jsx)("button",{className:"btn w-100 rounded-pill ".concat(i?"Yes"===e?"btn-success text-white":"btn-danger text-white":"btn-outline-secondary"),style:{transition:"all 0.3s ease-in-out",fontWeight:500,padding:"0.5rem 0.75rem",border:"none"},onClick:()=>V(t,s,l,e),disabled:!T,children:e})},e)})}),("No"===n||"Not Applicable"===n)&&(0,a.jsx)("input",{type:"text",className:"form-control mt-2",placeholder:"Enter remarks",value:i,onChange:e=>J(t,s,l,e.target.value)})]},e.id)})]},s):"current_control"===e.type?(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"mb-3",children:"Confirm that the below control measures are implemented and effective."}),e.option.map((e,l)=>{let n=e.toolbox_value||"",i=e.toolbox_remarks||"";return(0,a.jsxs)("div",{className:"mb-3 p-3 border rounded shadow-sm bg-light",children:[(0,a.jsx)("div",{className:"mb-2",children:(0,a.jsx)("p",{children:e.value})}),(0,a.jsx)("ul",{className:"nav nav-pills mb-2 p-2 d-flex justify-content-around bg-white align-items-center shadow-sm",style:{borderRadius:"9999px",gap:"0.5rem"},children:["Yes","No","Not Applicable"].map(e=>{let i=n===e;return(0,a.jsx)("li",{className:"nav-item flex-grow-1 text-center",children:(0,a.jsx)("button",{className:"btn w-100 rounded-pill ".concat(i?"Yes"===e?"btn-success text-white":"btn-danger text-white":"btn-outline-secondary"),disabled:!T,style:{transition:"all 0.3s ease-in-out",fontWeight:500,padding:"0.5rem 0.75rem",border:"none"},onClick:()=>$(t,s,l,e),children:e})},e)})}),("No"===n||"Not Applicable"===n)&&(0,a.jsx)("input",{type:"text",className:"form-control mt-2",placeholder:"Enter remarks",value:i,onChange:e=>G(t,s,l,e.target.value)})]},l)})]},s):null)]},t)),(0,a.jsxs)("div",{className:"mt-4 p-3 bg-white shadow-sm border rounded-lg",children:[(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("label",{className:"mb-2",children:"Did you identify need for any additional controls?"}),(0,a.jsx)("ul",{className:"nav nav-pills p-2 shadow-sm d-flex justify-content-around bg-light align-items-center",style:{borderRadius:"9999px",gap:"0.5rem"},children:["Yes","No"].map(e=>(0,a.jsx)("li",{className:"nav-item flex-grow-1 text-center",role:"presentation",children:(0,a.jsx)("button",{className:"btn w-100 rounded-pill ".concat(t.controls.isAdditionalControlsIdentified===("Yes"===e)?"Yes"===e?"btn-success text-white":"btn-danger text-white":"btn-outline-secondary"),disabled:!T,style:{transition:"all 0.3s ease-in-out",fontWeight:500,padding:"0.5rem 0.75rem",border:"none"},onClick:()=>{s(t=>({...t,controls:{...t.controls,isAdditionalControlsIdentified:"Yes"===e,describeAdditionalControls:"",teamBrief:"",teamBriefRemarks:""}}))},children:e})},e))}),y.isAdditionalControlsIdentified&&(0,a.jsx)("div",{className:"text-danger",children:y.isAdditionalControlsIdentified})]}),(t.controls.isAdditionalControlsIdentified||!1===t.controls.isAdditionalControlsIdentified)&&(0,a.jsxs)(a.Fragment,{children:[t.controls.isAdditionalControlsIdentified&&(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("label",{children:"Description *"}),(0,a.jsx)("textarea",{rows:3,className:"form-control",placeholder:"Describe the additional controls",value:t.controls.describeAdditionalControls,onChange:e=>s(t=>({...t,controls:{...t.controls,describeAdditionalControls:e.target.value}}))}),y.describeAdditionalControls&&(0,a.jsx)("div",{className:"text-danger",children:y.describeAdditionalControls})]}),(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("label",{className:"mb-2",children:"Have you briefed the team?"}),(0,a.jsx)("ul",{className:"nav nav-pills p-2 shadow-sm d-flex justify-content-around bg-light align-items-center",style:{borderRadius:"9999px",gap:"0.5rem"},children:["Yes","No","Not Applicable"].map(e=>(0,a.jsx)("li",{className:"nav-item flex-grow-1 text-center",children:(0,a.jsx)("button",{className:"btn w-100 rounded-pill ".concat(t.controls.teamBrief===e?"Yes"===e?"btn-success text-white":"btn-danger text-white":"btn-outline-secondary"),disabled:!T,style:{transition:"all 0.3s ease-in-out",fontWeight:500,padding:"0.5rem 0.75rem",border:"none"},onClick:()=>{s(t=>({...t,controls:{...t.controls,teamBrief:e,teamBriefRemarks:""}}))},children:e})},e))}),y.teamBrief&&(0,a.jsx)("div",{className:"text-danger",children:y.teamBrief})]}),("No"===t.controls.teamBrief||"Not Applicable"===t.controls.teamBrief)&&(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("label",{children:"Remarks *"}),(0,a.jsx)("input",{type:"text",className:"form-control",placeholder:"Enter remarks",value:t.controls.teamBriefRemarks,onChange:e=>s(t=>({...t,controls:{...t.controls,teamBriefRemarks:e.target.value}}))}),y.teamBriefRemarks&&(0,a.jsx)("div",{className:"text-danger",children:y.teamBriefRemarks})]})]})]}),(0,a.jsxs)("div",{className:"mt-4 p-3 bg-white shadow-sm border rounded-lg",children:[(0,a.jsx)("h6",{className:"mb-3",children:"Acknowledgement of Team Members *"}),y.toolboxSignStatuses&&(0,a.jsx)("div",{className:"text-danger mb-2",children:y.toolboxSignStatuses}),t.toolboxSignStatuses.map((e,l)=>(0,a.jsxs)("div",{className:"mb-4 p-3 bg-light rounded shadow-sm position-relative",children:[(0,a.jsx)(b.A,{title:"Select Team Member *",options:I,selectedValue:e.signedById,disabled:!T,onChange:e=>{let a=[...t.toolboxSignStatuses];a[l].signedById=e,s(e=>({...e,toolboxSignStatuses:a}))},placeholder:"Select Team Member",clearable:!0}),y["toolboxSignStatuses_".concat(l,"_signedById")]&&(0,a.jsx)("div",{className:"text-danger",children:y["toolboxSignStatuses_".concat(l,"_signedById")]}),(0,a.jsx)("div",{className:"mb-3 mt-3 text-center",children:(0,a.jsx)("i",{className:"bi bi-pencil-square",style:{fontSize:30,cursor:"pointer"},onClick:()=>{R(l),P(!0)}})}),e.sign&&(0,a.jsx)("div",{className:"mt-2 text-center",children:(0,a.jsx)("img",{src:e.sign,alt:"Signature",style:{width:"150px",border:"1px solid #ccc"}})}),y["toolboxSignStatuses_".concat(l,"_sign")]&&(0,a.jsx)("div",{className:"text-danger",children:y["toolboxSignStatuses_".concat(l,"_sign")]}),t.toolboxSignStatuses.length>1&&(0,a.jsx)("i",{className:"bi bi-x-circle text-danger position-absolute",style:{top:"10px",right:"10px",cursor:"pointer",fontSize:"1.3rem"},onClick:()=>{let e=[...t.toolboxSignStatuses];e.splice(l,1),s(t=>({...t,toolboxSignStatuses:e}))}})]},l)),(0,a.jsx)("div",{className:"d-flex justify-content-end",children:(0,a.jsx)("button",{type:"button",disabled:!T,className:"btn btn-outline-primary btn-sm",onClick:()=>s(e=>({...e,toolboxSignStatuses:[...e.toolboxSignStatuses,{signedDate:"",sign:"",signedById:""}]})),children:"+ Add Member"})})]}),(0,a.jsxs)("div",{className:"mt-4 p-3 bg-white shadow-sm border rounded-lg",children:[(0,a.jsx)("h6",{className:"mb-3",children:"Close Out *"}),y.isCloseOutChallenges&&(0,a.jsx)("div",{className:"text-danger mb-2",children:y.isCloseOutChallenges}),(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("label",{children:"Were there any unexpected challenges / hazards faced during the performance of this task?"}),(0,a.jsx)("ul",{className:"nav nav-pills p-2 shadow-sm d-flex justify-content-around bg-light align-items-center mb-3 mt-3",style:{borderRadius:"9999px",gap:"0.5rem"},children:["Yes","No"].map(e=>(0,a.jsx)("li",{className:"nav-item flex-grow-1 text-center",role:"presentation",children:(0,a.jsx)("button",{className:"btn w-100 rounded-pill ".concat(t.isCloseOutChallenges===("Yes"===e)?"Yes"===e?"btn-success text-white":"btn-danger text-white":"btn-outline-secondary"),disabled:!T,style:{transition:"all 0.3s ease-in-out",fontWeight:500,padding:"0.5rem 0.75rem",border:"none"},onClick:()=>{s(t=>({...t,isCloseOutChallenges:"Yes"===e,closeOutChallenges:"Yes"===e?[{unexpectedChallenges:"",remarks:""}]:[]}))},children:e})},e))}),t.isCloseOutChallenges&&(0,a.jsxs)(a.Fragment,{children:[y.closeOutChallenges&&(0,a.jsx)("div",{className:"text-danger mb-2",children:y.closeOutChallenges}),t.closeOutChallenges.map((e,l)=>(0,a.jsxs)("div",{className:"mb-3 p-3 bg-light rounded shadow-sm position-relative",children:[(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("label",{children:"Unexpected Challenges *"}),(0,a.jsx)("textarea",{rows:3,className:"form-control",placeholder:"Enter unexpected challenges",value:e.unexpectedChallenges,onChange:e=>{let a=[...t.closeOutChallenges];a[l].unexpectedChallenges=e.target.value,s(e=>({...e,closeOutChallenges:a}))}}),y["closeOutChallenges_".concat(l,"_unexpectedChallenges")]&&(0,a.jsx)("div",{className:"text-danger",children:y["closeOutChallenges_".concat(l,"_unexpectedChallenges")]})]}),(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("label",{children:"Remarks *"}),(0,a.jsx)("input",{type:"text",className:"form-control",placeholder:"Enter remarks",value:e.remarks,onChange:e=>{let a=[...t.closeOutChallenges];a[l].remarks=e.target.value,s(e=>({...e,closeOutChallenges:a}))}}),y["closeOutChallenges_".concat(l,"_remarks")]&&(0,a.jsx)("div",{className:"text-danger",children:y["closeOutChallenges_".concat(l,"_remarks")]})]}),t.closeOutChallenges.length>1&&(0,a.jsx)("i",{className:"bi bi-x-circle text-danger position-absolute",style:{top:"10px",right:"10px",cursor:"pointer",fontSize:"1.2rem"},onClick:()=>{let e=[...t.closeOutChallenges];e.splice(l,1),s(t=>({...t,closeOutChallenges:e}))}})]},l)),(0,a.jsx)("div",{className:"d-flex justify-content-end",children:(0,a.jsx)("button",{type:"button",className:"btn btn-outline-primary btn-sm",disabled:!T,onClick:()=>s(e=>({...e,closeOutChallenges:[...e.closeOutChallenges,{unexpectedChallenges:"",remarks:""}]})),children:"+ Add More"})})]})]})]})]})})})}),(0,a.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,a.jsx)("div",{className:"container px-0",children:(0,a.jsx)("div",{className:"footer-nav position-relative",children:(0,a.jsx)("div",{className:"h-100 d-flex align-items-center justify-content-between ps-0 w-100",children:(0,a.jsx)(i.A,{variant:"primary",className:"w-100",disabled:S,onClick:U,children:S?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),"Submitting..."]}):"Submit"})})})})})]}),(0,a.jsxs)(o.A,{show:B,onHide:()=>P(!1),centered:!0,children:[(0,a.jsx)(o.A.Header,{closeButton:!0,children:(0,a.jsx)(o.A.Title,{children:"Signature"})}),(0,a.jsxs)(o.A.Body,{children:[(0,a.jsx)("div",{style:{border:"1px solid #ced4da",borderRadius:"0.25rem",padding:"5px",background:"#fff"},children:(0,a.jsx)(x.A,{ref:Y,canvasProps:{width:300,height:150,className:"signatureCanvas w-100"},backgroundColor:"#fff"})}),(0,a.jsxs)("div",{className:"d-flex mt-3 justify-content-between",children:[(0,a.jsx)("button",{type:"button",className:"btn btn-outline-danger btn-sm",onClick:()=>{var e;return null==(e=Y.current)?void 0:e.clear()},children:"Clear"}),(0,a.jsx)("button",{type:"button",className:"btn btn-primary btn-sm",onClick:()=>{if(null!==D&&Y.current){let e=Y.current.toDataURL("image/png"),a=[...t.toolboxSignStatuses];a[D].sign=e,a[D].signedDate=new Date().toISOString(),s(e=>({...e,toolboxSignStatuses:a})),R(null),P(!1)}},children:"Save"})]})]})]})]})}},93954:(e,t,s)=>{Promise.resolve().then(s.bind(s,14832))}},e=>{var t=t=>e(e.s=t);e.O(0,[3496,586,8320,6078,635,4816,1205,5898,6639,9697,4952,3066,9314,8441,1684,7358],()=>t(93954)),_N_E=e.O()}]);