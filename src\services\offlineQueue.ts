// Offline queue system for handling failed requests and background sync
import { offlineStorage, OfflineQueueItem } from './offlineStorage';
import API from './API';

export interface QueueStats {
  pending: number;
  failed: number;
  processing: boolean;
}

class OfflineQueue {
  private isProcessing = false;
  private maxRetries = 3;
  private retryDelay = 1000; // Start with 1 second
  private listeners: Array<(stats: QueueStats) => void> = [];

  constructor() {
    // Listen for online/offline events
    if (typeof window !== 'undefined') {
      window.addEventListener('online', () => {
        console.log('🌐 Back online! Processing queue...');
        this.processQueue();
      });

      window.addEventListener('offline', () => {
        console.log('📱 Gone offline. Requests will be queued.');
      });

      // Process queue on initialization if online
      if (navigator.onLine) {
        setTimeout(() => this.processQueue(), 1000);
      }
    }
  }

  // Add a request to the offline queue
  async addRequest(
    url: string,
    method: string = 'GET',
    data?: any,
    headers?: Record<string, string>
  ): Promise<void> {
    try {
      await offlineStorage.addToQueue({
        url,
        method,
        data,
        headers,
        maxRetries: this.maxRetries
      });

      this.notifyListeners();
      console.log(`📤 Added ${method} request to queue: ${url}`);
    } catch (error) {
      console.error('❌ Failed to add request to queue:', error);
    }
  }

  // Process all queued requests
  async processQueue(): Promise<void> {
    if (this.isProcessing || !navigator.onLine) {
      return;
    }

    this.isProcessing = true;
    this.notifyListeners();

    try {
      const queueItems = await offlineStorage.getQueueItems();
      console.log(`🔄 Processing ${queueItems.length} queued requests`);

      for (const item of queueItems) {
        await this.processQueueItem(item);
      }

      console.log('✅ Queue processing completed');
    } catch (error) {
      console.error('❌ Error processing queue:', error);
    } finally {
      this.isProcessing = false;
      this.notifyListeners();
    }
  }

  // Process a single queue item
  private async processQueueItem(item: OfflineQueueItem): Promise<void> {
    try {
      console.log(`🔄 Processing queued request: ${item.method} ${item.url}`);

      // Prepare request configuration
      const config: any = {
        method: item.method,
        url: item.url,
        headers: item.headers || {}
      };

      if (item.data && (item.method === 'POST' || item.method === 'PUT' || item.method === 'PATCH')) {
        config.data = item.data;
      }

      // Make the request
      const response = await API.request(config);

      if (response.status >= 200 && response.status < 300) {
        // Success - remove from queue
        await offlineStorage.removeFromQueue(item.id);
        console.log(`✅ Successfully processed: ${item.url}`);
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error(`❌ Failed to process queued request: ${item.url}`, error);
      await this.handleFailedRequest(item);
    }
  }

  // Handle failed request retry logic
  private async handleFailedRequest(item: OfflineQueueItem): Promise<void> {
    const newRetryCount = item.retryCount + 1;

    if (newRetryCount >= item.maxRetries) {
      // Max retries reached - remove from queue
      await offlineStorage.removeFromQueue(item.id);
      console.log(`❌ Max retries reached for: ${item.url}. Removing from queue.`);
      
      // Optionally notify user about failed request
      this.notifyFailedRequest(item);
    } else {
      // Update retry count and try again later
      await offlineStorage.updateQueueItemRetryCount(item.id, newRetryCount);
      console.log(`🔄 Retry ${newRetryCount}/${item.maxRetries} for: ${item.url}`);
      
      // Exponential backoff
      const delay = this.retryDelay * Math.pow(2, newRetryCount - 1);
      setTimeout(() => {
        if (navigator.onLine) {
          this.processQueueItem(item);
        }
      }, delay);
    }
  }

  // Notify about failed requests
  private notifyFailedRequest(item: OfflineQueueItem): void {
    // You can implement user notification here
    console.warn(`🚨 Failed to sync request after ${item.maxRetries} attempts: ${item.url}`);
    
    // Example: Show toast notification
    if (typeof window !== 'undefined' && 'Notification' in window) {
      if (Notification.permission === 'granted') {
        new Notification('Sync Failed', {
          body: `Failed to sync data. Please try again later.`,
          icon: '/assets/icons/Icon-192.png'
        });
      }
    }
  }

  // Get queue statistics
  async getStats(): Promise<QueueStats> {
    try {
      const queueItems = await offlineStorage.getQueueItems();
      const failed = queueItems.filter(item => item.retryCount >= item.maxRetries).length;
      const pending = queueItems.length - failed;

      return {
        pending,
        failed,
        processing: this.isProcessing
      };
    } catch (error) {
      console.error('❌ Error getting queue stats:', error);
      return { pending: 0, failed: 0, processing: false };
    }
  }

  // Clear all queue items
  async clearQueue(): Promise<void> {
    try {
      const queueItems = await offlineStorage.getQueueItems();
      for (const item of queueItems) {
        await offlineStorage.removeFromQueue(item.id);
      }
      console.log('🗑️ Cleared offline queue');
      this.notifyListeners();
    } catch (error) {
      console.error('❌ Error clearing queue:', error);
    }
  }

  // Subscribe to queue updates
  subscribe(callback: (stats: QueueStats) => void): () => void {
    this.listeners.push(callback);
    
    // Immediately call with current stats
    this.getStats().then(callback);

    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // Notify all listeners
  private async notifyListeners(): Promise<void> {
    if (this.listeners.length > 0) {
      const stats = await this.getStats();
      this.listeners.forEach(callback => callback(stats));
    }
  }

  // Force retry all failed requests
  async retryFailedRequests(): Promise<void> {
    try {
      const queueItems = await offlineStorage.getQueueItems();
      const failedItems = queueItems.filter(item => item.retryCount >= item.maxRetries);

      for (const item of failedItems) {
        // Reset retry count
        await offlineStorage.updateQueueItemRetryCount(item.id, 0);
      }

      console.log(`🔄 Reset ${failedItems.length} failed requests for retry`);
      
      if (navigator.onLine) {
        this.processQueue();
      }
    } catch (error) {
      console.error('❌ Error retrying failed requests:', error);
    }
  }

  // Check if a request should be queued (when offline or request fails)
  shouldQueue(error: any): boolean {
    // Queue if offline
    if (!navigator.onLine) {
      return true;
    }

    // Queue if network error
    if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
      return true;
    }

    // Queue if server error (5xx)
    if (error.response?.status >= 500) {
      return true;
    }

    // Don't queue client errors (4xx) except 408 (timeout)
    if (error.response?.status >= 400 && error.response?.status < 500) {
      return error.response.status === 408; // Request timeout
    }

    return false;
  }
}

// Export singleton instance
export const offlineQueue = new OfflineQueue();

// Background sync registration for service worker
if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
  navigator.serviceWorker.ready.then(registration => {
    if ('sync' in registration) {
      // Register background sync
      (registration as any).sync.register('background-sync').catch(console.error);
    }
  });
}
