"use client";

import React, { useState, useEffect } from 'react';
import { offlineAPI } from '@/services/offlineAPI';
import { offlineQueue, QueueStats } from '@/services/offlineQueue';

interface OfflineIndicatorProps {
  className?: string;
  showDetails?: boolean;
}

const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({ 
  className = '', 
  showDetails = false 
}) => {
  const [isOnline, setIsOnline] = useState(true);
  const [queueStats, setQueueStats] = useState<QueueStats>({ pending: 0, failed: 0, processing: false });
  const [showTooltip, setShowTooltip] = useState(false);
  const [lastSync, setLastSync] = useState<{ services: string; actions: string }>({
    services: 'Never',
    actions: 'Never'
  });

  useEffect(() => {
    // Initialize online status
    setIsOnline(navigator.onLine);

    // Subscribe to connection changes
    const unsubscribeConnection = offlineAPI.onConnectionChange(setIsOnline);

    // Subscribe to queue updates
    const unsubscribeQueue = offlineQueue.subscribe(setQueueStats);

    // Update sync times
    updateSyncTimes();

    return () => {
      unsubscribeConnection();
      unsubscribeQueue();
    };
  }, []);

  const updateSyncTimes = async () => {
    try {
      const servicesAge = await offlineAPI.getDataAge('services');
      const actionsAge = await offlineAPI.getDataAge('actions');
      setLastSync({ services: servicesAge, actions: actionsAge });
    } catch (error) {
      console.error('Error updating sync times:', error);
    }
  };

  const handleSync = async () => {
    if (!isOnline) return;
    
    try {
      await offlineAPI.syncAll();
      updateSyncTimes();
    } catch (error) {
      console.error('Sync failed:', error);
    }
  };

  const getStatusColor = () => {
    if (!isOnline) return '#f44336'; // Red for offline
    if (queueStats.processing) return '#ff9800'; // Orange for syncing
    if (queueStats.pending > 0) return '#2196f3'; // Blue for pending
    return '#4caf50'; // Green for online and synced
  };

  const getStatusText = () => {
    if (!isOnline) return 'Offline';
    if (queueStats.processing) return 'Syncing...';
    if (queueStats.pending > 0) return `${queueStats.pending} pending`;
    return 'Online';
  };

  const getStatusIcon = () => {
    if (!isOnline) return 'bi-wifi-off';
    if (queueStats.processing) return 'bi-arrow-repeat';
    if (queueStats.pending > 0) return 'bi-cloud-upload';
    return 'bi-wifi';
  };

  return (
    <div className={`position-relative ${className}`}>
      {/* Main Status Indicator */}
      <div
        className="d-flex align-items-center"
        style={{ cursor: showDetails ? 'pointer' : 'default' }}
        onClick={() => showDetails && setShowTooltip(!showTooltip)}
      >
        <div
          className="rounded-circle d-flex align-items-center justify-content-center me-2"
          style={{
            width: '24px',
            height: '24px',
            backgroundColor: getStatusColor(),
            color: 'white'
          }}
        >
          <i 
            className={`${getStatusIcon()} ${queueStats.processing ? 'spin' : ''}`}
            style={{ fontSize: '12px' }}
          ></i>
        </div>
        
        {showDetails && (
          <div className="d-flex flex-column">
            <span style={{ fontSize: '12px', fontWeight: '500', color: getStatusColor() }}>
              {getStatusText()}
            </span>
            {!isOnline && (
              <span style={{ fontSize: '10px', color: '#666' }}>
                Using cached data
              </span>
            )}
          </div>
        )}
      </div>

      {/* Detailed Tooltip */}
      {showTooltip && showDetails && (
        <div
          className="position-absolute bg-white border rounded-3 shadow-lg p-3"
          style={{
            top: '100%',
            right: '0',
            marginTop: '8px',
            minWidth: '280px',
            zIndex: 1000,
            border: '1px solid #e0e0e0'
          }}
        >
          {/* Header */}
          <div className="d-flex justify-content-between align-items-center mb-3">
            <h6 className="mb-0" style={{ fontSize: '14px', fontWeight: '600' }}>
              Connection Status
            </h6>
            <button
              className="btn btn-link p-0"
              onClick={() => setShowTooltip(false)}
              style={{ fontSize: '16px', color: '#666' }}
            >
              <i className="bi bi-x"></i>
            </button>
          </div>

          {/* Status Details */}
          <div className="mb-3">
            <div className="d-flex align-items-center mb-2">
              <div
                className="rounded-circle me-2"
                style={{
                  width: '8px',
                  height: '8px',
                  backgroundColor: getStatusColor()
                }}
              ></div>
              <span style={{ fontSize: '13px', fontWeight: '500' }}>
                {getStatusText()}
              </span>
            </div>
            
            {queueStats.pending > 0 && (
              <div style={{ fontSize: '12px', color: '#666', marginLeft: '16px' }}>
                {queueStats.pending} request{queueStats.pending > 1 ? 's' : ''} waiting to sync
              </div>
            )}
            
            {queueStats.failed > 0 && (
              <div style={{ fontSize: '12px', color: '#f44336', marginLeft: '16px' }}>
                {queueStats.failed} failed request{queueStats.failed > 1 ? 's' : ''}
              </div>
            )}
          </div>

          {/* Last Sync Info */}
          <div className="mb-3">
            <div style={{ fontSize: '12px', fontWeight: '500', marginBottom: '8px' }}>
              Last Sync:
            </div>
            <div style={{ fontSize: '11px', color: '#666' }}>
              <div>Services: {lastSync.services}</div>
              <div>Actions: {lastSync.actions}</div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="d-flex gap-2">
            {isOnline && (
              <button
                className="btn btn-primary btn-sm flex-fill"
                onClick={handleSync}
                disabled={queueStats.processing}
                style={{ fontSize: '12px' }}
              >
                {queueStats.processing ? (
                  <>
                    <i className="bi bi-arrow-repeat spin me-1"></i>
                    Syncing...
                  </>
                ) : (
                  <>
                    <i className="bi bi-arrow-clockwise me-1"></i>
                    Sync Now
                  </>
                )}
              </button>
            )}
            
            {queueStats.failed > 0 && (
              <button
                className="btn btn-outline-warning btn-sm"
                onClick={() => offlineQueue.retryFailedRequests()}
                style={{ fontSize: '12px' }}
              >
                <i className="bi bi-arrow-repeat me-1"></i>
                Retry
              </button>
            )}
          </div>
        </div>
      )}

      {/* Spinning animation for processing */}
      <style jsx>{`
        .spin {
          animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default OfflineIndicator;
