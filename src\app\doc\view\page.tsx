"use client";

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import HeaderSeven from '@/layouts/headers/HeaderSeven';
import DocumentPDFViewer from '@/components/DocumentPDFViewer';

interface Document {
  id: string;
  name: string;
  type: string;
  size?: string;
  uploadedBy?: string;
  uploadedDate?: string;
  category?: string;
  tags?: string[];
  description?: string;
  maskId?: string;
  scopeApplicability?: string;
  purpose?: string;
  keywords?: string;
  docId?: string;
  created?: string;
  updated?: string;
  creatorTargetDate?: string;
  reviewerTargetDate?: string;
  approverTargetDate?: string;
  initiatorId?: string;
  creatorId?: string;
  reviewerId?: string;
  approverId?: string;
  documentCategoryId?: string;
  initiator?: any;
  creator?: any;
  reviewer?: any;
  approver?: any;
  documentCategory?: any;
  files?: any;
  status?: string;
  docStatus?: string;
  value?: any[];
}

const DocumentViewContent: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [document, setDocument] = useState<Document | null>(null);

  useEffect(() => {
    const docId = searchParams.get('id');

    if (docId) {
      // Try to get document from localStorage first
      const storedDocument = localStorage.getItem('selectedDocument');
      if (storedDocument) {
        const parsedDocument = JSON.parse(storedDocument);
        if (parsedDocument.id === docId) {
          setDocument(parsedDocument);
          return;
        }
      }

      // If not found in localStorage, you could fetch from API here
      // For now, redirect back to doc list
      router.push('/doc');
    } else {
      // No ID provided, check localStorage
      const storedDocument = localStorage.getItem('selectedDocument');
      if (storedDocument) {
        const parsedDocument = JSON.parse(storedDocument);
        setDocument(parsedDocument);
      } else {
        router.push('/doc');
      }
    }
  }, [router, searchParams]);

  const handleBack = () => {
    router.back();
  };

  if (!document) {
    return (
      <>
        <HeaderSeven  heading="Document Details" />
        <div className="page-content-wrapper py-3">
          <div className="container">
            <div className="text-center py-5">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p className="mt-3">Loading document...</p>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <HeaderSeven      heading={document.name} />
      <div className="page-content-wrapper py-3">
        <div className="container-fluid">
          {/* <div className="row mb-3">
            <div className="col-12">
              <button
                onClick={handleBack}
                className="btn btn-outline-primary btn-sm"
                style={{ borderRadius: '6px' }}
              >
                <i className="bi bi-arrow-left me-2"></i>
                Back
              </button>
            </div>
          </div> */}
          <DocumentPDFViewer document={document} />
        </div>
      </div>
    </>
  );
};

const DocumentViewPage: React.FC = () => {
  return (
    <Suspense fallback={
      <>
        <HeaderSeven heading="Document Details" />
        <div className="page-content-wrapper py-3">
          <div className="container">
            <div className="text-center py-5">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p className="mt-3">Loading document...</p>
            </div>
          </div>
        </div>
      </>
    }>
      <DocumentViewContent />
    </Suspense>
  );
};

export default DocumentViewPage;
