(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8403],{21217:(e,s,a)=>{"use strict";a.d(s,{default:()=>d});var l=a(95155),t=a(9e4),i=a(38808),r=a(12115);let c=e=>{let{handleShowSetting:s,showSetting:a}=e,{theme:r,handleDarkModeToggle:c}=(0,t.D)(),{viewMode:n,handleRTLToggling:o}=(0,i.L)();return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{id:"setting-popup-overlay",className:a?"active":"",onClick:s}),(0,l.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(a?"active":""),id:"settingCard",children:(0,l.jsx)("div",{className:"card-body",children:(0,l.jsxs)("div",{className:"container",children:[(0,l.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,l.jsx)("p",{className:"mb-0",children:"Settings"}),(0,l.jsx)("div",{onClick:s,className:"btn-close",id:"settingCardClose"})]}),(0,l.jsx)("div",{className:"single-setting-panel",children:(0,l.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,l.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,l.jsx)("div",{className:"single-setting-panel",children:(0,l.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,l.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,l.jsx)("div",{className:"single-setting-panel",children:(0,l.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,l.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===r,onChange:c}),(0,l.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===r?"Light":"Dark"," mode"]})]})}),(0,l.jsx)("div",{className:"single-setting-panel",children:(0,l.jsxs)("div",{className:"form-check form-switch",children:[(0,l.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===n,onChange:o}),(0,l.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===n?"LTR":"RTL"," mode"]})]})})]})})})]})};var n=a(6874),o=a.n(n);let d=e=>{let{links:s,title:a}=e,[t,i]=(0,r.useState)(!1),n=()=>i(!t);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"header-area",id:"headerArea",children:(0,l.jsx)("div",{className:"container",children:(0,l.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,l.jsx)("div",{className:"back-button",children:(0,l.jsx)(o(),{href:"/".concat(s),children:(0,l.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,l.jsx)("div",{className:"page-heading",children:(0,l.jsx)("h6",{className:"mb-0",children:a})}),(0,l.jsx)("div",{className:"setting-wrapper",onClick:n,children:(0,l.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,l.jsx)("i",{className:"bi bi-gear"}),(0,l.jsx)("span",{})]})})]})})}),(0,l.jsx)(c,{showSetting:t,handleShowSetting:n})]})}},38808:(e,s,a)=>{"use strict";a.d(s,{L:()=>t});var l=a(12115);let t=()=>{let[e,s]=(0,l.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,l.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let a=()=>{s(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:a,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let s=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(s),e.style.display="none")},20)},1e3))}a()}}}},38983:(e,s,a)=>{"use strict";a.d(s,{default:()=>c});var l=a(95155),t=a(6874),i=a.n(t);a(12115);let r=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],c=()=>(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,l.jsx)("div",{className:"container px-0",children:(0,l.jsx)("div",{className:"footer-nav position-relative",children:(0,l.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:r.map((e,s)=>(0,l.jsx)("li",{children:(0,l.jsxs)(i(),{href:"/".concat(e.link),children:[(0,l.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,l.jsx)("span",{children:e.title})]})},s))})})})})})},48726:(e,s,a)=>{Promise.resolve().then(a.bind(a,98066)),Promise.resolve().then(a.bind(a,38983)),Promise.resolve().then(a.bind(a,21217))},9e4:(e,s,a)=>{"use strict";a.d(s,{D:()=>t});var l=a(12115);let t=()=>{let[e,s]=(0,l.useState)("light"),[a,t]=(0,l.useState)(!1);(0,l.useEffect)(()=>{s(localStorage.getItem("theme")||"light"),t(!0)},[]),(0,l.useEffect)(()=>{a&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,a]);let i=(0,l.useCallback)(()=>{s(e=>"dark"===e?"light":"dark")},[]),r=(0,l.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let s=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(s),e.style.display="none")},20)},1e3))}i()},[i]);return{theme:e,toggleTheme:i,handleDarkModeToggle:r}}},98066:(e,s,a)=>{"use strict";a.d(s,{default:()=>t});var l=a(95155);a(12115);let t=()=>(a(81531),(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("div",{className:"page-content-wrapper py-3",children:[(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"element-heading",children:(0,l.jsx)("h6",{children:"Alerts 01"})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"card",children:(0,l.jsxs)("div",{className:"card-body",children:[(0,l.jsxs)("div",{className:"alert custom-alert-1 alert-primary alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-check-circle"}),"Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-1 alert-secondary alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-check-circle"}),"Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-1 alert-success alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-check-circle"}),"Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-1 alert-danger alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-x-circle"}),"Message not sent!",(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-1 alert-warning alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-exclamation-circle"}),"Oops! Message not sent!",(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-1 alert-info alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-info-circle"}),"Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-1 alert-light alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-check-circle"}),"Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-1 alert-dark alert-dismissible fade show mb-0",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-check-circle"}),"Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]})]})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"element-heading mt-3",children:(0,l.jsx)("h6",{children:"Alerts 02"})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"card",children:(0,l.jsxs)("div",{className:"card-body",children:[(0,l.jsxs)("div",{className:"alert custom-alert-2 alert-primary alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-check-circle"}),"Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close btn-close-white position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-2 alert-secondary alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-check-circle"}),"Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close btn-close-white position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-2 alert-success alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-check-circle"}),"Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close btn-close-white position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-2 alert-danger alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-x-circle"}),"Message not sent!",(0,l.jsx)("button",{className:"btn btn-close btn-close-white position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-2 alert-warning alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-exclamation-circle"}),"Oops! Message not sent!",(0,l.jsx)("button",{className:"btn btn-close btn-close-white position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-2 alert-info alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-info-circle"}),"Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close btn-close-white position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-2 alert-light alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-check-circle"}),"Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-2 alert-dark alert-dismissible fade show mb-0",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-check-circle"}),"Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close btn-close-white position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]})]})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"element-heading mt-3",children:(0,l.jsx)("h6",{children:"Alerts 03"})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"card",children:(0,l.jsxs)("div",{className:"card-body",children:[(0,l.jsxs)("div",{className:"alert custom-alert-3 alert-primary alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-check-circle"}),(0,l.jsxs)("div",{className:"alert-text",children:[(0,l.jsx)("h6",{children:"Successfully sent!"}),(0,l.jsx)("span",{children:"Your email successfully sent!"}),(0,l.jsx)("a",{className:"btn btn-sm btn-primary mt-2",href:"#",children:"View Details"})]}),(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-3 alert-secondary alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-person-plus"}),(0,l.jsxs)("div",{className:"alert-text",children:[(0,l.jsx)("h6",{children:"New user added"}),(0,l.jsx)("span",{children:"Your email successfully sent!"})]}),(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-3 alert-success alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-check-all"}),(0,l.jsxs)("div",{className:"alert-text",children:[(0,l.jsx)("h6",{children:"Payment received!"}),(0,l.jsx)("span",{children:"Your email successfully sent!"}),(0,l.jsx)("a",{className:"btn btn-sm btn-success mt-2",href:"#",children:"View balance"})]}),(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-3 alert-danger alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-x-circle"}),(0,l.jsxs)("div",{className:"alert-text",children:[(0,l.jsx)("h6",{children:"Oops! something is wrong"}),(0,l.jsx)("span",{children:"Your email successfully sent!"})]}),(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-3 alert-warning alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-exclamation-circle"}),(0,l.jsxs)("div",{className:"alert-text",children:[(0,l.jsx)("h6",{children:"Warning!"}),(0,l.jsx)("span",{children:"Your email successfully sent!"})]}),(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-3 alert-info alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-arrow-repeat"}),(0,l.jsxs)("div",{className:"alert-text",children:[(0,l.jsx)("h6",{children:"Update available"}),(0,l.jsx)("span",{children:"Your email successfully sent!"}),(0,l.jsx)("a",{className:"btn btn-sm btn-creative btn-info mt-2",href:"#",children:"Update Now"})]}),(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-3 alert-light alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-check-circle"}),(0,l.jsxs)("div",{className:"alert-text",children:[(0,l.jsx)("h6",{children:"Successfully sent!"}),(0,l.jsx)("span",{children:"Your email successfully sent!"})]}),(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert custom-alert-3 alert-dark mb-0 alert-dismissible fade show",role:"alert",children:[(0,l.jsx)("i",{className:"bi bi-check-circle"}),(0,l.jsxs)("div",{className:"alert-text",children:[(0,l.jsx)("h6",{children:"Successfully sent!"}),(0,l.jsx)("span",{children:"Your email successfully sent!"}),(0,l.jsx)("a",{className:"btn btn-sm btn-outline-dark mt-2",href:"#",children:"View Details"})]}),(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]})]})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"element-heading mt-3",children:(0,l.jsx)("h6",{children:"Bootstrap Alerts"})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"card",children:(0,l.jsxs)("div",{className:"card-body",children:[(0,l.jsx)("div",{className:"alert alert-primary",role:"alert",children:"A simple primary alert!"}),(0,l.jsx)("div",{className:"alert alert-secondary",role:"alert",children:"A simple secondary alert!"}),(0,l.jsx)("div",{className:"alert alert-success",role:"alert",children:"A simple success alert!"}),(0,l.jsx)("div",{className:"alert alert-danger",role:"alert",children:"A simple danger alert!"}),(0,l.jsx)("div",{className:"alert alert-warning",role:"alert",children:"A simple warning alert!"}),(0,l.jsx)("div",{className:"alert alert-info",role:"alert",children:"A simple info alert!"}),(0,l.jsx)("div",{className:"alert alert-light",role:"alert",children:"A simple light alert!"}),(0,l.jsx)("div",{className:"alert mb-0 alert-dark",role:"alert",children:"A simple dark alert!"})]})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"element-heading mt-3",children:(0,l.jsx)("h6",{children:"Alerts with close icon"})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"card",children:(0,l.jsxs)("div",{className:"card-body",children:[(0,l.jsxs)("div",{className:"alert d-flex align-items-center alert-primary alert-dismissible fade show",role:"alert",children:["Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert d-flex align-items-center alert-secondary alert-dismissible fade show",role:"alert",children:["Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert d-flex align-items-center alert-success alert-dismissible fade show",role:"alert",children:["Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert d-flex align-items-center alert-danger alert-dismissible fade show",role:"alert",children:["Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert d-flex align-items-center alert-warning alert-dismissible fade show",role:"alert",children:["Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert d-flex align-items-center alert-info alert-dismissible fade show",role:"alert",children:["Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert d-flex align-items-center alert-light alert-dismissible fade show",role:"alert",children:["Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]}),(0,l.jsxs)("div",{className:"alert d-flex align-items-center mb-0 alert-dark alert-dismissible fade show",role:"alert",children:["Message successfully sent!",(0,l.jsx)("button",{className:"btn btn-close position-relative p-1 ms-auto",type:"button","data-bs-dismiss":"alert","aria-label":"Close"})]})]})})})]})}))}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,1955,1531,8441,1684,7358],()=>s(48726)),_N_E=e.O()}]);