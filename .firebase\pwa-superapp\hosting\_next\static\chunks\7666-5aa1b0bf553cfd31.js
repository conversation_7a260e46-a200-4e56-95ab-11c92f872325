"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7666],{20996:(e,t,n)=>{n.d(t,{dG:()=>ej,gf:()=>eB,TT:()=>eP,xD:()=>eL});var r,a,i=n(81880),o=n(56391),s=n(49887),c=n(46984);let l="@firebase/installations",u="0.6.17",d=`w:${u}`,h="FIS_v2",p=new s.FA("installations","Installations",{"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."});function f(e){return e instanceof s.g&&e.code.includes("request-failed")}function g({projectId:e}){return`https://firebaseinstallations.googleapis.com/v1/projects/${e}/installations`}function m(e){return{token:e.token,requestStatus:2,expiresIn:Number(e.expiresIn.replace("s","000")),creationTime:Date.now()}}async function b(e,t){let n=(await t.json()).error;return p.create("request-failed",{requestName:e,serverCode:n.code,serverMessage:n.message,serverStatus:n.status})}function w({apiKey:e}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":e})}function y(e,{refreshToken:t}){var n;let r=w(e);return r.append("Authorization",(n=t,`${h} ${n}`)),r}async function v(e){let t=await e();return t.status>=500&&t.status<600?e():t}async function I({appConfig:e,heartbeatServiceProvider:t},{fid:n}){let r=g(e),a=w(e),i=t.getImmediate({optional:!0});if(i){let e=await i.getHeartbeatsHeader();e&&a.append("x-firebase-client",e)}let o={method:"POST",headers:a,body:JSON.stringify({fid:n,authVersion:h,appId:e.appId,sdkVersion:d})},s=await v(()=>fetch(r,o));if(s.ok){let e=await s.json();return{fid:e.fid||n,registrationStatus:2,refreshToken:e.refreshToken,authToken:m(e.authToken)}}throw await b("Create Installation",s)}function S(e){return new Promise(t=>{setTimeout(t,e)})}let _=/^[cdef][\w-]{21}$/;function E(e){return`${e.appName}!${e.appId}`}let C=new Map;function D(e,t){let n=E(e);k(n,t),function(e,t){let n=function(){return!A&&"BroadcastChannel"in self&&((A=new BroadcastChannel("[Firebase] FID Change")).onmessage=e=>{k(e.data.key,e.data.fid)}),A}();n&&n.postMessage({key:e,fid:t}),function(){0===C.size&&A&&(A.close(),A=null)}()}(n,t)}function k(e,t){let n=C.get(e);if(n)for(let e of n)e(t)}let A=null,T="firebase-installations-store",O=null;function M(){return O||(O=(0,c.P2)("firebase-installations-database",1,{upgrade:(e,t)=>{0===t&&e.createObjectStore(T)}})),O}async function N(e,t){let n=E(e),r=(await M()).transaction(T,"readwrite"),a=r.objectStore(T),i=await a.get(n);return await a.put(t,n),await r.done,i&&i.fid===t.fid||D(e,t.fid),t}async function P(e){let t=E(e),n=(await M()).transaction(T,"readwrite");await n.objectStore(T).delete(t),await n.done}async function j(e,t){let n=E(e),r=(await M()).transaction(T,"readwrite"),a=r.objectStore(T),i=await a.get(n),o=t(i);return void 0===o?await a.delete(n):await a.put(o,n),await r.done,o&&(!i||i.fid!==o.fid)&&D(e,o.fid),o}async function B(e){let t,n=await j(e.appConfig,n=>{let r=function(e,t){if(0===t.registrationStatus){if(!navigator.onLine)return{installationEntry:t,registrationPromise:Promise.reject(p.create("app-offline"))};let n={fid:t.fid,registrationStatus:1,registrationTime:Date.now()},r=L(e,n);return{installationEntry:n,registrationPromise:r}}return 1===t.registrationStatus?{installationEntry:t,registrationPromise:R(e)}:{installationEntry:t}}(e,F(n||{fid:function(){try{var e;let t=new Uint8Array(17);(self.crypto||self.msCrypto).getRandomValues(t),t[0]=112+t[0]%16;let n=(e=t,btoa(String.fromCharCode(...e)).replace(/\+/g,"-").replace(/\//g,"_").substr(0,22));return _.test(n)?n:""}catch(e){return""}}(),registrationStatus:0}));return t=r.registrationPromise,r.installationEntry});return""===n.fid?{installationEntry:await t}:{installationEntry:n,registrationPromise:t}}async function L(e,t){try{let n=await I(e,t);return N(e.appConfig,n)}catch(n){throw f(n)&&409===n.customData.serverCode?await P(e.appConfig):await N(e.appConfig,{fid:t.fid,registrationStatus:0}),n}}async function R(e){let t=await $(e.appConfig);for(;1===t.registrationStatus;)await S(100),t=await $(e.appConfig);if(0===t.registrationStatus){let{installationEntry:t,registrationPromise:n}=await B(e);return n||t}return t}function $(e){return j(e,e=>{if(!e)throw p.create("installation-not-found");return F(e)})}function F(e){var t;return 1===(t=e).registrationStatus&&t.registrationTime+1e4<Date.now()?{fid:e.fid,registrationStatus:0}:e}async function H({appConfig:e,heartbeatServiceProvider:t},n){let r=function(e,{fid:t}){return`${g(e)}/${t}/authTokens:generate`}(e,n),a=y(e,n),i=t.getImmediate({optional:!0});if(i){let e=await i.getHeartbeatsHeader();e&&a.append("x-firebase-client",e)}let o={method:"POST",headers:a,body:JSON.stringify({installation:{sdkVersion:d,appId:e.appId}})},s=await v(()=>fetch(r,o));if(s.ok)return m(await s.json());throw await b("Generate Auth Token",s)}async function x(e,t=!1){let n,r=await j(e.appConfig,r=>{var a;if(!U(r))throw p.create("not-registered");let i=r.authToken;if(!t&&2===(a=i).requestStatus&&!function(e){let t=Date.now();return t<e.creationTime||e.creationTime+e.expiresIn<t+36e5}(a))return r;if(1===i.requestStatus)return n=K(e,t),r;{if(!navigator.onLine)throw p.create("app-offline");let t=function(e){let t={requestStatus:1,requestTime:Date.now()};return Object.assign(Object.assign({},e),{authToken:t})}(r);return n=W(e,t),t}});return n?await n:r.authToken}async function K(e,t){let n=await V(e.appConfig);for(;1===n.authToken.requestStatus;)await S(100),n=await V(e.appConfig);let r=n.authToken;return 0===r.requestStatus?x(e,t):r}function V(e){return j(e,e=>{var t;if(!U(e))throw p.create("not-registered");return 1===(t=e.authToken).requestStatus&&t.requestTime+1e4<Date.now()?Object.assign(Object.assign({},e),{authToken:{requestStatus:0}}):e})}async function W(e,t){try{let n=await H(e,t),r=Object.assign(Object.assign({},t),{authToken:n});return await N(e.appConfig,r),n}catch(n){if(f(n)&&(401===n.customData.serverCode||404===n.customData.serverCode))await P(e.appConfig);else{let n=Object.assign(Object.assign({},t),{authToken:{requestStatus:0}});await N(e.appConfig,n)}throw n}}function U(e){return void 0!==e&&2===e.registrationStatus}async function z(e){let{installationEntry:t,registrationPromise:n}=await B(e);return n?n.catch(console.error):x(e).catch(console.error),t.fid}async function q(e,t=!1){return await G(e),(await x(e,t)).token}async function G(e){let{registrationPromise:t}=await B(e);t&&await t}function J(e){return p.create("missing-app-config-values",{valueName:e})}let Y="installations";(0,i.om)(new o.uA(Y,e=>{let t=e.getProvider("app").getImmediate(),n=function(e){if(!e||!e.options)throw J("App Configuration");if(!e.name)throw J("App Name");for(let t of["projectId","apiKey","appId"])if(!e.options[t])throw J(t);return{appName:e.name,projectId:e.options.projectId,apiKey:e.options.apiKey,appId:e.options.appId}}(t),r=(0,i.j6)(t,"heartbeat");return{app:t,appConfig:n,heartbeatServiceProvider:r,_delete:()=>Promise.resolve()}},"PUBLIC")),(0,i.om)(new o.uA("installations-internal",e=>{let t=e.getProvider("app").getImmediate(),n=(0,i.j6)(t,Y).getImmediate();return{getId:()=>z(n),getToken:e=>q(n,e)}},"PRIVATE")),(0,i.KO)(l,u),(0,i.KO)(l,u,"esm2017");let X="BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4",Q="google.c.a.c_id";function Z(e){return btoa(String.fromCharCode(...new Uint8Array(e))).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}!function(e){e[e.DATA_MESSAGE=1]="DATA_MESSAGE",e[e.DISPLAY_NOTIFICATION=3]="DISPLAY_NOTIFICATION"}(r||(r={})),function(e){e.PUSH_RECEIVED="push-received",e.NOTIFICATION_CLICKED="notification-clicked"}(a||(a={}));let ee="fcm_token_details_db",et="fcm_token_object_Store";async function en(e){if("databases"in indexedDB&&!(await indexedDB.databases()).map(e=>e.name).includes(ee))return null;let t=null;return(await (0,c.P2)(ee,5,{upgrade:async(n,r,a,i)=>{var o;if(r<2||!n.objectStoreNames.contains(et))return;let s=i.objectStore(et),c=await s.index("fcmSenderId").get(e);if(await s.clear(),c)if(2===r){if(!c.auth||!c.p256dh||!c.endpoint)return;t={token:c.fcmToken,createTime:null!=(o=c.createTime)?o:Date.now(),subscriptionOptions:{auth:c.auth,p256dh:c.p256dh,endpoint:c.endpoint,swScope:c.swScope,vapidKey:"string"==typeof c.vapidKey?c.vapidKey:Z(c.vapidKey)}}}else 3===r?t={token:c.fcmToken,createTime:c.createTime,subscriptionOptions:{auth:Z(c.auth),p256dh:Z(c.p256dh),endpoint:c.endpoint,swScope:c.swScope,vapidKey:Z(c.vapidKey)}}:4===r&&(t={token:c.fcmToken,createTime:c.createTime,subscriptionOptions:{auth:Z(c.auth),p256dh:Z(c.p256dh),endpoint:c.endpoint,swScope:c.swScope,vapidKey:Z(c.vapidKey)}})}})).close(),await (0,c.MR)(ee),await (0,c.MR)("fcm_vapid_details_db"),await (0,c.MR)("undefined"),!function(e){if(!e||!e.subscriptionOptions)return!1;let{subscriptionOptions:t}=e;return"number"==typeof e.createTime&&e.createTime>0&&"string"==typeof e.token&&e.token.length>0&&"string"==typeof t.auth&&t.auth.length>0&&"string"==typeof t.p256dh&&t.p256dh.length>0&&"string"==typeof t.endpoint&&t.endpoint.length>0&&"string"==typeof t.swScope&&t.swScope.length>0&&"string"==typeof t.vapidKey&&t.vapidKey.length>0}(t)?null:t}let er="firebase-messaging-store",ea=null;function ei(){return ea||(ea=(0,c.P2)("firebase-messaging-database",1,{upgrade:(e,t)=>{0===t&&e.createObjectStore(er)}})),ea}async function eo(e){let t=function({appConfig:e}){return e.appId}(e),n=await ei(),r=await n.transaction(er).objectStore(er).get(t);if(r)return r;{let t=await en(e.appConfig.senderId);if(t)return await es(e,t),t}}async function es(e,t){let n=function({appConfig:e}){return e.appId}(e),r=(await ei()).transaction(er,"readwrite");return await r.objectStore(er).put(t,n),await r.done,t}async function ec(e){let t=function({appConfig:e}){return e.appId}(e),n=(await ei()).transaction(er,"readwrite");await n.objectStore(er).delete(t),await n.done}let el=new s.FA("messaging","Messaging",{"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"only-available-in-window":"This method is available in a Window context.","only-available-in-sw":"This method is available in a service worker context.","permission-default":"The notification permission was not granted and dismissed instead.","permission-blocked":"The notification permission was not granted and blocked instead.","unsupported-browser":"This browser doesn't support the API's required to use the Firebase SDK.","indexed-db-unsupported":"This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)","failed-service-worker-registration":"We are unable to register the default service worker. {$browserErrorMessage}","token-subscribe-failed":"A problem occurred while subscribing the user to FCM: {$errorInfo}","token-subscribe-no-token":"FCM returned no token when subscribing the user to push.","token-unsubscribe-failed":"A problem occurred while unsubscribing the user from FCM: {$errorInfo}","token-update-failed":"A problem occurred while updating the user from FCM: {$errorInfo}","token-update-no-token":"FCM returned no token when updating the user to push.","use-sw-after-get-token":"The useServiceWorker() method may only be called once and must be called before calling getToken() to ensure your service worker is used.","invalid-sw-registration":"The input to useServiceWorker() must be a ServiceWorkerRegistration.","invalid-bg-handler":"The input to setBackgroundMessageHandler() must be a function.","invalid-vapid-key":"The public VAPID key must be a string.","use-vapid-key-after-get-token":"The usePublicVapidKey() method may only be called once and must be called before calling getToken() to ensure your VAPID key is used."});async function eu(e,t){let n,r={method:"POST",headers:await ef(e),body:JSON.stringify(eg(t))};try{let t=await fetch(ep(e.appConfig),r);n=await t.json()}catch(e){throw el.create("token-subscribe-failed",{errorInfo:null==e?void 0:e.toString()})}if(n.error){let e=n.error.message;throw el.create("token-subscribe-failed",{errorInfo:e})}if(!n.token)throw el.create("token-subscribe-no-token");return n.token}async function ed(e,t){let n,r={method:"PATCH",headers:await ef(e),body:JSON.stringify(eg(t.subscriptionOptions))};try{let a=await fetch(`${ep(e.appConfig)}/${t.token}`,r);n=await a.json()}catch(e){throw el.create("token-update-failed",{errorInfo:null==e?void 0:e.toString()})}if(n.error){let e=n.error.message;throw el.create("token-update-failed",{errorInfo:e})}if(!n.token)throw el.create("token-update-no-token");return n.token}async function eh(e,t){let n=await ef(e);try{let r=await fetch(`${ep(e.appConfig)}/${t}`,{method:"DELETE",headers:n}),a=await r.json();if(a.error){let e=a.error.message;throw el.create("token-unsubscribe-failed",{errorInfo:e})}}catch(e){throw el.create("token-unsubscribe-failed",{errorInfo:null==e?void 0:e.toString()})}}function ep({projectId:e}){return`https://fcmregistrations.googleapis.com/v1/projects/${e}/registrations`}async function ef({appConfig:e,installations:t}){let n=await t.getToken();return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":e.apiKey,"x-goog-firebase-installations-auth":`FIS ${n}`})}function eg({p256dh:e,auth:t,endpoint:n,vapidKey:r}){let a={web:{endpoint:n,auth:t,p256dh:e}};return r!==X&&(a.web.applicationPubKey=r),a}async function em(e){let t=await ev(e.swRegistration,e.vapidKey),n={vapidKey:e.vapidKey,swScope:e.swRegistration.scope,endpoint:t.endpoint,auth:Z(t.getKey("auth")),p256dh:Z(t.getKey("p256dh"))},r=await eo(e.firebaseDependencies);if(!r)return ey(e.firebaseDependencies,n);if(function(e,t){let n=t.vapidKey===e.vapidKey,r=t.endpoint===e.endpoint,a=t.auth===e.auth,i=t.p256dh===e.p256dh;return n&&r&&a&&i}(r.subscriptionOptions,n))if(Date.now()>=r.createTime+6048e5)return ew(e,{token:r.token,createTime:Date.now(),subscriptionOptions:n});else return r.token;try{await eh(e.firebaseDependencies,r.token)}catch(e){console.warn(e)}return ey(e.firebaseDependencies,n)}async function eb(e){let t=await eo(e.firebaseDependencies);t&&(await eh(e.firebaseDependencies,t.token),await ec(e.firebaseDependencies));let n=await e.swRegistration.pushManager.getSubscription();return!n||n.unsubscribe()}async function ew(e,t){try{let n=await ed(e.firebaseDependencies,t),r=Object.assign(Object.assign({},t),{token:n,createTime:Date.now()});return await es(e.firebaseDependencies,r),n}catch(e){throw e}}async function ey(e,t){let n={token:await eu(e,t),createTime:Date.now(),subscriptionOptions:t};return await es(e,n),n.token}async function ev(e,t){let n=await e.pushManager.getSubscription();return n||e.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:function(e){let t="=".repeat((4-e.length%4)%4),n=atob((e+t).replace(/\-/g,"+").replace(/_/g,"/")),r=new Uint8Array(n.length);for(let e=0;e<n.length;++e)r[e]=n.charCodeAt(e);return r}(t)})}function eI(e){var t,n;let r={from:e.from,collapseKey:e.collapse_key,messageId:e.fcmMessageId};return function(e,t){if(!t.notification)return;e.notification={};let n=t.notification.title;n&&(e.notification.title=n);let r=t.notification.body;r&&(e.notification.body=r);let a=t.notification.image;a&&(e.notification.image=a);let i=t.notification.icon;i&&(e.notification.icon=i)}(r,e),t=r,(n=e).data&&(t.data=n.data),function(e,t){var n,r,a,i,o;if(!t.fcmOptions&&!(null==(n=t.notification)?void 0:n.click_action))return;e.fcmOptions={};let s=null!=(a=null==(r=t.fcmOptions)?void 0:r.link)?a:null==(i=t.notification)?void 0:i.click_action;s&&(e.fcmOptions.link=s);let c=null==(o=t.fcmOptions)?void 0:o.analytics_label;c&&(e.fcmOptions.analyticsLabel=c)}(r,e),r}function eS(e){return el.create("missing-app-config-values",{valueName:e})}!function(e,t){let n=[];for(let r=0;r<e.length;r++)n.push(e.charAt(r)),r<t.length&&n.push(t.charAt(r));n.join("")}("AzSCbw63g1R0nCw85jG8","Iaya3yLKwmgvh7cF0q4");class e_{constructor(e,t,n){this.deliveryMetricsExportedToBigQueryEnabled=!1,this.onBackgroundMessageHandler=null,this.onMessageHandler=null,this.logEvents=[],this.isLogServiceStarted=!1;let r=function(e){if(!e||!e.options)throw eS("App Configuration Object");if(!e.name)throw eS("App Name");let{options:t}=e;for(let e of["projectId","apiKey","appId","messagingSenderId"])if(!t[e])throw eS(e);return{appName:e.name,projectId:t.projectId,apiKey:t.apiKey,appId:t.appId,senderId:t.messagingSenderId}}(e);this.firebaseDependencies={app:e,appConfig:r,installations:t,analyticsProvider:n}}_delete(){return Promise.resolve()}}async function eE(e){try{e.swRegistration=await navigator.serviceWorker.register("/firebase-messaging-sw.js",{scope:"/firebase-cloud-messaging-push-scope"}),e.swRegistration.update().catch(()=>{}),await eC(e.swRegistration)}catch(e){throw el.create("failed-service-worker-registration",{browserErrorMessage:null==e?void 0:e.message})}}async function eC(e){return new Promise((t,n)=>{let r=setTimeout(()=>n(Error("Service worker not registered after 10000 ms")),1e4),a=e.installing||e.waiting;e.active?(clearTimeout(r),t()):a?a.onstatechange=e=>{var n;(null==(n=e.target)?void 0:n.state)==="activated"&&(a.onstatechange=null,clearTimeout(r),t())}:(clearTimeout(r),n(Error("No incoming service worker found.")))})}async function eD(e,t){if(t||e.swRegistration||await eE(e),t||!e.swRegistration){if(!(t instanceof ServiceWorkerRegistration))throw el.create("invalid-sw-registration");e.swRegistration=t}}async function ek(e,t){t?e.vapidKey=t:e.vapidKey||(e.vapidKey=X)}async function eA(e,t){if(!navigator)throw el.create("only-available-in-window");if("default"===Notification.permission&&await Notification.requestPermission(),"granted"!==Notification.permission)throw el.create("permission-blocked");return await ek(e,null==t?void 0:t.vapidKey),await eD(e,null==t?void 0:t.serviceWorkerRegistration),em(e)}async function eT(e,t,n){let r=function(e){switch(e){case a.NOTIFICATION_CLICKED:return"notification_open";case a.PUSH_RECEIVED:return"notification_foreground";default:throw Error()}}(t);(await e.firebaseDependencies.analyticsProvider.get()).logEvent(r,{message_id:n[Q],message_name:n["google.c.a.c_l"],message_time:n["google.c.a.ts"],message_device_time:Math.floor(Date.now()/1e3)})}async function eO(e,t){let n=t.data;if(!n.isFirebaseMessaging)return;e.onMessageHandler&&n.messageType===a.PUSH_RECEIVED&&("function"==typeof e.onMessageHandler?e.onMessageHandler(eI(n)):e.onMessageHandler.next(eI(n)));let r=n.data;"object"==typeof r&&r&&Q in r&&"1"===r["google.c.a.e"]&&await eT(e,n.messageType,r)}let eM="@firebase/messaging",eN="0.12.21";async function eP(){try{await (0,s.eX)()}catch(e){return!1}return"undefined"!=typeof window&&(0,s.zW)()&&(0,s.dM)()&&"serviceWorker"in navigator&&"PushManager"in window&&"Notification"in window&&"fetch"in window&&ServiceWorkerRegistration.prototype.hasOwnProperty("showNotification")&&PushSubscription.prototype.hasOwnProperty("getKey")}function ej(e=(0,i.Sx)()){return eP().then(e=>{if(!e)throw el.create("unsupported-browser")},e=>{throw el.create("indexed-db-unsupported")}),(0,i.j6)((0,s.Ku)(e),"messaging").getImmediate()}async function eB(e,t){return eA(e=(0,s.Ku)(e),t)}function eL(e,t){var n=e=(0,s.Ku)(e);if(!navigator)throw el.create("only-available-in-window");return n.onMessageHandler=t,()=>{n.onMessageHandler=null}}(0,i.om)(new o.uA("messaging",e=>{let t=new e_(e.getProvider("app").getImmediate(),e.getProvider("installations-internal").getImmediate(),e.getProvider("analytics-internal"));return navigator.serviceWorker.addEventListener("message",e=>eO(t,e)),t},"PUBLIC")),(0,i.om)(new o.uA("messaging-internal",e=>{let t=e.getProvider("messaging").getImmediate();return{getToken:e=>eA(t,e)}},"PRIVATE")),(0,i.KO)(eM,eN),(0,i.KO)(eM,eN,"esm2017")},23915:(e,t,n)=>{n.d(t,{Wp:()=>r.Wp});var r=n(81880);(0,r.KO)("firebase","11.8.1","app")},46984:(e,t,n)=>{let r,a;n.d(t,{MR:()=>g,P2:()=>f});let i=(e,t)=>t.some(t=>e instanceof t),o=new WeakMap,s=new WeakMap,c=new WeakMap,l=new WeakMap,u=new WeakMap,d={get(e,t,n){if(e instanceof IDBTransaction){if("done"===t)return s.get(e);if("objectStoreNames"===t)return e.objectStoreNames||c.get(e);if("store"===t)return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return h(e[t])},set:(e,t,n)=>(e[t]=n,!0),has:(e,t)=>e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e};function h(e){if(e instanceof IDBRequest){let t=new Promise((t,n)=>{let r=()=>{e.removeEventListener("success",a),e.removeEventListener("error",i)},a=()=>{t(h(e.result)),r()},i=()=>{n(e.error),r()};e.addEventListener("success",a),e.addEventListener("error",i)});return t.then(t=>{t instanceof IDBCursor&&o.set(t,e)}).catch(()=>{}),u.set(t,e),t}if(l.has(e))return l.get(e);let t=function(e){if("function"==typeof e)return e!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(a||(a=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(e)?function(...t){return e.apply(p(this),t),h(o.get(this))}:function(...t){return h(e.apply(p(this),t))}:function(t,...n){let r=e.call(p(this),t,...n);return c.set(r,t.sort?t.sort():[t]),h(r)};return(e instanceof IDBTransaction&&function(e){if(s.has(e))return;let t=new Promise((t,n)=>{let r=()=>{e.removeEventListener("complete",a),e.removeEventListener("error",i),e.removeEventListener("abort",i)},a=()=>{t(),r()},i=()=>{n(e.error||new DOMException("AbortError","AbortError")),r()};e.addEventListener("complete",a),e.addEventListener("error",i),e.addEventListener("abort",i)});s.set(e,t)}(e),i(e,r||(r=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])))?new Proxy(e,d):e}(e);return t!==e&&(l.set(e,t),u.set(t,e)),t}let p=e=>u.get(e);function f(e,t,{blocked:n,upgrade:r,blocking:a,terminated:i}={}){let o=indexedDB.open(e,t),s=h(o);return r&&o.addEventListener("upgradeneeded",e=>{r(h(o.result),e.oldVersion,e.newVersion,h(o.transaction),e)}),n&&o.addEventListener("blocked",e=>n(e.oldVersion,e.newVersion,e)),s.then(e=>{i&&e.addEventListener("close",()=>i()),a&&e.addEventListener("versionchange",e=>a(e.oldVersion,e.newVersion,e))}).catch(()=>{}),s}function g(e,{blocked:t}={}){let n=indexedDB.deleteDatabase(e);return t&&n.addEventListener("blocked",e=>t(e.oldVersion,e)),h(n).then(()=>void 0)}let m=["get","getKey","getAll","getAllKeys","count"],b=["put","add","delete","clear"],w=new Map;function y(e,t){if(!(e instanceof IDBDatabase&&!(t in e)&&"string"==typeof t))return;if(w.get(t))return w.get(t);let n=t.replace(/FromIndex$/,""),r=t!==n,a=b.includes(n);if(!(n in(r?IDBIndex:IDBObjectStore).prototype)||!(a||m.includes(n)))return;let i=async function(e,...t){let i=this.transaction(e,a?"readwrite":"readonly"),o=i.store;return r&&(o=o.index(t.shift())),(await Promise.all([o[n](...t),a&&i.done]))[0]};return w.set(t,i),i}d=(e=>({...e,get:(t,n,r)=>y(t,n)||e.get(t,n,r),has:(t,n)=>!!y(t,n)||e.has(t,n)}))(d)},49887:(e,t,n)=>{n.d(t,{cY:()=>b,FA:()=>S,g:()=>I,dM:()=>v,u:()=>d,Uj:()=>u,bD:()=>function e(t,n){if(t===n)return!0;let r=Object.keys(t),a=Object.keys(n);for(let i of r){if(!a.includes(i))return!1;let r=t[i],o=n[i];if(E(r)&&E(o)){if(!e(r,o))return!1}else if(r!==o)return!1}for(let e of a)if(!r.includes(e))return!1;return!0},T9:()=>m,Ku:()=>D,zW:()=>w,eX:()=>y});let r=()=>void 0;var a=n(49509);let i=function(e){let t=[],n=0;for(let r=0;r<e.length;r++){let a=e.charCodeAt(r);a<128?t[n++]=a:(a<2048?t[n++]=a>>6|192:((64512&a)==55296&&r+1<e.length&&(64512&e.charCodeAt(r+1))==56320?(a=65536+((1023&a)<<10)+(1023&e.charCodeAt(++r)),t[n++]=a>>18|240,t[n++]=a>>12&63|128):t[n++]=a>>12|224,t[n++]=a>>6&63|128),t[n++]=63&a|128)}return t},o=function(e){let t=[],n=0,r=0;for(;n<e.length;){let a=e[n++];if(a<128)t[r++]=String.fromCharCode(a);else if(a>191&&a<224){let i=e[n++];t[r++]=String.fromCharCode((31&a)<<6|63&i)}else if(a>239&&a<365){let i=e[n++],o=((7&a)<<18|(63&i)<<12|(63&e[n++])<<6|63&e[n++])-65536;t[r++]=String.fromCharCode(55296+(o>>10)),t[r++]=String.fromCharCode(56320+(1023&o))}else{let i=e[n++],o=e[n++];t[r++]=String.fromCharCode((15&a)<<12|(63&i)<<6|63&o)}}return t.join("")},s={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(e,t){if(!Array.isArray(e))throw Error("encodeByteArray takes an array as a parameter");this.init_();let n=t?this.byteToCharMapWebSafe_:this.byteToCharMap_,r=[];for(let t=0;t<e.length;t+=3){let a=e[t],i=t+1<e.length,o=i?e[t+1]:0,s=t+2<e.length,c=s?e[t+2]:0,l=a>>2,u=(3&a)<<4|o>>4,d=(15&o)<<2|c>>6,h=63&c;!s&&(h=64,i||(d=64)),r.push(n[l],n[u],n[d],n[h])}return r.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray(i(e),t)},decodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?atob(e):o(this.decodeStringToByteArray(e,t))},decodeStringToByteArray(e,t){this.init_();let n=t?this.charToByteMapWebSafe_:this.charToByteMap_,r=[];for(let t=0;t<e.length;){let a=n[e.charAt(t++)],i=t<e.length?n[e.charAt(t)]:0,o=++t<e.length?n[e.charAt(t)]:64,s=++t<e.length?n[e.charAt(t)]:64;if(++t,null==a||null==i||null==o||null==s)throw new c;let l=a<<2|i>>4;if(r.push(l),64!==o){let e=i<<4&240|o>>2;if(r.push(e),64!==s){let e=o<<6&192|s;r.push(e)}}}return r},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e,e>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class c extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}let l=function(e){let t=i(e);return s.encodeByteArray(t,!0)},u=function(e){return l(e).replace(/\./g,"")},d=function(e){try{return s.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null},h=()=>(function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==n.g)return n.g;throw Error("Unable to locate global object.")})().__FIREBASE_DEFAULTS__,p=()=>{if(void 0===a||void 0===a.env)return;let e=a.env.__FIREBASE_DEFAULTS__;if(e)return JSON.parse(e)},f=()=>{let e;if("undefined"==typeof document)return;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(e){return}let t=e&&d(e[1]);return t&&JSON.parse(t)},g=()=>{try{return r()||h()||p()||f()}catch(e){console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);return}},m=()=>{var e;return null==(e=g())?void 0:e.config};class b{constructor(){this.reject=()=>{},this.resolve=()=>{},this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}wrapCallback(e){return(t,n)=>{t?this.reject(t):this.resolve(n),"function"==typeof e&&(this.promise.catch(()=>{}),1===e.length?e(t):e(t,n))}}}function w(){try{return"object"==typeof indexedDB}catch(e){return!1}}function y(){return new Promise((e,t)=>{try{let n=!0,r="validate-browser-context-for-indexeddb-analytics-module",a=self.indexedDB.open(r);a.onsuccess=()=>{a.result.close(),n||self.indexedDB.deleteDatabase(r),e(!0)},a.onupgradeneeded=()=>{n=!1},a.onerror=()=>{var e;t((null==(e=a.error)?void 0:e.message)||"")}}catch(e){t(e)}})}function v(){return"undefined"!=typeof navigator&&!!navigator.cookieEnabled}class I extends Error{constructor(e,t,n){super(t),this.code=e,this.customData=n,this.name="FirebaseError",Object.setPrototypeOf(this,I.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,S.prototype.create)}}class S{constructor(e,t,n){this.service=e,this.serviceName=t,this.errors=n}create(e,...t){var n,r;let a=t[0]||{},i=`${this.service}/${e}`,o=this.errors[e],s=o?(n=o,r=a,n.replace(_,(e,t)=>{let n=r[t];return null!=n?String(n):`<${t}?>`})):"Error",c=`${this.serviceName}: ${s} (${i}).`;return new I(i,c,a)}}let _=/\{\$([^}]+)}/g;function E(e){return null!==e&&"object"==typeof e}function C(){}function D(e){return e&&e._delegate?e._delegate:e}},56391:(e,t,n)=>{n.d(t,{h1:()=>s,uA:()=>a});var r=n(49887);class a{constructor(e,t,n){this.name=e,this.instanceFactory=t,this.type=n,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}let i="[DEFAULT]";class o{constructor(e,t){this.name=e,this.container=t,this.component=null,this.instances=new Map,this.instancesDeferred=new Map,this.instancesOptions=new Map,this.onInitCallbacks=new Map}get(e){let t=this.normalizeInstanceIdentifier(e);if(!this.instancesDeferred.has(t)){let e=new r.cY;if(this.instancesDeferred.set(t,e),this.isInitialized(t)||this.shouldAutoInitialize())try{let n=this.getOrInitializeService({instanceIdentifier:t});n&&e.resolve(n)}catch(e){}}return this.instancesDeferred.get(t).promise}getImmediate(e){var t;let n=this.normalizeInstanceIdentifier(null==e?void 0:e.identifier),r=null!=(t=null==e?void 0:e.optional)&&t;if(this.isInitialized(n)||this.shouldAutoInitialize())try{return this.getOrInitializeService({instanceIdentifier:n})}catch(e){if(r)return null;throw e}if(r)return null;throw Error(`Service ${this.name} is not available`)}getComponent(){return this.component}setComponent(e){if(e.name!==this.name)throw Error(`Mismatching Component ${e.name} for Provider ${this.name}.`);if(this.component)throw Error(`Component for ${this.name} has already been provided`);if(this.component=e,this.shouldAutoInitialize()){if("EAGER"===e.instantiationMode)try{this.getOrInitializeService({instanceIdentifier:i})}catch(e){}for(let[e,t]of this.instancesDeferred.entries()){let n=this.normalizeInstanceIdentifier(e);try{let e=this.getOrInitializeService({instanceIdentifier:n});t.resolve(e)}catch(e){}}}}clearInstance(e=i){this.instancesDeferred.delete(e),this.instancesOptions.delete(e),this.instances.delete(e)}async delete(){let e=Array.from(this.instances.values());await Promise.all([...e.filter(e=>"INTERNAL"in e).map(e=>e.INTERNAL.delete()),...e.filter(e=>"_delete"in e).map(e=>e._delete())])}isComponentSet(){return null!=this.component}isInitialized(e=i){return this.instances.has(e)}getOptions(e=i){return this.instancesOptions.get(e)||{}}initialize(e={}){let{options:t={}}=e,n=this.normalizeInstanceIdentifier(e.instanceIdentifier);if(this.isInitialized(n))throw Error(`${this.name}(${n}) has already been initialized`);if(!this.isComponentSet())throw Error(`Component ${this.name} has not been registered yet`);let r=this.getOrInitializeService({instanceIdentifier:n,options:t});for(let[e,t]of this.instancesDeferred.entries())n===this.normalizeInstanceIdentifier(e)&&t.resolve(r);return r}onInit(e,t){var n;let r=this.normalizeInstanceIdentifier(t),a=null!=(n=this.onInitCallbacks.get(r))?n:new Set;a.add(e),this.onInitCallbacks.set(r,a);let i=this.instances.get(r);return i&&e(i,r),()=>{a.delete(e)}}invokeOnInitCallbacks(e,t){let n=this.onInitCallbacks.get(t);if(n)for(let r of n)try{r(e,t)}catch(e){}}getOrInitializeService({instanceIdentifier:e,options:t={}}){var n;let r=this.instances.get(e);if(!r&&this.component&&(r=this.component.instanceFactory(this.container,{instanceIdentifier:(n=e)===i?void 0:n,options:t}),this.instances.set(e,r),this.instancesOptions.set(e,t),this.invokeOnInitCallbacks(r,e),this.component.onInstanceCreated))try{this.component.onInstanceCreated(this.container,e,r)}catch(e){}return r||null}normalizeInstanceIdentifier(e=i){return this.component?this.component.multipleInstances?e:i:e}shouldAutoInitialize(){return!!this.component&&"EXPLICIT"!==this.component.instantiationMode}}class s{constructor(e){this.name=e,this.providers=new Map}addComponent(e){let t=this.getProvider(e.name);if(t.isComponentSet())throw Error(`Component ${e.name} has already been registered with ${this.name}`);t.setComponent(e)}addOrOverwriteComponent(e){this.getProvider(e.name).isComponentSet()&&this.providers.delete(e.name),this.addComponent(e)}getProvider(e){if(this.providers.has(e))return this.providers.get(e);let t=new o(e,this);return this.providers.set(e,t),t}getProviders(){return Array.from(this.providers.values())}}},81880:(e,t,n)=>{n.d(t,{j6:()=>E,om:()=>_,Sx:()=>T,Wp:()=>A,KO:()=>M});var r,a=n(56391);let i=[];!function(e){e[e.DEBUG=0]="DEBUG",e[e.VERBOSE=1]="VERBOSE",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT"}(r||(r={}));let o={debug:r.DEBUG,verbose:r.VERBOSE,info:r.INFO,warn:r.WARN,error:r.ERROR,silent:r.SILENT},s=r.INFO,c={[r.DEBUG]:"log",[r.VERBOSE]:"log",[r.INFO]:"info",[r.WARN]:"warn",[r.ERROR]:"error"},l=(e,t,...n)=>{if(t<e.logLevel)return;let r=new Date().toISOString(),a=c[t];if(a)console[a](`[${r}]  ${e.name}:`,...n);else throw Error(`Attempted to log a message with an invalid logType (value: ${t})`)};class u{constructor(e){this.name=e,this._logLevel=s,this._logHandler=l,this._userLogHandler=null,i.push(this)}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in r))throw TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?o[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,r.DEBUG,...e),this._logHandler(this,r.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,r.VERBOSE,...e),this._logHandler(this,r.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,r.INFO,...e),this._logHandler(this,r.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,r.WARN,...e),this._logHandler(this,r.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,r.ERROR,...e),this._logHandler(this,r.ERROR,...e)}}var d=n(49887),h=n(46984);class p{constructor(e){this.container=e}getPlatformInfoString(){return this.container.getProviders().map(e=>{if(!function(e){let t=e.getComponent();return(null==t?void 0:t.type)==="VERSION"}(e))return null;{let t=e.getImmediate();return`${t.library}/${t.version}`}}).filter(e=>e).join(" ")}}let f="@firebase/app",g="0.13.0",m=new u("@firebase/app"),b="[DEFAULT]",w={[f]:"fire-core","@firebase/app-compat":"fire-core-compat","@firebase/analytics":"fire-analytics","@firebase/analytics-compat":"fire-analytics-compat","@firebase/app-check":"fire-app-check","@firebase/app-check-compat":"fire-app-check-compat","@firebase/auth":"fire-auth","@firebase/auth-compat":"fire-auth-compat","@firebase/database":"fire-rtdb","@firebase/data-connect":"fire-data-connect","@firebase/database-compat":"fire-rtdb-compat","@firebase/functions":"fire-fn","@firebase/functions-compat":"fire-fn-compat","@firebase/installations":"fire-iid","@firebase/installations-compat":"fire-iid-compat","@firebase/messaging":"fire-fcm","@firebase/messaging-compat":"fire-fcm-compat","@firebase/performance":"fire-perf","@firebase/performance-compat":"fire-perf-compat","@firebase/remote-config":"fire-rc","@firebase/remote-config-compat":"fire-rc-compat","@firebase/storage":"fire-gcs","@firebase/storage-compat":"fire-gcs-compat","@firebase/firestore":"fire-fst","@firebase/firestore-compat":"fire-fst-compat","@firebase/ai":"fire-vertex","fire-js":"fire-js",firebase:"fire-js-all"},y=new Map,v=new Map,I=new Map;function S(e,t){try{e.container.addComponent(t)}catch(n){m.debug(`Component ${t.name} failed to register with FirebaseApp ${e.name}`,n)}}function _(e){let t=e.name;if(I.has(t))return m.debug(`There were multiple attempts to register component ${t}.`),!1;for(let n of(I.set(t,e),y.values()))S(n,e);for(let t of v.values())S(t,e);return!0}function E(e,t){let n=e.container.getProvider("heartbeat").getImmediate({optional:!0});return n&&n.triggerHeartbeat(),e.container.getProvider(t)}let C=new d.FA("app","Firebase",{"no-app":"No Firebase App '{$appName}' has been created - call initializeApp() first","bad-app-name":"Illegal App name: '{$appName}'","duplicate-app":"Firebase App named '{$appName}' already exists with different options or config","app-deleted":"Firebase App named '{$appName}' already deleted","server-app-deleted":"Firebase Server App has been deleted","no-options":"Need to provide options, when not being deployed to hosting via source.","invalid-app-argument":"firebase.{$appName}() takes either no argument or a Firebase App instance.","invalid-log-argument":"First argument to `onLog` must be null or a function.","idb-open":"Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.","idb-get":"Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.","idb-set":"Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.","idb-delete":"Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.","finalization-registry-not-supported":"FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.","invalid-server-app-environment":"FirebaseServerApp is not for use in browser environments."});class D{constructor(e,t,n){this._isDeleted=!1,this._options=Object.assign({},e),this._config=Object.assign({},t),this._name=t.name,this._automaticDataCollectionEnabled=t.automaticDataCollectionEnabled,this._container=n,this.container.addComponent(new a.uA("app",()=>this,"PUBLIC"))}get automaticDataCollectionEnabled(){return this.checkDestroyed(),this._automaticDataCollectionEnabled}set automaticDataCollectionEnabled(e){this.checkDestroyed(),this._automaticDataCollectionEnabled=e}get name(){return this.checkDestroyed(),this._name}get options(){return this.checkDestroyed(),this._options}get config(){return this.checkDestroyed(),this._config}get container(){return this._container}get isDeleted(){return this._isDeleted}set isDeleted(e){this._isDeleted=e}checkDestroyed(){if(this.isDeleted)throw C.create("app-deleted",{appName:this._name})}}function k(e,t){let n=(0,d.u)(e.split(".")[1]);if(null===n)return void console.error(`FirebaseServerApp ${t} is invalid: second part could not be parsed.`);if(void 0===JSON.parse(n).exp)return void console.error(`FirebaseServerApp ${t} is invalid: expiration claim could not be parsed`);let r=1e3*JSON.parse(n).exp;r-new Date().getTime()<=0&&console.error(`FirebaseServerApp ${t} is invalid: the token has expired.`)}function A(e,t={}){let n=e;"object"!=typeof t&&(t={name:t});let r=Object.assign({name:b,automaticDataCollectionEnabled:!0},t),i=r.name;if("string"!=typeof i||!i)throw C.create("bad-app-name",{appName:String(i)});if(n||(n=(0,d.T9)()),!n)throw C.create("no-options");let o=y.get(i);if(o)if((0,d.bD)(n,o.options)&&(0,d.bD)(r,o.config))return o;else throw C.create("duplicate-app",{appName:i});let s=new a.h1(i);for(let e of I.values())s.addComponent(e);let c=new D(n,r,s);return y.set(i,c),c}function T(e=b){let t=y.get(e);if(!t&&e===b&&(0,d.T9)())return A();if(!t)throw C.create("no-app",{appName:e});return t}async function O(e){let t=!1,n=e.name;y.has(n)?(t=!0,y.delete(n)):v.has(n)&&0>=e.decRefCount()&&(v.delete(n),t=!0),t&&(await Promise.all(e.container.getProviders().map(e=>e.delete())),e.isDeleted=!0)}function M(e,t,n){var r;let i=null!=(r=w[e])?r:e;n&&(i+=`-${n}`);let o=i.match(/\s|\//),s=t.match(/\s|\//);if(o||s){let e=[`Unable to register library "${i}" with version "${t}":`];o&&e.push(`library name "${i}" contains illegal characters (whitespace or "/")`),o&&s&&e.push("and"),s&&e.push(`version name "${t}" contains illegal characters (whitespace or "/")`),m.warn(e.join(" "));return}_(new a.uA(`${i}-version`,()=>({library:i,version:t}),"VERSION"))}let N="firebase-heartbeat-store",P=null;function j(){return P||(P=(0,h.P2)("firebase-heartbeat-database",1,{upgrade:(e,t)=>{if(0===t)try{e.createObjectStore(N)}catch(e){console.warn(e)}}}).catch(e=>{throw C.create("idb-open",{originalErrorMessage:e.message})})),P}async function B(e){try{let t=(await j()).transaction(N),n=await t.objectStore(N).get(R(e));return await t.done,n}catch(e){if(e instanceof d.g)m.warn(e.message);else{let t=C.create("idb-get",{originalErrorMessage:null==e?void 0:e.message});m.warn(t.message)}}}async function L(e,t){try{let n=(await j()).transaction(N,"readwrite"),r=n.objectStore(N);await r.put(t,R(e)),await n.done}catch(e){if(e instanceof d.g)m.warn(e.message);else{let t=C.create("idb-set",{originalErrorMessage:null==e?void 0:e.message});m.warn(t.message)}}}function R(e){return`${e.name}!${e.options.appId}`}class ${constructor(e){this.container=e,this._heartbeatsCache=null;let t=this.container.getProvider("app").getImmediate();this._storage=new H(t),this._heartbeatsCachePromise=this._storage.read().then(e=>(this._heartbeatsCache=e,e))}async triggerHeartbeat(){var e,t;try{let n=this.container.getProvider("platform-logger").getImmediate().getPlatformInfoString(),r=F();if((null==(e=this._heartbeatsCache)?void 0:e.heartbeats)==null&&(this._heartbeatsCache=await this._heartbeatsCachePromise,(null==(t=this._heartbeatsCache)?void 0:t.heartbeats)==null)||this._heartbeatsCache.lastSentHeartbeatDate===r||this._heartbeatsCache.heartbeats.some(e=>e.date===r))return;if(this._heartbeatsCache.heartbeats.push({date:r,agent:n}),this._heartbeatsCache.heartbeats.length>30){let e=function(e){if(0===e.length)return -1;let t=0,n=e[0].date;for(let r=1;r<e.length;r++)e[r].date<n&&(n=e[r].date,t=r);return t}(this._heartbeatsCache.heartbeats);this._heartbeatsCache.heartbeats.splice(e,1)}return this._storage.overwrite(this._heartbeatsCache)}catch(e){m.warn(e)}}async getHeartbeatsHeader(){var e;try{if(null===this._heartbeatsCache&&await this._heartbeatsCachePromise,(null==(e=this._heartbeatsCache)?void 0:e.heartbeats)==null||0===this._heartbeatsCache.heartbeats.length)return"";let t=F(),{heartbeatsToSend:n,unsentEntries:r}=function(e,t=1024){let n=[],r=e.slice();for(let a of e){let e=n.find(e=>e.agent===a.agent);if(e){if(e.dates.push(a.date),x(n)>t){e.dates.pop();break}}else if(n.push({agent:a.agent,dates:[a.date]}),x(n)>t){n.pop();break}r=r.slice(1)}return{heartbeatsToSend:n,unsentEntries:r}}(this._heartbeatsCache.heartbeats),a=(0,d.Uj)(JSON.stringify({version:2,heartbeats:n}));return this._heartbeatsCache.lastSentHeartbeatDate=t,r.length>0?(this._heartbeatsCache.heartbeats=r,await this._storage.overwrite(this._heartbeatsCache)):(this._heartbeatsCache.heartbeats=[],this._storage.overwrite(this._heartbeatsCache)),a}catch(e){return m.warn(e),""}}}function F(){return new Date().toISOString().substring(0,10)}class H{constructor(e){this.app=e,this._canUseIndexedDBPromise=this.runIndexedDBEnvironmentCheck()}async runIndexedDBEnvironmentCheck(){return!!(0,d.zW)()&&(0,d.eX)().then(()=>!0).catch(()=>!1)}async read(){if(!await this._canUseIndexedDBPromise)return{heartbeats:[]};{let e=await B(this.app);return(null==e?void 0:e.heartbeats)?e:{heartbeats:[]}}}async overwrite(e){var t;if(await this._canUseIndexedDBPromise){let n=await this.read();return L(this.app,{lastSentHeartbeatDate:null!=(t=e.lastSentHeartbeatDate)?t:n.lastSentHeartbeatDate,heartbeats:e.heartbeats})}}async add(e){var t;if(await this._canUseIndexedDBPromise){let n=await this.read();return L(this.app,{lastSentHeartbeatDate:null!=(t=e.lastSentHeartbeatDate)?t:n.lastSentHeartbeatDate,heartbeats:[...n.heartbeats,...e.heartbeats]})}}}function x(e){return(0,d.Uj)(JSON.stringify({version:2,heartbeats:e})).length}_(new a.uA("platform-logger",e=>new p(e),"PRIVATE")),_(new a.uA("heartbeat",e=>new $(e),"PRIVATE")),M(f,g,""),M(f,g,"esm2017"),M("fire-js","")}}]);