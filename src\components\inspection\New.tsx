"use client";

import HeaderSeven from "@/layouts/headers/HeaderSeven";
import React, { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import API from "@/services/API"; // Import API service
import { FILE_DOWNLOAD, OBSERVATION_ARCHIVED_URL, USERS_URL } from "@/constant"; // Your API endpoint
import { Modal, Button } from "react-bootstrap"; // Import Bootstrap Modal
import moment from 'moment'
import ImageComponent from "@/services/FileDownlodS3";
import MyLogoComponent from "@/services/MyLogoComponet";
import axios from "axios";
import ViewInspection from "./ViewInspection";



interface User {
    id: string | number;
    firstName: string;
}
interface ObservationAction {
    actionType: string;
    // Add more fields if necessary
}
const New: React.FC = () => {
    const router = useRouter();
    const user = useSelector((state: RootState) => state.login.user);
    const [activeTab, setActiveTab] = useState("reported");
    let k = 0;

    // Check if user has ins_inspection_assignee role
    const isInspectionAssignee = useMemo(() => {
        return user && (user as any)?.roles?.some((role: any) => ['ins_inspection_assignee'].includes(role.maskId));
    }, [user]);
    // State for Archived Data
    const [archivedData, setArchivedData] = useState<any[]>([]);
    const [inspectionData, setInspectionData] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [reportData, setReportData] = useState<any | null>(null);
    const [showModal, setShowModal] = useState(false);
    const [users, setUsers] = useState([])
    const [logo, setLogo] = useState('')
    // Function to Fetch Archived Data
    const fetchArchivedData = async () => {
        setLoading(true);
        setError(null);
        try {
            const uriString = {

                // where: {
                //     status: { neq: 'Scheduled' } // or use $ne if your backend expects Mongo-style
                // },
                include: [
                    { relation: "locationOne" },
                    { relation: "locationTwo" },
                    { relation: "locationThree" },
                    { relation: "locationFour" },
                    { relation: "locationFive" },
                    { relation: "locationSix" },
                    { relation: "checklist" },
                    { relation: "inspector" },
                    { relation: 'assignedBy' }

                ]
            };

            const url = `/my-scheduled-inspections?filter=` + encodeURIComponent(JSON.stringify(uriString));
            const response = await API.get(url);
            if (response.status === 200) {
                setArchivedData(response.data.reverse()); // Assuming response.data is an array
            }
        } catch (err) {
            setError("Failed to fetch archived data.");
            console.error("Error fetching archived data:", err);
        } finally {
            setLoading(false);
        }
    };

    const fetchInspectionData = async () => {
        setLoading(true);
        setError(null);
        try {
            const uriString = {

                // where: {
                //     status: { neq: 'Scheduled' } // or use $ne if your backend expects Mongo-style
                // },
                include: [
                    { relation: "locationOne" },
                    { relation: "locationTwo" },
                    { relation: "locationThree" },
                    { relation: "locationFour" },
                    { relation: "locationFive" },
                    { relation: "locationSix" },
                    { relation: "checklist" },
                    { relation: "inspector" },
                    { relation: 'assignedBy' }

                ]
            };

            const url = `/my-inspections?filter=` + encodeURIComponent(JSON.stringify(uriString));
            const response = await API.get(url);
            if (response.status === 200) {
                setInspectionData(response.data.reverse()); // Assuming response.data is an array

            }
        } catch (err) {
            setError("Failed to fetch archived data.");
            console.error("Error fetching archived data:", err);
        } finally {
            setLoading(false);
        }
    };

    // Fetch data when Archived tab is selected
    useEffect(() => {
        if (activeTab === "reported") {
            fetchArchivedData();
            getAllUsers()
        }
        else if (activeTab === "draft") {
            getAllUsers()
            fetchInspectionData();
        }
    }, [activeTab]);

    const getAllUsers = async () => {
        const response = await API.get(USERS_URL);
        setUsers(response.data)
    }
    useEffect(() => {
        getFetchLogo()
    }, [])
    const getFetchLogo = async () => {
        try {
            const response = await API.get(FILE_DOWNLOAD(localStorage.getItem('logo')), {
                headers: {
                    'Content-Type': 'application/json'
                },
            });
            const data = response.data;
            const logoUrl = data // Assuming the API returns an object with a `url` field
            setLogo(logoUrl);

        } catch (error) {
            console.error('Error fetching logo:', error);
        }
    }
    const handleObservationClick = (obs: any) => {
        setReportData(obs);
        setShowModal(true);
    };

    return (
        <>
            <div className="pt-3"></div>
            <div className="container direction-rtl">

                {/* Assign Inspection - Only show if user has ins_inspection_assignee role */}
                {isInspectionAssignee && (
                    <div className="card shadow-sm p-3 bg-white rounded d-flex align-items-center flex-row"
                        style={{ cursor: "pointer" }}
                        onClick={() => router.push("/inspection/new")}
                    >
                        <div className="icon-container bg-danger text-white rounded-circle d-flex align-items-center justify-content-center" style={{ width: "50px", height: "50px" }}>
                            <i className="bi bi-file-earmark-plus fs-3"></i>
                        </div>
                        <div className="flex-grow-1 ms-3">
                            <h6 className="mb-0">Assign Inspection</h6>
                        </div>
                        <i className="bi bi-chevron-right text-secondary fs-5"></i>
                    </div>
                )}

                {/* Tab Navigation */}
                <div className="standard-tab mt-4">
                    <ul className="nav nav-pills rounded-pill mb-3 p-2 shadow-sm d-flex justify-content-around bg-light" role="tablist">
                        {[
                            { id: "draft", label: "Inspections" },
                            { id: "reported", label: "Scheduled" }
                        ].map((tab) => (
                            <li className="nav-item flex-grow-1 text-center" role="presentation" key={tab.id}>
                                <button
                                    className={`btn w-100 rounded-pill ${activeTab === tab.id ? "btn-danger text-white" : "btn-light text-dark"}`}
                                    onClick={() => setActiveTab(tab.id)}
                                >
                                    {tab.label}
                                </button>
                            </li>
                        ))}
                    </ul>

                    {/* Tab Content */}
                    <div className="tab-content rounded-lg p-3 shadow-sm ">
                        {/* Draft Tab */}
                        {activeTab === "draft" && (
                            <div className="scroll-container">
                                {/* <h6>Archived Observations</h6> */}

                                {/* Loading State */}
                                {loading && <p className="text-primary">Loading...</p>}

                                {/* Error State */}
                                {error && <p className="text-danger">{error}</p>}

                                {/* Archived Data List */}
                                {archivedData.length > 0 ? (
                                    <ul className="list-group">
                                        {archivedData.map((obs, index) => (
                                            <div key={index} className="card shadow-sm p-3 mb-3 rounded bg-white" style={{ cursor: "pointer" }}
                                                onClick={() => handleObservationClick(obs)}>
                                                <div className="d-flex justify-content-between align-items-center">
                                                    <span className="text-muted">#{obs.maskId}</span>
                                                    <span className=" small badge bg-danger text-white">{obs.inspectionCategory}</span>
                                                </div>

                                                <h6 className="mt-2 mb-3">{obs.checklist?.name} - {obs.checklist?.version}</h6>
                                                <p className=" text-primary mb-2">{obs.status}</p>

                                                <div className="row text-muted small mb-2">
                                                    <div className="col">
                                                        <strong>Scheduled:</strong>{" "}
                                                        {obs.scheduledDate ? new Date(obs.scheduledDate).toLocaleDateString() : "N/A"}
                                                    </div>
                                                    <div className="col">
                                                        <strong>Due:</strong>{" "}
                                                        {obs.dueDate ? new Date(obs.dueDate).toLocaleDateString() : "N/A"}
                                                    </div>
                                                </div>

                                                <div className="d-flex justify-content-between align-items-center mt-2">
                                                    <small className="text-danger fw-bold">
                                                        Reported on {new Date(obs.created).toLocaleString()}
                                                    </small>
                                                    <span
                                                        className={`badge ${obs.observationType === "Safe" ? "bg-success" : "bg-danger"} text-white`}
                                                    >
                                                        {obs.observationType}
                                                    </span>
                                                </div>
                                            </div>
                                        ))}
                                    </ul>
                                ) : (
                                    !loading && <p>No archived observations found.</p>
                                )}
                            </div>
                        )}

                        {/* Archived Tab */}
                        {activeTab === "reported" && (
                            <div className="scroll-container">
                                {/* <h6>Archived Observations</h6> */}

                                {/* Loading State */}
                                {loading && <p className="text-primary">Loading...</p>}

                                {/* Error State */}
                                {error && <p className="text-danger">{error}</p>}

                                {/* Archived Data List */}
                                {archivedData.length > 0 ? (
                                    <ul className="list-group">
                                        {archivedData.map((obs, index) => (
                                            <div key={index} className="card shadow-sm p-3 mb-3 rounded bg-white" style={{ cursor: "pointer" }}
                                                onClick={() => handleObservationClick(obs)}>
                                                <div className="d-flex justify-content-between align-items-center">
                                                    <span className="text-muted">#{obs.maskId}</span>
                                                    <span className=" small badge bg-danger text-white">{obs.inspectionCategory}</span>
                                                </div>

                                                <h6 className="mt-2 mb-3">{obs.checklist?.name} - {obs.checklist?.version}</h6>
                                                <p className=" text-primary mb-2">{obs.status}</p>

                                                <div className="row text-muted small mb-2">
                                                    <div className="col">
                                                        <strong>Scheduled:</strong>{" "}
                                                        {obs.scheduledDate ? new Date(obs.scheduledDate).toLocaleDateString() : "N/A"}
                                                    </div>
                                                    <div className="col">
                                                        <strong>Due:</strong>{" "}
                                                        {obs.dueDate ? new Date(obs.dueDate).toLocaleDateString() : "N/A"}
                                                    </div>
                                                </div>

                                                <div className="d-flex justify-content-between align-items-center mt-2">
                                                    <small className="text-danger fw-bold">
                                                        Reported on {new Date(obs.created).toLocaleString()}
                                                    </small>
                                                    <span
                                                        className={`badge ${obs.observationType === "Safe" ? "bg-success" : "bg-danger"} text-white`}
                                                    >
                                                        {obs.observationType}
                                                    </span>
                                                </div>
                                            </div>
                                        ))}
                                    </ul>
                                ) : (
                                    !loading && <p>No archived observations found.</p>
                                )}
                            </div>
                        )}
                    </div>
                </div>

                {/* Styles */}
                <style jsx>{`
                    .btn {
                        transition: all 0.3s ease-in-out;
                    }
                    .btn-light:hover {
                        background-color: #f8d7da;
                    }
                    .tab-content {
                        border: 1px solid #ddd;
                    }
                        .scroll-container {
                    max-height: 450px; /* Adjust height as needed */
                    overflow-y: auto;
                    padding-right: 10px; /* Avoid scrollbar overlaying content */
                }
                `}</style>

                {/* Observation Details Modal */}
                <Modal show={showModal} onHide={() => setShowModal(false)} centered>
                    <Modal.Header closeButton>
                        {reportData && (
                            <div className="row" style={{ width: '100%' }}>
                                <div className="col-12">
                                    <div className="row">
                                        <div  >
                                            {/* <img src={logo} className="me-3" alt="logo" style={{ maxWidth: '125px' }} /> */}
                                            <MyLogoComponent logo={logo} />
                                        </div>
                                        <div className="col">
                                            <h6>Inspection</h6>
                                            <div className="d-flex align-items-center">
                                                <p className="me-2">#{reportData.maskId || ''} </p>
                                                <p className=" badge bg-primary text-white">{reportData.status} </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                            </div>
                        )}
                    </Modal.Header>
                    <Modal.Body>
                        <ViewInspection reportData={reportData} type ={"view"}/>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={() => setShowModal(false)}>Close</Button>
                    </Modal.Footer>
                </Modal>

                <style jsx>{`
        .observation-report {
          background: #fff;
          border-radius: 10px;
          padding: 20px;
        }
        .section-title {
          font-size: 1.1rem;
          font-weight: bold;
          color: #333;
        }
        .obs-title {
          font-size: 0.9rem;
          font-weight: bold;
          color: #555;
          margin-bottom: 5px;
        }
        .obs-content {
          font-size: 0.9rem;
          color: #777;
        }
        .image-box {
          border: 1px solid #ddd;
          background: #f8f9fa;
          height: 100px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      `}</style>

            </div>
        </>
    );
};

export default New;
