(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8620],{12734:(e,s,t)=>{"use strict";t.d(s,{default:()=>c});var a=t(95155),i=t(12115),l=t(34540),n=t(15123),r=t(99376),o=t(66215);let c=()=>{let[e,s]=(0,i.useState)(!1),[t,c]=(0,i.useState)(""),[d,m]=(0,i.useState)("success"),[x,h]=(0,i.useState)(!1),p="fcm_notification_dismissed",u="fcm_enabled_notification_shown",b="fcm_prompt_shown_today",g=(0,l.d4)(e=>e.login.isLogin),f=()=>"true"===localStorage.getItem(p),j=()=>{let e=localStorage.getItem(u);return!!e&&e===new Date().toDateString()},N=()=>{let e=localStorage.getItem(b);return!!e&&e===new Date().toDateString()},y=()=>{localStorage.setItem(p,"true")},v=()=>{let e=new Date().toDateString();localStorage.setItem(u,e)},A=()=>{let e=new Date().toDateString();localStorage.setItem(b,e)},k=()=>{localStorage.removeItem(p),localStorage.removeItem(u),localStorage.removeItem(b),console.log("\uD83D\uDD04 Notification preferences reset. Reload the page to see prompts again.")};i.useEffect(()=>{window.resetFCMNotifications=k},[]),(0,i.useEffect)(()=>{console.log("\uD83D\uDD0D PostLoginFCMInitializer - Login state check:"),console.log("  - isLoggedIn (Redux):",g),console.log("  - isInitialized:",x),console.log("  - access_token exists:",!!localStorage.getItem("access_token"));let e=!!localStorage.getItem("access_token"),s=(g||e)&&!x;console.log("  - shouldInitialize:",s),s&&(console.log("\uD83D\uDE80 Triggering FCM initialization after login..."),w(),h(!0))},[g,x]),(0,i.useEffect)(()=>{(()=>{let e=!!localStorage.getItem("access_token");console.log("\uD83D\uDD0D Initial login state check on mount:"),console.log("  - access_token exists:",e),console.log("  - Redux isLoggedIn:",g),e&&!x&&(console.log("\uD83D\uDE80 User already logged in, initializing FCM..."),setTimeout(()=>{w(),h(!0)},1e3))})()},[]);let w=async()=>{try{if(console.log("\uD83D\uDE80 Initializing FCM after successful login..."),!localStorage.getItem("access_token"))return void console.log("⚠️ No access token found, skipping FCM initialization");await n.A.initialize();let e=await n.A.getStoredToken();if(e)console.log("✅ Existing FCM token found:",e.substring(0,50)+"..."),await n.A.sendTokenToServer(e),j()||(T("Push notifications are already enabled!","success"),v());else{if(!("Notification"in window))return void console.log("⚠️ Push notifications not supported in this browser");let e=Notification.permission;"granted"===e?await S():"default"===e?f()||N()||setTimeout(()=>{C(),A()},2e3):(console.log("⚠️ Notification permission denied"),T("Push notifications are disabled. You can enable them in Settings.","warning"))}}catch(e){console.error("❌ Error initializing FCM after login:",e)}},S=async()=>{try{console.log("\uD83D\uDD04 Generating FCM token silently..."),await n.A.generateFCMToken()&&!j()&&(T("Push notifications enabled successfully!","success"),v())}catch(e){console.error("❌ Error generating FCM token:",e)}},C=()=>{c("Welcome! Would you like to enable push notifications to stay updated with important alerts and messages?"),m("warning"),s(!0),setTimeout(()=>{s(!1)},15e3)},F=async()=>{try{s(!1),console.log("\uD83D\uDD04 User requested to enable notifications..."),await n.A.generateFCMToken()?(T("Push notifications enabled successfully!","success"),v(),localStorage.removeItem(p)):T("Failed to enable push notifications. Please try again.","danger")}catch(e){console.error("❌ Error enabling notifications:",e),T("Failed to enable push notifications. Please try again.","danger")}},T=(e,t)=>{c(e),m(t),s(!0),"success"===t&&setTimeout(()=>{s(!1)},5e3)};return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(r.A,{position:"top-center",className:"modern-notification-toast",style:{position:"fixed",top:"20px",left:"50%",transform:"translateX(-50%)",zIndex:1060,width:"100%",maxWidth:"420px",padding:"0 16px"},children:(0,a.jsxs)(o.A,{show:e,onClose:()=>s(!1),className:"modern-toast-card",style:{width:"100%",border:"none",borderRadius:"16px",boxShadow:"0 10px 40px rgba(0, 0, 0, 0.1), 0 4px 16px rgba(0, 0, 0, 0.05)",background:"#ffffff",color:"#1f2937",overflow:"hidden"},children:[(0,a.jsx)(o.A.Header,{closeButton:!0,style:{background:"success"===d?"#f0fdf4":"warning"===d?"#fffbeb":"danger"===d?"#fef2f2":"#f0f9ff",border:"none",borderRadius:"16px 16px 0 0",padding:"16px 20px 12px 20px",borderBottom:"1px solid ".concat("success"===d?"#dcfce7":"warning"===d?"#fef3c7":"danger"===d?"#fecaca":"#dbeafe")},className:"modern-toast-header",children:(0,a.jsxs)("div",{className:"d-flex align-items-center",children:[(0,a.jsx)("div",{className:"toast-icon-wrapper me-3",style:{width:"40px",height:"40px",borderRadius:"12px",background:"success"===d?"#22c55e":"warning"===d?"#f59e0b":"danger"===d?"#ef4444":"#3b82f6",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,a.jsx)("i",{className:"bi ".concat("success"===d?"bi-check-circle-fill":"warning"===d?"bi-bell-fill":"danger"===d?"bi-exclamation-triangle-fill":"bi-bell-fill"),style:{fontSize:"20px",color:"white"}})}),(0,a.jsx)("div",{children:(0,a.jsx)("strong",{className:"toast-title",style:{fontSize:"16px",fontWeight:"600",color:"success"===d?"#166534":"warning"===d?"#92400e":"danger"===d?"#991b1b":"#1e40af"},children:"success"===d?"Success!":"warning"===d?"Enable Notifications":"danger"===d?"Error":"Notifications"})})]})}),(0,a.jsxs)(o.A.Body,{style:{padding:"20px",background:"#ffffff"},children:[(0,a.jsx)("p",{className:"mb-0",style:{fontSize:"15px",lineHeight:"1.6",color:"#6b7280",marginBottom:"warning"===d?"16px":"0"},children:t}),"warning"===d&&(0,a.jsxs)("div",{className:"mt-3 d-flex flex-column gap-2",children:[(0,a.jsxs)("button",{className:"btn modern-primary-btn",onClick:F,style:{background:"#3b82f6",border:"1px solid #3b82f6",color:"white",borderRadius:"12px",padding:"12px 20px",fontSize:"15px",fontWeight:"600",transition:"all 0.2s ease",boxShadow:"0 2px 8px rgba(59, 130, 246, 0.2)"},onMouseEnter:e=>{e.currentTarget.style.background="#2563eb",e.currentTarget.style.borderColor="#2563eb",e.currentTarget.style.transform="translateY(-1px)",e.currentTarget.style.boxShadow="0 4px 12px rgba(59, 130, 246, 0.3)"},onMouseLeave:e=>{e.currentTarget.style.background="#3b82f6",e.currentTarget.style.borderColor="#3b82f6",e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="0 2px 8px rgba(59, 130, 246, 0.2)"},children:[(0,a.jsx)("i",{className:"bi bi-bell me-2"}),"Enable Notifications"]}),(0,a.jsx)("button",{className:"btn modern-secondary-btn",onClick:()=>{s(!1),y()},style:{background:"#f9fafb",border:"1px solid #e5e7eb",color:"#6b7280",borderRadius:"12px",padding:"12px 20px",fontSize:"14px",fontWeight:"500",transition:"all 0.2s ease"},onMouseEnter:e=>{e.currentTarget.style.background="#f3f4f6",e.currentTarget.style.borderColor="#d1d5db",e.currentTarget.style.color="#374151"},onMouseLeave:e=>{e.currentTarget.style.background="#f9fafb",e.currentTarget.style.borderColor="#e5e7eb",e.currentTarget.style.color="#6b7280"},children:"Maybe Later"})]})]})]})})})}},15123:(e,s,t)=>{"use strict";t.d(s,{A:()=>m});var a=t(23915),i=t(20996);let l={apiKey:"AIzaSyCvHAIHZPnzl9xMJu9NpPQ8J-iL8zKDh9k",authDomain:"pwa-superapp.firebaseapp.com",projectId:"pwa-superapp",storageBucket:"pwa-superapp.firebasestorage.app",messagingSenderId:"596753047002",appId:"1:596753047002:web:e809373bd1190e6a8bf4e3"},n=(0,a.Wp)(l),r=null,o=async()=>{try{console.log("\uD83D\uDD04 Checking Firebase messaging support..."),console.log("\uD83D\uDD0D Firebase config check:"),console.log("  - API Key:",l.apiKey?"Set":"Missing"),console.log("  - Project ID:",l.projectId),console.log("  - Sender ID:",l.messagingSenderId),console.log("  - App ID:",l.appId?"Set":"Missing");let e=await (0,i.TT)();if(console.log("\uD83D\uDD0D Firebase messaging supported:",e),!e)return console.log("❌ Firebase messaging is not supported in this browser"),console.log("This could be due to:"),console.log("  - Browser doesn't support service workers"),console.log("  - Browser doesn't support push notifications"),console.log("  - Running in incognito/private mode"),null;if(console.log("\uD83D\uDD04 Initializing Firebase messaging..."),"serviceWorker"in navigator)try{await navigator.serviceWorker.ready,console.log("✅ Service worker is ready for Firebase messaging")}catch(e){console.warn("⚠️ Service worker not ready:",e)}return r=(0,i.dG)(n),console.log("✅ Firebase messaging initialized successfully"),console.log("  - Messaging instance:",!!r),r}catch(e){if(console.error("❌ Error initializing Firebase messaging:",e),console.error("Error details:",{name:e.name,message:e.message,code:e.code}),e.code)switch(e.code){case"app/invalid-api-key":console.error("\uD83D\uDD0D Invalid Firebase API key");break;case"app/invalid-app-id":console.error("\uD83D\uDD0D Invalid Firebase App ID");break;case"messaging/unsupported-browser":console.error("\uD83D\uDD0D Browser not supported for messaging");break;default:console.error("\uD83D\uDD0D Unknown Firebase error:",e.code)}return null}},c="BDukpatijXC4YB_g4W7FvW-UjBfYtMAM7epw7MJFkcb4ICU8udWq7UYAQlk-hFJ9WmZ-EutlgybC5-HIAkaMSnU";class d{async initialize(){if(this.isInitialized)return console.log("✅ FCM Service already initialized"),this.messaging;try{return console.log("\uD83D\uDD04 Initializing FCM Service..."),console.log("Browser environment check:",!0),console.log("Notification support:","Notification"in window),console.log("Service Worker support:","serviceWorker"in navigator),this.messaging=await o(),this.isInitialized=!0,this.messaging?(this.setupForegroundMessageListener(),console.log("✅ FCM Service initialized successfully"),console.log("Messaging instance:",!!this.messaging)):console.log("⚠️ FCM messaging not supported or failed to initialize"),this.messaging}catch(e){return console.error("❌ Error initializing FCM service:",e),console.error("Error details:",{name:e.name,message:e.message,code:e.code}),null}}async requestNotificationPermission(){try{let e=await Notification.requestPermission();if("granted"===e)return console.log("✅ Notification permission granted"),!0;return console.log("❌ Notification permission denied"),!1}catch(e){return console.error("Error requesting notification permission:",e),!1}}async generateFCMToken(){console.log("\uD83D\uDD04 Starting FCM token generation..."),console.log("\uD83D\uDD0D Environment check:"),console.log("  - Window available:",!0),console.log("  - HTTPS/Localhost:","https:"===window.location.protocol||"localhost"===window.location.hostname),console.log("  - Current URL:",window.location.href),console.log("\uD83D\uDD0D Environment variables:"),console.log("  - API Key:","Set"),console.log("  - Project ID:","pwa-superapp"),console.log("  - Sender ID:","596753047002"),console.log("  - VAPID Key:","Set");try{if(this.messaging||(console.log("\uD83D\uDD04 Step 1: Initializing FCM messaging..."),await this.initialize()),!this.messaging)return console.error("❌ Step 1 FAILED: FCM messaging not available"),console.error("This usually means:"),console.error("  - Firebase configuration is incorrect"),console.error("  - Browser doesn't support FCM"),console.error("  - Network connectivity issues"),null;if(console.log("✅ Step 1: FCM messaging initialized"),console.log("\uD83D\uDD04 Step 2: Checking service worker..."),"serviceWorker"in navigator)try{let e=await navigator.serviceWorker.ready;console.log("✅ Step 2: Service Worker is ready"),console.log("  - Scope:",e.scope),console.log("  - Active:",!!e.active)}catch(e){console.warn("⚠️ Step 2: Service Worker not ready:",e)}else console.warn("⚠️ Step 2: Service Worker not supported");if(console.log("\uD83D\uDD04 Step 3: Checking notification permission..."),console.log("  - Current permission:",Notification.permission),!await this.requestNotificationPermission())return console.log("❌ Step 3 FAILED: Cannot generate token without notification permission"),null;console.log("✅ Step 3: Notification permission granted"),console.log("\uD83D\uDD04 Step 4: Generating FCM token..."),console.log("  - Using VAPID key:",c.substring(0,20)+"..."),console.log("  - Messaging instance type:",typeof this.messaging);let e=await (0,i.gf)(this.messaging,{vapidKey:c});if(e)return console.log("✅ Step 4: FCM Token generated successfully!"),console.log("  - Token length:",e.length),console.log("  - Token preview:",e.substring(0,50)+"..."),console.log("  - Full token:",e),localStorage.setItem("fcm_token",e),console.log("✅ Token stored in localStorage"),console.log("\uD83D\uDD04 Step 5: Sending token to server..."),await this.sendTokenToServer(e),e;return console.log("❌ Step 4 FAILED: No registration token available"),console.log("This could be due to:"),console.log("  - Browser blocking notifications"),console.log("  - Invalid VAPID key"),console.log("  - Firebase configuration issues"),console.log("  - Network connectivity issues"),console.log("  - Service worker not properly configured"),null}catch(e){if(console.error("❌ CRITICAL ERROR in FCM token generation:",e),console.error("Error details:",{name:e.name,message:e.message,code:e.code,stack:e.stack}),e.code)switch(e.code){case"messaging/unsupported-browser":console.error("\uD83D\uDD0D Browser not supported for FCM");break;case"messaging/permission-blocked":console.error("\uD83D\uDD0D Notification permission blocked");break;case"messaging/vapid-key-required":console.error("\uD83D\uDD0D VAPID key is required but missing");break;case"messaging/invalid-vapid-key":console.error("\uD83D\uDD0D VAPID key is invalid");break;default:console.error("\uD83D\uDD0D Unknown Firebase error code:",e.code)}return null}}async sendTokenToServer(e){try{let s=localStorage.getItem("access_token")||localStorage.getItem("token")||localStorage.getItem("authToken");if(!s)return void console.log("⚠️ No authorization token found, skipping token registration");let t=await fetch("".concat("https://client-api.acuizen.com","/users/me"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)},body:JSON.stringify({deviceToken:e})});if(t.ok){console.log("✅ FCM token sent to server successfully");let e=await t.json();console.log("Server response:",e)}else{let e=await t.text();console.log("⚠️ Failed to send FCM token to server:",t.status,e)}}catch(e){console.error("❌ Error sending token to server:",e)}}setupForegroundMessageListener(){this.messaging&&(0,i.xD)(this.messaging,e=>{console.log("\uD83D\uDCF1 Foreground message received:",e),this.showNotification(e)})}showNotification(e){let{notification:s,data:t}=e;if(s){let e=s.title||"New Notification",a={body:s.body||"",icon:s.icon||"/assets/icons/Icon-192.png",badge:"/assets/icons/Icon-72.png",tag:(null==t?void 0:t.tag)||"default",data:t||{},requireInteraction:!0,actions:[{action:"view",title:"View",icon:"/assets/icons/Icon-72.png"},{action:"dismiss",title:"Dismiss"}]};"serviceWorker"in navigator&&"showNotification"in ServiceWorkerRegistration.prototype?navigator.serviceWorker.ready.then(s=>{s.showNotification(e,a)}):new Notification(e,a)}}async getStoredToken(){return localStorage.getItem("fcm_token")}async refreshToken(){return localStorage.removeItem("fcm_token"),await this.generateFCMToken()}async deleteToken(){try{this.messaging&&(localStorage.removeItem("fcm_token"),console.log("✅ FCM token cleared from local storage"))}catch(e){console.error("Error deleting FCM token:",e)}}constructor(){this.messaging=null,this.isInitialized=!1}}let m=new d},37759:(e,s,t)=>{"use strict";t.d(s,{A:()=>l,X:()=>i});let a=(0,t(51990).Z0)({name:"service",initialState:{loading:!1,service:[]},reducers:{setService(e,s){e.service=s.payload}}}),i=a.actions,l=a},48916:(e,s,t)=>{Promise.resolve().then(t.bind(t,70406)),Promise.resolve().then(t.bind(t,12734))},53686:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(95155);t(12115);var i=t(82940),l=t.n(i),n=t(65677);let r=e=>{var s,t;let{reportData:i}=e;return(0,a.jsxs)("div",{className:"observation-report",style:{background:"transparent",padding:"0"},children:["High-Risk Hazard"!==i.type?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"card border-0 mb-3",style:{backgroundColor:"#f8fafc"},children:(0,a.jsxs)("div",{className:"card-body p-3",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-2",children:[(0,a.jsx)("i",{className:"bi bi-briefcase text-primary"}),(0,a.jsx)("h6",{className:"section-title mb-0",children:"Work Activity"})]}),(0,a.jsx)("p",{className:"obs-dec text-muted mb-0",children:"Routine"===i.type?null==(s=i.workActivity)?void 0:s.name:i.description})]})}),(0,a.jsx)("div",{className:"card border-0 mb-3",style:{backgroundColor:"#f8fafc"},children:(0,a.jsxs)("div",{className:"card-body p-3",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-2",children:[(0,a.jsx)("i",{className:"bi bi-building text-primary"}),(0,a.jsx)("h6",{className:"section-title mb-0",children:"Department"})]}),(0,a.jsx)("p",{className:"obs-dec text-muted mb-0",children:"Routine"===i.type?null==(t=i.department)?void 0:t.name:i.description})]})})]}):(0,a.jsx)("div",{className:"card border-0 mb-3",style:{backgroundColor:"#fef2f2"},children:(0,a.jsxs)("div",{className:"card-body p-3",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-2",children:[(0,a.jsx)("i",{className:"bi bi-exclamation-triangle text-danger"}),(0,a.jsx)("h6",{className:"section-title mb-0",children:"Hazard Name"})]}),(0,a.jsx)("p",{className:"obs-dec text-muted mb-0",children:i.hazardName})]})}),(0,a.jsxs)("div",{className:"row g-3 mb-3",children:[(0,a.jsx)("div",{className:"col-md-6",children:(0,a.jsx)("div",{className:"card border-0",style:{backgroundColor:"#f0f9ff"},children:(0,a.jsxs)("div",{className:"card-body p-3",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-2",children:[(0,a.jsx)("i",{className:"bi bi-tag text-info"}),(0,a.jsx)("h6",{className:"obs-title mb-0",children:"Type"})]}),(0,a.jsx)("p",{className:"obs-content mb-0",children:i.type})]})})}),(0,a.jsx)("div",{className:"col-md-6",children:(0,a.jsx)("div",{className:"card border-0",style:{backgroundColor:"#f0f9ff"},children:(0,a.jsxs)("div",{className:"card-body p-3",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-2",children:[(0,a.jsx)("i",{className:"bi bi-calendar3 text-info"}),(0,a.jsx)("h6",{className:"obs-title mb-0",children:"Reported Date"})]}),(0,a.jsx)("p",{className:"obs-content mb-0",children:i.created?l()(i.created).format("Do MMM YYYY, hh:mm:ss a"):"N/A"})]})})})]}),"High-Risk Hazard"!==i.type?(0,a.jsxs)("div",{className:"",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-3",children:[(0,a.jsx)("i",{className:"bi bi-list-check text-primary"}),(0,a.jsx)("h5",{className:"mb-0 fw-semibold",children:"Risk Assessment Tasks"})]}),(0,a.jsx)(n.A,{children:i.tasks.map((e,s)=>(0,a.jsxs)(n.A.Item,{eventKey:s.toString(),className:"mb-3 border-0 shadow-sm",style:{borderRadius:"12px",overflow:"hidden"},children:[(0,a.jsx)(n.A.Header,{style:{borderRadius:"12px 12px 0 0"},children:(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2",children:[(0,a.jsx)("span",{className:"badge bg-primary text-white",style:{fontSize:"10px"},children:s+1}),e[0].name||"Sub Activity ".concat(s+1)]})}),(0,a.jsxs)(n.A.Body,{style:{backgroundColor:"#fafbfc"},children:[(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)("div",{className:"card border-0 mb-3",style:{backgroundColor:"#fff7ed"},children:(0,a.jsxs)("div",{className:"card-body p-3",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-3",children:[(0,a.jsx)("i",{className:"bi bi-exclamation-triangle text-warning"}),(0,a.jsx)("h6",{className:"obs-title mb-0",children:"Identified Hazards"})]}),e[1].selected.map((e,s)=>(0,a.jsx)("div",{className:"card border-0 mb-2 shadow-sm",children:(0,a.jsx)("div",{className:"card-body p-3",children:(0,a.jsxs)("div",{className:"d-flex align-items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 rounded",style:{backgroundColor:"#f3f4f6"},children:(0,a.jsx)("img",{src:"https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/".concat(e.image),style:{height:32,width:32,objectFit:"contain"},alt:"hazard"})}),(0,a.jsx)("div",{className:"flex-grow-1",children:(0,a.jsx)("p",{className:"mb-0 fw-medium",children:e.name})})]})})},s))]})})}),(0,a.jsxs)("div",{className:"row g-3",children:[(0,a.jsx)("div",{className:"col-md-6",children:(0,a.jsx)("div",{className:"card border-0",style:{backgroundColor:"#fef2f2"},children:(0,a.jsxs)("div",{className:"card-body p-3",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-3",children:[(0,a.jsx)("i",{className:"bi bi-exclamation-circle text-danger"}),(0,a.jsx)("h6",{className:"obs-title mb-0",children:"Consequences"})]}),e[2].option.map((e,s)=>(0,a.jsx)("div",{className:"card border-0 mb-2",style:{backgroundColor:"rgba(255,255,255,0.7)"},children:(0,a.jsx)("div",{className:"card-body p-2",children:(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2",children:[(0,a.jsx)("span",{className:"badge bg-danger text-white",style:{fontSize:"9px"},children:e.current_type}),(0,a.jsx)("span",{className:"small",children:e.value})]})})},s))]})})}),(0,a.jsx)("div",{className:"col-md-6",children:(0,a.jsx)("div",{className:"card border-0",style:{backgroundColor:"#f0fdf4"},children:(0,a.jsxs)("div",{className:"card-body p-3",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-3",children:[(0,a.jsx)("i",{className:"bi bi-shield-check text-success"}),(0,a.jsx)("h6",{className:"obs-title mb-0",children:"Controls"})]}),e[3].option.map((e,s)=>(0,a.jsx)("div",{className:"card border-0 mb-2",style:{backgroundColor:"rgba(255,255,255,0.7)"},children:(0,a.jsx)("div",{className:"card-body p-2",children:(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2",children:[(0,a.jsx)("span",{className:"badge bg-success text-white",style:{fontSize:"9px"},children:e.current_type}),(0,a.jsx)("span",{className:"small",children:e.value})]})})},s))]})})})]})]})]},s))})]}):(0,a.jsxs)("div",{className:"",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-3",children:[(0,a.jsx)("i",{className:"bi bi-exclamation-triangle-fill text-danger"}),(0,a.jsx)("h5",{className:"mb-0 fw-semibold text-danger",children:"High-Risk Assessment"})]}),i.tasks.map((e,s)=>(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("div",{className:"row g-3",children:[(0,a.jsx)("div",{className:"col-md-6",children:(0,a.jsx)("div",{className:"card border-0",style:{backgroundColor:"#fef2f2"},children:(0,a.jsxs)("div",{className:"card-body p-3",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-3",children:[(0,a.jsx)("i",{className:"bi bi-exclamation-circle text-danger"}),(0,a.jsx)("h6",{className:"obs-title mb-0",children:"Consequences"})]}),e[0].option.map((e,s)=>(0,a.jsx)("div",{className:"card border-0 mb-2",style:{backgroundColor:"rgba(255,255,255,0.7)"},children:(0,a.jsx)("div",{className:"card-body p-2",children:(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2",children:[(0,a.jsx)("span",{className:"badge bg-danger text-white",style:{fontSize:"9px"},children:e.current_type}),(0,a.jsx)("span",{className:"small",children:e.value})]})})},s))]})})}),(0,a.jsx)("div",{className:"col-md-6",children:(0,a.jsx)("div",{className:"card border-0",style:{backgroundColor:"#f0fdf4"},children:(0,a.jsxs)("div",{className:"card-body p-3",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-3",children:[(0,a.jsx)("i",{className:"bi bi-shield-check text-success"}),(0,a.jsx)("h6",{className:"obs-title mb-0",children:"Controls"})]}),e[1].option.map((e,s)=>(0,a.jsx)("div",{className:"card border-0 mb-2",style:{backgroundColor:"rgba(255,255,255,0.7)"},children:(0,a.jsx)("div",{className:"card-body p-2",children:(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2",children:[(0,a.jsx)("span",{className:"badge bg-success text-white",style:{fontSize:"9px"},children:e.current_type}),(0,a.jsx)("span",{className:"small",children:e.value})]})})},s))]})})})]})},s))]})]})}},70406:(e,s,t)=>{"use strict";t.d(s,{default:()=>eo});var a=t(95155),i=t(12115),l=t(37759),n=t(81359),r=t(34540),o=t(6874),c=t.n(o),d=t(11518),m=t.n(d),x=t(38336),h=t(17227),p=t(26957),u=t(56160),b=t(68136),g=t(94016),f=t(11846),j=t(16639),N=t(92809),y=t(60902),v=t(24752),A=t.n(v),k=t(54239);t(35279);var w=t(76852),S=t(16344),C=t(27347);let F=e=>{let{show:s,applicationDetails:t,showItem:l,closeModal:n}=e,[r,o]=(0,i.useState)(""),[c,d]=(0,i.useState)(""),[m,h]=(0,i.useState)(!1),[v,F]=(0,i.useState)(""),[T,D]=(0,i.useState)(!1),[I,R]=(0,i.useState)([]),[E,z]=(0,i.useState)(""),[M,B]=(0,i.useState)(""),[W,Y]=(0,i.useState)(""),[P,O]=(0,i.useState)([]),[_,q]=(0,i.useState)(null);(0,i.useEffect)(()=>{l&&("review"===l.actionType?L("obsactionowner"):("take_action"===l.actionType||"reperform_action"===l.actionType)&&L("obsreviewer"))},[l]);let L=(0,i.useCallback)(async e=>{try{let s=await x.A.post(p.u3,{locationOneId:"",locationTwoId:"",locationThreeId:"",locationFourId:"",mode:e});if(200===s.status){let e=s.data.map(e=>({label:e.firstName,value:e.id}));R(e)}}catch(e){console.error("Error fetching crew list:",e)}},[]),U=async()=>{if(D(!0),!l)return;let e=!1;if("take_action"===l.actionType||"reperform_action"===l.actionType?(M||(e=!0),E||(e=!0),0===P.length&&(e=!0)):"review"===l.actionType?(W||(e=!0),_||(e=!0),E||(e=!0)):"verify_action"===l.actionType&&(r||(e=!0),"Return"!==r||v||(e=!0)),e)return void A().fire({icon:"error",title:"Validation Error",text:"Please fill all required fields before submitting!"});let s={};"take_action"===l.actionType||"reperform_action"===l.actionType?s={actionTaken:M,reviewerId:E,evidence:P}:"review"===l.actionType?s={actionToBeTaken:W,dueDate:_,actionOwnerId:E}:"verify_action"===l.actionType&&(s={status:"Approve"===r?"Completed":"Returned",comments:v});try{let e=await x.A.patch((0,p.FI)(l.id),s);if(204===e.status)A().fire({icon:"success",title:"Success",text:"Action submitted successfully!"}),n(!1);else throw Error("Something went wrong. Please try again.")}catch(e){A().fire({icon:"error",title:"Submission Failed",text:e.message||"Failed to submit action. Please try again."})}};return(0,a.jsxs)(u.A,{show:s,size:"lg",onHide:()=>n(!1),"aria-labelledby":"example-modal-sizes-title-md",id:"pdf-content",children:[(0,a.jsx)(u.A.Header,{closeButton:!0,children:t&&(0,a.jsx)("div",{className:"row",style:{width:"100%"},children:(0,a.jsx)("div",{className:"col-9",children:(0,a.jsx)("div",{className:"row",children:(0,a.jsxs)("div",{className:"col-12",children:[(0,a.jsx)("h4",{children:"Observation"}),(0,a.jsxs)("div",{className:"d-flex align-items-center",children:[(0,a.jsxs)("p",{className:"me-2",children:["#",t.maskId||""]}),(0,a.jsx)("p",{className:"badge bg-primary text-white",children:t.status})]})]})})})})}),(0,a.jsxs)(u.A.Body,{children:[t&&(0,a.jsx)(w.A,{reportData:t}),(null==l?void 0:l.actionType)==="take_action"||(null==l?void 0:l.actionType)==="reperform_action"?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)("h5",{className:" mb-3",children:"Action Taken"}),(0,a.jsx)(f.A,{className:"justify-content-center",children:(0,a.jsx)(b.A,{xs:12,className:"d-flex text-center",children:(0,a.jsx)("textarea",{rows:4,cols:50,className:"form-control",placeholder:"Enter your action here...",onChange:e=>B(e.target.value)})})}),T&&!M&&(0,a.jsx)("p",{className:"text-danger mt-2",children:"Action Taken is required."})]})})}),(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)("h5",{className:"mb-3",children:"Evidence"}),(0,a.jsx)(S.A,{onFilesSelected:e=>{O(e)},disabled:!1,files:P}),T&&0===P.length&&(0,a.jsx)("p",{className:"text-danger mt-2",children:"At least one evidence file is required."})]})})}),(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)(j.A.Group,{className:"mb-3",children:(0,a.jsx)(C.A,{title:["review"].includes(l.actionType)?"Assessor":"Reviewer",options:I,selectedValue:E,onChange:e=>z(e),placeholder:["review"].includes(l.actionType)?"Select Assessor":"Select Reviewer",clearable:!0,disabled:!1})}),T&&!E&&(0,a.jsxs)("p",{className:"text-danger mt-2",children:[["review"].includes(l.actionType)?"Assessor":"Reviewer"," required."]})]})})})]}):(null==l?void 0:l.actionType)==="review"?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)("h5",{className:"mb-3",children:"Action to be Taken"}),(0,a.jsx)(f.A,{className:"justify-content-center",children:(0,a.jsx)(b.A,{xs:12,className:"d-flex text-center",children:(0,a.jsx)("textarea",{rows:4,cols:50,className:"form-control",placeholder:"Enter your action here...",onChange:e=>Y(e.target.value)})})}),T&&!W&&(0,a.jsx)("p",{className:"text-danger mt-2",children:"Action to be Taken is required."})]})})}),(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsxs)(j.A.Group,{controlId:"dueDate",className:"mb-3",children:[(0,a.jsx)(j.A.Label,{className:"mb-3",children:"Due Date"}),(0,a.jsx)(k.Ay,{selected:_?new Date(_):null,onChange:e=>q(e?e.toISOString():null),dateFormat:"yyyy-MM-dd",className:"form-control",placeholderText:"Select a date",minDate:new Date})]}),T&&null===_&&(0,a.jsx)("p",{className:"text-danger mt-2",children:"Due Date is required."})]})})}),(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)(j.A.Group,{className:"mb-3",children:(0,a.jsx)(C.A,{title:"review"===l.actionType?"Action Owner":"Reviewer",options:I,selectedValue:E,onChange:e=>z(e),placeholder:"review"===l.actionType?"Select Action Owner":"Select Reviewer",clearable:!0,disabled:!1})}),T&&!E&&(0,a.jsxs)("p",{className:"text-danger mt-2",children:["review"===l.actionType?"Assessor":"Reviewer"," required."]})]})})})]}):(null==l?void 0:l.actionType)==="verify_action"?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)(f.A,{className:"justify-content-center",children:(0,a.jsxs)(b.A,{xs:12,className:"d-flex text-center",children:[(0,a.jsx)(N.A,{fluid:!0,className:"col-6 p-2",style:{border:"1px solid #dee2e6",borderRadius:10,color:"#000000",cursor:"pointer"},onClick:()=>o("Approve"),children:(0,a.jsxs)(f.A,{children:[(0,a.jsx)(b.A,{xs:4,children:(0,a.jsx)("div",{style:"Approve"===r?{width:24,height:24,borderRadius:12,background:"green"}:{width:24,height:24,borderRadius:12,background:"lightgray"},children:"Approve"===r&&(0,a.jsx)("i",{className:"bi bi-check text-white"})})}),(0,a.jsx)(b.A,{xs:8,style:"Approve"===r?{color:"green"}:{},children:"Approve"})]})}),(0,a.jsx)(N.A,{fluid:!0,className:"col-5 p-2",style:{border:"1px solid #dee2e6",borderRadius:10,color:"#000000",cursor:"pointer"},onClick:()=>o("Return"),children:(0,a.jsxs)(f.A,{children:[(0,a.jsx)(b.A,{xs:4,children:(0,a.jsx)("div",{style:"Return"===r?{width:24,height:24,borderRadius:12,background:"red"}:{width:24,height:24,borderRadius:12,background:"lightgray"},children:"Return"===r&&(0,a.jsx)("i",{className:"bi bi-check text-white"})})}),(0,a.jsx)(b.A,{xs:8,style:"Return"===r?{color:"red"}:{},children:"Return"})]})})]})}),T&&!r&&(0,a.jsx)("p",{className:"text-danger mt-2",children:"Please select Approve or Return."})]})})}),(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)("h5",{className:"mb-3",children:"Comments"}),(0,a.jsx)(f.A,{className:"justify-content-center",children:(0,a.jsx)(b.A,{xs:12,className:"d-flex text-center",children:(0,a.jsx)("textarea",{rows:4,cols:50,className:"form-control",placeholder:"Enter your comments here...",onChange:e=>F(e.target.value)})})}),T&&"Return"===r&&!v&&(0,a.jsx)("p",{className:"text-danger mt-2",children:"Comments are required when returning."})]})})})]}):null]}),(0,a.jsx)(u.A.Footer,{children:(null==l?void 0:l.actionType)==="verify_action"?(0,a.jsx)(y.A,{variant:"primary",onClick:U,children:"Return"===r?"Return to Action Owner":"Approve"}):(0,a.jsx)(y.A,{variant:"primary",onClick:U,children:"Submit"})})]})};var T=t(24952),D=t(53686);let I=e=>{let{show:s,applicationDetails:t,showItem:l,closeModal:n}=e,r=(0,i.useRef)(null),[o,c]=(0,i.useState)(""),[d,m]=(0,i.useState)(""),h=(e,s)=>{let t=atob(e.split(",")[1]),a=e.split(",")[0].split(":")[1].split(";")[0],i=new ArrayBuffer(t.length),l=new DataView(i);for(let e=0;e<t.length;e++)l.setUint8(e,t.charCodeAt(e));return new File([i],s,{type:a})},b=async()=>{let e=new Date().getTime()+"captin_sign.png";if(r.current&&!r.current.isEmpty()){let s=r.current.toDataURL("image/png"),t=new FormData;t.append("file",h(s,e));try{let e=await x.A.post(p.Dp,t,{headers:{"Content-Type":"multipart/form-data"}});if(e&&200===e.status&&r.current&&!r.current.isEmpty()){let s=await x.A.patch((0,p.F4)(l.id),{signature:e.data.files[0].originalname,signatureDate:new Date});204===s.status&&(A().fire("Risk Assessment Updated!","","success"),n(!1))}}catch(e){console.error("File upload error: ",e)}}};return(0,a.jsxs)(u.A,{show:s,size:"lg",onHide:()=>n(!1),"aria-labelledby":"example-modal-sizes-title-md",id:"pdf-content",children:[(0,a.jsx)(u.A.Header,{closeButton:!0,children:t&&(0,a.jsx)("div",{className:"row",style:{width:"100%"},children:(0,a.jsx)("div",{className:"col-9",children:(0,a.jsx)("div",{className:"row",children:(0,a.jsxs)("div",{className:"col-12",children:[(0,a.jsx)("h4",{children:"Risk Assessment"}),(0,a.jsxs)("div",{className:"d-flex align-items-center",children:[(0,a.jsxs)("p",{className:"me-2",children:["#",t.maskId||""]}),(0,a.jsx)("p",{className:"badge bg-primary text-white",children:t.status})]})]})})})})}),(0,a.jsxs)(u.A.Body,{children:[t&&(0,a.jsx)(D.A,{reportData:t}),(0,a.jsx)("div",{className:"row mt-4",children:(0,a.jsxs)("div",{className:"col-md-12 text-center",children:[(0,a.jsx)("div",{className:"mb-4",children:"I confirm my participation in this Risk Assessment as a team member. The outcome reflects our shared professional judgment to the best of our abilities through consensus."}),(0,a.jsx)(T.A,{penColor:"#1F3BB3",canvasProps:{width:350,height:100,className:"sigCanvas",style:{boxShadow:"0px 0px 10px 3px rgb(189 189 189)"}},ref:r}),(0,a.jsx)("i",{className:"fa fa-undo undo",onClick:()=>{var e;return null==(e=r.current)?void 0:e.clear()}})]})})]}),(0,a.jsx)(u.A.Footer,{children:(0,a.jsx)(y.A,{variant:"primary",onClick:()=>b(),children:"Submit"})})]})};var R=t(79555);let E=e=>{let{show:s,applicationDetails:t,showItem:l,closeModal:n}=e,r=(0,i.useRef)(null),[o,c]=(0,i.useState)(""),[d,m]=(0,i.useState)(""),[h,v]=(0,i.useState)(!1),[k,w]=(0,i.useState)(""),[F,D]=(0,i.useState)(!1),[I,E]=(0,i.useState)([]),[z,M]=(0,i.useState)(""),[B,W]=(0,i.useState)(""),[Y,P]=(0,i.useState)(""),[O,_]=(0,i.useState)([]),[q,L]=(0,i.useState)(null),[U,H]=(0,i.useState)([]);(0,i.useEffect)(()=>{(null==l?void 0:l.actionType)==="Review"?V("eptwAssessor"):(null==l?void 0:l.actionType)==="Assess"&&V("eptwApprover")},[l]);let V=(0,i.useCallback)(async e=>{try{let s=await x.A.post(p.u3,{locationOneId:"",locationTwoId:"",locationThreeId:"",locationFourId:"",mode:e});if(200===s.status){let e=s.data.map(e=>({label:e.firstName,value:e.id}));E(e)}}catch(e){console.error("Error fetching crew list:",e)}},[]),G=(e,s)=>{for(var t=atob(e.split(",")[1]),a=e.split(",")[0].split(":")[1].split(";")[0],i=new ArrayBuffer(t.length),l=new DataView(i),n=0;n<t.length;n++)l.setUint8(n,t.charCodeAt(n));return new File([i],s,{type:a})},J=async()=>{let e="".concat(new Date().getTime(),"_captin_sign.png"),s=new FormData,t=G(d,e);s.append("file",t);try{let e=await x.A.post(p.Dp,s,{headers:{"Content-Type":"multipart/form-data"}});if(e&&200===e.status)return e.data.files[0].originalname;throw Error("File upload failed.")}catch(e){throw console.error("File upload error:",e),e}},K=()=>(null==l?void 0:l.actionType)==="Acknowledgement"?""!==d:"Approve"===o?(null==l?void 0:l.actionType)==="Approve"?""!==k&&""!==d:""!==d&&""!==z:"Return"===o&&""!==k,Z=async()=>{if(D(!0),K())try{let e=await J(),s=await x.A.patch((0,p.WD)((null==l?void 0:l.id)||""),{acknowledgementStatus:{signature:e,comments:"",uploads:U}});204===s.status&&(A().fire("Permit","Submitted Successfully","success"),n())}catch(e){console.error("Error:",e)}else A().fire({icon:"error",title:"Validation",text:"Please fill all the required fields"})},Q=async()=>{if(D(!0),K())try{let e;if("Return"===o)e={comments:k,status:"Returned"};else{let s=await J();e={comments:k,...(null==l?void 0:l.actionType)==="Review"?{reviewerStatus:{signature:s},assessorId:z}:(null==l?void 0:l.actionType)==="Assess"?{assessorStatus:{signature:s},approverId:z}:{approverStatus:{signature:s}}}}let s=await x.A.patch((0,p.WU)((null==l?void 0:l.id)||""),e);204===s.status&&(A().fire("Permit","Submitted Successfully","success"),n())}catch(e){console.error("Error:",e)}else A().fire({icon:"error",title:"Validation",text:"Please fill all the required fields"})};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(u.A,{show:s,size:"lg",onHide:()=>n(!1),"aria-labelledby":"example-modal-sizes-title-md",id:"pdf-content",children:[(0,a.jsx)(u.A.Header,{closeButton:!0,children:t&&(0,a.jsx)("div",{className:"row",style:{width:"100%"},children:(0,a.jsx)("div",{className:"col-9",children:(0,a.jsx)("div",{className:"row",children:(0,a.jsxs)("div",{className:"col-12",children:[(0,a.jsx)("h4",{children:"Permit"}),(0,a.jsxs)("div",{className:"d-flex align-items-center",children:[(0,a.jsxs)("p",{className:"me-2",children:["#",t.maskId||""]}),(0,a.jsx)("p",{className:"badge bg-primary text-white",children:t.status})]})]})})})})}),(0,a.jsxs)(u.A.Body,{children:[t&&(0,a.jsx)(R.A,{applicationDetails:t}),(0,a.jsx)("h5",{className:"p-3 fw-bold",children:null==l?void 0:l.actionToBeTaken}),(null==l?void 0:l.actionType)==="Acknowledgement"?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.A,{className:"m-auto mb-3 mt-3",xs:12,sm:12,md:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)("label",{htmlFor:"incidentImages",className:"mb-2",children:"Add/Upload Evidence if any"}),(0,a.jsx)(S.A,{disabled:!1,onFilesSelected:e=>H(e),files:U})]})})}),(0,a.jsx)(b.A,{className:"m-auto mb-3 mt-3",xs:12,sm:12,md:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsx)(g.A.Body,{children:(0,a.jsxs)(f.A,{children:[(0,a.jsx)(b.A,{xs:12,sm:12,md:12,className:"d-flex text-justify",children:(0,a.jsx)("label",{style:{textAlign:"justify"},children:"I acknowledge that,to the best of my knowledge, the work has been completed as per the permit."})}),(0,a.jsx)(b.A,{xs:12,sm:12,md:12,className:"d-flex justify-content-center p-2",onClick:()=>v(!0),children:(0,a.jsx)("span",{className:"bi bi-pencil-square",style:{fontSize:60}})}),(0,a.jsx)("div",{className:"d-flex justify-content-center",children:d?(0,a.jsx)("img",{src:d,height:100,style:{minWidth:150}}):F&&(0,a.jsx)("p",{className:"text-danger mt-2",children:"Signature is required for approval."})})]})})})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,sm:12,md:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)(f.A,{className:"justify-content-center",children:(0,a.jsxs)(b.A,{xs:12,className:"d-flex text-center",children:[(0,a.jsx)(N.A,{fluid:!0,className:"col-6 p-2",style:{border:"1px solid #dee2e6",borderRadius:10,color:"#000000",cursor:"pointer"},onClick:()=>c("Approve"),children:(0,a.jsxs)(f.A,{children:[(0,a.jsx)(b.A,{xs:4,children:(0,a.jsx)("div",{style:"Approve"===o?{width:24,height:24,borderRadius:12,background:"green"}:{width:24,height:24,borderRadius:12,background:"lightgray"},children:"Approve"===o&&(0,a.jsx)("i",{className:"bi bi-check text-white"})})}),(0,a.jsx)(b.A,{xs:8,style:"Approve"===o?{color:"green"}:{},children:"Approve"})]})}),(0,a.jsx)(N.A,{fluid:!0,className:"col-5 p-2",style:{border:"1px solid #dee2e6",borderRadius:10,color:"#000000",cursor:"pointer"},onClick:()=>c("Return"),children:(0,a.jsxs)(f.A,{children:[(0,a.jsx)(b.A,{xs:4,children:(0,a.jsx)("div",{style:"Return"===o?{width:24,height:24,borderRadius:12,background:"red"}:{width:24,height:24,borderRadius:12,background:"lightgray"},children:"Return"===o&&(0,a.jsx)("i",{className:"bi bi-check text-white"})})}),(0,a.jsx)(b.A,{xs:8,style:"Return"===o?{color:"red"}:{},children:"Return"})]})})]})}),F&&!o&&(0,a.jsx)("p",{className:"text-danger mt-2",children:"Please select Approve or Return."})]})})}),F&&!o&&(0,a.jsx)("p",{className:"text-danger mt-2",children:"Please select Approve or Return."})]})})}),(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,sm:12,md:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)(f.A,{className:"justify-content-center",children:(0,a.jsx)(b.A,{className:"d-flex text-center",children:(0,a.jsx)("textarea",{rows:4,cols:50,className:"form-control",placeholder:"Enter your comments here...",onChange:e=>w(e.target.value)})})}),F&&""===k&&(0,a.jsx)("p",{className:"text-danger mt-2",children:"Comments are required."})]})})}),"Approve"===o&&(0,a.jsxs)(a.Fragment,{children:[(null==l?void 0:l.actionType)!=="Approve"&&(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,sm:12,md:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)(j.A.Group,{className:"mb-3",children:(0,a.jsx)(C.A,{title:(null==l?void 0:l.actionType)==="Review"?"Assessor":(null==l?void 0:l.actionType)==="Assess"?"Approver":"Reviewer",options:I,selectedValue:z,onChange:e=>M(e),placeholder:(null==l?void 0:l.actionType)==="Review"?"Select Assessor":(null==l?void 0:l.actionType)==="Assess"?"Select Approver":"Select Reviewer",clearable:!0,disabled:!1})}),F&&""===z&&(0,a.jsxs)("p",{className:"text-danger mt-2",children:[(null==l?void 0:l.actionType)==="Review"?"Assessor":"Approver","  required."]})]})})}),(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,sm:12,md:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsx)(g.A.Body,{children:(0,a.jsxs)(f.A,{children:[(0,a.jsx)(b.A,{xs:12,sm:12,md:12,className:"d-flex text-justify",children:(0,a.jsx)("label",{style:{textAlign:"justify"},children:"I acknowledge this permit application."})}),(0,a.jsx)(b.A,{xs:12,sm:12,md:12,className:"d-flex justify-content-center p-2",onClick:()=>v(!0),children:(0,a.jsx)("span",{className:"bi bi-pencil-square",style:{fontSize:60}})}),(0,a.jsx)("div",{className:"d-flex justify-content-center",children:d?(0,a.jsx)("img",{src:d,height:100,style:{minWidth:150}}):F&&(0,a.jsx)("p",{className:"text-danger mt-2",children:"Signature is required for approval."})})]})})})})]})]})]}),(0,a.jsx)(u.A.Footer,{children:(null==l?void 0:l.actionType)==="Review"?(0,a.jsx)(y.A,{variant:"primary",onClick:Q,children:"Return"===o?"Return to Applicant":"Submit to Assessor"}):(null==l?void 0:l.actionType)==="Assess"?(0,a.jsx)(y.A,{variant:"primary",onClick:Q,children:"Return"===o?"Return to Applicant":"Submit to Approver"}):(null==l?void 0:l.actionType)==="Acknowledgement"?(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(y.A,{variant:"primary",onClick:Z,children:"Permit CloseOut"})}):(0,a.jsx)(y.A,{variant:"primary",onClick:Q,children:"Return"===o?"Return to Applicant":"Approve"})})]}),(0,a.jsxs)(u.A,{show:h,onHide:()=>{v(!1)},"aria-labelledby":"contained-modal-title-vcenter",centered:!0,backdrop:"static",children:[(0,a.jsx)(u.A.Header,{closeButton:!1,children:(0,a.jsx)(u.A.Title,{id:"contained-modal-title-vcenter",children:"Sign"})}),(0,a.jsx)(u.A.Body,{style:{background:"#f5f5f5",width:"100%"},children:(0,a.jsx)(T.A,{ref:r,penColor:"#1F3BB3",backgroundColor:"white",canvasProps:{className:"sigCanvas",style:{width:"100%",background:"#fff",boxShadow:"0px 0px 10px 3px rgb(189 189 189)",height:"100px"}}})}),(0,a.jsxs)(u.A.Footer,{children:[(0,a.jsx)(y.A,{onClick:()=>{var e;let s=null==(e=r.current)?void 0:e.toDataURL("image/png");s&&m(s),v(!1)},children:"confirm"}),(0,a.jsx)(y.A,{onClick:()=>{var e;return null==(e=r.current)?void 0:e.clear()},children:"Clear"}),(0,a.jsx)(y.A,{onClick:()=>{v(!1)},children:"Close"})]})]})]})};var z=t(43864),M=t(95272),B=t(82940),W=t.n(B);let Y=e=>{var s,t,l,n,r;let{show:o,applicationDetails:c,showItem:d,closeModal:m}=e,[h,v]=(0,i.useState)(""),[w,F]=(0,i.useState)(""),[T,D]=(0,i.useState)(!1),[I,R]=(0,i.useState)(""),[E,B]=(0,i.useState)(!1),[Y,P]=(0,i.useState)([]),[O,_]=(0,i.useState)(""),[q,L]=(0,i.useState)(""),[U,H]=(0,i.useState)(""),[V,G]=(0,i.useState)([]),[J,K]=(0,i.useState)(null);(0,i.useEffect)(()=>{d&&("review"===d.actionType?Z("obsactionowner"):("perform_task"===d.actionType||"reperform_task"===d.actionType)&&Z("ins_action_reviewer"))},[d]);let Z=(0,i.useCallback)(async e=>{try{let s=await x.A.post(p.u3,{locationOneId:"",locationTwoId:"",locationThreeId:"",locationFourId:"",mode:e});if(200===s.status){let e=s.data.map(e=>({label:e.firstName,value:e.id}));P(e)}}catch(e){console.error("Error fetching crew list:",e)}},[]),Q=async()=>{if(B(!0),!d)return;let e=!1;if("perform_task"===d.actionType||"reperform_task"===d.actionType?(q||(e=!0),O||(e=!0),0===V.length&&(e=!0)):"review"===d.actionType?(U||(e=!0),J||(e=!0),O||(e=!0)):"verify_task"===d.actionType&&(h||(e=!0),"Return"!==h||I||(e=!0)),e)return void A().fire({icon:"error",title:"Validation Error",text:"Please fill all required fields before submitting!"});let s={};"perform_task"===d.actionType||"reperform_task"===d.actionType?s={actionTaken:q,reviewerId:O,evidence:V}:"review"===d.actionType?s={actionToBeTaken:U,dueDate:J,actionOwnerId:O}:"verify_task"===d.actionType&&(s={status:"Approve"===h?"Completed":"Returned",comments:I});try{let e=await x.A.patch((0,p.bW)(d.id),s);if(204===e.status)A().fire({icon:"success",title:"Success",text:"Action submitted successfully!"}),m(!1);else throw Error("Something went wrong. Please try again.")}catch(e){A().fire({icon:"error",title:"Submission Failed",text:e.message||"Failed to submit action. Please try again."})}};return(0,a.jsxs)(u.A,{show:o,size:"lg",onHide:()=>m(!1),"aria-labelledby":"example-modal-sizes-title-md",id:"pdf-content",children:[(0,a.jsx)(u.A.Header,{closeButton:!0,children:c&&(0,a.jsx)("div",{className:"row",style:{width:"100%"},children:(0,a.jsx)("div",{className:"col-9",children:(0,a.jsx)("div",{className:"row",children:(0,a.jsxs)("div",{className:"col-12",children:[(0,a.jsx)("h4",{children:"Inspection"}),(0,a.jsxs)("div",{className:"d-flex align-items-center",children:[(0,a.jsxs)("p",{className:"me-2",children:["#",c.maskId||""]}),(0,a.jsx)("p",{className:"badge bg-primary text-white",children:c.status})]})]})})})})}),(0,a.jsxs)(u.A.Body,{children:[c&&(0,a.jsx)(M.A,{reportData:c,type:"action"}),(null==d?void 0:d.actionType)==="perform_task"||(null==d?void 0:d.actionType)==="reperform_task"?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,sm:12,md:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)("h6",{className:"fw-bold mb-3",children:"INS-CL"===d.prefix&&d.description}),(0,a.jsx)("h6",{className:"fw-bold mb-3",children:"Action to be Taken"}),(0,a.jsx)("p",{children:d.actionToBeTaken}),(0,a.jsx)("div",{className:"row",children:(null==d||null==(s=d.uploads)?void 0:s.length)>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("h5",{className:"fw-bold mb-3 mt-3",children:"Images"}),(0,a.jsx)("div",{className:"col-6 mt-2",children:null==d?void 0:d.uploads.map((e,s)=>(0,a.jsx)("div",{className:"m-2 position-relative",children:(0,a.jsx)(z.A,{fileName:e,size:100,name:!1})},s))})]})}),(null==d?void 0:d.actionType)==="reperform_task"&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("h5",{className:"fw-bold mb-3",children:"Action Taken"}),(0,a.jsx)("p",{children:d.actionTaken}),(null==(t=d.evidence)?void 0:t.length)>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("h5",{className:"fw-bold mb-3 mt-3",children:"Evidence"}),(0,a.jsx)("div",{className:"col-6 mt-2",children:d.evidence.map((e,s)=>(0,a.jsx)("div",{className:"m-2 position-relative",children:(0,a.jsx)(z.A,{fileName:e,size:100,name:!1})},s))})]}),(0,a.jsx)("h5",{className:"fw-bold mb-3",children:"Reviewer Comments"}),(0,a.jsx)("p",{children:d.comments})]})]})})}),(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)("h5",{className:" mb-3",children:"Action Taken"}),(0,a.jsx)(f.A,{className:"justify-content-center",children:(0,a.jsx)(b.A,{xs:12,className:"d-flex text-center",children:(0,a.jsx)("textarea",{rows:4,cols:50,className:"form-control",placeholder:"Enter your action here...",onChange:e=>L(e.target.value)})})}),E&&!q&&(0,a.jsx)("p",{className:"text-danger mt-2",children:"Action Taken is required."})]})})}),(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)("h5",{className:"mb-3",children:"Evidence"}),(0,a.jsx)(S.A,{onFilesSelected:e=>{G(e)},disabled:!1,files:V}),E&&0===V.length&&(0,a.jsx)("p",{className:"text-danger mt-2",children:"At least one evidence file is required."})]})})}),(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)(j.A.Group,{className:"mb-3",children:(0,a.jsx)(C.A,{title:["review"].includes(d.actionType)?"Assessor":"Reviewer",options:Y,selectedValue:O,onChange:e=>_(e),placeholder:["review"].includes(d.actionType)?"Select Assessor":"Select Reviewer",clearable:!0,disabled:!1})}),E&&!O&&(0,a.jsxs)("p",{className:"text-danger mt-2",children:[["review"].includes(d.actionType)?"Assessor":"Reviewer"," required."]})]})})})]}):(null==d?void 0:d.actionType)==="review"?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)("h5",{className:"mb-3",children:"Action to be Taken"}),(0,a.jsx)(f.A,{className:"justify-content-center",children:(0,a.jsx)(b.A,{xs:12,className:"d-flex text-center",children:(0,a.jsx)("textarea",{rows:4,cols:50,className:"form-control",placeholder:"Enter your action here...",onChange:e=>H(e.target.value)})})}),E&&!U&&(0,a.jsx)("p",{className:"text-danger mt-2",children:"Action to be Taken is required."})]})})}),(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsxs)(j.A.Group,{controlId:"dueDate",className:"mb-3",children:[(0,a.jsx)(j.A.Label,{className:"mb-3",children:"Due Date"}),(0,a.jsx)(k.Ay,{selected:J?new Date(J):null,onChange:e=>K(e?e.toISOString():null),dateFormat:"yyyy-MM-dd",className:"form-control",placeholderText:"Select a date",minDate:new Date})]}),E&&null===J&&(0,a.jsx)("p",{className:"text-danger mt-2",children:"Due Date is required."})]})})}),(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)(j.A.Group,{className:"mb-3",children:(0,a.jsx)(C.A,{title:"review"===d.actionType?"Action Owner":"Reviewer",options:Y,selectedValue:O,onChange:e=>_(e),placeholder:"review"===d.actionType?"Select Action Owner":"Select Reviewer",clearable:!0,disabled:!1})}),E&&!O&&(0,a.jsxs)("p",{className:"text-danger mt-2",children:["review"===d.actionType?"Assessor":"Reviewer"," required."]})]})})})]}):(null==d?void 0:d.actionType)==="verify_task"?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,sm:12,md:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)("h4",{className:"fw-bold mb-3",children:"INS-CL"===d.prefix&&d.description}),(0,a.jsx)("h5",{className:"fw-bold mb-3",children:"Action to be Taken"}),(0,a.jsx)("p",{children:d.actionToBeTaken}),(0,a.jsx)("div",{className:"row",children:(null==(l=d.uploads)?void 0:l.length)>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("h5",{className:"fw-bold mb-3 mt-3",children:"Images"}),(0,a.jsx)("div",{className:"col-6 mt-2",children:d.uploads.map((e,s)=>(0,a.jsx)("div",{className:"m-2 position-relative",children:(0,a.jsx)(z.A,{fileName:e,size:100,name:!1})},s))})]})}),(0,a.jsxs)("h5",{className:"fw-bold mb-3",children:["Action Taken by ",null==(n=d.submittedBy)?void 0:n.firstName," - ",W()(d.created).format("DD-MM-YYYY")]}),(0,a.jsx)("p",{children:d.actionTaken}),(0,a.jsx)("div",{className:"row",children:(null==(r=d.evidence)?void 0:r.length)>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("h5",{className:"fw-bold mb-3 mt-3",children:"Evidence"}),(0,a.jsx)("div",{className:"col-6 mt-2",children:d.evidence.map((e,s)=>(0,a.jsx)("div",{className:"m-2 position-relative",children:(0,a.jsx)(z.A,{fileName:e,size:100,name:!1})},s))})]})})]})})}),(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)(f.A,{className:"justify-content-center",children:(0,a.jsxs)(b.A,{xs:12,className:"d-flex text-center",children:[(0,a.jsx)(N.A,{fluid:!0,className:"col-6 p-2",style:{border:"1px solid #dee2e6",borderRadius:10,color:"#000000",cursor:"pointer"},onClick:()=>v("Approve"),children:(0,a.jsxs)(f.A,{children:[(0,a.jsx)(b.A,{xs:4,children:(0,a.jsx)("div",{style:"Approve"===h?{width:24,height:24,borderRadius:12,background:"green"}:{width:24,height:24,borderRadius:12,background:"lightgray"},children:"Approve"===h&&(0,a.jsx)("i",{className:"bi bi-check text-white"})})}),(0,a.jsx)(b.A,{xs:8,style:"Approve"===h?{color:"green"}:{},children:"Approve"})]})}),(0,a.jsx)(N.A,{fluid:!0,className:"col-5 p-2",style:{border:"1px solid #dee2e6",borderRadius:10,color:"#000000",cursor:"pointer"},onClick:()=>v("Return"),children:(0,a.jsxs)(f.A,{children:[(0,a.jsx)(b.A,{xs:4,children:(0,a.jsx)("div",{style:"Return"===h?{width:24,height:24,borderRadius:12,background:"red"}:{width:24,height:24,borderRadius:12,background:"lightgray"},children:"Return"===h&&(0,a.jsx)("i",{className:"bi bi-check text-white"})})}),(0,a.jsx)(b.A,{xs:8,style:"Return"===h?{color:"red"}:{},children:"Return"})]})})]})}),E&&!h&&(0,a.jsx)("p",{className:"text-danger mt-2",children:"Please select Approve or Return."})]})})}),(0,a.jsx)(b.A,{className:"m-auto mb-3",xs:12,children:(0,a.jsx)(g.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsx)("h5",{className:"mb-3",children:"Comments"}),(0,a.jsx)(f.A,{className:"justify-content-center",children:(0,a.jsx)(b.A,{xs:12,className:"d-flex text-center",children:(0,a.jsx)("textarea",{rows:4,cols:50,className:"form-control",placeholder:"Enter your comments here...",onChange:e=>R(e.target.value)})})}),E&&"Return"===h&&!I&&(0,a.jsx)("p",{className:"text-danger mt-2",children:"Comments are required when returning."})]})})})]}):null]}),(0,a.jsx)(u.A.Footer,{children:(null==d?void 0:d.actionType)==="verify_task"?(0,a.jsx)(y.A,{variant:"primary",onClick:Q,children:"Return"===h?"Return to Action Owner":"Approve"}):(0,a.jsx)(y.A,{variant:"primary",onClick:Q,children:"Submit"})})]})},P=e=>{let{component:s}=e,t=s.data;return(0,a.jsx)("div",{className:"mb-3 p-3 rounded  text-white",children:(0,a.jsxs)("h5",{className:"mb-0 fw-semibold d-flex align-items-center",children:[(0,a.jsx)("i",{className:"bi bi-info-circle me-2"}),t.text]})})},O=e=>{let{component:s}=e,t=s.data;return(0,a.jsx)("div",{className:"mb-3 p-4 rounded  text-white text-center",children:(0,a.jsx)("h4",{className:"mb-0 fw-bold",children:t.text})})},_=e=>{let{component:s}=e,t=s.data;return(0,a.jsx)("div",{className:"mb-3 p-3 rounded border bg-light",children:(0,a.jsx)("p",{className:"mb-0 text-muted",style:{whiteSpace:"pre-wrap",fontSize:"0.9rem"},children:t.content})})},q=e=>{let{component:s,componentIndex:t,checklistData:i,setChecklistData:l,errorMap:n}=e,r=s.data;return(0,a.jsx)(g.A,{className:"mb-3 shadow-sm",children:(0,a.jsx)(g.A.Body,{className:"p-3",children:(0,a.jsxs)(j.A.Group,{children:[(0,a.jsxs)(j.A.Label,{className:"fw-semibold d-flex align-items-center mb-2",children:[(0,a.jsx)("i",{className:"bi bi-calendar3 me-2 text-primary"}),r.label,r.required&&(0,a.jsx)("span",{className:"text-danger ms-1",children:" *"})]}),(0,a.jsx)("div",{className:n.checklist["".concat(t,"-date")]?"border border-danger rounded":"border rounded",style:{padding:"0.5rem"},children:(0,a.jsx)(k.Ay,{selected:s.selectedDate?new Date(s.selectedDate):null,onChange:e=>{let s=[...i];s[t].selectedDate=e?e.toISOString():null,l(s)},placeholderText:"Select date",dateFormat:"dd-MM-yyyy",className:"form-control border-0",popperClassName:"datepicker-high-zindex"})}),n.checklist["".concat(t,"-date")]&&(0,a.jsx)("div",{className:"small mt-1 text-danger",children:n.checklist["".concat(t,"-date")]})]})})})},L=e=>{let{component:s,componentIndex:t,checklistData:i,setChecklistData:l,errorMap:n,signRefs:r,handleSaveSignature:o,handleClearSignature:c}=e,d=s.data,m="".concat(d.id,"-").concat(t);return(0,a.jsx)(g.A,{className:"mb-3 shadow-sm",children:(0,a.jsx)(g.A.Body,{className:"p-3",children:(0,a.jsxs)(j.A.Group,{children:[(0,a.jsxs)(j.A.Label,{className:"fw-semibold d-flex align-items-center mb-2",children:[(0,a.jsx)("i",{className:"bi bi-pen me-2 text-primary"}),d.label,d.required&&(0,a.jsx)("span",{className:"text-danger ms-1",children:" *"})]}),(0,a.jsx)("div",{className:"border rounded p-3 bg-light",children:s.signature?(0,a.jsxs)("div",{className:"position-relative",style:{height:200},children:[(0,a.jsx)(z.A,{fileName:s.signature,size:300,name:!1}),(0,a.jsxs)(y.A,{size:"sm",variant:"outline-danger",className:"position-absolute top-0 end-0",onClick:()=>{let e=[...i];e[t].signature="",l(e),A().fire("Success","Signature removed. You can sign again if needed.","success")},children:[(0,a.jsx)("i",{className:"bi bi-trash me-1"}),"Clear"]})]}):(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"border rounded bg-white position-relative overflow-hidden",style:{width:"100%",height:"200px"},children:(0,a.jsx)(T.A,{ref:e=>{r.current[m]=e},canvasProps:{className:"w-100 h-100",style:{width:"100%",height:"200px",display:"block"}},backgroundColor:"white",penColor:"black"})}),(0,a.jsxs)("div",{className:"mt-2 d-flex flex-column flex-sm-row gap-2",children:[(0,a.jsxs)(y.A,{variant:"outline-secondary",size:"sm",onClick:()=>c(d.id,t),children:[(0,a.jsx)("i",{className:"bi bi-eraser me-1"}),"Clear"]}),(0,a.jsxs)(y.A,{variant:"primary",size:"sm",onClick:()=>o(d.id,t),children:[(0,a.jsx)("i",{className:"bi bi-check-circle me-1"}),"Save Signature"]})]})]})}),n.checklist["".concat(t,"-sign")]&&(0,a.jsx)("div",{className:"text-danger small mt-1",children:n.checklist["".concat(t,"-sign")]})]})})})},U=e=>{let{component:s,componentIndex:t,checklistData:i,setChecklistData:l,errorMap:n,assessor:r,handleFileUpload:o}=e,c=s.data,d=s.selected||"";return(0,a.jsx)(g.A,{className:"mb-3 shadow-sm",style:{border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"Yes"===d?"#f0fdf4":"No"===d?"#fef2f2":"#ffffff"},children:(0,a.jsxs)(g.A.Body,{className:"p-3",children:[(0,a.jsxs)("div",{className:"mb-3 fw-semibold d-flex align-items-start",children:[(0,a.jsx)("i",{className:"bi bi-check-square me-2 mt-1",style:{color:"Yes"===d?"#10b981":"No"===d?"#ef4444":"#6b7280",fontSize:"1rem"}}),(0,a.jsx)("span",{style:{color:"#374151",fontSize:"0.95rem"},children:c.text})]}),(0,a.jsx)("div",{className:"d-flex flex-column flex-sm-row w-100 mb-2 gap-2",children:["Yes","No","N/A"].map(e=>{let s="checkpoint-".concat(t,"-").concat(e),n=d===e;return(0,a.jsxs)("div",{className:"flex-grow-1",children:[(0,a.jsx)("input",{type:"radio",className:"btn-check",id:s,name:"checkpoint-".concat(t),autoComplete:"off",checked:d===e,onChange:()=>{let s=[...i];s[t].selected=e,"No"!==e&&(s[t].actionToBeTaken="",s[t].dueDate=null,s[t].assignee="",s[t].uploads=[]),s[t].remarks="",l(s)}}),(0,a.jsx)("label",{className:"btn w-100 ".concat("Yes"===e?n?"btn-success":"btn-outline-success":"No"===e?n?"btn-danger":"btn-outline-danger":n?"btn-warning":"btn-outline-warning"),htmlFor:s,style:{fontSize:"0.9rem",padding:"0.5rem"},children:e})]},e)})}),n.checklist["".concat(t,"-sel")]&&(0,a.jsx)("div",{className:"text-danger small",children:n.checklist["".concat(t,"-sel")]}),["Yes","N/A"].includes(d)&&(0,a.jsxs)(j.A.Group,{className:"mb-2",children:[(0,a.jsx)(j.A.Control,{placeholder:"Remarks",value:s.remarks||"",onChange:e=>{let s=[...i];s[t].remarks=e.target.value,l(s)},isInvalid:!!n.checklist["".concat(t,"-remarks")]}),(0,a.jsx)(j.A.Control.Feedback,{type:"invalid",children:n.checklist["".concat(t,"-remarks")]})]}),"No"===d&&(0,a.jsxs)("div",{className:"p-3 rounded border border-danger bg-light",children:[(0,a.jsxs)("h6",{className:"mb-3 d-flex align-items-center fw-semibold text-danger",children:[(0,a.jsx)("i",{className:"bi bi-exclamation-triangle me-2"}),"Action Required"]}),(0,a.jsxs)(j.A.Group,{className:"mb-2",children:[(0,a.jsx)(j.A.Label,{children:"Remarks"}),(0,a.jsx)(j.A.Control,{placeholder:"Remarks ",value:s.remarks||"",onChange:e=>{let s=[...i];s[t].remarks=e.target.value,l(s)},isInvalid:!!n.checklist["".concat(t,"-remarks")]}),(0,a.jsx)(j.A.Control.Feedback,{type:"invalid",children:n.checklist["".concat(t,"-remarks")]})]}),(0,a.jsxs)(j.A.Group,{className:"mb-2",children:[(0,a.jsx)(j.A.Label,{children:"Actions to be taken"}),(0,a.jsx)(j.A.Control,{placeholder:"Actions to be taken *",value:s.actionToBeTaken||"",onChange:e=>{let s=[...i];s[t].actionToBeTaken=e.target.value,l(s)},isInvalid:!!n.checklist["".concat(t,"-action")]}),(0,a.jsx)(j.A.Control.Feedback,{type:"invalid",children:n.checklist["".concat(t,"-action")]})]}),(0,a.jsxs)(j.A.Group,{className:"mb-2",children:[(0,a.jsx)(j.A.Label,{children:"Due Date"}),(0,a.jsx)("div",{className:n.checklist["".concat(t,"-due")]?"border border-danger rounded":"",children:(0,a.jsx)(k.Ay,{selected:s.dueDate?new Date(s.dueDate):null,onChange:e=>{let s=[...i];s[t].dueDate=e?e.toISOString():null,l(s)},minDate:new Date,placeholderText:"Select due date",dateFormat:"dd-MM-yyyy",className:"form-control",popperClassName:"datepicker-high-zindex"})}),n.checklist["".concat(t,"-due")]&&(0,a.jsx)("div",{className:"text-danger small mt-1",children:n.checklist["".concat(t,"-due")]})]}),(0,a.jsxs)(j.A.Group,{className:"mb-2",children:[(0,a.jsx)(j.A.Label,{children:"Assign Action to *"}),(0,a.jsx)(C.A,{title:"",options:r,selectedValue:s.assignee,onChange:e=>{let s=[...i];s[t].assignee=e,l(s)},placeholder:"Select Action Owner",clearable:!0,disabled:!1}),n.checklist["".concat(t,"-own")]&&(0,a.jsx)("div",{className:"text-danger small mt-1",children:n.checklist["".concat(t,"-own")]})]})]})]})})},H=e=>{let{component:s,componentIndex:t,checklistData:i,setChecklistData:l,errorMap:n,assessor:r,handleFileUpload:o}=e,c=s.data,d=n.group["".concat(t,"-answer")];return(0,a.jsx)(g.A,{className:"mb-3 shadow-sm",style:{border:"1px solid #e5e7eb",borderRadius:"8px",overflow:"hidden"},children:(0,a.jsxs)(g.A.Body,{className:"p-0",children:[(0,a.jsx)("div",{className:"p-3 border-bottom",style:{backgroundColor:"#3b82f6",color:"white"},children:(0,a.jsxs)("h6",{className:"fw-semibold mb-0 d-flex align-items-center",style:{fontSize:"0.95rem"},children:[(0,a.jsx)("i",{className:"bi bi-list-ul me-2"}),c.title]})}),(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center mb-3 p-3 border rounded",style:{backgroundColor:"Yes"===s.groupAnswer?"#f0f9ff":"#f9fafb",borderColor:"Yes"===s.groupAnswer?"#3b82f6":"#d1d5db"},children:[(0,a.jsx)("div",{className:"form-check form-switch me-3",children:(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"group-check-".concat(t),style:{width:"3em",height:"1.5em"},checked:"Yes"===s.groupAnswer,onChange:e=>{let s=[...i];s[t].groupAnswer=e.target.checked?"Yes":"No",!e.target.checked&&s[t].checkpoints&&s[t].checkpoints.forEach(e=>{e.selected="",e.remarks="",e.actionToBeTaken="",e.dueDate=null,e.assignee="",e.uploads=[]}),l(s)}})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"fw-semibold",style:{color:"Yes"===s.groupAnswer?"#3b82f6":"#6b7280",fontSize:"0.95rem"},children:"Yes"===s.groupAnswer?"✓ Section is applicable":"Section not applicable"}),(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"small text-muted",children:"Yes"===s.groupAnswer?"Complete the checkpoints below":"Toggle to show section checkpoints"})]})]}),d&&(0,a.jsx)("div",{className:"mb-3 small p-2 rounded text-danger bg-light border border-danger",children:d}),"No"===s.groupAnswer&&(0,a.jsx)("div",{className:"mb-3",children:(0,a.jsxs)(j.A.Group,{children:[(0,a.jsx)(j.A.Label,{children:"Reason for not applicable"}),(0,a.jsx)(j.A.Control,{as:"textarea",rows:3,placeholder:"Please provide a reason why this section is not applicable...",value:s.reason||"",onChange:e=>{let s=[...i];s[t].reason=e.target.value,l(s)},style:{borderRadius:"8px",borderWidth:"2px",padding:"0.75rem"}})]})}),"Yes"===s.groupAnswer&&s.checkpoints&&(0,a.jsx)("div",{className:"ps-3 pt-2",style:{borderLeft:"3px solid #3b82f6",backgroundColor:"#f8fafc"},children:s.checkpoints.map((e,s)=>{let o=e.selected||"",c="".concat(t,"-").concat(s);return(0,a.jsxs)("div",{className:"mb-3 p-3 rounded border",style:{backgroundColor:"Yes"===o?"#f0fdf4":"No"===o?"#fef2f2":"#ffffff",borderColor:"Yes"===o?"#10b981":"No"===o?"#ef4444":"#e5e7eb"},children:[(0,a.jsxs)("div",{className:"mb-3 fw-semibold d-flex align-items-start",children:[(0,a.jsx)("i",{className:"bi bi-check-circle me-2 mt-1",style:{color:"Yes"===o?"#10b981":"No"===o?"#ef4444":"#6b7280",fontSize:"1rem"}}),(0,a.jsx)("span",{style:{color:"#374151",fontSize:"0.9rem"},children:e.text})]}),(0,a.jsx)("div",{className:"d-flex flex-column flex-sm-row w-100 mb-2 gap-2",children:["Yes","No","N/A"].map(e=>{let n="cp-".concat(t,"-").concat(s,"-").concat(e),r=o===e;return(0,a.jsxs)("div",{className:"flex-grow-1",children:[(0,a.jsx)("input",{type:"radio",className:"btn-check",id:n,name:"cp-".concat(t,"-").concat(s),autoComplete:"off",checked:o===e,onChange:()=>{let a=[...i];a[t].checkpoints&&(a[t].checkpoints[s].selected=e,"No"!==e&&(a[t].checkpoints[s].actionToBeTaken="",a[t].checkpoints[s].dueDate=null,a[t].checkpoints[s].assignee="",a[t].checkpoints[s].uploads=[]),a[t].checkpoints[s].remarks=""),l(a)}}),(0,a.jsx)("label",{className:"btn w-100 ".concat("Yes"===e?r?"btn-success":"btn-outline-success":"No"===e?r?"btn-danger":"btn-outline-danger":r?"btn-warning":"btn-outline-warning"),htmlFor:n,style:{fontSize:"0.9rem",padding:"0.5rem"},children:e})]},e)})}),n.checklist["".concat(c,"-sel")]&&(0,a.jsx)("div",{className:"small p-2 rounded mb-2",style:{color:"#dc2626",backgroundColor:"#fef2f2",border:"1px solid #fecaca"},children:n.checklist["".concat(c,"-sel")]}),["Yes","N/A"].includes(o)&&(0,a.jsxs)(j.A.Group,{className:"mb-3",children:[(0,a.jsx)(j.A.Control,{placeholder:"Remarks (mandatory)...",value:e.remarks||"",onChange:e=>{let a=[...i];a[t].checkpoints&&(a[t].checkpoints[s].remarks=e.target.value),l(a)},isInvalid:!!n.checklist["".concat(c,"-remarks")],style:{borderRadius:"8px",borderWidth:"2px",padding:"0.75rem"}}),(0,a.jsx)(j.A.Control.Feedback,{type:"invalid",children:n.checklist["".concat(c,"-remarks")]})]}),"No"===o&&(0,a.jsxs)("div",{className:"p-3 rounded border border-danger bg-light",children:[(0,a.jsxs)("h6",{className:"mb-3 d-flex align-items-center fw-semibold text-danger",children:[(0,a.jsx)("i",{className:"bi bi-exclamation-triangle me-2"}),"Action Required"]}),(0,a.jsxs)(j.A.Group,{className:"mb-2",children:[(0,a.jsx)(j.A.Label,{children:"Remarks"}),(0,a.jsx)(j.A.Control,{placeholder:"Remarks (mandatory)...",value:e.remarks||"",onChange:e=>{let a=[...i];a[t].checkpoints&&(a[t].checkpoints[s].remarks=e.target.value),l(a)},isInvalid:!!n.checklist["".concat(c,"-remarks")]}),(0,a.jsx)(j.A.Control.Feedback,{type:"invalid",children:n.checklist["".concat(c,"-remarks")]})]}),(0,a.jsxs)(j.A.Group,{className:"mb-2",children:[(0,a.jsx)(j.A.Label,{children:"Actions to be taken"}),(0,a.jsx)(j.A.Control,{placeholder:"Actions to be taken *",value:e.actionToBeTaken||"",onChange:e=>{let a=[...i];a[t].checkpoints&&(a[t].checkpoints[s].actionToBeTaken=e.target.value),l(a)},isInvalid:!!n.checklist["".concat(c,"-action")]}),(0,a.jsx)(j.A.Control.Feedback,{type:"invalid",children:n.checklist["".concat(c,"-action")]})]}),(0,a.jsxs)(j.A.Group,{className:"mb-2",children:[(0,a.jsx)(j.A.Label,{children:"Due Date"}),(0,a.jsx)("div",{className:n.checklist["".concat(c,"-due")]?"border border-danger rounded":"",children:(0,a.jsx)(k.Ay,{selected:e.dueDate?new Date(e.dueDate):null,onChange:e=>{let a=[...i];a[t].checkpoints&&(a[t].checkpoints[s].dueDate=e?e.toISOString():null),l(a)},minDate:new Date,placeholderText:"Select due date",dateFormat:"dd-MM-yyyy",className:"form-control",popperClassName:"datepicker-high-zindex"})}),n.checklist["".concat(c,"-due")]&&(0,a.jsx)("div",{className:"text-danger small mt-1",children:n.checklist["".concat(c,"-due")]})]}),(0,a.jsxs)(j.A.Group,{className:"mb-2",children:[(0,a.jsx)(j.A.Label,{children:"Assign Action to *"}),(0,a.jsx)(C.A,{title:"",options:r,selectedValue:e.assignee,onChange:e=>{let a=[...i];a[t].checkpoints&&(a[t].checkpoints[s].assignee=e),l(a)},placeholder:"Select Action Owner",clearable:!0,disabled:!1}),n.checklist["".concat(c,"-own")]&&(0,a.jsx)("div",{className:"text-danger small mt-1",children:n.checklist["".concat(c,"-own")]})]})]})]},s)})})]})]})})},V=e=>{let{component:s,componentIndex:t,checklistData:i,setChecklistData:l,errorMap:n}=e,r=s.data,o=n.checklist["".concat(t,"-text")],c=e=>{let s=[...i];s[t].textValue=e,l(s)};return(0,a.jsx)(g.A,{className:"mb-3 shadow-sm",style:{border:"1px solid #e5e7eb",borderRadius:"8px",overflow:"hidden"},children:(0,a.jsx)(g.A.Body,{className:"p-2",children:(0,a.jsxs)(j.A.Group,{children:[(0,a.jsxs)(j.A.Label,{className:"fw-bold",children:[r.label,r.required&&(0,a.jsx)("span",{className:"text-danger ms-1",children:"*"})]}),(0,a.jsx)(j.A.Control,{type:"text",value:s.textValue||"",onChange:e=>c(e.target.value),placeholder:r.placeholder||"Enter text here...",className:o?"border-danger":""}),o&&(0,a.jsx)("div",{className:"text-danger small mt-1",children:o})]})})})};var G=t(57111);let J=e=>{let{component:s,componentIndex:t,checklistData:i,setChecklistData:l,errorMap:n}=e,r=s.data,o=n.checklist["".concat(t,"-image")];return(0,a.jsx)("div",{className:"mb-3",children:(0,a.jsxs)(j.A.Group,{children:[(0,a.jsxs)(j.A.Label,{className:"fw-bold",children:[r.label,r.required&&(0,a.jsx)("span",{className:"text-danger",children:" *"})]}),o&&(0,a.jsx)(G.A,{variant:"danger",className:"py-1 px-2 small mb-2",children:o}),(0,a.jsx)("div",{style:{maxWidth:"100%",overflow:"hidden"},children:(0,a.jsx)(S.A,{onFilesSelected:e=>{let s=[...i];s[t].imageFiles=e,s[t].data.uploads=e,l(s)},disabled:!1,files:s.imageFiles||r.uploads||[]})})]})})};var K=t(97726);let Z=e=>{let{component:s,componentIndex:t,checklistData:i,setChecklistData:l,errorMap:n}=e,r=s.data,o=n.checklist["".concat(t,"-attachment")];return(0,a.jsx)("div",{className:"mb-3",children:(0,a.jsxs)(j.A.Group,{children:[(0,a.jsxs)(j.A.Label,{className:"fw-bold",children:[r.label,r.required&&(0,a.jsx)("span",{className:"text-danger",children:" *"})]}),o&&(0,a.jsx)(G.A,{variant:"danger",className:"py-1 px-2 small mb-2",children:o}),(0,a.jsx)("div",{style:{maxWidth:"100%",overflow:"hidden"},children:(0,a.jsx)(K.A,{attachmentConfig:r.attachmentConfig,onFilesSelected:e=>{let s=[...i];s[t].uploads=e,s[t].data.uploads=e,l(s)},disabled:!1,files:s.uploads||r.uploads||[]})})]})})},Q=e=>{let{component:s,componentIndex:t,checklistData:i,setChecklistData:l,errorMap:n,assessor:r,signRefs:o,handleFileUpload:c,handleSaveSignature:d,handleClearSignature:m}=e;switch(s.type){case"header":return(0,a.jsx)(P,{component:s,componentIndex:t});case"section-header":return(0,a.jsx)(O,{component:s,componentIndex:t});case"text-body":return(0,a.jsx)(_,{component:s,componentIndex:t});case"date":return(0,a.jsx)(q,{component:s,componentIndex:t,checklistData:i,setChecklistData:l,errorMap:n});case"sign":return(0,a.jsx)(L,{component:s,componentIndex:t,checklistData:i,setChecklistData:l,errorMap:n,signRefs:o,handleSaveSignature:d,handleClearSignature:m});case"checkpoint":return(0,a.jsx)(U,{component:s,componentIndex:t,checklistData:i,setChecklistData:l,errorMap:n,assessor:r,handleFileUpload:c});case"checkpoint-group":return(0,a.jsx)(H,{component:s,componentIndex:t,checklistData:i,setChecklistData:l,errorMap:n,assessor:r,handleFileUpload:c});case"text-input":return(0,a.jsx)(V,{component:s,componentIndex:t,checklistData:i,setChecklistData:l,errorMap:n});case"image-input":return(0,a.jsx)(J,{component:s,componentIndex:t,checklistData:i,setChecklistData:l,errorMap:n});case"attachment-input":return(0,a.jsx)(Z,{component:s,componentIndex:t,checklistData:i,setChecklistData:l,errorMap:n});default:return null}},X=e=>{let{postActions:s,setPostActions:t,showPostActions:i,setShowPostActions:l,errorMap:n,assessor:r,handlePostActionChange:o,handlePostFileUpload:c,handleRemovePostAction:d,addNewPostAction:m}=e;return(0,a.jsxs)("div",{className:"mt-4",children:[!i&&(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)(y.A,{variant:"outline-primary",onClick:()=>{l(!0),t([{actionToBeTaken:"",dueDate:null,uploads:[],assignee:""}])},className:"w-100 w-sm-auto",children:[(0,a.jsx)("i",{className:"bi bi-plus-circle me-2"}),"Additional Actions"]})}),i&&(0,a.jsxs)("div",{className:"border-top pt-4",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center justify-content-between mb-2",children:[(0,a.jsxs)("h5",{className:"mb-0 fw-bold text-primary d-flex align-items-center",children:[(0,a.jsx)("i",{className:"bi bi-clipboard-check me-2"}),"Additional Actions"]}),(0,a.jsxs)(y.A,{variant:"outline-secondary",size:"sm",onClick:()=>{l(!1),t([])},children:[(0,a.jsx)("i",{className:"bi bi-x-circle me-1"}),"Cancel All"]})]}),(0,a.jsx)("p",{className:"mb-0 text-muted",children:"Please list any additional recommended actions identified during your QC Check"})]}),s.map((e,s)=>(0,a.jsxs)(g.A,{className:"mb-3 position-relative shadow-sm",children:[(0,a.jsx)("div",{className:"position-absolute bg-primary text-white rounded-circle d-flex justify-content-center align-items-center",style:{width:30,height:30,top:-10,left:15,fontWeight:"bold",fontSize:"14px",zIndex:2},children:s+1}),(0,a.jsx)(y.A,{variant:"outline-danger",size:"sm",className:"position-absolute",style:{top:10,right:10,zIndex:2},onClick:()=>d(s),title:"Remove this action",children:(0,a.jsx)("i",{className:"bi bi-trash"})}),(0,a.jsx)(g.A.Body,{className:"pt-4 p-3",children:(0,a.jsxs)("div",{className:"row",children:[(0,a.jsx)("div",{className:"col-12 mb-3",children:(0,a.jsxs)(j.A.Group,{children:[(0,a.jsxs)(j.A.Label,{className:"fw-semibold",children:[(0,a.jsx)("i",{className:"bi bi-pencil-square me-2 text-primary"}),"Action to be Taken *"]}),(0,a.jsx)(j.A.Control,{as:"textarea",rows:3,placeholder:"Describe the action that needs to be taken...",value:e.actionToBeTaken,onChange:e=>o(s,"actionToBeTaken",e.target.value),isInvalid:!!n.post["".concat(s,"-action")]}),(0,a.jsx)(j.A.Control.Feedback,{type:"invalid",children:n.post["".concat(s,"-action")]})]})}),(0,a.jsx)("div",{className:"col-12 col-md-6 mb-3",children:(0,a.jsxs)(j.A.Group,{children:[(0,a.jsxs)(j.A.Label,{className:"fw-semibold",children:[(0,a.jsx)("i",{className:"bi bi-calendar-event me-2 text-primary"}),"Due Date *"]}),(0,a.jsx)("div",{className:n.post["".concat(s,"-due")]?"border border-danger rounded":"",children:(0,a.jsx)(k.Ay,{selected:e.dueDate,onChange:e=>o(s,"dueDate",e?e.toISOString():null),minDate:new Date,placeholderText:"Select due date",dateFormat:"dd-MM-yyyy",className:"form-control"})}),n.post["".concat(s,"-due")]&&(0,a.jsx)("div",{className:"text-danger small mt-1",children:n.post["".concat(s,"-due")]})]})}),(0,a.jsx)("div",{className:"col-12 col-md-6 mb-3",children:(0,a.jsxs)(j.A.Group,{children:[(0,a.jsxs)(j.A.Label,{className:"fw-semibold",children:[(0,a.jsx)("i",{className:"bi bi-person-check me-2 text-primary"}),"Assign To *"]}),(0,a.jsx)(C.A,{title:"Action Owner",options:r,selectedValue:e.assignee,onChange:e=>o(s,"assignee",e||""),placeholder:"Select Action Owner",clearable:!0,disabled:!1}),n.post["".concat(s,"-own")]&&(0,a.jsx)("div",{className:"text-danger small mt-1",children:n.post["".concat(s,"-own")]})]})})]})})]},s)),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)(y.A,{variant:"outline-primary",onClick:m,className:"px-4",children:[(0,a.jsx)("i",{className:"bi bi-plus-circle me-2"}),"Add More"]})})]})]})},$=e=>{var s,t;let{applicationDetails:i}=e;return(0,a.jsx)(g.A,{className:"mb-4 shadow-sm",children:(0,a.jsxs)(g.A.Body,{children:[(0,a.jsxs)(f.A,{className:"mb-3",children:[(0,a.jsxs)(b.A,{xs:12,md:4,className:"mb-2 mb-md-0",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center mb-1",children:[(0,a.jsx)("i",{className:"bi bi-tag me-2 text-primary"}),(0,a.jsx)("h6",{className:"obs-title mb-0 small",children:"Inspection Category"})]}),(0,a.jsx)("p",{className:"obs-content ms-4 mb-0 small",children:i.inspectionCategory||"N/A"})]}),(0,a.jsxs)(b.A,{xs:12,md:4,className:"mb-2 mb-md-0",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center mb-1",children:[(0,a.jsx)("i",{className:"bi bi-calendar-check me-2 text-primary"}),(0,a.jsx)("h6",{className:"obs-title mb-0 small",children:"Scheduled Date"})]}),(0,a.jsx)("p",{className:"obs-content ms-4 mb-0 small",children:i.scheduledDate?W()(i.scheduledDate).format("DD-MM-YYYY"):"N/A"})]}),(0,a.jsxs)(b.A,{xs:12,md:4,className:"mb-2 mb-md-0",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center mb-1",children:[(0,a.jsx)("i",{className:"bi bi-calendar-x me-2 text-danger"}),(0,a.jsx)("h6",{className:"obs-title mb-0 small",children:"Due Date"})]}),(0,a.jsx)("p",{className:"obs-content ms-4 mb-0 small",children:i.dueDate?W()(i.dueDate).format("DD-MM-YYYY"):"N/A"})]})]}),(0,a.jsxs)(f.A,{className:"mb-3",children:[(0,a.jsxs)(b.A,{xs:12,md:4,className:"mb-2 mb-md-0",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center mb-1",children:[(0,a.jsx)("i",{className:"bi bi-person-badge me-2 text-primary"}),(0,a.jsx)("h6",{className:"obs-title mb-0 small",children:"Inspector"})]}),(0,a.jsx)("p",{className:"obs-content ms-4 mb-0 small",children:(null==(s=i.inspector)?void 0:s.firstName)||"N/A"})]}),(0,a.jsxs)(b.A,{xs:12,md:4,className:"mb-2 mb-md-0",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center mb-1",children:[(0,a.jsx)("i",{className:"bi bi-list-check me-2 text-primary"}),(0,a.jsx)("h6",{className:"obs-title mb-0 small",children:"Checklist"})]}),(0,a.jsx)("p",{className:"obs-content ms-4 mb-0 small",children:(null==(t=i.checklist)?void 0:t.name)||"N/A"})]}),(0,a.jsxs)(b.A,{xs:12,md:4,className:"mb-2 mb-md-0",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center mb-1",children:[(0,a.jsx)("i",{className:"bi bi-code-square me-2 text-primary"}),(0,a.jsx)("h6",{className:"obs-title mb-0 small",children:"Checklist Version"})]}),(0,a.jsx)("p",{className:"obs-content ms-4 mb-0 small",children:i.checklistVersion||"N/A"})]})]}),(0,a.jsx)(f.A,{className:"mb-3",children:(0,a.jsxs)(b.A,{xs:12,children:[(0,a.jsxs)("div",{className:"d-flex align-items-center mb-1",children:[(0,a.jsx)("i",{className:"bi bi-geo-alt me-2 text-primary"}),(0,a.jsx)("h6",{className:"obs-title mb-0 small",children:"Location"})]}),(0,a.jsx)("p",{className:"obs-content ms-4 mb-0 small",children:[i.locationOne,i.locationTwo,i.locationThree,i.locationFour,i.locationFive,i.locationSix].filter(e=>null==e?void 0:e.name).map(e=>null==e?void 0:e.name).join(" > ")||"N/A"})]})}),(0,a.jsx)("div",{className:"border-top pt-3",children:(0,a.jsxs)("h6",{className:"fw-semibold font-size-14 mb-0 text-primary d-flex align-items-center",children:[(0,a.jsx)("i",{className:"bi bi-clipboard-check me-2"}),"Select all applicable sections and complete their checklist items"]})})]})})},ee=e=>{var s,t,l,n;let{show:r,applicationDetails:o,showItem:c,closeModal:d}=e,m=(0,i.useRef)({}),[h,b]=(0,i.useState)([]),[g,f]=(0,i.useState)([]),[j,N]=(0,i.useState)([]),[v,k]=(0,i.useState)(!1),[w,S]=(0,i.useState)({group:{},checklist:{},post:{}}),C=(0,i.useCallback)(async e=>{try{var s,t,a,i;let l=await x.A.post(p.u3,{locationOneId:(null==o||null==(s=o.locationOne)?void 0:s.id)||"",locationTwoId:(null==o||null==(t=o.locationTwo)?void 0:t.id)||"",locationThreeId:(null==o||null==(a=o.locationThree)?void 0:a.id)||"",locationFourId:(null==o||null==(i=o.locationFour)?void 0:i.id)||"",mode:e});if(200===l.status){let e=l.data.map(e=>({label:e.firstName,value:e.id}));b(e)}}catch(e){console.error("Error fetching crew list:",e)}},[null==o||null==(s=o.locationOne)?void 0:s.id,null==o||null==(t=o.locationTwo)?void 0:t.id,null==o||null==(l=o.locationThree)?void 0:l.id,null==o||null==(n=o.locationFour)?void 0:n.id]);(0,i.useEffect)(()=>{var e,s;(null==o||null==(s=o.checklist)||null==(e=s.value)?void 0:e.components)&&f(JSON.parse(JSON.stringify(o.checklist.value.components)).map(e=>{let s={...e};if("checkpoint-group"===e.type)s.groupAnswer="",s.checkpoints=e.data.checkpoints.map(e=>({id:e.id,text:e.text,selected:"",remarks:"",actionToBeTaken:"",dueDate:null,assignee:"",uploads:[]}));else if("checkpoint"===e.type)s.selected="",s.remarks="",s.actionToBeTaken="",s.dueDate=null,s.assignee="",s.uploads=[];else if("date"===e.type)s.selectedDate=null;else if("sign"===e.type)s.signature="";else if("text-input"===e.type)s.textValue="";else if("image-input"===e.type){let t=e.data.uploads||[];s.imageFiles=t,s.data.uploads=t}return s}).sort((e,s)=>e.position-s.position))},[o]),(0,i.useEffect)(()=>{C("ins_action_owner")},[c,C]);let F=async(e,s)=>{let t=[...j];t[s].uploads=e,N(t)},T=async(e,s,t)=>{if(!e.length)return;let a=[...g],i=a[s];"checkpoint-group"===i.type&&void 0!==t&&i.checkpoints?i.checkpoints[t].uploads=e:"checkpoint"===i.type&&(i.uploads=e),f(a)},D=async(e,s)=>{let t="".concat(e,"-").concat(s),a=m.current[t];if(console.log("Save signature called:",{componentId:e,cIdx:s,componentKey:t,canvas:a}),!a)return void A().fire("Error","Signature canvas not found","error");if(a.isEmpty())return void A().fire("Error","Please provide a signature before saving","error");try{var i,l,n;let t=a.toDataURL("image/png",1);console.log("DataURL generated:",t.substring(0,50)+"...");let r=await fetch(t),o=await r.blob();console.log("Blob created:",o.size,"bytes");let c=new FormData,d="signature_".concat(e,"_").concat(Date.now(),".png");c.append("file",o,d),console.log("FormData created with filename:",d);let m=await x.A.post(p.Dp,c,{headers:{"Content-Type":"multipart/form-data"}});if(console.log("Upload response:",m),null==m||null==(n=m.data)||null==(l=n.files)||null==(i=l[0])?void 0:i.originalname){let e=m.data.files[0].originalname;console.log("File uploaded successfully:",e);let t=[...g];t[s].signature=e,f(t),A().fire("Success","Signature saved successfully","success")}else console.error("Upload response missing files array:",m),A().fire("Error","Upload response was invalid","error")}catch(e){console.error("Signature save error:",e),A().fire("Error","Failed to save signature","error")}},I=(e,s)=>{let t="".concat(e,"-").concat(s),a=m.current[t];a&&a.clear()},R=()=>{let e={group:{},checklist:{},post:{}};return g.forEach((s,t)=>{let a=s.data;if("checkpoint-group"===s.type){if(!s.groupAnswer){e.group["".concat(t,"-answer")]="Select Yes or No";return}"Yes"===s.groupAnswer&&s.checkpoints&&s.checkpoints.forEach((s,a)=>{let i="".concat(t,"-").concat(a);s.selected?("No"!==s.selected&&"N/A"!==s.selected||s.remarks||(e.checklist["".concat(i,"-remarks")]="Required"),"No"===s.selected&&(s.actionToBeTaken||(e.checklist["".concat(i,"-action")]="Required"),s.dueDate||(e.checklist["".concat(i,"-due")]="Required"),s.assignee||(e.checklist["".concat(i,"-own")]="Required"))):e.checklist["".concat(i,"-sel")]="Select an option"})}else if("checkpoint"===s.type)a.required&&!s.selected?e.checklist["".concat(t,"-sel")]="Select an option":s.selected&&("No"!==s.selected&&"N/A"!==s.selected||s.remarks||(e.checklist["".concat(t,"-remarks")]="Required"),"No"===s.selected&&(s.actionToBeTaken||(e.checklist["".concat(t,"-action")]="Required"),s.dueDate||(e.checklist["".concat(t,"-due")]="Required"),s.assignee||(e.checklist["".concat(t,"-own")]="Required")));else if("date"===s.type)a.required&&!s.selectedDate&&(e.checklist["".concat(t,"-date")]="Date is required");else if("sign"===s.type)a.required&&!s.signature&&(e.checklist["".concat(t,"-sign")]="Signature is required");else if("text-input"===s.type){var i;!a.required||(null==(i=s.textValue)?void 0:i.trim())||(e.checklist["".concat(t,"-text")]="This field is required")}else if("image-input"===s.type){let i=s.imageFiles&&s.imageFiles.length>0||a.uploads&&a.uploads.length>0;a.required&&!i&&(e.checklist["".concat(t,"-image")]="At least one image is required")}else if("attachment-input"===s.type){let i=s.uploads&&s.uploads.length>0||a.uploads&&a.uploads.length>0;a.required&&!i&&(e.checklist["".concat(t,"-attachment")]="At least one attachment is required")}}),j.forEach((s,t)=>{s.actionToBeTaken||(e.post["".concat(t,"-action")]="Required"),s.dueDate||(e.post["".concat(t,"-due")]="Required"),s.assignee||(e.post["".concat(t,"-own")]="Required")}),S(e),!(Object.keys(e.group).length+Object.keys(e.checklist).length+Object.keys(e.post).length>0)},E=e=>{var s;if("checkpoint-group"!==e.type)return e;let t=e.data,a=(null==(s=e.checkpoints)?void 0:s.map(e=>({id:e.id,text:e.text,selected:e.selected||"",remarks:e.remarks||"",actionToBeTaken:e.actionToBeTaken||"",dueDate:e.dueDate||null,assignee:e.assignee||"",uploads:e.uploads||[]})))||[];return{id:e.id,type:e.type,position:e.position,data:{id:t.id,type:t.type,position:t.position,required:t.required,title:t.title,checkpoints:a},validation:e.validation,isChecked:"Yes"===e.groupAnswer,groupAnswer:e.groupAnswer||"",reason:e.reason||""}},z=async()=>{if(!R())return void A().fire("Validation","Please fix the highlighted errors.","error");try{let e=g.map(e=>"checkpoint-group"===e.type?E(e):e),s={checklist:e,postActions:j};console.log("Transformed payload:",JSON.stringify(s,null,2));let t=e.filter(e=>"checkpoint-group"===e.type);t.length>0&&console.log("Checkpoint Groups in desired format:",JSON.stringify(t,null,2));let a=await x.A.patch((0,p.x2)(c.id),s);204===a.status&&(A().fire("Inspection","Submitted Successfully","success"),d())}catch(e){console.error("Error:",e)}};return(0,a.jsxs)(u.A,{show:r,size:"lg",onHide:()=>d(),"aria-labelledby":"conduct-action",children:[(0,a.jsx)(u.A.Header,{closeButton:!0,children:o&&(0,a.jsxs)("div",{className:"w-100",children:[(0,a.jsx)("h4",{children:"Inspection"}),(0,a.jsxs)("div",{className:"d-flex align-items-center",children:[(0,a.jsxs)("p",{className:"me-2 mb-0",children:["#",o.maskId||""]}),(0,a.jsx)("p",{className:"card-eptw mb-0",children:o.status})]})]})}),(0,a.jsxs)(u.A.Body,{style:{maxHeight:"70vh",overflowY:"auto"},children:[(0,a.jsx)($,{applicationDetails:o}),(0,a.jsx)("div",{className:"px-2 px-md-0",children:Array.isArray(g)&&g.map((e,s)=>(0,a.jsx)("div",{className:"mb-3 mb-md-4",children:(0,a.jsx)(Q,{component:e,componentIndex:s,checklistData:g,setChecklistData:f,errorMap:w,assessor:h,signRefs:m,handleFileUpload:T,handleSaveSignature:D,handleClearSignature:I})},s))}),(0,a.jsx)(X,{postActions:j,setPostActions:N,showPostActions:v,setShowPostActions:k,errorMap:w,assessor:h,handlePostActionChange:(e,s,t)=>{let a=[...j];a[e][s]=t,N(a)},handlePostFileUpload:F,handleRemovePostAction:e=>{let s=[...j];s.splice(e,1),N(s)},addNewPostAction:()=>{N([...j,{actionToBeTaken:"",dueDate:null,uploads:[],assignee:""}])}})]}),(0,a.jsx)(u.A.Footer,{children:(0,a.jsx)(y.A,{type:"button",variant:"primary",onClick:z,children:"Submit"})})]})},es=e=>{let s=new Date,t=(new Date(e).getTime()-s.getTime())/36e5;return t<0?{status:"Overdue",color:"#dc3545",bgColor:"#f8d7da",textColor:"#721c24"}:t/24<=1?{status:"Due Soon",color:"#fd7e14",bgColor:"#fff3cd",textColor:"#856404"}:{status:"Upcoming",color:"#198754",bgColor:"#d1e7dd",textColor:"#0f5132"}},et=function(){let{externalShowModal:e,externalSetShowModal:s,selectedFilter:t,setSelectedFilter:l,searchQuery:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[o,c]=(0,i.useState)(!1),[d,u]=(0,i.useState)(!1),b=s||c,[g,f]=(0,i.useState)("All"),j=void 0!==t?t:g,N=l||f,[y,v]=(0,i.useState)([]),[A,k]=(0,i.useState)(!1),[w,S]=(0,i.useState)(null),[C,T]=(0,i.useState)(null),D=(0,r.d4)(e=>e.service.service),R=!!w&&["perform_task","reperform_task","verify_task"].includes(w.actionType);(0,i.useEffect)(()=>{z(j)},[d,j]);let z=async e=>{try{k(!0);let s=await h.M.getActions(e);v(s||[])}catch(e){console.error("Error fetching actions:",e),v([])}finally{k(!1)}},M=async e=>{try{let s="".concat((0,p.P4)(e.applicationId),"?filter=").concat(encodeURIComponent(JSON.stringify({include:[{relation:"locationOne"},{relation:"locationTwo"},{relation:"locationThree"},{relation:"locationFour"},{relation:"locationFive"},{relation:"locationSix"},{relation:"reporter"},{relation:"actionOwner"},{relation:"reviewer"},{relation:"observationActions"}]}))),t=await x.A.get(s);200===t.status?T(t.data):console.error("Unexpected response status:",t.status)}catch(e){console.error("Error fetching incidents:",e)}S(e),u(!0)},B=async e=>{try{let s={include:[{relation:"department"},{relation:"teamLeader"},{relation:"workActivity"},{relation:"raTeamMembers",scope:{include:[{relation:"user"}]}}]},t="".concat((0,p.iJ)(e.applicationId),"?filter=").concat(encodeURIComponent(JSON.stringify(s))),a=await x.A.get(t);200===a.status?T(a.data):console.error("Unexpected response status:",a.status)}catch(e){console.error("Error fetching incidents:",e)}S(e),u(!0)},W=async e=>{try{let s="".concat((0,p.mh)(e.applicationId),"?filter=").concat(encodeURIComponent(JSON.stringify({include:[{relation:"locationOne"},{relation:"locationTwo"},{relation:"locationThree"},{relation:"locationFour"},{relation:"locationFive"},{relation:"locationSix"},{relation:"applicant"},{relation:"assessor"},{relation:"approver"},{relation:"reviewer"}]}))),t=await x.A.get(s);200===t.status?T(t.data):console.error("Unexpected response status:",t.status)}catch(e){console.error("Error fetching incidents:",e)}S(e),u(!0)},P=async e=>{try{let s="".concat((0,p.xo)(e.applicationId),"?filter=").concat(encodeURIComponent(JSON.stringify({include:[{relation:"locationOne"},{relation:"locationTwo"},{relation:"locationThree"},{relation:"locationFour"},{relation:"locationFive"},{relation:"locationSix"},{relation:"inspector"},{relation:"assignedBy"},{relation:"checklist"}]}))),t=await x.A.get(s);200===t.status?T(t.data):console.error("Unexpected response status:",t.status)}catch(e){console.error("Error fetching incidents:",e)}S(e),u(!0)},O=y.filter(e=>{var s,t,a,i,l;if(!n||""===n.trim())return!0;let r=n.toLowerCase();return(null==(s=e.maskId)?void 0:s.toLowerCase().includes(r))||(null==(t=e.description)?void 0:t.toLowerCase().includes(r))||(null==(a=e.actionToBeTaken)?void 0:a.toLowerCase().includes(r))||(null==(i=e.application)?void 0:i.toLowerCase().includes(r))||(null==(l=e.actionType)?void 0:l.toLowerCase().includes(r))});return(0,a.jsxs)("div",{className:"jsx-abef1019a04c510e container-fluid px-0",children:[(0,a.jsx)("div",{className:"jsx-abef1019a04c510e bg-transparent",children:(0,a.jsx)("div",{className:"jsx-abef1019a04c510e p-0",children:(0,a.jsx)("div",{className:"jsx-abef1019a04c510e",children:A?(0,a.jsx)("p",{className:"jsx-abef1019a04c510e m-2",children:"Loading..."}):O.length>0?O.map((e,s)=>{if("OBS"===e.application){let t=es(e.dueDate);return(0,a.jsxs)("div",{style:{cursor:"pointer",border:"none"},onClick:()=>M(e),className:"jsx-abef1019a04c510e bg-white rounded-3 mb-3 position-relative shadow-sm",children:[(0,a.jsx)("div",{style:{left:0,top:0,width:"4px",backgroundColor:t.color},className:"jsx-abef1019a04c510e position-absolute h-100 rounded-start"}),(0,a.jsxs)("div",{style:{paddingLeft:"20px"},className:"jsx-abef1019a04c510e p-3",children:[(0,a.jsxs)("div",{className:"jsx-abef1019a04c510e d-flex justify-content-between align-items-start mb-2",children:[(0,a.jsxs)("div",{className:"jsx-abef1019a04c510e flex-grow-1",children:[(0,a.jsx)("h6",{style:{fontSize:"16px",color:"#212121"},className:"jsx-abef1019a04c510e mb-1 fw-medium",children:e.maskId}),(0,a.jsx)("p",{style:{fontSize:"14px"},className:"jsx-abef1019a04c510e mb-2 text-muted",children:e.description})]}),(0,a.jsx)("span",{style:{backgroundColor:"#fff3e0",color:"#f57c00",fontSize:"12px"},className:"jsx-abef1019a04c510e badge rounded-pill px-2 py-1",children:"take_action"===e.actionType?"Take Action":"verify_action"===e.actionType?"Verify Action":"review"===e.actionType?"In Review":"reperform_action"===e.actionType?"Reperform Action":""})]}),(0,a.jsxs)("div",{className:"jsx-abef1019a04c510e d-flex justify-content-between align-items-center",children:[(0,a.jsx)("span",{style:{backgroundColor:t.bgColor,color:t.textColor,fontSize:"11px",fontWeight:"500"},className:"jsx-abef1019a04c510e badge rounded-pill px-2 py-1",children:t.status}),(0,a.jsxs)("button",{style:{color:"#1976d2"},className:"jsx-abef1019a04c510e btn btn-link p-0 text-decoration-none d-flex align-items-center small",children:["View Details ",(0,a.jsx)("i",{className:"jsx-abef1019a04c510e bi bi-chevron-right ms-1"})]})]})]})]},s)}if("RA"===e.application){let t=es(e.dueDate);return(0,a.jsxs)("div",{style:{cursor:"pointer",border:"none"},onClick:()=>B(e),className:"jsx-abef1019a04c510e bg-white rounded-3 mb-3 position-relative shadow-sm",children:[(0,a.jsx)("div",{style:{left:0,top:0,width:"4px",backgroundColor:t.color},className:"jsx-abef1019a04c510e position-absolute h-100 rounded-start"}),(0,a.jsxs)("div",{style:{paddingLeft:"20px"},className:"jsx-abef1019a04c510e p-3",children:[(0,a.jsxs)("div",{className:"jsx-abef1019a04c510e d-flex justify-content-between align-items-start mb-2",children:[(0,a.jsxs)("div",{className:"jsx-abef1019a04c510e flex-grow-1",children:[(0,a.jsx)("h6",{style:{fontSize:"16px",color:"#212121"},className:"jsx-abef1019a04c510e mb-1 fw-medium",children:e.description}),(0,a.jsx)("p",{style:{fontSize:"14px"},className:"jsx-abef1019a04c510e mb-2 text-muted",children:e.maskId})]}),(0,a.jsx)("span",{style:{backgroundColor:"#e3f2fd",color:"#1565c0",fontSize:"12px"},className:"jsx-abef1019a04c510e badge rounded-pill px-2 py-1",children:"Pending"})]}),(0,a.jsxs)("div",{className:"jsx-abef1019a04c510e d-flex justify-content-between align-items-center",children:[(0,a.jsx)("span",{style:{backgroundColor:t.bgColor,color:t.textColor,fontSize:"11px",fontWeight:"500"},className:"jsx-abef1019a04c510e badge rounded-pill px-2 py-1",children:t.status}),(0,a.jsxs)("button",{style:{color:"#1976d2"},className:"jsx-abef1019a04c510e btn btn-link p-0 text-decoration-none d-flex align-items-center small",children:["View Details ",(0,a.jsx)("i",{className:"jsx-abef1019a04c510e bi bi-chevron-right ms-1"})]})]})]})]},s)}if("EPTW-GEN"===e.application){let t=es(e.dueDate);return(0,a.jsxs)("div",{style:{cursor:"pointer",boxShadow:"0 2px 4px rgba(0,0,0,0.1)",border:"none"},onClick:()=>W(e),className:"jsx-abef1019a04c510e bg-white rounded-3 mb-3 position-relative",children:[(0,a.jsx)("div",{style:{left:0,top:0,width:"4px",backgroundColor:t.color},className:"jsx-abef1019a04c510e position-absolute h-100 rounded-start"}),(0,a.jsxs)("div",{style:{paddingLeft:"20px"},className:"jsx-abef1019a04c510e p-4",children:[(0,a.jsxs)("div",{className:"jsx-abef1019a04c510e d-flex justify-content-between align-items-start mb-2",children:[(0,a.jsxs)("div",{className:"jsx-abef1019a04c510e flex-grow-1",children:[(0,a.jsx)("h6",{style:{fontSize:"16px",color:"#212121",lineHeight:"1.4"},className:"jsx-abef1019a04c510e mb-1 fw-medium",children:e.description}),(0,a.jsxs)("p",{style:{fontSize:"14px",color:"#757575",lineHeight:"1.4"},className:"jsx-abef1019a04c510e mb-2",children:[e.maskId," - ",e.actionToBeTaken]})]}),(0,a.jsx)("span",{style:{backgroundColor:"#f3e5f5",color:"#7b1fa2",fontSize:"12px",fontWeight:"500"},className:"jsx-abef1019a04c510e badge rounded-pill px-3 py-1",children:"Pending"})]}),(0,a.jsxs)("div",{className:"jsx-abef1019a04c510e d-flex justify-content-between align-items-center",children:[(0,a.jsx)("span",{style:{backgroundColor:t.bgColor,color:t.textColor,fontSize:"11px",fontWeight:"500"},className:"jsx-abef1019a04c510e badge rounded-pill px-2 py-1",children:t.status}),(0,a.jsxs)("button",{style:{fontSize:"14px",color:"#1976d2",fontWeight:"500"},className:"jsx-abef1019a04c510e btn btn-link p-0 text-decoration-none d-flex align-items-center",children:["View Details ",(0,a.jsx)("i",{className:"jsx-abef1019a04c510e bi bi-chevron-right ms-1"})]})]})]})]},s)}if("INS"===e.application){let t=es(e.dueDate);return(0,a.jsxs)("div",{style:{cursor:"pointer",boxShadow:"0 2px 4px rgba(0,0,0,0.1)",border:"none"},onClick:()=>P(e),className:"jsx-abef1019a04c510e bg-white rounded-3 mb-3 position-relative",children:[(0,a.jsx)("div",{style:{left:0,top:0,width:"4px",backgroundColor:t.color},className:"jsx-abef1019a04c510e position-absolute h-100 rounded-start"}),(0,a.jsxs)("div",{style:{paddingLeft:"20px"},className:"jsx-abef1019a04c510e p-4",children:[(0,a.jsxs)("div",{className:"jsx-abef1019a04c510e d-flex justify-content-between align-items-start mb-2",children:[(0,a.jsxs)("div",{className:"jsx-abef1019a04c510e flex-grow-1",children:[(0,a.jsx)("h6",{style:{fontSize:"16px",color:"#212121",lineHeight:"1.4"},className:"jsx-abef1019a04c510e mb-1 fw-medium",children:"conduct_inspection"===e.actionType?"Conduct Inspection":"perform_task"===e.actionType?"Take Action":"verify_task"===e.actionType?"Verify Action":"reperform_task"===e.actionType?"Retake Action":e.actionToBeTaken}),(0,a.jsx)("p",{style:{fontSize:"14px",color:"#757575",lineHeight:"1.4"},className:"jsx-abef1019a04c510e mb-2",children:"conduct_inspection"===e.actionType?e.maskId:e.maskId+"-IA-"+(e.counter+1)}),(0,a.jsx)("p",{className:"jsx-abef1019a04c510e m-0",children:e.description})]}),(0,a.jsx)("span",{style:{backgroundColor:"#e8f5e8",color:"#2e7d32",fontSize:"12px",fontWeight:"500"},className:"jsx-abef1019a04c510e badge rounded-pill px-3 py-1",children:"In Progress"})]}),(0,a.jsxs)("div",{className:"jsx-abef1019a04c510e d-flex justify-content-between align-items-center",children:[(0,a.jsx)("span",{style:{backgroundColor:t.bgColor,color:t.textColor,fontSize:"11px",fontWeight:"500"},className:"jsx-abef1019a04c510e badge rounded-pill px-2 py-1",children:t.status}),(0,a.jsxs)("button",{style:{fontSize:"14px",color:"#1976d2",fontWeight:"500"},className:"jsx-abef1019a04c510e btn btn-link p-0 text-decoration-none d-flex align-items-center",children:["View Details ",(0,a.jsx)("i",{className:"jsx-abef1019a04c510e bi bi-chevron-right ms-1"})]})]})]})]},s)}return null}):(0,a.jsx)("div",{className:"jsx-abef1019a04c510e text-center py-4",children:(0,a.jsxs)("div",{className:"jsx-abef1019a04c510e d-flex flex-column align-items-center",children:[(0,a.jsx)("i",{style:{fontSize:"24px"},className:"jsx-abef1019a04c510e bi bi-search text-muted mb-2"}),(0,a.jsx)("p",{className:"jsx-abef1019a04c510e text-muted mb-1",children:n&&""!==n.trim()?"No actions match your search":"No actions found"}),n&&""!==n.trim()&&(0,a.jsx)("small",{className:"jsx-abef1019a04c510e text-muted",children:"Try adjusting your search terms"})]})})})})}),(void 0!==e?e:o)&&(0,a.jsx)("div",{style:{backgroundColor:"rgba(0,0,0,0.5)"},onClick:()=>b(!1),className:"jsx-abef1019a04c510e modal fade show d-block",children:(0,a.jsx)("div",{onClick:e=>e.stopPropagation(),className:"jsx-abef1019a04c510e modal-dialog modal-dialog-centered",children:(0,a.jsxs)("div",{style:{borderRadius:"16px",border:"none"},className:"jsx-abef1019a04c510e modal-content",children:[(0,a.jsxs)("div",{style:{borderColor:"#F0F0F0 !important"},className:"jsx-abef1019a04c510e modal-header border-bottom",children:[(0,a.jsx)("h5",{style:{fontSize:"18px",fontWeight:"600"},className:"jsx-abef1019a04c510e modal-title",children:"Filter Actions"}),(0,a.jsx)("button",{type:"button",onClick:()=>b(!1),className:"jsx-abef1019a04c510e btn-close"})]}),(0,a.jsxs)("div",{style:{padding:"16px"},className:"jsx-abef1019a04c510e modal-body",children:[(0,a.jsxs)("button",{style:{padding:"16px",borderRadius:"12px",border:"1px solid #E5E7EB",backgroundColor:"All"===j?"rgba(0, 122, 255, 0.1)":"transparent",color:"All"===j?"#007AFF":"#000000",textAlign:"left"},onClick:()=>{N("All"),b(!1)},className:"jsx-abef1019a04c510e btn w-100 d-flex justify-content-between align-items-center mb-2",children:[(0,a.jsx)("span",{style:{fontSize:"16px",fontWeight:"500"},className:"jsx-abef1019a04c510e",children:"All Actions"}),"All"===j&&(0,a.jsx)("i",{style:{color:"#007AFF",fontSize:"20px"},className:"jsx-abef1019a04c510e bi bi-check-circle-fill"})]}),D&&D.map(e=>(0,a.jsxs)("button",{style:{padding:"16px",borderRadius:"12px",border:"1px solid #E5E7EB",backgroundColor:j===e.maskName?"rgba(0, 122, 255, 0.1)":"transparent",color:j===e.maskName?"#007AFF":"#000000",textAlign:"left"},onClick:()=>{N(e.maskName),b(!1)},className:"jsx-abef1019a04c510e btn w-100 d-flex justify-content-between align-items-center mb-2",children:[(0,a.jsx)("span",{style:{fontSize:"16px",fontWeight:"500"},className:"jsx-abef1019a04c510e",children:e.name}),j===e.maskName&&(0,a.jsx)("i",{style:{color:"#007AFF",fontSize:"20px"},className:"jsx-abef1019a04c510e bi bi-check-circle-fill"})]},e.id)),"All"!==j&&(0,a.jsxs)("button",{style:{padding:"16px",borderRadius:"12px",backgroundColor:"#FFF2F2",border:"1px solid #FFE5E5",color:"#FF3B30"},onClick:()=>{N("All"),b(!1)},className:"jsx-abef1019a04c510e btn w-100 d-flex align-items-center justify-content-center mt-3",children:[(0,a.jsx)("i",{style:{fontSize:"20px"},className:"jsx-abef1019a04c510e bi bi-x-circle me-2"}),(0,a.jsx)("span",{style:{fontSize:"16px",fontWeight:"600"},className:"jsx-abef1019a04c510e",children:"Clear All Filters"})]})]})]})})}),d&&w&&"OBS"===w.application&&(0,a.jsx)(F,{show:d,applicationDetails:C||void 0,showItem:w,closeModal:()=>u(!1)}),d&&w&&"RA"===w.application&&(0,a.jsx)(I,{show:d,applicationDetails:C,showItem:w,closeModal:()=>u(!1)}),d&&w&&"EPTW-GEN"===w.application&&(0,a.jsx)(E,{show:d,applicationDetails:C,showItem:w,closeModal:()=>u(!1)}),d&&w&&"INS"===w.application&&(R?(0,a.jsx)(Y,{show:d,applicationDetails:C,showItem:w,closeModal:()=>u(!1)}):(0,a.jsx)(ee,{show:d,applicationDetails:C,showItem:w,closeModal:()=>u(!1)})),(0,a.jsx)(m(),{id:"abef1019a04c510e",children:'.tick-container.jsx-abef1019a04c510e{display:-webkit-inline-box;display:-webkit-inline-flex;display:-moz-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;position:relative;padding-left:32px;margin-bottom:12px;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;font-size:1rem;gap:8px;width:100%}.tick-container.jsx-abef1019a04c510e input.jsx-abef1019a04c510e{position:absolute;opacity:0;cursor:pointer;height:0;width:0}.checkmark.jsx-abef1019a04c510e{position:absolute;left:0;top:0;height:20px;width:20px;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;border:2px solid#007bff;background-color:#fff;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.tick-container.jsx-abef1019a04c510e:hover input.jsx-abef1019a04c510e~.checkmark.jsx-abef1019a04c510e{background-color:#f0f8ff}.tick-container.jsx-abef1019a04c510e input.jsx-abef1019a04c510e:checked~.checkmark.jsx-abef1019a04c510e{background-color:#007bff}.checkmark.jsx-abef1019a04c510e:after{content:"";position:absolute;display:none}.tick-container.jsx-abef1019a04c510e input.jsx-abef1019a04c510e:checked~.checkmark.jsx-abef1019a04c510e:after{display:block}.tick-container.jsx-abef1019a04c510e .checkmark.jsx-abef1019a04c510e:after{left:5px;top:2px;width:5px;height:10px;border:solid#fff;border-width:0 2px 2px 0;-webkit-transform:rotate(45deg);-moz-transform:rotate(45deg);-ms-transform:rotate(45deg);-o-transform:rotate(45deg);transform:rotate(45deg)}'})]})},ea={RA:"bi bi-shield-exclamation","EPTW-GEN":"bi bi-file-earmark",IR:"bi bi-exclamation-triangle-fill",OBS:"bi bi-eye-fill",INC:"bi bi-exclamation-triangle-fill",KNOWLEDGE:"bi bi-folder-fill",TBT:"bi bi-chat-dots-fill",OTT:"bi bi-list-task",INCINV:"bi bi-briefcase-fill",INS:"bi bi-clipboard-check-fill",DOC:"bi bi-folder-fill",GC:"bi bi-hand-thumbs-up-fill",AUDIT:"bi bi-check-circle-fill",STATS:"bi bi-bar-chart-fill",TRACK:"bi bi-geo-alt-fill"},ei=e=>e.color&&e.color.startsWith("#")?e.color:({RA:"#FF6B6B","EPTW-GEN":"#4ECDC4",IR:"#FFE66D",OBS:"#4DABF7",INC:"#FFE66D",KNOWLEDGE:"#51CF66",TBT:"#9775FA",OTT:"#FF8CC8",INCINV:"#FF9F43",INS:"#51CF66",DOC:"#51CF66",GC:"#51CF66",AUDIT:"#FF8CC8",STATS:"#9775FA",TRACK:"#4ECDC4"})[e.maskName]||"#667eea",el=()=>({id:"tbt",name:"TBT",description:"Toolbox Talk",maskName:"TBT",applicability:"",status:!0,url:"/tbt",created:"",updated:"",mobileShortName:"TBT",color:"#9775FA",icon:"bi bi-chat-dots-fill"}),en=()=>[{id:"1",title:"Safety Incident Report - Slip and Fall",description:"Employee slipped on wet floor in cafeteria area. Minor injury reported.",type:"incident",module:"Incident Reporting",progress:75,lastModified:"2 hours ago",createdDate:"2024-01-15T09:00:00Z",status:"in-progress",data:{location:"Cafeteria",severity:"Minor",witnesses:["John Doe","Jane Smith"]}},{id:"2",title:"Chemical Storage Risk Assessment",description:"Quarterly assessment of chemical storage facility compliance and safety measures.",type:"risk-assessment",module:"Risk Assessment",progress:45,lastModified:"1 day ago",createdDate:"2024-01-14T14:00:00Z",status:"in-progress",data:{facility:"Chemical Storage Unit A",assessor:"Safety Team",riskLevel:"Medium"}},{id:"3",title:"Monthly Safety Audit - Production Floor",description:"Comprehensive safety audit of production floor operations and equipment.",type:"audit",module:"Audit",progress:20,lastModified:"3 days ago",createdDate:"2024-01-13T08:00:00Z",status:"draft",data:{area:"Production Floor",auditor:"Safety Inspector",scheduledDate:"2024-01-20"}},{id:"4",title:"Hot Work Permit - Welding Operations",description:"Permit application for welding work in maintenance area.",type:"e-permit",module:"E-Permit to Work",progress:60,lastModified:"5 days ago",createdDate:"2024-01-12T13:00:00Z",status:"in-progress",data:{workType:"Welding",location:"Maintenance Bay 2",duration:"4 hours",supervisor:"Mike Johnson"}},{id:"5",title:"Near Miss Observation - Equipment Malfunction",description:"Observation report for equipment malfunction that could have caused injury.",type:"observation",module:"Observation Reporting",progress:90,lastModified:"1 week ago",createdDate:"2024-01-11T08:45:00Z",status:"in-progress",data:{equipment:"Conveyor Belt #3",observer:"Line Supervisor",severity:"High"}},{id:"6",title:"Fire Safety Equipment Inspection",description:"Monthly inspection of fire extinguishers and emergency equipment.",type:"inspection",module:"Inspection Management",progress:30,lastModified:"1 week ago",createdDate:"2024-01-10T14:00:00Z",status:"draft",data:{equipmentType:"Fire Safety",inspector:"Safety Officer",nextDue:"2024-02-10"}}],er=()=>[{id:"1",name:"Risk Assessment",description:"Risk Assessment Module",maskName:"RA",applicability:"All",status:!0,url:"/risk-assessment",created:"",updated:"",mobileShortName:"Risk Assessment",color:"#FF6B6B",icon:"bi bi-shield-exclamation"},{id:"2",name:"E-Permit to Work",description:"Electronic Permit to Work",maskName:"EPTW-GEN",applicability:"All",status:!0,url:"/permit",created:"",updated:"",mobileShortName:"E-Permit",color:"#4ECDC4",icon:"bi bi-file-earmark"},{id:"3",name:"Incident Reporting",description:"Report Incidents",maskName:"IR",applicability:"All",status:!0,url:"/incident",created:"",updated:"",mobileShortName:"Incident",color:"#FFE66D",icon:"bi bi-exclamation-triangle-fill"},{id:"4",name:"Observation",description:"Safety Observations",maskName:"OBS",applicability:"All",status:!0,url:"/observation",created:"",updated:"",mobileShortName:"Observation",color:"#4DABF7",icon:"bi bi-eye-fill"},{id:"5",name:"Inspection",description:"Safety Inspections",maskName:"INS",applicability:"All",status:!0,url:"/inspection",created:"",updated:"",mobileShortName:"Inspection",color:"#51CF66",icon:"bi bi-clipboard-check-fill"},{id:"6",name:"Documents",description:"Document Management",maskName:"DOC",applicability:"All",status:!0,url:"/documents",created:"",updated:"",mobileShortName:"Documents",color:"#51CF66",icon:"bi bi-folder-fill"}],eo=()=>{let[e,s]=(0,i.useState)([]),[t,o]=(0,i.useState)([]),[d,m]=(0,i.useState)(!0),[u,b]=(0,i.useState)(!0),[g,f]=(0,i.useState)(!1),[j,N]=(0,i.useState)("All"),[y,v]=(0,i.useState)(""),[A,k]=(0,i.useState)(""),[w,S]=(0,i.useState)(""),[C,F]=(0,i.useState)("actions"),[T,D]=(0,i.useState)([]),[I,R]=(0,i.useState)(""),[E,z]=(0,i.useState)("All"),[M,B]=(0,i.useState)(!1),[W,Y]=(0,i.useState)(3),[P,O]=(0,i.useState)(!1),[_,q]=(0,i.useState)([{id:"1",title:"Safety Alert",message:"Emergency drill scheduled for tomorrow at 2 PM. All personnel must participate.",type:"warning",timestamp:"2024-01-15T14:30:00Z",isRead:!1,category:"Safety",priority:"high",actionUrl:"/safety/emergency-drill"},{id:"2",title:"Incident Report Submitted",message:"Your incident report #IR-2024-001 has been successfully submitted and is under review.",type:"success",timestamp:"2024-01-15T10:15:00Z",isRead:!1,category:"Incident",priority:"medium",actionUrl:"/incident/IR-2024-001"},{id:"3",title:"Risk Assessment Due",message:"Chemical storage risk assessment is due for renewal within 7 days.",type:"warning",timestamp:"2024-01-14T16:45:00Z",isRead:!0,category:"Risk Assessment",priority:"medium",actionUrl:"/risk-assessment/chemical-storage"}]),L=()=>{O(!1)},U=e=>{q(s=>s.map(s=>s.id===e?{...s,isRead:!0}:s)),Y(_.filter(s=>!s.isRead&&s.id!==e).length)},H=e=>{switch(e){case"urgent":return"bi bi-exclamation-triangle-fill";case"warning":return"bi bi-exclamation-circle-fill";case"success":return"bi bi-check-circle-fill";case"error":return"bi bi-x-circle-fill";default:return"bi bi-info-circle-fill"}},V=e=>{switch(e){case"urgent":case"error":return"#FF3B30";case"warning":return"#FF9500";case"success":return"#34C759";default:return"#007AFF"}},G=e=>{let s=new Date(e),t=Math.floor((new Date().getTime()-s.getTime())/6e4);return t<1?"Just now":t<60?"".concat(t,"m ago"):t<1440?"".concat(Math.floor(t/60),"h ago"):"".concat(Math.floor(t/1440),"d ago")},J=(0,r.wA)(),K=(0,r.d4)(e=>e.login.user),Z=e.some(e=>"RA"===e.maskName),Q=(0,i.useCallback)(async()=>{try{console.log("\uD83D\uDD04 Fetching services...");let e=localStorage.getItem("access_token");console.log("\uD83D\uDD11 Auth token present:",!!e);let t=await h.M.getServices();console.log("✅ Services fetched:",t),console.log("\uD83D\uDCCA Services count:",t.length),s(t),J(l.X.setService(t)),m(!1)}catch(t){console.error("❌ Error fetching services:",t),console.error("❌ Error details:",t instanceof Error?t.message:String(t)),console.log("\uD83D\uDD04 Using mock services as fallback");let e=er();s(e),J(l.X.setService(e)),m(!1)}},[J]),X=(0,i.useCallback)(async()=>{try{let e=await h.M.getActions();o(e),b(!1)}catch(e){console.error("Error fetching actions:",e),b(!1)}},[]),$=(0,i.useCallback)(async()=>{try{if(window.localStorage){let e=localStorage.getItem("logo");if(!e)return void console.warn("No logo key found in localStorage");let s=(await x.A.get((0,p._i)(e),{headers:{"Content-Type":"application/json"}})).data;k(s)}else console.warn("localStorage is not available")}catch(e){console.error("Error fetching logo:",e)}},[]),ee=(0,i.useCallback)(async()=>{try{let e=await x.A.get(p.AM);200===e.status?(S(e.data.firstName),J(n.l.setUser(e.data)),console.log("✅ User data fetched:",e.data)):console.warn("Failed to fetch user data, status:",e.status)}catch(e){console.error("❌ Error fetching user data:",e)}},[J]);(0,i.useEffect)(()=>{Q(),X(),$(),ee(),D(en()),Y(_.filter(e=>!e.isRead).length);let e=setInterval(()=>{.1>Math.random()&&Y(e=>Math.min(99,e+1))},3e4);return()=>clearInterval(e)},[Q,X,$,ee,_]);let es=e=>e.replace(/^\/apps\//,"/"),eo=T.filter(e=>{let s=e.title.toLowerCase().includes(I.toLowerCase())||e.description.toLowerCase().includes(I.toLowerCase()),t="All"===E||e.module===E;return s&&t}),ec=e=>{switch(e){case"incident":return"#FF3B30";case"risk-assessment":return"#FF9500";case"audit":return"#FF2D92";case"e-permit":return"#34C759";case"observation":default:return"#007AFF";case"inspection":return"#32D74B"}},ed=e=>{switch(e){case"incident":return"#FFE5E5";case"risk-assessment":return"#FFF3E0";case"audit":return"#FCE4EC";case"e-permit":case"inspection":return"#E8F5E9";default:return"#E3F2FD"}};return(0,a.jsxs)("div",{className:"d-flex flex-column",style:{backgroundColor:"#FAFBFC",height:"100vh",overflow:"hidden"},children:[(0,a.jsxs)("div",{className:"flex-shrink-0",style:{padding:"20px",paddingBottom:"10px"},children:[(0,a.jsxs)("div",{style:{backgroundColor:"#FFFFFF",borderRadius:"20px",padding:"20px",marginBottom:"24px",boxShadow:"0 6px 12px rgba(0,0,0,0.1)",border:"1px solid #F0F0F0"},children:[(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-3",children:[(0,a.jsx)("div",{className:"flex-grow-1",children:A?(0,a.jsx)("img",{src:A,alt:"Company Logo",style:{maxWidth:"200px",height:"50px",objectFit:"contain",borderRadius:"8px"}}):(0,a.jsx)("img",{src:"/assets/img/core-img/logo.png",alt:"Company Logo",style:{maxWidth:"200px",height:"50px",objectFit:"contain",borderRadius:"8px"}})}),(0,a.jsxs)("button",{className:"btn d-flex align-items-center justify-content-center position-relative",onClick:()=>{console.log("\uD83D\uDD14 Notification button clicked! Opening popup..."),O(!0)},style:{width:"50px",height:"50px",borderRadius:"12px",backgroundColor:"#FFFFFF",border:"1.5px solid #E5E7EB",boxShadow:"0 2px 6px rgba(0,0,0,0.08)",transition:"all 0.2s ease",cursor:"pointer",zIndex:10},onMouseDown:e=>{e.currentTarget.style.opacity="0.7",e.currentTarget.style.transform="scale(0.95)"},onMouseUp:e=>{e.currentTarget.style.opacity="1",e.currentTarget.style.transform="scale(1)"},onMouseLeave:e=>{e.currentTarget.style.opacity="1",e.currentTarget.style.transform="scale(1)"},onMouseEnter:e=>{e.currentTarget.style.boxShadow="0 4px 12px rgba(0,0,0,0.15)"},onMouseOut:e=>{e.currentTarget.style.boxShadow="0 2px 6px rgba(0,0,0,0.08)"},children:[(0,a.jsx)("i",{className:"bi bi-bell-fill",style:{fontSize:"22px",color:"#6B7280"}}),W>0&&(0,a.jsx)("span",{className:"position-absolute badge d-flex align-items-center justify-content-center",style:{top:"-6px",right:"-6px",width:"18px",height:"18px",borderRadius:"9px",fontSize:"10px",fontWeight:"700",backgroundColor:"#EF4444",color:"white",border:"2px solid white",boxShadow:"0 2px 4px rgba(239, 68, 68, 0.3)",lineHeight:"12px",textAlign:"center"},children:W>99?"99+":W})]})]}),(0,a.jsxs)("h6",{className:"mb-0",style:{color:"#1F2937",fontSize:"20px",fontWeight:"600",letterSpacing:"0.3px",fontFamily:"system-ui, -apple-system, sans-serif"},children:["Hello, ",w||(null==K?void 0:K.firstName)||"User"]})]}),(0,a.jsxs)("div",{style:{marginBottom:"16px",padding:"0 4px"},children:[(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-3",children:[(0,a.jsx)("h6",{className:"mb-0",style:{fontSize:"20px",fontWeight:"700",color:"#1A1A1A"},children:"Quick Access"}),(0,a.jsxs)(c(),{href:"/services",className:"text-decoration-none d-flex align-items-center",style:{color:"#007AFF",fontSize:"12px",fontWeight:"600",padding:"4px 8px",borderRadius:"6px",backgroundColor:"rgba(0, 122, 255, 0.1)",transition:"all 0.2s ease"},children:["Show All",(0,a.jsx)("i",{className:"bi bi-chevron-right ms-1",style:{fontSize:"12px"}})]})]}),(0,a.jsx)("div",{className:"d-flex flex-wrap justify-content-between",style:{gap:"8px"},children:d?Array.from({length:8}).map((e,s)=>(0,a.jsx)("div",{style:{width:"23%",aspectRatio:"1"},children:(0,a.jsxs)("div",{className:"bg-white d-flex flex-column align-items-center justify-content-center",style:{width:"100%",height:"100%",padding:"8px",borderRadius:"12px",border:"1px solid #E5E7EB",boxShadow:"0 1px 2px rgba(0,0,0,0.05)"},children:[(0,a.jsx)("div",{style:{width:32,height:32,borderRadius:"50%",background:"#f2f4f7",marginBottom:"6px",animation:"pulse 1.5s ease-in-out infinite"}}),(0,a.jsx)("div",{style:{width:"80%",height:8,backgroundColor:"#f2f4f7",borderRadius:4,animation:"pulse 1.5s ease-in-out infinite"}})]})},s)):(0,a.jsxs)(a.Fragment,{children:[e.slice(0,8).map(e=>(0,a.jsx)("div",{style:{width:"23%",aspectRatio:"1"},children:(0,a.jsx)(c(),{href:es(e.url),className:"text-decoration-none",children:(0,a.jsxs)("div",{className:"bg-white d-flex flex-column align-items-center justify-content-center",style:{width:"100%",height:"100%",padding:"8px",borderRadius:"12px",border:"1px solid #E5E7EB",boxShadow:"0 1px 2px rgba(0,0,0,0.05)",transition:"all 0.2s ease"},children:[(0,a.jsx)("div",{className:"d-flex align-items-center justify-content-center",style:{width:35,height:35,borderRadius:16,backgroundColor:ei(e)+"20",marginBottom:"6px"},children:(0,a.jsx)("i",{className:"".concat(ea[e.maskName]||"bi bi-file-earmark"),style:{fontSize:20,color:ei(e)}})}),(0,a.jsx)("div",{className:"text-center",style:{fontSize:10,lineHeight:1.2,color:"#1A1A1A",fontWeight:600,display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%"},children:e.name})]})})},e.id)),Z&&e.length<8&&(0,a.jsx)("div",{style:{width:"23%",aspectRatio:"1"},children:(0,a.jsx)(c(),{href:"/tbt",className:"text-decoration-none",children:(0,a.jsxs)("div",{className:"bg-white d-flex flex-column align-items-center justify-content-center",style:{width:"100%",height:"100%",padding:"8px",borderRadius:"12px",border:"1px solid #E5E7EB",boxShadow:"0 1px 2px rgba(0,0,0,0.05)",transition:"all 0.2s ease"},children:[(0,a.jsx)("div",{className:"d-flex align-items-center justify-content-center",style:{width:35,height:35,borderRadius:16,backgroundColor:ei(el())+"20",marginBottom:"6px"},children:(0,a.jsx)("i",{className:"bi bi-chat-dots-fill",style:{fontSize:20,color:ei(el())}})}),(0,a.jsx)("div",{className:"text-center",style:{fontSize:10,lineHeight:1.2,color:"#1A1A1A",fontWeight:600},children:"TBT"})]})})})]})})]})]}),(0,a.jsxs)("div",{className:"flex-grow-1 d-flex flex-column px-3",style:{overflow:"hidden"},children:[(0,a.jsx)("div",{className:"mb-3 flex-shrink-0",children:(0,a.jsxs)("div",{className:"d-flex",style:{borderBottom:"1px solid #E5E7EB"},children:[(0,a.jsxs)("div",{className:"position-relative flex-grow-1",children:[(0,a.jsx)("button",{className:"btn w-100 border-0 bg-transparent",onClick:()=>F("actions"),style:{padding:"16px 12px"},children:(0,a.jsxs)("div",{className:"d-flex align-items-center justify-content-center",children:[(0,a.jsx)("span",{style:{fontSize:"16px",fontWeight:"actions"===C?"600":"500",color:"actions"===C?"#1F2937":"#9CA3AF",textAlign:"center",letterSpacing:"0.1px",fontFamily:"system-ui, -apple-system, sans-serif"},children:"My Actions"}),t.length>0&&(0,a.jsx)("span",{className:"badge rounded-pill d-flex align-items-center justify-content-center ms-2",style:{fontSize:"11px",minWidth:"20px",height:"20px",padding:"0 6px",fontWeight:"700",lineHeight:"13px",backgroundColor:"#EF4444",color:"#FFFFFF"},children:t.length})]})}),"actions"===C&&(0,a.jsx)("div",{style:{position:"absolute",bottom:"-1px",left:"0",right:"0",height:"2px",backgroundColor:"#1976D2",borderRadius:"1px"}})]}),(0,a.jsxs)("div",{className:"position-relative flex-grow-1",children:[(0,a.jsx)("button",{className:"btn w-100 border-0 bg-transparent",onClick:()=>F("drafts"),style:{padding:"16px 12px"},children:(0,a.jsxs)("div",{className:"d-flex align-items-center justify-content-center",children:[(0,a.jsx)("span",{style:{fontSize:"16px",fontWeight:"drafts"===C?"600":"500",color:"drafts"===C?"#1F2937":"#9CA3AF",textAlign:"center",letterSpacing:"0.1px",fontFamily:"system-ui, -apple-system, sans-serif"},children:"Draft"}),T.length>0&&(0,a.jsx)("span",{className:"badge rounded-pill d-flex align-items-center justify-content-center ms-2",style:{fontSize:"11px",minWidth:"20px",height:"20px",padding:"0 6px",fontWeight:"700",lineHeight:"13px",backgroundColor:"#EF4444",color:"#FFFFFF"},children:T.length})]})}),"drafts"===C&&(0,a.jsx)("div",{style:{position:"absolute",bottom:"-1px",left:"0",right:"0",height:"2px",backgroundColor:"#1976D2",borderRadius:"1px"}})]})]})}),(0,a.jsxs)("div",{className:"d-flex align-items-center mb-3 flex-shrink-0",style:{gap:"12px"},children:[(0,a.jsx)("div",{className:"flex-grow-1",children:(0,a.jsxs)("div",{className:"d-flex align-items-center",style:{backgroundColor:"#F2F2F7",borderRadius:"10px",padding:"8px 12px"},children:[(0,a.jsx)("i",{className:"bi bi-search me-2",style:{fontSize:"20px",color:"#8E8E93"}}),(0,a.jsx)("input",{type:"text",className:"form-control border-0 bg-transparent p-0",placeholder:"actions"===C?"Search actions...":"Search drafts...",value:"actions"===C?y:I,onChange:e=>{"actions"===C?v(e.target.value):R(e.target.value)},style:{fontSize:"16px",color:"#000000",fontFamily:"system-ui, -apple-system, sans-serif"}})]})}),(0,a.jsx)("button",{className:"btn d-flex align-items-center justify-content-center",onClick:()=>{"actions"===C?f(!0):B(!0)},style:{width:"44px",height:"44px",borderRadius:"10px",border:"1px solid ".concat(("actions"===C?j:E)!=="All"?"#007AFF":"#E5E7EB"),backgroundColor:("actions"===C?j:E)!=="All"?"#007AFF":"#F2F2F7",transition:"all 0.2s ease"},children:(0,a.jsx)("i",{className:"bi bi-sliders",style:{fontSize:"18px",color:("actions"===C?j:E)!=="All"?"#FFFFFF":"#007AFF"}})})]}),("actions"===C&&("All"!==j||y&&""!==y.trim())||"drafts"===C&&("All"!==E||I&&""!==I.trim()))&&(0,a.jsxs)("div",{className:"d-flex align-items-center justify-content-between mt-2 mb-2 px-1 flex-shrink-0",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2",children:[("actions"===C&&"All"!==j||"drafts"===C&&"All"!==E)&&(0,a.jsxs)("span",{className:"badge rounded-pill d-flex align-items-center px-2 py-1",style:{backgroundColor:"#E3F2FD",color:"#2196F3",fontSize:"11px",fontWeight:"500"},children:[(0,a.jsx)("i",{className:"bi bi-funnel-fill me-1",style:{fontSize:"10px"}}),"actions"===C?j:E]}),("actions"===C&&y&&""!==y.trim()||"drafts"===C&&I&&""!==I.trim())&&(0,a.jsxs)("span",{className:"badge rounded-pill d-flex align-items-center px-2 py-1",style:{backgroundColor:"#FFF3E0",color:"#FF9800",fontSize:"11px",fontWeight:"500"},children:[(0,a.jsx)("i",{className:"bi bi-search me-1",style:{fontSize:"10px"}}),"“","actions"===C?y:I,"”"]})]}),(0,a.jsx)("button",{className:"btn btn-link p-0 text-decoration-none d-flex align-items-center",onClick:()=>{"actions"===C?(N("All"),v("")):(z("All"),R(""))},style:{color:"#757575",fontSize:"11px"},children:(0,a.jsx)("i",{className:"bi bi-x",style:{fontSize:"12px"}})})]}),(0,a.jsx)("div",{className:"flex-grow-1",style:{overflowY:"auto",paddingBottom:"80px"},children:"actions"===C?(0,a.jsx)(et,{externalShowModal:g,externalSetShowModal:f,selectedFilter:j,setSelectedFilter:N,searchQuery:y}):(0,a.jsx)("div",{children:0===eo.length?(0,a.jsxs)("div",{className:"text-center py-5",children:[(0,a.jsx)("i",{className:"bi bi-file-earmark-text",style:{fontSize:"48px",color:"#8E8E93"}}),(0,a.jsx)("p",{className:"mt-3 mb-0",style:{color:"#8E8E93",fontSize:"16px"},children:"No drafts found"})]}):eo.map(e=>(0,a.jsxs)("div",{className:"bg-white border rounded-3 p-3 mb-3 position-relative",style:{borderColor:"#E5E7EB",boxShadow:"0 1px 2px rgba(0,0,0,0.05)",overflow:"hidden"},children:[(0,a.jsx)("div",{style:{position:"absolute",left:0,top:0,bottom:0,width:"4px",backgroundColor:ec(e.type)}}),(0,a.jsxs)("div",{style:{marginLeft:"8px"},children:[(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-start mb-2",children:[(0,a.jsx)("h6",{className:"mb-0 flex-grow-1",style:{fontSize:"16px",fontWeight:"600",color:"#1A1A1A",lineHeight:"20px"},children:e.title}),(0,a.jsxs)("div",{className:"d-flex align-items-center ms-3",style:{minWidth:"80px"},children:[(0,a.jsx)("div",{style:{width:"40px",height:"4px",backgroundColor:"#E5E5E5",borderRadius:"2px",marginRight:"6px",overflow:"hidden"},children:(0,a.jsx)("div",{style:{width:"".concat(e.progress,"%"),height:"100%",backgroundColor:ec(e.type),borderRadius:"2px"}})}),(0,a.jsxs)("span",{className:"badge",style:{fontSize:"11px",fontWeight:"600",minWidth:"32px",padding:"2px 6px",borderRadius:"10px",backgroundColor:ed(e.type),color:ec(e.type)},children:[e.progress,"%"]})]})]}),(0,a.jsx)("p",{className:"mb-2",style:{fontSize:"14px",color:"#666666",lineHeight:"18px"},children:e.description}),(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-center",children:[(0,a.jsxs)("small",{style:{fontSize:"12px",color:"#666666",fontWeight:"500"},children:["Last modified: ",e.lastModified]}),(0,a.jsxs)("button",{className:"btn btn-sm d-flex align-items-center",style:{fontSize:"11px",color:"#007AFF",fontWeight:"600",backgroundColor:"#F0F8FF",border:"none",borderRadius:"6px",padding:"4px 8px"},children:["Continue",(0,a.jsx)("i",{className:"bi bi-chevron-right ms-1",style:{fontSize:"10px"}})]})]})]})]},e.id))})})]}),(0,a.jsx)("div",{className:"flex-shrink-0 bg-white border-top shadow-sm",children:(0,a.jsx)("div",{className:"container-fluid",children:(0,a.jsxs)("div",{className:"row text-center py-2",children:[(0,a.jsx)("div",{className:"col",children:(0,a.jsx)(c(),{href:"/dashboard",className:"text-decoration-none text-muted",children:(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,a.jsx)("i",{className:"bi bi-grid-3x3-gap fs-5 mb-1"}),(0,a.jsx)("small",{style:{fontSize:"0.7rem"},children:"Dashboard"})]})})}),(0,a.jsx)("div",{className:"col",children:(0,a.jsx)(c(),{href:"/services",className:"text-decoration-none text-muted",children:(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,a.jsx)("i",{className:"bi bi-grid fs-5 mb-1"}),(0,a.jsx)("small",{style:{fontSize:"0.7rem"},children:"Services"})]})})}),(0,a.jsx)("div",{className:"col",children:(0,a.jsx)(c(),{href:"/home",className:"text-decoration-none text-primary",children:(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,a.jsx)("i",{className:"bi bi-house-fill fs-5 mb-1"}),(0,a.jsx)("small",{style:{fontSize:"0.7rem"},children:"Home"})]})})}),(0,a.jsx)("div",{className:"col",children:(0,a.jsx)(c(),{href:"/history",className:"text-decoration-none text-muted",children:(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,a.jsx)("i",{className:"bi bi-clock-history fs-5 mb-1"}),(0,a.jsx)("small",{style:{fontSize:"0.7rem"},children:"History"})]})})}),(0,a.jsx)("div",{className:"col",children:(0,a.jsx)(c(),{href:"/profile",className:"text-decoration-none text-muted",children:(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,a.jsx)("i",{className:"bi bi-person fs-5 mb-1"}),(0,a.jsx)("small",{style:{fontSize:"0.7rem"},children:"Profile"})]})})})]})})}),M&&(0,a.jsx)("div",{className:"modal fade show d-block",style:{backgroundColor:"rgba(0,0,0,0.5)"},onClick:()=>B(!1),children:(0,a.jsx)("div",{className:"modal-dialog modal-dialog-centered",onClick:e=>e.stopPropagation(),children:(0,a.jsxs)("div",{className:"modal-content",style:{borderRadius:"16px",border:"none"},children:[(0,a.jsxs)("div",{className:"modal-header border-bottom",style:{borderColor:"#F0F0F0 !important"},children:[(0,a.jsx)("h5",{className:"modal-title",style:{fontSize:"18px",fontWeight:"600"},children:"Filter Drafts"}),(0,a.jsx)("button",{type:"button",className:"btn-close",onClick:()=>B(!1)})]}),(0,a.jsxs)("div",{className:"modal-body",style:{padding:"16px"},children:[["All","Risk Assessment","Observation Reporting","E-Permit to Work","Incident Reporting","Document Management","Audit","Inspection Management"].map(e=>(0,a.jsxs)("button",{className:"btn w-100 d-flex justify-content-between align-items-center mb-2",style:{padding:"16px",borderRadius:"12px",border:"1px solid #E5E7EB",backgroundColor:E===e?"rgba(0, 122, 255, 0.1)":"transparent",color:E===e?"#007AFF":"#000000",textAlign:"left"},onClick:()=>{z(e),B(!1)},children:[(0,a.jsx)("span",{style:{fontSize:"16px",fontWeight:"500"},children:e}),E===e&&(0,a.jsx)("i",{className:"bi bi-check-circle-fill",style:{color:"#007AFF",fontSize:"20px"}})]},e)),"All"!==E&&(0,a.jsxs)("button",{className:"btn w-100 d-flex align-items-center justify-content-center mt-3",style:{padding:"16px",borderRadius:"12px",backgroundColor:"#FFF2F2",border:"1px solid #FFE5E5",color:"#FF3B30"},onClick:()=>{z("All"),B(!1)},children:[(0,a.jsx)("i",{className:"bi bi-x-circle me-2",style:{fontSize:"20px"}}),(0,a.jsx)("span",{style:{fontSize:"16px",fontWeight:"600"},children:"Clear All Filters"})]})]})]})})}),P&&(0,a.jsx)("div",{className:"modal fade show d-block",style:{backgroundColor:"rgba(0,0,0,0.5)",zIndex:1050},onClick:L,children:(0,a.jsx)("div",{className:"modal-dialog modal-dialog-centered modal-dialog-scrollable",style:{maxWidth:"400px"},onClick:e=>e.stopPropagation(),children:(0,a.jsxs)("div",{className:"modal-content",style:{borderRadius:"16px",border:"none",maxHeight:"80vh"},children:[(0,a.jsx)("div",{className:"modal-header border-bottom",style:{borderColor:"#F0F0F0 !important",padding:"20px"},children:(0,a.jsxs)("div",{className:"d-flex align-items-center justify-content-between w-100",children:[(0,a.jsx)("h5",{className:"modal-title mb-0",style:{fontSize:"20px",fontWeight:"600"},children:"Notifications"}),(0,a.jsxs)("div",{className:"d-flex align-items-center gap-3",children:[W>0&&(0,a.jsx)("span",{className:"badge rounded-pill",style:{backgroundColor:"#EF4444",color:"white",fontSize:"12px",fontWeight:"600",padding:"4px 8px"},children:W}),(0,a.jsx)("button",{type:"button",className:"btn-close",onClick:L,style:{fontSize:"12px"}})]})]})}),(0,a.jsx)("div",{className:"modal-body",style:{padding:"0",maxHeight:"60vh",overflowY:"auto"},children:0===_.length?(0,a.jsxs)("div",{className:"text-center py-5",children:[(0,a.jsx)("i",{className:"bi bi-bell-slash",style:{fontSize:"48px",color:"#8E8E93"}}),(0,a.jsx)("p",{className:"mt-3 mb-0",style:{color:"#8E8E93",fontSize:"16px"},children:"No notifications yet"})]}):_.map(e=>(0,a.jsx)("div",{className:"border-bottom p-3",style:{backgroundColor:e.isRead?"transparent":"#F8F9FA",borderColor:"#E5E7EB !important",cursor:"pointer"},onClick:()=>U(e.id),children:(0,a.jsxs)("div",{className:"d-flex align-items-start gap-3",children:[(0,a.jsx)("div",{className:"d-flex align-items-center justify-content-center flex-shrink-0",style:{width:"40px",height:"40px",borderRadius:"20px",backgroundColor:V(e.type)+"20"},children:(0,a.jsx)("i",{className:H(e.type),style:{fontSize:"18px",color:V(e.type)}})}),(0,a.jsxs)("div",{className:"flex-grow-1",children:[(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-start mb-1",children:[(0,a.jsx)("h6",{className:"mb-0",style:{fontSize:"16px",fontWeight:"600",color:"#1A1A1A"},children:e.title}),!e.isRead&&(0,a.jsx)("div",{style:{width:"8px",height:"8px",borderRadius:"4px",backgroundColor:"#007AFF",marginTop:"4px"}})]}),(0,a.jsx)("p",{className:"mb-2",style:{fontSize:"14px",color:"#666666",lineHeight:"1.4"},children:e.message}),(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-center",children:[(0,a.jsx)("small",{style:{fontSize:"12px",color:"#8E8E93",fontWeight:"500"},children:e.category}),(0,a.jsx)("small",{style:{fontSize:"12px",color:"#8E8E93"},children:G(e.timestamp)})]})]})]})},e.id))}),_.length>0&&(0,a.jsx)("div",{className:"modal-footer border-top",style:{borderColor:"#F0F0F0 !important",padding:"15px 20px"},children:(0,a.jsx)("button",{className:"btn btn-link text-decoration-none w-100",style:{color:"#007AFF",fontWeight:"500"},onClick:L,children:"View All Notifications"})})]})})})]})}},76852:(e,s,t)=>{"use strict";t.d(s,{A:()=>p});var a=t(95155),i=t(12115),l=t(43864),n=t(82940),r=t.n(n),o=t(38336),c=t(26957);let d=e=>{let{images:s,title:t,showName:i=!1}=e;return s&&0!==s.length?(0,a.jsxs)("div",{className:"image-gallery mt-3",children:[t&&(0,a.jsx)("h6",{className:"obs-title",children:t}),(0,a.jsx)("div",{className:"row",children:s.map((e,s)=>(0,a.jsx)("div",{className:"col-md-3 col-sm-4 col-6 p-2",children:(0,a.jsx)("div",{className:"image-box shadow-sm p-2 rounded d-flex align-items-center justify-content-center",children:(0,a.jsx)(l.A,{fileName:e,size:100,name:i})})},s))})]}):null},m=e=>{let{action:s,actionIndex:t=1,getMultipleNames:i,getClosedDate:l,maskId:n}=e,o="reperform_action"===s.actionType,c="Initiated"===s.status&&s.assignedToId&&s.assignedToId.length>0;return(0,a.jsxs)("div",{className:"obs-section shadow p-3 mb-3",children:[(0,a.jsxs)("div",{className:"row mb-3",children:[(0,a.jsx)("div",{className:"col-md-12",children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("h6",{className:"obs-title",children:["Action Verifier Comments & Reassigned Action ",n," - A",t]}),(0,a.jsx)("p",{className:"obs-content",children:s.comments})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("h6",{className:"obs-title",children:["Assigned Action ",n," - A",t]}),(0,a.jsx)("p",{className:"obs-content",children:s.actionToBeTaken})]})}),"reperform_action"!==s.actionType&&(0,a.jsxs)("div",{className:"col-md-12",children:[(0,a.jsx)("h6",{className:"obs-title",children:"Due Date"}),(0,a.jsx)("p",{className:"obs-content",children:(null==s?void 0:s.dueDate)&&r()(s.dueDate,["DD-MM-YYYY","DD/MM/YYYY",r().ISO_8601]).isValid()?r()(s.dueDate,["DD-MM-YYYY","DD/MM/YYYY",r().ISO_8601]).format("Do MMM YYYY"):"-"})]}),c&&(0,a.jsxs)("div",{className:"col-md-6",children:[(0,a.jsxs)("h6",{className:"obs-title",children:["Action Assignee",s.assignedToId&&s.assignedToId.length>1?"s":""]}),(0,a.jsx)("p",{className:"obs-content",children:i(s.assignedToId)})]})]}),"Completed"===s.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"row",children:(0,a.jsxs)("div",{className:"col-md-12",children:[(0,a.jsx)("h6",{className:"obs-title",children:"Action Taken"}),(0,a.jsx)("p",{className:"obs-content",children:s.actionTaken})]})}),(0,a.jsxs)("div",{className:"row",children:[(0,a.jsxs)("div",{className:"col-md-6",children:[(0,a.jsx)("h6",{className:"obs-title",children:"Action Taken By"}),(0,a.jsx)("p",{className:"obs-content",children:i(s.assignedToId)})]}),(0,a.jsxs)("div",{className:"col-md-6",children:[(0,a.jsx)("h6",{className:"obs-title",children:"Date"}),(0,a.jsx)("p",{className:"obs-content",children:(null==l?void 0:l(s))||"-"})]})]}),(0,a.jsx)(d,{images:s.uploads,title:"Evidence",showName:!0})]})]})},x=e=>{let{action:s,actionIndex:t=1,getMultipleNames:i}=e;return s.assignedToId&&0!==s.assignedToId.length?(0,a.jsxs)("div",{className:"obs-section shadow p-3 mt-3 mb-3",children:["Initiated"===s.status&&(0,a.jsx)("div",{className:"row mb-3",children:(0,a.jsxs)("div",{className:"col-md-12",children:[(0,a.jsxs)("h6",{className:"obs-title",children:["Action Verifier",s.assignedToId&&s.assignedToId.length>1?"s":""," - A",t]}),(0,a.jsx)("p",{className:"obs-content",children:i(s.assignedToId)})]})}),"Completed"===s.status&&(0,a.jsxs)("div",{className:"row mb-3",children:[(0,a.jsxs)("div",{className:"col-md-12",children:[(0,a.jsx)("h6",{className:"obs-title",children:"Action Verified By"}),(0,a.jsx)("p",{className:"obs-content",children:i(s.assignedToId)})]}),(0,a.jsxs)("div",{className:"col-md-12",children:[(0,a.jsx)("h6",{className:"obs-title",children:"Date"}),(0,a.jsx)("p",{className:"obs-content",children:r()(s.created).format("Do MMM YYYY, hh:mm:ss a")})]}),s.comments&&(0,a.jsxs)("div",{className:"col-md-12",children:[(0,a.jsx)("h6",{className:"obs-title",children:"Comments"}),(0,a.jsx)("p",{className:"obs-content",children:s.comments})]})]})]}):null},h=e=>{let{action:s,getMultipleNames:t}=e;if(!s.assignedToId||0===s.assignedToId.length)return null;let i="Initiated"===s.status,l="Completed"===s.status;return(0,a.jsx)("div",{className:"obs-section shadow p-3 mb-3",children:(0,a.jsxs)("div",{className:"row mb-3",children:[(0,a.jsxs)("div",{className:"col-md-6",children:[(0,a.jsxs)("p",{className:"obs-title",children:[i?"Action Reviewer":l?"Action Reviewed By":"Reviewer",s.assignedToId&&s.assignedToId.length>1?"s":""]}),(0,a.jsx)("p",{className:"obs-content",children:t(s.assignedToId)})]}),(0,a.jsxs)("div",{className:"col-md-6",children:[(0,a.jsx)("p",{className:"obs-title",children:"Date"}),(0,a.jsx)("p",{className:"obs-content",children:r()(s.created).format("Do MMM YYYY, hh:mm:ss a")})]})]})})},p=e=>{var s,t,l,n;let{reportData:p}=e,[u,b]=(0,i.useState)([]);(0,i.useEffect)(()=>{g()},[]);let g=async()=>{try{let e=await o.A.get(c.Jo);b(e.data)}catch(e){console.error("Error fetching users:",e)}};function f(e){if(!e)return"No Assignee";let s=u.find(s=>s.id===e);return s?s.firstName:"Unknown User"}function j(e){if(!e||0===e.length)return"No Assignee";if(1===e.length)return f(e[0]);let s=e.map(e=>f(e)).filter(e=>"Unknown User"!==e);return s.length>0?s.join(" & "):"Unknown Users"}function N(e){return(null==e?void 0:e.created)?r()(e.created).format("Do MMM YYYY, hh:mm:ss a"):"-"}let y=0,v=e=>"take_action"===e.actionType||"reperform_action"===e.actionType?(y++,(0,a.jsx)(m,{action:e,actionIndex:y,getName:f,getMultipleNames:j,getClosedDate:N,maskId:p.maskId},e.id)):"verify_action"===e.actionType?(0,a.jsx)(x,{action:e,actionIndex:y,getName:f,getMultipleNames:j,getClosedDate:N,maskId:p.maskId},e.id):"review"===e.actionType?(0,a.jsx)(h,{action:e,actionIndex:y,getName:f,getMultipleNames:j,getClosedDate:N,maskId:p.maskId},e.id):null;return(0,a.jsxs)("div",{className:"observation-report card shadow-sm p-3 rounded",children:[(0,a.jsxs)("div",{className:"description-section",children:[(0,a.jsx)("h5",{className:"section-title",children:"Description"}),(0,a.jsx)("p",{className:"obs-dec text-muted",children:p.isQR?p.describeActionTaken||p.describeAtRiskObservation||p.describeSafekObservation||"No description available":p.description})]}),(0,a.jsxs)("div",{className:"row",children:[(0,a.jsxs)("div",{className:"col-md-6",children:[(0,a.jsx)("h6",{className:"obs-title",children:"Category"}),(0,a.jsx)("p",{className:"obs-content",children:p.observationCategory||"N/A"})]}),(0,a.jsxs)("div",{className:"col-md-6",children:[(0,a.jsx)("h6",{className:"obs-title",children:"Type"}),(0,a.jsxs)("p",{className:"obs-content",children:[p.observationType||""," - ",p.observationActOrCondition]})]})]}),(0,a.jsx)(d,{images:p.uploads,title:"Images"}),(0,a.jsx)("div",{className:"row",children:(0,a.jsxs)("div",{className:"col-md-6",children:[(0,a.jsx)("h6",{className:"obs-title",children:"Reported Date"}),(0,a.jsx)("p",{className:"obs-content",children:p.created?r()(p.created).format("Do MMM YYYY, hh:mm:ss a"):"N/A"})]})}),(0,a.jsx)("div",{className:"row",children:p.isQR?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"col-md-6",children:[(0,a.jsx)("h6",{className:"obs-title",children:"Role"}),(0,a.jsx)("p",{className:"obs-content",children:p.qrRole||"N/A"})]}),(0,a.jsxs)("div",{className:"col-md-6",children:[(0,a.jsx)("h6",{className:"obs-title",children:"Hazard Category"}),(0,a.jsx)("p",{className:"obs-content",children:(null==(s=p.hazardCategory)?void 0:s.name)||"N/A"})]}),(0,a.jsxs)("div",{className:"col-md-6",children:[(0,a.jsx)("h6",{className:"obs-title",children:"Hazard Description"}),(0,a.jsx)("p",{className:"obs-content",children:(null==(t=p.hazardDescription)?void 0:t.name)||"N/A"})]})]}):(0,a.jsxs)("div",{className:"col-md-6",children:[(0,a.jsx)("h6",{className:"obs-title",children:"Reporter"}),(0,a.jsx)("p",{className:"obs-content",children:(null==(l=p.reporter)?void 0:l.firstName)||"N/A"})]})}),(0,a.jsx)("div",{className:"row",children:(0,a.jsxs)("div",{className:"col-md-6",children:[(0,a.jsx)("h6",{className:"obs-title",children:"Location"}),(0,a.jsx)("p",{className:"obs-content",children:[p.locationOne,p.locationTwo,p.locationThree,p.locationFour,p.locationFive,p.locationSix].filter(e=>null==e?void 0:e.name).map(e=>{var s;return null!=(s=null==e?void 0:e.name)?s:"N/A"}).join(" > ")||"N/A"})]})}),"Unsafe"===p.observationType&&(0,a.jsx)("div",{className:"obs-section",children:!p.rectifiedOnSpot&&(null==(n=p.observationActions)?void 0:n.length)?(0,a.jsx)(a.Fragment,{children:p.observationActions.map(e=>v(e))}):null}),p.rectifiedOnSpot&&(0,a.jsx)("div",{className:"obs-section shadow p-3",children:(0,a.jsxs)("div",{className:"col-md-12",children:[(0,a.jsx)("div",{className:"row mb-3",children:(0,a.jsxs)("div",{className:"col-md-12",children:[(0,a.jsx)("p",{className:"obs-title",children:"Action Taken"}),(0,a.jsx)("p",{className:"obs-content",children:p.actionTaken||"N/A"})]})}),(0,a.jsx)(d,{images:p.evidence,title:"Evidence",showName:!0})]})})]})}},79555:(e,s,t)=>{"use strict";t.d(s,{A:()=>A});var a=t(95155),i=t(12115),l=t(92809),n=t(94016),r=t(11846),o=t(68136),c=t(16639),d=t(60902),m=t(56160),x=t(82940),h=t.n(x),p=t(43864),u=t(34540),b=t(27347),g=t(26957),f=t(38336),j=t(24952),N=t(24752),y=t.n(N),v=t(35695);let A=e=>{var s,t,x,N,A,k,w,S,C,F,T,D,I,R,E,z,M,B,W,Y,P,O,_,q,L,U,H,V,G,J,K,Z,Q,X;let{applicationDetails:$}=e,ee=(0,i.useRef)(null),es=(0,u.d4)(e=>e.login.user),[et,ea]=(0,i.useState)(""),[ei,el]=(0,i.useState)(!1),[en,er]=(0,i.useState)([]),[eo,ec]=(0,i.useState)(""),[ed,em]=i.useState(!1),ex=(0,v.useRouter)(),eh=(null==(s=$.permitRiskControl)?void 0:s.reduce((e,s,t)=>((e[s.permitType]=e[s.permitType]||[]).push({...s,controlIndex:t}),e),{}))||{},ep=[{role:"Applicant",name:(null==(t=$.applicant)?void 0:t.firstName)||"N/A",roleName:"Applicant - ".concat((null==(x=$.applicant)?void 0:x.firstName)||"N/A"),signature:(null==(N=$.applicantStatus)?void 0:N.signature)||"N/A",status:(null==(A=$.applicantStatus)?void 0:A.status)?"Approved":"Pending",signedDate:(null==(k=$.applicantStatus)?void 0:k.signedDate)||"N/A",comments:(null==(w=$.applicantStatus)?void 0:w.comments)||"N/A",declaration:"I confirm that all required fields are accurately completed, and I acknowledge responsibility for adhering to the specified safety controls for this work activity."},$.reviewerId&&{role:"Reviewer",name:(null==(S=$.reviewer)?void 0:S.firstName)||"N/A",roleName:"Reviewer - ".concat((null==(C=$.reviewer)?void 0:C.firstName)||"N/A"),signature:(null==(F=$.reviewerStatus)?void 0:F.signature)||"N/A",status:(null==(T=$.reviewerStatus)?void 0:T.status)?"Approved":"Pending",signedDate:(null==(D=$.reviewerStatus)?void 0:D.signedDate)||"N/A",comments:(null==(I=$.reviewerStatus)?void 0:I.comments)||"N/A",declaration:"I have reviewed the application details and verify that the listed controls and prerequisites are suitable and sufficient for safe task execution."},$.assessorId&&{role:"Assessor",name:(null==(R=$.assessor)?void 0:R.firstName)||"N/A",roleName:"Assessor - ".concat((null==(E=$.assessor)?void 0:E.firstName)||"N/A"),signature:(null==(z=$.assessorStatus)?void 0:z.signature)||"N/A",status:(null==(M=$.assessorStatus)?void 0:M.status)?"Approved":"Pending",signedDate:(null==(B=$.assessorStatus)?void 0:B.signedDate)||"N/A",comments:(null==(W=$.assessorStatus)?void 0:W.comments)||"N/A",declaration:"I affirm that I have carefully assessed the risk levels, controls, and work conditions and that all necessary precautions are documented."},$.approverId&&{role:"Approver",name:(null==(Y=$.approver)?void 0:Y.firstName)||"N/A",roleName:"Approver - ".concat((null==(P=$.approver)?void 0:P.firstName)||"N/A"),signature:(null==(O=$.approverStatus)?void 0:O.signature)||"N/A",status:(null==(_=$.approverStatus)?void 0:_.status)?"Approved":"Pending",signedDate:(null==(q=$.approverStatus)?void 0:q.signedDate)||"N/A",comments:(null==(L=$.approverStatus)?void 0:L.comments)||"N/A",declaration:"I approve this permit with the assurance that all safety measures and controls have been verified and are in place to safely conduct this work."},$.closeoutStatus&&{role:"Closeout",name:$.closeoutStatus.by||"N/A",roleName:"Closeout - ".concat($.closeoutStatus.by||"N/A"),signature:(null==(U=$.closeoutStatus)?void 0:U.signature)||"N/A",status:null==(H=$.closeoutStatus)?void 0:H.status,signedDate:(null==(V=$.closeoutStatus)?void 0:V.signedDate)||"N/A",comments:(null==(G=$.closeoutStatus)?void 0:G.comments)||"N/A",declaration:"The task(s) have been completed. The work area(s) have been left in a tidy and safe condition."},$.acknowledgementStatus&&{role:"Acknowledger",name:$.acknowledgementStatus.by||"N/A",roleName:"Acknowledger - ".concat($.acknowledgementStatus.by||"N/A"),signature:(null==(J=$.acknowledgementStatus)?void 0:J.signature)||"N/A",status:null==(K=$.acknowledgementStatus)?void 0:K.status,signedDate:(null==(Z=$.acknowledgementStatus)?void 0:Z.signedDate)||"N/A",comments:(null==(Q=$.acknowledgementStatus)?void 0:Q.comments)||"N/A",declaration:"I acknowledge that, to the best of my knowledge, the work area(s) have been left in a tidy and safe condition by the applicant."}].filter(Boolean),eu="Internal"===es.type?ep.filter(e=>"Reviewer"!==e.role):ep;(0,i.useEffect)(()=>{eb("eptwAcknowledger")},[]);let eb=(0,i.useCallback)(async e=>{try{let s=await f.A.post(g.u3,{locationOneId:"",locationTwoId:"",locationThreeId:"",locationFourId:"",mode:e});if(200===s.status){let e=s.data.map(e=>({label:e.firstName,value:e.id}));er(e)}}catch(e){console.error("Error fetching crew list:",e)}},[]),eg=(e,s)=>{for(var t=atob(e.split(",")[1]),a=e.split(",")[0].split(":")[1].split(";")[0],i=new ArrayBuffer(t.length),l=new DataView(i),n=0;n<t.length;n++)l.setUint8(n,t.charCodeAt(n));return new File([i],s,{type:a})},ef=async()=>{let e="".concat(new Date().getTime(),"_captin_sign.png"),s=new FormData,t=eg(et,e);s.append("file",t);try{let e=await f.A.post(g.Dp,s,{headers:{"Content-Type":"multipart/form-data"}});if(e&&200===e.status)return e.data.files[0].originalname;throw Error("File upload failed.")}catch(e){throw console.error("File upload error:",e),e}},ej=async()=>{if(!ed)return void y().fire("Validation Error","Please confirm the closeout checkbox before proceeding.","warning");if(!et)return void y().fire("Validation Error","Please provide your signature to close out the permit.","warning");if(!eo)return void y().fire("Validation Error","Please select an Acknowledger before submitting.","warning");try{let e=await ef(),s=await f.A.patch((0,g.UR)($.id),{status:"Closed",closeoutStatus:{signature:e,comments:""},acknowledgerId:eo});204===s.status?(y().fire("Success","Permit Closed Successfully","success"),ex.back()):y().fire("Error","Unexpected response from server","error")}catch(e){console.error("Closeout failed:",e),y().fire("Error","Something went wrong while closing the permit","error")}};return(0,a.jsxs)(l.A,{fluid:!0,className:"p-2",children:[(0,a.jsx)(n.A,{className:"mb-4",children:(0,a.jsxs)(n.A.Body,{className:"p-0",children:[(0,a.jsx)(r.A,{children:(0,a.jsx)(o.A,{children:(0,a.jsx)("h5",{children:"Permit Details"})})}),(0,a.jsxs)(r.A,{className:"mb-2",children:[(0,a.jsxs)(o.A,{md:4,children:[(0,a.jsx)("strong",{children:"Work Type:"}),(0,a.jsx)("br",{}),(null==(X=$.permitType)?void 0:X[0])||"N/A"]}),(0,a.jsxs)(o.A,{md:4,children:[(0,a.jsx)("strong",{children:"Start Date:"}),(0,a.jsx)("br",{}),$.permitStartDate?h()($.permitStartDate).format("DD-MM-YYYY hh:mm A"):"N/A"]}),(0,a.jsxs)(o.A,{md:4,children:[(0,a.jsx)("strong",{children:"End Date:"}),(0,a.jsx)("br",{}),$.permitEndDate?h()($.permitEndDate).format("DD-MM-YYYY hh:mm A"):"N/A"]})]}),(0,a.jsx)(r.A,{className:"mb-2",children:(0,a.jsxs)(o.A,{children:[(0,a.jsx)("strong",{children:"Work Description:"}),(0,a.jsx)("br",{}),$.workDescription||"N/A"]})}),(0,a.jsxs)(r.A,{className:"mb-2",children:[(0,a.jsxs)(o.A,{md:4,children:[(0,a.jsx)("strong",{children:"Site Supervisor:"}),(0,a.jsx)("br",{}),$.nameOfSiteSupervisor||"N/A"]}),(0,a.jsxs)(o.A,{md:4,children:[(0,a.jsx)("strong",{children:"No. of Workers:"}),(0,a.jsx)("br",{}),$.noOfWorkers||"N/A"]})]}),(0,a.jsxs)(r.A,{className:"mb-2",children:[(0,a.jsxs)(o.A,{md:4,children:[(0,a.jsx)("strong",{children:"Supervisor Contact:"}),(0,a.jsx)("br",{}),$.supervisorContactNo||"N/A"]}),(0,a.jsxs)(o.A,{md:4,children:[(0,a.jsx)("strong",{children:"Applicant Contact:"}),(0,a.jsx)("br",{}),$.applicantContactNo||"N/A"]}),(0,a.jsxs)(o.A,{md:4,children:[(0,a.jsx)("strong",{children:"Work Order No:"}),(0,a.jsx)("br",{}),$.workOrderNo||"N/A"]})]}),(0,a.jsx)(r.A,{className:"mb-2",children:(0,a.jsxs)(o.A,{children:[(0,a.jsx)("strong",{children:"Location:"}),(0,a.jsx)("br",{}),[$.locationOne,$.locationTwo,$.locationThree,$.locationFour,$.locationFive,$.locationSix].filter(e=>null==e?void 0:e.name).map(e=>null==e?void 0:e.name).join(" > ")||"N/A"]})}),(0,a.jsx)(r.A,{className:"mb-3",children:$.supportingDocuments&&$.supportingDocuments.length>0&&(0,a.jsxs)(o.A,{children:[(0,a.jsx)("strong",{children:"Supporting Documents"}),(0,a.jsx)(r.A,{className:"mt-2",children:$.supportingDocuments.map((e,s)=>(0,a.jsx)(o.A,{md:3,children:(0,a.jsx)(p.A,{fileName:e,name:!0})},s))})]})})]})}),Object.entries(eh).map(e=>{let[s,t]=e;return(0,a.jsxs)(n.A,{className:"mb-4",children:[(0,a.jsx)(n.A.Header,{as:"h5",children:s}),(0,a.jsx)(n.A.Body,{children:t.map(e=>{var s;return(0,a.jsxs)("div",{className:"border p-3 mb-3 rounded",children:[(0,a.jsxs)("p",{children:[(0,a.jsxs)("strong",{children:[e.controlIndex+1,"."]})," ",e.description]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Type:"})," ",e.currentType]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Value:"})," ",e.value]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Remarks:"})," ",e.remarks]}),(null==(s=e.evidence)?void 0:s.length)>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("strong",{children:"Evidence:"}),(0,a.jsx)(r.A,{className:"mt-2",children:e.evidence.map((e,s)=>(0,a.jsx)(o.A,{md:3,children:(0,a.jsx)(p.A,{fileName:e,name:!1})},s))})]})]},e.controlIndex)})})]},s)}),(0,a.jsxs)(n.A,{children:[(0,a.jsx)(n.A.Header,{as:"h4",children:"Status Details"}),(0,a.jsx)(n.A.Body,{children:eu.map((e,s)=>(0,a.jsxs)("div",{className:"border p-3 mb-3 rounded",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Role/Name:"})," ",e.roleName]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Declaration:"})," ",e.declaration]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Signature:"})," ","N/A"!==e.signature?(0,a.jsx)(p.A,{fileName:e.signature,name:!1}):"N/A"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Signed Date:"})," ","N/A"!==e.signedDate?h()(e.signedDate).format("DD-MM-YYYY hh:mm A"):"N/A"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Comments:"})," ",e.comments]})]},s))})]}),"Active"===$.status&&(null==es?void 0:es.id)===$.applicantId&&(0,a.jsx)(n.A,{className:"mt-4",children:(0,a.jsxs)(n.A.Body,{children:[(0,a.jsx)(c.A.Check,{type:"checkbox",id:"closeout-confirm",label:"The task(s) has been completed. The work area(s) have been left in a tidy and safe condition.",checked:ed,onChange:e=>em(e.target.checked)}),ed&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{className:"m-auto mb-3",xs:12,sm:12,md:12,children:(0,a.jsx)(n.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsx)(n.A.Body,{children:(0,a.jsxs)(r.A,{children:[(0,a.jsx)(o.A,{xs:12,sm:12,md:12,className:"d-flex justify-content-center p-2",onClick:()=>el(!0),children:(0,a.jsx)("span",{className:"bi bi-pencil-square",style:{fontSize:60}})}),(0,a.jsx)("div",{className:"d-flex justify-content-center",children:et?(0,a.jsx)("img",{src:et,height:100,style:{minWidth:150}}):(0,a.jsx)(a.Fragment,{})})]})})})}),(0,a.jsx)(o.A,{className:"m-auto mb-3",xs:12,sm:12,md:12,children:(0,a.jsx)(n.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsx)(n.A.Body,{children:(0,a.jsx)(c.A.Group,{className:"mb-3",children:(0,a.jsx)(b.A,{title:"Identify Acknowledger to closeOut",options:en,selectedValue:eo,onChange:e=>ec(e),placeholder:"Select Acknowledger",clearable:!0,disabled:!1})})})})}),(0,a.jsx)("div",{className:"mt-3 text-end",children:(0,a.jsx)(d.A,{variant:"success",onClick:()=>{ej()},children:"Closeout Permit"})})]})]})}),(0,a.jsxs)(m.A,{show:ei,onHide:()=>{el(!1)},"aria-labelledby":"contained-modal-title-vcenter",centered:!0,backdrop:"static",children:[(0,a.jsx)(m.A.Header,{closeButton:!1,children:(0,a.jsx)(m.A.Title,{id:"contained-modal-title-vcenter",children:"Sign"})}),(0,a.jsx)(m.A.Body,{style:{background:"#f5f5f5",width:"100%"},children:(0,a.jsx)(j.A,{ref:ee,penColor:"#1F3BB3",backgroundColor:"white",canvasProps:{className:"sigCanvas",style:{width:"100%",background:"#fff",boxShadow:"0px 0px 10px 3px rgb(189 189 189)",height:"100px"}}})}),(0,a.jsxs)(m.A.Footer,{children:[(0,a.jsx)(d.A,{onClick:()=>{var e;let s=null==(e=ee.current)?void 0:e.toDataURL("image/png");s&&ea(s),el(!1)},children:"confirm"}),(0,a.jsx)(d.A,{onClick:()=>{var e;return null==(e=ee.current)?void 0:e.clear()},children:"Clear"}),(0,a.jsx)(d.A,{onClick:()=>{el(!1)},children:"Close"})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[5302,3496,586,8320,5579,2545,6874,6078,635,4816,1205,5898,6639,9697,7666,3678,4952,124,4746,1973,381,3066,5272,7726,8441,1684,7358],()=>s(48916)),_N_E=e.O()}]);