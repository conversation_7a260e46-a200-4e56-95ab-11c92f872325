(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9065],{21217:(e,s,t)=>{"use strict";t.d(s,{default:()=>o});var a=t(95155),l=t(9e4),i=t(38808),c=t(12115);let n=e=>{let{handleShowSetting:s,showSetting:t}=e,{theme:c,handleDarkModeToggle:n}=(0,l.D)(),{viewMode:r,handleRTLToggling:d}=(0,i.L)();return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{id:"setting-popup-overlay",className:t?"active":"",onClick:s}),(0,a.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(t?"active":""),id:"settingCard",children:(0,a.jsx)("div",{className:"card-body",children:(0,a.jsxs)("div",{className:"container",children:[(0,a.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,a.jsx)("p",{className:"mb-0",children:"Settings"}),(0,a.jsx)("div",{onClick:s,className:"btn-close",id:"settingCardClose"})]}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,a.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,a.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===c,onChange:n}),(0,a.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===c?"Light":"Dark"," mode"]})]})}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===r,onChange:d}),(0,a.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===r?"LTR":"RTL"," mode"]})]})})]})})})]})};var r=t(6874),d=t.n(r);let o=e=>{let{links:s,title:t}=e,[l,i]=(0,c.useState)(!1),r=()=>i(!l);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"header-area",id:"headerArea",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,a.jsx)("div",{className:"back-button",children:(0,a.jsx)(d(),{href:"/".concat(s),children:(0,a.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,a.jsx)("div",{className:"page-heading",children:(0,a.jsx)("h6",{className:"mb-0",children:t})}),(0,a.jsx)("div",{className:"setting-wrapper",onClick:r,children:(0,a.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,a.jsx)("i",{className:"bi bi-gear"}),(0,a.jsx)("span",{})]})})]})})}),(0,a.jsx)(n,{showSetting:l,handleShowSetting:r})]})}},38808:(e,s,t)=>{"use strict";t.d(s,{L:()=>l});var a=t(12115);let l=()=>{let[e,s]=(0,a.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,a.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let t=()=>{s(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:t,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let s=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(s),e.style.display="none")},20)},1e3))}t()}}}},38983:(e,s,t)=>{"use strict";t.d(s,{default:()=>n});var a=t(95155),l=t(6874),i=t.n(l);t(12115);let c=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],n=()=>(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,a.jsx)("div",{className:"container px-0",children:(0,a.jsx)("div",{className:"footer-nav position-relative",children:(0,a.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:c.map((e,s)=>(0,a.jsx)("li",{children:(0,a.jsxs)(i(),{href:"/".concat(e.link),children:[(0,a.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,a.jsx)("span",{children:e.title})]})},s))})})})})})},62454:(e,s,t)=>{"use strict";t.d(s,{default:()=>c});var a=t(95155);t(12115);var l=t(38983),i=t(21217);let c=()=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.default,{links:"elements",title:"Input group"}),(0,a.jsxs)("div",{className:"page-content-wrapper py-3",children:[(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"element-heading",children:(0,a.jsx)("h6",{children:"Input group"})})}),(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"card",children:(0,a.jsx)("div",{className:"card-body",children:(0,a.jsxs)("form",{onSubmit:e=>e.preventDefault(),children:[(0,a.jsxs)("div",{className:"input-group mb-3",children:[(0,a.jsx)("span",{className:"input-group-text",id:"basic-addon1",children:"@"}),(0,a.jsx)("input",{className:"form-control",type:"text",placeholder:"Designing World","aria-label":"Username","aria-describedby":"basic-addon1"})]}),(0,a.jsxs)("div",{className:"input-group mb-3",children:[(0,a.jsx)("input",{className:"form-control",type:"text",placeholder:"care.designingworld","aria-label":"Recipients username","aria-describedby":"basic-addon2"}),(0,a.jsx)("span",{className:"input-group-text",id:"basic-addon2",children:"@gmail.com"})]}),(0,a.jsx)("label",{className:"form-label",htmlFor:"basic-url",children:"Your profile URL"}),(0,a.jsxs)("div",{className:"input-group mb-3",children:[(0,a.jsx)("span",{className:"input-group-text",id:"basic-addon3",children:"themeforest.net/user/"}),(0,a.jsx)("input",{className:"form-control",id:"basic-url",type:"text","aria-describedby":"basic-addon3",placeholder:"designing-world"})]}),(0,a.jsxs)("div",{className:"input-group mb-3",children:[(0,a.jsx)("span",{className:"input-group-text",children:"Price"}),(0,a.jsx)("input",{className:"form-control",type:"text","aria-label":"Amount (to the nearest dollar)",placeholder:"24"}),(0,a.jsx)("span",{className:"input-group-text",children:"$"})]}),(0,a.jsxs)("div",{className:"input-group mb-3",children:[(0,a.jsx)("div",{className:"input-group-text",children:(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",value:"","aria-label":"Checkbox for following text input"})}),(0,a.jsx)("input",{className:"form-control",type:"text","aria-label":"Text input with checkbox",placeholder:"Checkbox"})]}),(0,a.jsxs)("div",{className:"input-group mb-3",children:[(0,a.jsx)("div",{className:"input-group-text",children:(0,a.jsx)("input",{className:"form-check-input",type:"radio",value:"","aria-label":"Radio button for following text input"})}),(0,a.jsx)("input",{className:"form-control",type:"text","aria-label":"Text input with radio button",placeholder:"Radio"})]}),(0,a.jsxs)("div",{className:"input-group mb-3",children:[(0,a.jsx)("span",{className:"input-group-text",children:"Name"}),(0,a.jsx)("input",{className:"form-control",type:"text","aria-label":"First name",placeholder:"First name"}),(0,a.jsx)("input",{className:"form-control",type:"text","aria-label":"Last name",placeholder:"Last name"})]}),(0,a.jsxs)("div",{className:"input-group mb-3",children:[(0,a.jsx)("button",{className:"btn btn-primary dropdown-toggle",type:"button","data-bs-toggle":"dropdown","aria-expanded":"false",children:"Dropdown"}),(0,a.jsxs)("ul",{className:"dropdown-menu",children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{className:"dropdown-item",href:"#",children:"Search By Name"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{className:"dropdown-item",href:"#",children:"Search By Price"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{className:"dropdown-item",href:"#",children:"Search By Date"})})]}),(0,a.jsx)("input",{className:"form-control",type:"text","aria-label":"Text input with dropdown button"})]}),(0,a.jsxs)("div",{className:"input-group",children:[(0,a.jsx)("span",{className:"input-group-text",children:"Message"}),(0,a.jsx)("textarea",{className:"form-control","aria-label":"With textarea",placeholder:"Hello, Designing World!"})]})]})})})})]}),(0,a.jsx)(l.default,{})]})},9e4:(e,s,t)=>{"use strict";t.d(s,{D:()=>l});var a=t(12115);let l=()=>{let[e,s]=(0,a.useState)("light"),[t,l]=(0,a.useState)(!1);(0,a.useEffect)(()=>{s(localStorage.getItem("theme")||"light"),l(!0)},[]),(0,a.useEffect)(()=>{t&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,t]);let i=(0,a.useCallback)(()=>{s(e=>"dark"===e?"light":"dark")},[]),c=(0,a.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let s=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(s),e.style.display="none")},20)},1e3))}i()},[i]);return{theme:e,toggleTheme:i,handleDarkModeToggle:c}}},95034:(e,s,t)=>{Promise.resolve().then(t.bind(t,62454))}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,8441,1684,7358],()=>s(95034)),_N_E=e.O()}]);