import { createSlice, PayloadAction } from "@reduxjs/toolkit";

// ✅ Define User interface
interface User {
  firstName: string;
  lastName: string;
  email: string;
  type: string;
}

// ✅ Define the state structure
interface LoginState {
  isLogin: boolean;
  user: User | null; // `null` instead of `{}` to prevent errors
}

const initialState: LoginState = {
  isLogin: false,
  user: null, // Initial value should be `null`
};

const loginSlice = createSlice({
  name: "login",
  initialState,
  reducers: {
    setLogin(state) {
      state.isLogin = true;
    },
    setLogout(state) {
      state.isLogin = false;
      state.user = null; // Clear user on logout
    },
    setUser(state, action: PayloadAction<User>) {
      state.user = action.payload;
    },
  },
});

export const loginActions = loginSlice.actions;
export default loginSlice;
