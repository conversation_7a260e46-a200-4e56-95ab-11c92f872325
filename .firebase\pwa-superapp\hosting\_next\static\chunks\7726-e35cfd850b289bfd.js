"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7726],{97726:(e,t,a)=>{a.d(t,{A:()=>v});var o=a(95155),n=a(12115),l=a(91867),i=a(16639),s=a(38336),r=a(26957),c=a(15743),d=a.n(c),u=a(68086),m=a(43864),g=a(89348);let p=async()=>{if(!navigator.permissions||!navigator.geolocation)return!1;switch((await navigator.permissions.query({name:"geolocation"})).state){case"granted":return!0;case"prompt":return await new Promise(e=>navigator.geolocation.getCurrentPosition(()=>e(!0),()=>e(!1),{maximumAge:0,timeout:8e3}));default:return!1}},h=async()=>!!navigator.geolocation&&new Promise(e=>{navigator.geolocation.getCurrentPosition(t=>{console.log("Location permission granted, coordinates:",t.coords.latitude,t.coords.longitude),e(!0)},t=>{console.log("Location permission denied or error:",t.message),e(!1)},{enableHighAccuracy:!0,timeout:1e4,maximumAge:0})}),f=()=>new Promise(e=>{if(!navigator.geolocation)return e(null);let t=!1,a=setTimeout(()=>{t||(t=!0,e(null))},8e3);navigator.geolocation.getCurrentPosition(o=>{t||(t=!0,clearTimeout(a),e(o.coords))},()=>{t||(t=!0,clearTimeout(a),e(null))},{enableHighAccuracy:!0,maximumAge:1e4})}),b=async e=>"createImageBitmap"in window?await createImageBitmap(e):new Promise((t,a)=>{let o=new Image;o.onload=()=>t(o),o.onerror=a,o.src=URL.createObjectURL(e)}),x=async(e,t)=>{let a="AIzaSyDNdPSBifICIPY7YumDCko5JX5tTRkYYSk";if(!a)return null;let o=new AbortController,n=setTimeout(()=>o.abort(),4e3);try{let n="https://maps.googleapis.com/maps/api/geocode/json?latlng=".concat(e,",").concat(t,"&key=").concat(a),l=await fetch(n,{signal:o.signal});if(!l.ok)return null;let i=await l.json();if(i.results&&i.results.length>0){for(let e of i.results)if(e.types.includes("street_address")||e.types.includes("premise")||e.types.includes("route"))return e.formatted_address;let e=i.results[0].formatted_address,t=e.split(",");if(t.length>2)return t.slice(0,2).join(",");return e}return null}catch(e){return console.error("Error fetching address:",e),null}finally{clearTimeout(n)}},w=async e=>{if(!/\.jpe?g$/i.test(e.name))return console.log("Not a JPEG file, skipping GPS injection (but will still add footer):",e.name),e;console.log("Getting current coordinates...");let t=await f();if(!t)return console.log("Could not get coordinates, returning original file"),e;console.log("Injecting GPS coordinates:",t.latitude,t.longitude);try{let a,o=new FileReader,n=await new Promise((t,a)=>{o.onload=()=>t(o.result),o.onerror=a,o.readAsDataURL(e)});try{a=d().load(n)}catch(e){a={"0th":{},Exif:{},GPS:{},"1st":{},thumbnail:null}}let l=t.latitude,i=t.longitude;a.GPS[d().GPSIFD.GPSLatitudeRef]=l>=0?"N":"S",a.GPS[d().GPSIFD.GPSLatitude]=d().GPSHelper.degToDmsRational(Math.abs(l)),a.GPS[d().GPSIFD.GPSLongitudeRef]=i>=0?"E":"W",a.GPS[d().GPSIFD.GPSLongitude]=d().GPSHelper.degToDmsRational(Math.abs(i));let s=d().dump(a),r=d().insert(s,n),c=await fetch(r),u=await c.blob();return new File([u],e.name,{type:e.type})}catch(t){return console.error("GPS injection failed:",t),e}},y=async function(e,t){let a=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=await b(e),n=o.width,l=o.height,i=Math.max(16,.025*l),s=1.2*i,r=document.createElement("canvas"),c=r.getContext("2d");c.font="".concat(i,'px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif');let d=t;if(a){let e=new Date().toLocaleString("en-US",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!0});d="".concat(t," | Taken: ").concat(e)}let u=((e,t)=>{let a=e.split(" "),o=[],n="";for(let e of a){let a=n?"".concat(n," ").concat(e):e;c.measureText(a).width<=t?n=a:n?(o.push(n),n=e):o.push(e)}return n&&o.push(n),o})(d,n-32),m=u.length*s+32;return r.width=n,r.height=l+m,c.drawImage(o,0,0),c.fillStyle="rgba(0, 0, 0, 0.8)",c.fillRect(0,l,n,m),c.fillStyle="white",c.font="".concat(i,'px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'),c.textAlign="left",c.textBaseline="top",u.forEach((e,t)=>{c.fillText(e,16,l+16+t*s)}),new Promise(t=>{r.toBlob(a=>{a?t(new File([a],e.name,{type:e.type})):t(e)},e.type,.9)})},v=e=>{let{attachmentConfig:t,onFilesSelected:a,disabled:c=!1,files:d,label:b}=e,[v,j]=(0,n.useState)([]),[N,P]=(0,n.useState)(!1),S=[".jpg",".jpeg",".png",".gif",".bmp",".webp",".svg"],C=[".mp4",".mov",".avi",".webm",".mkv"],k=[".pdf",".doc",".docx",".xls",".xlsx",".txt",".rtf"],G=()=>{var e,a,o,n,l;let i=[],s=[];if(!t)return"Camera: Photos";(null==t||null==(e=t.image)?void 0:e.galleryUploads)&&i.push("Images"),(null==t||null==(a=t.video)?void 0:a.galleryUploads)&&i.push("Videos"),(null==t||null==(o=t.documents)?void 0:o.enabled)&&i.push("Documents"),(null==t||null==(n=t.image)?void 0:n.enabled)&&s.push("Photos"),(null==t||null==(l=t.video)?void 0:l.enabled)&&s.push("Videos");let r="";return i.length>0&&(r+="Gallery: ".concat(i.join(", "))),s.length>0&&(r&&(r+=" | "),r+="Camera: ".concat(s.join(", "))),r||"No file types allowed"},L=(()=>{var e,a,o,n,l;let i=[];return t?(((null==t||null==(e=t.image)?void 0:e.galleryUploads)||(null==t||null==(a=t.image)?void 0:a.enabled))&&i.push(...S),((null==t||null==(o=t.video)?void 0:o.galleryUploads)||(null==t||null==(n=t.video)?void 0:n.enabled))&&i.push(...C),(null==t||null==(l=t.documents)?void 0:l.enabled)&&i.push(...k)):i.push(...S),i})(),A=(()=>{var e,a,o;let n=[];return t&&((null==t||null==(e=t.image)?void 0:e.galleryUploads)&&n.push(...S),(null==t||null==(a=t.video)?void 0:a.galleryUploads)&&n.push(...C),(null==t||null==(o=t.documents)?void 0:o.enabled)&&n.push(...k)),n})(),I=async e=>{j(t=>[...t,e.name]);try{var t,o,n;let l=new FormData;l.append("file",e);let i=await s.A.post(r.Dp,l,{headers:{"Content-Type":"multipart/form-data"}});(null==i?void 0:i.status)===200&&(null==(n=i.data)||null==(o=n.files)||null==(t=o[0])?void 0:t.originalname)&&a([...d,i.data.files[0].originalname])}finally{j(t=>t.filter(t=>t!==e.name))}},U=async e=>{for(let t of(P(!0),Array.from(e)))try{let e=t.name.substring(t.name.lastIndexOf(".")).toLowerCase();if(!L.includes(e)){alert("File format not allowed: ".concat(t.name,". Allowed types: ").concat(G()));continue}let a=t;if(S.includes(e))if(/\.jpe?g$/i.test(a.name)){a=await w(a);let e=await u.NZ(a).catch(e=>(console.error("Error reading GPS data:",e),null));if(e&&e.latitude&&e.longitude){console.log("Valid GPS coordinates found:",e.latitude,e.longitude);let t=await x(e.latitude,e.longitude);if(t)t="Location: ".concat(t);else{let a=(e,t)=>{let a=t?e>=0?"N":"S":e>=0?"E":"W";return"".concat(Math.abs(e).toFixed(6),"\xb0 ").concat(a)};t="Location: ".concat(a(e.latitude,!0),", ").concat(a(e.longitude,!1))}console.log("Adding location label:",t),a=await y(a,t,!0),a=await w(a)}else console.log("No location data available for:",a.name),a=await y(a,"No location data available",!0)}else{let e=await f();if(e){let t=await x(e.latitude,e.longitude);if(t)t="Location: ".concat(t);else{let a=(e,t)=>{let a=t?e>=0?"N":"S":e>=0?"E":"W";return"".concat(Math.abs(e).toFixed(6),"\xb0 ").concat(a)};t="Location: ".concat(a(e.latitude,!0),", ").concat(a(e.longitude,!1))}console.log("Adding location label to non-JPEG image:",t),a=await y(a,t,!0)}else console.log("No location data available for non-JPEG image:",a.name),a=await y(a,"No location data available",!0)}await I(a)}catch(e){console.error("Attachment upload error:",e),alert("Could not process ".concat(t.name,". Skipping."))}P(!1)},R=async e=>{e.target.files&&(Array.from(e.target.files).some(e=>{let t=e.name.substring(e.name.lastIndexOf(".")).toLowerCase();return S.includes(t)})&&(console.log("Image files detected, checking location permission"),await p()||(window.confirm("Location access is needed to add location data to your photos.\n\n• Click OK to allow location access now\n• Click Cancel to continue without location data")?(console.log("Requesting location permission..."),await h()?console.log("Location permission granted successfully!"):alert("Location access was denied. To enable location for photos:\n\n1. Click the location icon in your browser's address bar\n2. Select 'Allow' for location access\n3. Try uploading again for location data\n\nContinuing to upload without location data...")):console.log("User chose to continue without location data"))),U(e.target.files),e.target.value="")},T=async()=>{try{await p()||(window.confirm("Location access is needed to add location data to your photos.\n\n• Click OK to allow location access now\n• Click Cancel to continue without location data")?(console.log("Requesting location permission..."),await h()?console.log("Location permission granted successfully!"):alert("Location access was denied. To enable location for photos:\n\n1. Click the location icon in your browser's address bar\n2. Select 'Allow' for location access\n3. Try taking the photo again\n\nContinuing without location data...")):console.log("User chose to continue taking photo without location data"));let e=await g.i7.getPhoto({source:g.ru.Camera,resultType:g.LK.Uri,quality:80});if(!e.webPath)return;let t=await fetch(e.webPath).then(e=>e.blob()),a=new File([t],"camera_".concat(Date.now(),".jpg"),{type:"image/jpeg"});P(!0),U([a])}catch(e){console.error("Camera capture error:",e),alert("Could not capture photo. Please try again.")}},F=async()=>{try{let e=await g.i7.getPhoto({source:g.ru.Camera,resultType:g.LK.Uri,quality:80});if(!e.webPath)return;let t=await fetch(e.webPath).then(e=>e.blob()),a=new File([t],"video_".concat(Date.now(),".mp4"),{type:"video/mp4"});P(!0),U([a])}catch(e){console.error("Video capture error:",e),alert("Could not capture video. Please try again.")}},D=()=>{var e,a,o;return!!t&&((null==t||null==(e=t.image)?void 0:e.galleryUploads)||(null==t||null==(a=t.video)?void 0:a.galleryUploads)||(null==t||null==(o=t.documents)?void 0:o.enabled))},E=()=>{var e;return!t||(null==t||null==(e=t.image)?void 0:e.enabled)},z=()=>{var e;return!!t&&(null==t||null==(e=t.video)?void 0:e.enabled)};return D()||E()||z()?(0,o.jsxs)("div",{className:"attachment-uploader mt-3 position-relative",children:[N&&(0,o.jsx)("div",{className:"position-absolute top-0 start-0 w-100 h-100 bg-white bg-opacity-75 d-flex justify-content-center align-items-center",style:{zIndex:10,backdropFilter:"blur(2px)"},children:(0,o.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,o.jsx)(l.A,{animation:"border",variant:"primary"}),(0,o.jsx)("span",{className:"mt-2 text-primary fw-medium",children:"Processing..."})]})}),b&&(0,o.jsx)(i.A.Label,{className:"fw-medium mb-2",children:b}),(0,o.jsxs)("div",{className:"upload-buttons-container p-3 mb-3 bg-light rounded-3 border",style:{backgroundColor:"#f0f1fe"},children:[(0,o.jsxs)("div",{className:"d-flex justify-content-center gap-2 flex-wrap",children:[D()&&(0,o.jsxs)("label",{className:"btn px-3 py-2 d-flex align-items-center",style:{background:"#5a67d8",borderRadius:"8px",border:"none",color:"white",fontSize:"14px",fontWeight:"500",boxShadow:"0 2px 4px rgba(0,0,0,0.1)",minWidth:"120px",justifyContent:"center"},children:[(0,o.jsx)("i",{className:"bi bi-cloud-arrow-up me-2"}),"Gallery",(0,o.jsx)("input",{hidden:!0,multiple:!0,disabled:c,type:"file",accept:A.join(","),onChange:R})]}),E()&&(0,o.jsxs)("button",{className:"btn px-3 py-2 d-flex align-items-center",style:{background:"#38bdf8",borderRadius:"8px",border:"none",color:"white",fontSize:"14px",fontWeight:"500",boxShadow:"0 2px 4px rgba(0,0,0,0.1)",minWidth:"120px",justifyContent:"center"},disabled:c,onClick:T,children:[(0,o.jsx)("i",{className:"bi bi-camera me-2"}),"Take Photo"]}),z()&&(0,o.jsxs)("button",{className:"btn px-3 py-2 d-flex align-items-center",style:{background:"#f59e0b",borderRadius:"8px",border:"none",color:"white",fontSize:"14px",fontWeight:"500",boxShadow:"0 2px 4px rgba(0,0,0,0.1)",minWidth:"120px",justifyContent:"center"},disabled:c,onClick:F,children:[(0,o.jsx)("i",{className:"bi bi-camera-video me-2"}),"Record Video"]})]}),(0,o.jsx)("div",{className:"text-center mt-2",children:(0,o.jsxs)("small",{className:"text-muted",children:["Accepted file types: ",G()]})})]}),v.length>0&&(0,o.jsx)("div",{className:"mt-2 mb-3 p-2 bg-info bg-opacity-10 rounded-3 border border-info border-opacity-25",children:v.map(e=>(0,o.jsxs)("div",{className:"d-flex align-items-center gap-2 p-1",children:[(0,o.jsx)(l.A,{animation:"border",size:"sm",variant:"info"}),(0,o.jsxs)("span",{className:"text-info",children:["Uploading ",e,"..."]})]},e))}),d.length>0&&(0,o.jsxs)("div",{className:"uploaded-files-container mt-3",children:[(0,o.jsxs)("div",{className:"d-flex align-items-center mb-2",children:[(0,o.jsx)("i",{className:"bi bi-images me-2 text-secondary"}),(0,o.jsxs)("span",{className:"text-secondary",children:["Uploaded Files (",d.length,")"]})]}),(0,o.jsx)("div",{className:"row g-2",children:d.map((e,t)=>(0,o.jsx)("div",{className:"col-6",children:(0,o.jsxs)("div",{className:"position-relative border rounded p-2",style:{backgroundColor:"#f8f9fa",height:"100px"},children:[(0,o.jsx)("div",{className:"d-flex justify-content-center align-items-center h-100",style:{position:"relative"},children:(0,o.jsx)(m.A,{size:80,fileName:e,name:!1})}),(0,o.jsx)("button",{className:"btn btn-sm position-absolute top-0 end-0 p-0",style:{backgroundColor:"#f87171",borderRadius:"50%",width:"22px",height:"22px",display:"flex",justifyContent:"center",alignItems:"center",color:"white",border:"none",margin:"2px",zIndex:10},onClick:e=>{e.preventDefault(),e.stopPropagation();let o=[...d];o.splice(t,1),a(o)},children:(0,o.jsx)("i",{className:"bi bi-x",style:{fontSize:"14px"}})}),(0,o.jsx)("div",{className:"small text-truncate mt-1",style:{fontSize:"11px",color:"#666"},children:e.split("/").pop()})]})},t))})]})]}):(0,o.jsxs)("div",{className:"attachment-uploader",children:[b&&(0,o.jsx)("div",{className:"mb-2",children:(0,o.jsx)("label",{className:"fw-bold",children:b})}),(0,o.jsx)("div",{className:"alert alert-warning",children:"No file uploads are enabled for this attachment."})]})}}}]);