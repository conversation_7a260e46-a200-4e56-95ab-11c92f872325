[debug] [2025-08-09T03:39:13.178Z] ----------------------------------------------------------------------
[debug] [2025-08-09T03:39:13.180Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js deploy
[debug] [2025-08-09T03:39:13.180Z] CLI Version:   14.12.0
[debug] [2025-08-09T03:39:13.181Z] Platform:      win32
[debug] [2025-08-09T03:39:13.181Z] Node Version:  v20.10.0
[debug] [2025-08-09T03:39:13.181Z] Time:          Sat Aug 09 2025 09:09:13 GMT+0530 (India Standard Time)
[debug] [2025-08-09T03:39:13.181Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-08-09T03:39:13.339Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-08-09T03:39:13.340Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-08-09T03:39:13.340Z] [iam] checking project pwa-superapp for permissions ["firebase.projects.get","firebasehosting.sites.update"]
[debug] [2025-08-09T03:39:13.341Z] No OAuth tokens found
[debug] [2025-08-09T03:39:13.341Z] No OAuth tokens found
[debug] [2025-08-09T03:39:13.341Z] > refreshing access token with scopes: []
[debug] [2025-08-09T03:39:13.343Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-08-09T03:39:13.343Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-08-09T03:39:13.625Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-08-09T03:39:13.625Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-08-09T03:39:13.629Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/pwa-superapp:testIamPermissions [none]
[debug] [2025-08-09T03:39:13.629Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/pwa-superapp:testIamPermissions x-goog-quota-user=projects/pwa-superapp
[debug] [2025-08-09T03:39:13.629Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/pwa-superapp:testIamPermissions {"permissions":["firebase.projects.get","firebasehosting.sites.update"]}
[debug] [2025-08-09T03:39:15.014Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/pwa-superapp:testIamPermissions 200
[debug] [2025-08-09T03:39:15.014Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/pwa-superapp:testIamPermissions {"permissions":["firebase.projects.get","firebasehosting.sites.update"]}
[debug] [2025-08-09T03:39:15.015Z] Checked if tokens are valid: true, expires at: 1754714352625
[debug] [2025-08-09T03:39:15.015Z] Checked if tokens are valid: true, expires at: 1754714352625
[debug] [2025-08-09T03:39:15.015Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/pwa-superapp [none]
[debug] [2025-08-09T03:39:15.488Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/pwa-superapp 200
[debug] [2025-08-09T03:39:15.488Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/pwa-superapp {"projectId":"pwa-superapp","projectNumber":"596753047002","displayName":"pwa-superapp","name":"projects/pwa-superapp","resources":{"hostingSite":"pwa-superapp"},"state":"ACTIVE","etag":"1_6ae3feff-84f5-4d7a-842e-385e267439a9"}
[debug] [2025-08-09T03:39:19.811Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\venkatesh4win_gmail_com_application_default_credentials.json
[debug] [2025-08-09T03:39:19.813Z] Checked if tokens are valid: true, expires at: 1754714352625
[debug] [2025-08-09T03:39:19.813Z] Checked if tokens are valid: true, expires at: 1754714352625
[debug] [2025-08-09T03:39:19.813Z] >>> [apiv2][query] GET https://firebasehosting.googleapis.com/v1beta1/projects/pwa-superapp/sites pageToken=&pageSize=10
[debug] [2025-08-09T03:39:21.058Z] <<< [apiv2][status] GET https://firebasehosting.googleapis.com/v1beta1/projects/pwa-superapp/sites 200
[debug] [2025-08-09T03:39:21.058Z] <<< [apiv2][body] GET https://firebasehosting.googleapis.com/v1beta1/projects/pwa-superapp/sites {"sites":[{"name":"projects/pwa-superapp/sites/pwa-superapp","defaultUrl":"https://pwa-superapp.web.app","type":"DEFAULT_SITE"}]}
[debug] [2025-08-09T03:39:21.059Z] Checked if tokens are valid: true, expires at: 1754714352625
[debug] [2025-08-09T03:39:21.059Z] Checked if tokens are valid: true, expires at: 1754714352625
[debug] [2025-08-09T03:39:21.059Z] >>> [apiv2][query] GET https://firebasehosting.googleapis.com/v1beta1/projects/pwa-superapp/sites 
[debug] [2025-08-09T03:39:21.661Z] <<< [apiv2][status] GET https://firebasehosting.googleapis.com/v1beta1/projects/pwa-superapp/sites 200
[debug] [2025-08-09T03:39:21.662Z] <<< [apiv2][body] GET https://firebasehosting.googleapis.com/v1beta1/projects/pwa-superapp/sites {"sites":[{"name":"projects/pwa-superapp/sites/pwa-superapp","defaultUrl":"https://pwa-superapp.web.app","type":"DEFAULT_SITE"}]}
[debug] [2025-08-09T03:39:21.662Z] Checked if tokens are valid: true, expires at: 1754714352625
[debug] [2025-08-09T03:39:21.662Z] Checked if tokens are valid: true, expires at: 1754714352625
[debug] [2025-08-09T03:39:21.662Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/pwa-superapp/webApps/-/config [none]
[debug] [2025-08-09T03:39:22.476Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/pwa-superapp/webApps/-/config 200
[debug] [2025-08-09T03:39:22.476Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/pwa-superapp/webApps/-/config {"projectId":"pwa-superapp","appId":"1:596753047002:web:e809373bd1190e6a8bf4e3","storageBucket":"pwa-superapp.firebasestorage.app","apiKey":"AIzaSyCvHAIHZPnzl9xMJu9NpPQ8J-iL8zKDh9k","authDomain":"pwa-superapp.firebaseapp.com","messagingSenderId":"596753047002"}
[info] 
   Thank you for trying our early preview of Next.js support on Firebase Hosting.
   During the preview, support is best-effort and breaking changes can be expected. Proceed with caution.
   The integration is known to work with Next.js version 12 - 15.0. You may encounter errors.

   Documentation: https://firebase.google.com/docs/hosting/frameworks/nextjs
   File a bug: https://github.com/firebase/firebase-tools/issues/new?template=bug_report.md
   Submit a feature request: https://github.com/firebase/firebase-tools/issues/new?template=feature_request.md

   We'd love to learn from you. Express your interest in helping us shape the future of Firebase Hosting: https://goo.gle/41enW5X

[debug] [2025-08-09T03:39:31.041Z] Checked if tokens are valid: true, expires at: 1754714352625
[debug] [2025-08-09T03:39:31.041Z] Checked if tokens are valid: true, expires at: 1754714352625
[debug] [2025-08-09T03:39:31.042Z] >>> [apiv2][query] GET https://firebasehosting.googleapis.com/v1beta1/projects/pwa-superapp/sites/pwa-superapp [none]
[debug] [2025-08-09T03:39:31.978Z] <<< [apiv2][status] GET https://firebasehosting.googleapis.com/v1beta1/projects/pwa-superapp/sites/pwa-superapp 200
[debug] [2025-08-09T03:39:31.978Z] <<< [apiv2][body] GET https://firebasehosting.googleapis.com/v1beta1/projects/pwa-superapp/sites/pwa-superapp {"name":"projects/pwa-superapp/sites/pwa-superapp","defaultUrl":"https://pwa-superapp.web.app","type":"DEFAULT_SITE"}
[info]    ▲ Next.js 15.3.5

[info]    - Environments: .env


[info]    Creating an optimized production build ...

[info] uncaughtException [Error: EPERM: operation not permitted, open 'C:\Users\<USER>\OneDrive\Documents\pwas\old\.next\trace'] {
  errno: -4048,
  code: 'EPERM',
  syscall: 'open',
  path: 'C:\\Users\\<USER>\\OneDrive\\Documents\\pwas\\old\\.next\\trace'
}

[debug] [2025-08-09T03:39:47.376Z] Error: ENOENT: no such file or directory, open 'C:\Users\<USER>\OneDrive\Documents\pwas\old\.next\server\middleware-manifest.json'
[error] 
[error] Error: An unexpected error has occurred.
