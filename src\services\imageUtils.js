// imageUtils.js
import axios from 'axios';
import { FILE_DOWNLOAD, GET_BLOB } from '@/constant';
import API from './API';

// Function to fetch the full image URL, replace with actual API endpoint
export const fetchFullImageUrl = async (fileName) => {
    try {
        const response = await API.get(FILE_DOWNLOAD(fileName), {
            headers: {
                'Content-Type': 'application/json',
            },
        });
        const data = response.data;
        return data; // Adjust this according to your API response structure
    } catch (error) {
        console.error('Failed to fetch image URL:', error);
        return null;
    }
};

export const fetchDataUrlForImage = async (url) => {
    try {
        const response = await API.post(
            GET_BLOB,
            { presignedUrl: url },
            {
                responseType: 'blob', // Ensure the response is returned as binary data
            }
        );

        const blob = response.data; // Extract the binary data (blob)

        // Convert the blob to a Data URL
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result); // reader.result is the Data URL
            reader.onerror = reject;
            reader.readAsDataURL(blob); // Read the blob as a Data URL
        });
    } catch (error) {
        console.error('Error fetching Data URL:', error);
        throw error;
    }
};



