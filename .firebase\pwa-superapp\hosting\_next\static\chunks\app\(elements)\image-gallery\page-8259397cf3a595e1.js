(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7826],{10255:(e,t,l)=>{"use strict";function s(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return s}}),l(95155),l(47650),l(85744),l(20589)},17828:(e,t,l)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return s}});let s=(0,l(64054).createAsyncLocalStorage)()},21217:(e,t,l)=>{"use strict";l.d(t,{default:()=>d});var s=l(95155),n=l(9e4),a=l(38808),i=l(12115);let r=e=>{let{handleShowSetting:t,showSetting:l}=e,{theme:i,handleDarkModeToggle:r}=(0,n.D)(),{viewMode:c,handleRTLToggling:o}=(0,a.L)();return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{id:"setting-popup-overlay",className:l?"active":"",onClick:t}),(0,s.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(l?"active":""),id:"settingCard",children:(0,s.jsx)("div",{className:"card-body",children:(0,s.jsxs)("div",{className:"container",children:[(0,s.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,s.jsx)("p",{className:"mb-0",children:"Settings"}),(0,s.jsx)("div",{onClick:t,className:"btn-close",id:"settingCardClose"})]}),(0,s.jsx)("div",{className:"single-setting-panel",children:(0,s.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,s.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,s.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,s.jsx)("div",{className:"single-setting-panel",children:(0,s.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,s.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,s.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,s.jsx)("div",{className:"single-setting-panel",children:(0,s.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,s.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===i,onChange:r}),(0,s.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===i?"Light":"Dark"," mode"]})]})}),(0,s.jsx)("div",{className:"single-setting-panel",children:(0,s.jsxs)("div",{className:"form-check form-switch",children:[(0,s.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===c,onChange:o}),(0,s.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===c?"LTR":"RTL"," mode"]})]})})]})})})]})};var c=l(6874),o=l.n(c);let d=e=>{let{links:t,title:l}=e,[n,a]=(0,i.useState)(!1),c=()=>a(!n);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"header-area",id:"headerArea",children:(0,s.jsx)("div",{className:"container",children:(0,s.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,s.jsx)("div",{className:"back-button",children:(0,s.jsx)(o(),{href:"/".concat(t),children:(0,s.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,s.jsx)("div",{className:"page-heading",children:(0,s.jsx)("h6",{className:"mb-0",children:l})}),(0,s.jsx)("div",{className:"setting-wrapper",onClick:c,children:(0,s.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,s.jsx)("i",{className:"bi bi-gear"}),(0,s.jsx)("span",{})]})})]})})}),(0,s.jsx)(r,{showSetting:n,handleShowSetting:c})]})}},36645:(e,t,l)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}});let s=l(88229)._(l(67357));function n(e,t){var l;let n={};"function"==typeof e&&(n.loader=e);let a={...n,...t};return(0,s.default)({...a,modules:null==(l=a.loadableGenerated)?void 0:l.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38808:(e,t,l)=>{"use strict";l.d(t,{L:()=>n});var s=l(12115);let n=()=>{let[e,t]=(0,s.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,s.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let l=()=>{t(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:l,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}l()}}}},38983:(e,t,l)=>{"use strict";l.d(t,{default:()=>r});var s=l(95155),n=l(6874),a=l.n(n);l(12115);let i=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],r=()=>(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,s.jsx)("div",{className:"container px-0",children:(0,s.jsx)("div",{className:"footer-nav position-relative",children:(0,s.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:i.map((e,t)=>(0,s.jsx)("li",{children:(0,s.jsxs)(a(),{href:"/".concat(e.link),children:[(0,s.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,s.jsx)("span",{children:e.title})]})},t))})})})})})},48077:(e,t,l)=>{Promise.resolve().then(l.bind(l,73951))},55028:(e,t,l)=>{"use strict";l.d(t,{default:()=>n.a});var s=l(36645),n=l.n(s)},62146:(e,t,l)=>{"use strict";function s(e){let{reason:t,children:l}=e;return l}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return s}}),l(45262)},64054:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var l in t)Object.defineProperty(e,l,{enumerable:!0,get:t[l]})}(t,{bindSnapshot:function(){return i},createAsyncLocalStorage:function(){return a},createSnapshot:function(){return r}});let l=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class s{disable(){throw l}getStore(){}run(){throw l}exit(){throw l}enterWith(){throw l}static bind(e){return e}}let n="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function a(){return n?new n:new s}function i(e){return n?n.bind(e):s.bind(e)}function r(){return n?n.snapshot():function(e,...t){return e(...t)}}},67357:(e,t,l)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let s=l(95155),n=l(12115),a=l(62146);function i(e){return{default:e&&"default"in e?e.default:e}}l(10255);let r={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},c=function(e){let t={...r,...e},l=(0,n.lazy)(()=>t.loader().then(i)),c=t.loading;function o(e){let i=c?(0,s.jsx)(c,{isLoading:!0,pastDelay:!0,error:null}):null,r=!t.ssr||!!t.loading,o=r?n.Suspense:n.Fragment,d=t.ssr?(0,s.jsxs)(s.Fragment,{children:[null,(0,s.jsx)(l,{...e})]}):(0,s.jsx)(a.BailoutToCSR,{reason:"next/dynamic",children:(0,s.jsx)(l,{...e})});return(0,s.jsx)(o,{...r?{fallback:i}:{},children:d})}return o.displayName="LoadableComponent",o}},73951:(e,t,l)=>{"use strict";l.d(t,{default:()=>r});var s=l(95155);l(12115);var n=l(38983),a=l(21217);let i=(0,l(55028).default)(()=>Promise.all([l.e(7677),l.e(6766),l.e(1450),l.e(5981)]).then(l.bind(l,85981)),{loadableGenerated:{webpack:()=>[85981]},ssr:!1}),r=()=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.default,{links:"elements",title:"Image Gallery"}),(0,s.jsx)(i,{}),(0,s.jsx)(n.default,{})]})},85744:(e,t,l)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return s.workAsyncStorageInstance}});let s=l(17828)},9e4:(e,t,l)=>{"use strict";l.d(t,{D:()=>n});var s=l(12115);let n=()=>{let[e,t]=(0,s.useState)("light"),[l,n]=(0,s.useState)(!1);(0,s.useEffect)(()=>{t(localStorage.getItem("theme")||"light"),n(!0)},[]),(0,s.useEffect)(()=>{l&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,l]);let a=(0,s.useCallback)(()=>{t(e=>"dark"===e?"light":"dark")},[]),i=(0,s.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}a()},[a]);return{theme:e,toggleTheme:a,handleDarkModeToggle:i}}}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,8441,1684,7358],()=>t(48077)),_N_E=e.O()}]);