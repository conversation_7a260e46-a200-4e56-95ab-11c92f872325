

interface DataType {
  id: number;
  color: string;
  img: string;
  name: string;
  designation: string;
  email: string;
}


const team_data:DataType[] = [
{
  id: 1,
  color: "primary",
  img: "/assets/img/bg-img/2.jpg",
  name: "<PERSON><PERSON><PERSON>",
  designation: "UX/UI Designer",
  email: "<EMAIL>",
},
{
  id: 2,
  color: "primary",
  img: "/assets/img/bg-img/20.jpg",
  name: "Hasnain Riyadh",
  designation: "Support Manager",
  email: "<EMAIL>",
},
{
  id: 3,
  color: "warning",
  img: "/assets/img/bg-img/21.jpg",
  name: "<PERSON><PERSON><PERSON><PERSON>",
  designation: "Developer",
  email: "<EMAIL>",
},
{
  id: 4,
  color: "warning",
  img: "/assets/img/bg-img/2.jpg",
  name: "<PERSON><PERSON><PERSON>",
  designation: "UX/UI Designer",
  email: "<EMAIL>",
},
{
  id: 5,
  color: "info",
  img: "/assets/img/bg-img/20.jpg",
  name: "<PERSON><PERSON><PERSON>",
  designation: "Support Manager",
  email: "<EMAIL>",
},
{
  id: 6,
  color: "info",
  img: "/assets/img/bg-img/21.jpg",
  name: "Nazrul Sumon",
  designation: "Developer",
  email: "<EMAIL>",
},


]

export default team_data