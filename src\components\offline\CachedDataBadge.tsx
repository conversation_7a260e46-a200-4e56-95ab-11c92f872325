"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { offlineAPI } from '@/services/offlineAPI';

interface CachedDataBadgeProps {
  type: 'services' | 'actions';
  className?: string;
  showAge?: boolean;
}

const CachedDataBadge: React.FC<CachedDataBadgeProps> = ({ 
  type, 
  className = '', 
  showAge = true 
}) => {
  const [isOnline, setIsOnline] = useState(true);
  const [dataAge, setDataAge] = useState<string>('');
  const [isDataFresh, setIsDataFresh] = useState(true);
  const [showTooltip, setShowTooltip] = useState(false);

  const updateStatus = useCallback(async () => {
    try {
      setIsOnline(navigator.onLine);
      const age = await offlineAPI.getDataAge(type);
      const fresh = await offlineAPI.isDataFresh(type);

      setDataAge(age);
      setIsDataFresh(fresh);
    } catch (error) {
      console.error('Error updating cache status:', error);
    }
  }, [type]);

  useEffect(() => {
    updateStatus();

    // Subscribe to connection changes
    const unsubscribe = offlineAPI.onConnectionChange((online) => {
      setIsOnline(online);
      updateStatus();
    });

    // Update status every minute
    const interval = setInterval(updateStatus, 60000);

    return () => {
      unsubscribe();
      clearInterval(interval);
    };
  }, [type, updateStatus]);



  const getBadgeColor = () => {
    if (!isOnline) {
      return {
        background: '#fff3e0',
        color: '#f57c00',
        border: '#ffcc02'
      };
    }
    
    if (!isDataFresh) {
      return {
        background: '#fff3e0',
        color: '#f57c00',
        border: '#ffcc02'
      };
    }
    
    return {
      background: '#e8f5e8',
      color: '#2e7d32',
      border: '#4caf50'
    };
  };

  const getBadgeIcon = () => {
    if (!isOnline) return 'bi-wifi-off';
    if (!isDataFresh) return 'bi-clock-history';
    return 'bi-check-circle';
  };

  const getBadgeText = () => {
    if (!isOnline) return 'Offline';
    if (!isDataFresh) return 'Cached';
    return 'Fresh';
  };

  const getTooltipText = () => {
    if (!isOnline) {
      return `Showing cached ${type} data. Last updated ${dataAge}.`;
    }
    
    if (!isDataFresh) {
      return `Data is ${dataAge} old. Tap to refresh.`;
    }
    
    return `${type.charAt(0).toUpperCase() + type.slice(1)} data is up to date.`;
  };

  const handleRefresh = async () => {
    if (!isOnline) return;
    
    try {
      await offlineAPI.forceRefresh(type);
      updateStatus();
      setShowTooltip(false);
    } catch (error) {
      console.error('Refresh failed:', error);
    }
  };

  // Don't show badge if data is fresh and online
  if (isOnline && isDataFresh) {
    return null;
  }

  const colors = getBadgeColor();

  return (
    <div className={`position-relative ${className}`}>
      <span
        className="badge d-inline-flex align-items-center"
        style={{
          backgroundColor: colors.background,
          color: colors.color,
          border: `1px solid ${colors.border}`,
          fontSize: '10px',
          fontWeight: '500',
          padding: '4px 8px',
          borderRadius: '12px',
          cursor: !isOnline || !isDataFresh ? 'pointer' : 'default'
        }}
        onClick={() => setShowTooltip(!showTooltip)}
      >
        <i className={`${getBadgeIcon()} me-1`} style={{ fontSize: '10px' }}></i>
        {getBadgeText()}
        {showAge && dataAge !== 'Never synced' && (
          <span className="ms-1" style={{ opacity: 0.8 }}>
            ({dataAge})
          </span>
        )}
      </span>

      {/* Tooltip */}
      {showTooltip && (
        <div
          className="position-absolute bg-dark text-white rounded px-2 py-1"
          style={{
            bottom: '100%',
            left: '50%',
            transform: 'translateX(-50%)',
            marginBottom: '4px',
            fontSize: '11px',
            whiteSpace: 'nowrap',
            zIndex: 1000,
            maxWidth: '200px',
            textAlign: 'center'
          }}
        >
          {getTooltipText()}
          
          {/* Refresh button for stale data */}
          {isOnline && !isDataFresh && (
            <div className="mt-1">
              <button
                className="btn btn-link text-white p-0"
                onClick={handleRefresh}
                style={{ fontSize: '10px', textDecoration: 'underline' }}
              >
                Refresh now
              </button>
            </div>
          )}
          
          {/* Tooltip arrow */}
          <div
            className="position-absolute"
            style={{
              top: '100%',
              left: '50%',
              transform: 'translateX(-50%)',
              width: 0,
              height: 0,
              borderLeft: '4px solid transparent',
              borderRight: '4px solid transparent',
              borderTop: '4px solid #333'
            }}
          ></div>
        </div>
      )}
    </div>
  );
};

export default CachedDataBadge;
