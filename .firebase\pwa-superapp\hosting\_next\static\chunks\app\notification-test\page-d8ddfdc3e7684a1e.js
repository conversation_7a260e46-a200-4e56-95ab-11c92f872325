(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4759],{11846:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var i=t(29300),n=t.n(i),a=t(12115),c=t(97390),r=t(95155);let o=a.forwardRef((e,s)=>{let{bsPrefix:t,className:i,as:a="div",...o}=e,l=(0,c.oU)(t,"row"),d=(0,c.gy)(),u=(0,c.Jm)(),h="".concat(l,"-cols"),m=[];return d.forEach(e=>{let s,t=o[e];delete o[e],null!=t&&"object"==typeof t?{cols:s}=t:s=t,null!=s&&m.push("".concat(h).concat(e!==u?"-".concat(e):"","-").concat(s))}),(0,r.jsx)(a,{ref:s,...o,className:n()(i,l,...m)})});o.displayName="Row";let l=o},60466:(e,s,t)=>{"use strict";t.d(s,{A:()=>p});var i=t(29300),n=t.n(i),a=t(73666),c=t(12115),r=t(48573),o=t(2489),l=t(74874);let d=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter(e=>null!=e).reduce((e,s)=>{if("function"!=typeof s)throw Error("Invalid Argument Type, must only provide functions, undefined, or null.");return null===e?s:function(){for(var t=arguments.length,i=Array(t),n=0;n<t;n++)i[n]=arguments[n];e.apply(this,i),s.apply(this,i)}},null)};var u=t(78283),h=t(54692),m=t(95155);let f={height:["marginTop","marginBottom"],width:["marginLeft","marginRight"]};function g(e,s){let t=s["offset".concat(e[0].toUpperCase()).concat(e.slice(1))],i=f[e];return t+parseInt((0,a.A)(s,i[0]),10)+parseInt((0,a.A)(s,i[1]),10)}let x={[r.kp]:"collapse",[r.ze]:"collapsing",[r.ns]:"collapsing",[r._K]:"collapse show"},p=c.forwardRef((e,s)=>{let{onEnter:t,onEntering:i,onEntered:a,onExit:r,onExiting:f,className:p,children:j,dimension:A="height",in:N=!1,timeout:b=300,mountOnEnter:y=!1,unmountOnExit:S=!1,appear:v=!1,getDimensionValue:w=g,...C}=e,k="function"==typeof A?A():A,T=(0,c.useMemo)(()=>d(e=>{e.style[k]="0"},t),[k,t]),P=(0,c.useMemo)(()=>d(e=>{let s="scroll".concat(k[0].toUpperCase()).concat(k.slice(1));e.style[k]="".concat(e[s],"px")},i),[k,i]),R=(0,c.useMemo)(()=>d(e=>{e.style[k]=null},a),[k,a]),E=(0,c.useMemo)(()=>d(e=>{e.style[k]="".concat(w(k,e),"px"),(0,u.A)(e)},r),[r,w,k]),O=(0,c.useMemo)(()=>d(e=>{e.style[k]=null},f),[k,f]);return(0,m.jsx)(h.A,{ref:s,addEndListener:l.A,...C,"aria-expanded":C.role?N:null,onEnter:T,onEntering:P,onEntered:R,onExit:E,onExiting:O,childRef:(0,o.am)(j),in:N,timeout:b,mountOnEnter:y,unmountOnExit:S,appear:v,children:(e,s)=>c.cloneElement(j,{...s,className:n()(p,j.props.className,x[e],"width"===k&&"collapse-horizontal")})})})},68136:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var i=t(29300),n=t.n(i),a=t(12115),c=t(97390),r=t(95155);let o=a.forwardRef((e,s)=>{let[{className:t,...i},{as:a="div",bsPrefix:o,spans:l}]=function(e){let{as:s,bsPrefix:t,className:i,...a}=e;t=(0,c.oU)(t,"col");let r=(0,c.gy)(),o=(0,c.Jm)(),l=[],d=[];return r.forEach(e=>{let s,i,n,c=a[e];delete a[e],"object"==typeof c&&null!=c?{span:s,offset:i,order:n}=c:s=c;let r=e!==o?"-".concat(e):"";s&&l.push(!0===s?"".concat(t).concat(r):"".concat(t).concat(r,"-").concat(s)),null!=n&&d.push("order".concat(r,"-").concat(n)),null!=i&&d.push("offset".concat(r,"-").concat(i))}),[{...a,className:n()(i,...l,...d)},{as:s,bsPrefix:t,spans:l}]}(e);return(0,r.jsx)(a,{...i,ref:s,className:n()(t,!l.length&&o)})});o.displayName="Col";let l=o},79630:(e,s,t)=>{"use strict";function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var t=arguments[s];for(var i in t)({}).hasOwnProperty.call(t,i)&&(e[i]=t[i])}return e}).apply(null,arguments)}t.d(s,{A:()=>i})},86715:(e,s,t)=>{Promise.resolve().then(t.bind(t,99047))},91867:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var i=t(29300),n=t.n(i),a=t(12115),c=t(97390),r=t(95155);let o=a.forwardRef((e,s)=>{let{bsPrefix:t,variant:i,animation:a="border",size:o,as:l="div",className:d,...u}=e;t=(0,c.oU)(t,"spinner");let h="".concat(t,"-").concat(a);return(0,r.jsx)(l,{ref:s,...u,className:n()(d,h,o&&"".concat(h,"-").concat(o),i&&"text-".concat(i))})});o.displayName="Spinner";let l=o},92809:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var i=t(29300),n=t.n(i),a=t(12115),c=t(97390),r=t(95155);let o=a.forwardRef((e,s)=>{let{bsPrefix:t,fluid:i=!1,as:a="div",className:o,...l}=e,d=(0,c.oU)(t,"container");return(0,r.jsx)(a,{ref:s,...l,className:n()(o,i?"".concat(d).concat("string"==typeof i?"-".concat(i):"-fluid"):d)})});o.displayName="Container";let l=o},99047:(e,s,t)=>{"use strict";t.d(s,{default:()=>v});var i=t(95155),n=t(12115),a=t(92809),c=t(11846),r=t(68136),o=t(94016),l=t(29300),d=t.n(l),u=t(97390);let h=n.forwardRef((e,s)=>{let{bsPrefix:t,bg:n="primary",pill:a=!1,text:c,className:r,as:o="span",...l}=e,h=(0,u.oU)(t,"badge");return(0,i.jsx)(o,{ref:s,...l,className:d()(r,h,a&&"rounded-pill",c&&"text-".concat(c),n&&"bg-".concat(n))})});h.displayName="Badge";var m=t(60902),f=t(57111);let g=n.forwardRef((e,s)=>{let{bsPrefix:t,className:n,striped:a,bordered:c,borderless:r,hover:o,size:l,variant:h,responsive:m,...f}=e,g=(0,u.oU)(t,"table"),x=d()(n,g,h&&"".concat(g,"-").concat(h),l&&"".concat(g,"-").concat(l),a&&"".concat(g,"-").concat("string"==typeof a?"striped-".concat(a):"striped"),c&&"".concat(g,"-bordered"),r&&"".concat(g,"-borderless"),o&&"".concat(g,"-hover")),p=(0,i.jsx)("table",{...f,className:x,ref:s});if(m){let e="".concat(g,"-responsive");return"string"==typeof m&&(e="".concat(e,"-").concat(m)),(0,i.jsx)("div",{className:e,children:p})}return p});var x=t(91867),p=t(60466),j=t(46222);let A=e=>{let{onTokenGenerated:s,showTestButton:t=!0,autoInitialize:a=!0}=e,[c,r]=(0,n.useState)(!1),[l,d]=(0,n.useState)(!1),[u,g]=(0,n.useState)("default"),[A,N]=(0,n.useState)(null),[b,y]=(0,n.useState)(null),[S,v]=(0,n.useState)(null),[w,C]=(0,n.useState)(""),[k,T]=(0,n.useState)(!1),[P,R]=(0,n.useState)([]);(0,n.useEffect)(()=>{a&&E()},[a]);let E=async()=>{try{r(!0),v(null);let e=await j.A.initialize();d(e);let s=j.A.getDeviceInfo();N(s);let t=j.A.getPermissionStatus();g(t);let i=j.A.getStatusMessage();C(i);let n=j.A.getSetupInstructions();R(n),e||v("Notifications are not supported on this device")}catch(e){console.error("Error initializing notification service:",e),v(e.message||"Failed to initialize notification service")}finally{r(!1)}},O=async()=>{try{if(r(!0),v(null),!await j.A.requestPermission()){v("Notification permission was denied"),T(!0);return}let e=await j.A.registerForNotifications();if(e){y(e),g("granted"),null==s||s(e);let t=j.A.getStatusMessage();C(t)}else v("Failed to register for notifications")}catch(e){console.error("Error enabling notifications:",e),v(e.message||"Failed to enable notifications")}finally{r(!1)}},I=async()=>{try{r(!0),await j.A.showTestNotification()||v("Failed to show test notification")}catch(e){console.error("Error showing test notification:",e),v("Failed to show test notification")}finally{r(!1)}};return!l&&c?(0,i.jsxs)(o.A,{className:"text-center p-4",children:[(0,i.jsx)(x.A,{animation:"border",className:"mx-auto mb-3"}),(0,i.jsx)("p",{children:"Checking notification support..."})]}):(0,i.jsxs)(o.A,{className:"shadow-sm",children:[(0,i.jsxs)(o.A.Header,{className:"d-flex justify-content-between align-items-center",children:[(0,i.jsxs)("h6",{className:"mb-0",children:[A?A.isIOS?"\uD83C\uDF4E":A.isAndroid?"\uD83E\uDD16":"\uD83D\uDCBB":"\uD83D\uDCF1"," Push Notifications"]}),A&&(0,i.jsx)(h,{bg:(()=>{if(!A)return"secondary";switch(A.recommendedMethod){case"fcm":return"success";case"ios-web-push":return"info";case"local-only":return"warning";case"unsupported":return"danger";default:return"secondary"}})(),children:A.recommendedMethod.replace("-"," ").toUpperCase()})]}),(0,i.jsxs)(o.A.Body,{children:[(0,i.jsxs)("div",{className:"mb-3",children:[(0,i.jsx)("small",{className:"text-muted",children:"Status: "}),(0,i.jsx)("span",{className:"fw-bold ".concat("granted"===u?"text-success":"text-warning"),children:w})]}),A&&(0,i.jsx)("div",{className:"mb-3",children:(0,i.jsxs)("small",{className:"text-muted d-block",children:["Device: ",A.isIOS?"iOS":A.isAndroid?"Android":"Desktop"," • Browser: ",A.browser," • PWA: ",A.isIOSPWA?"Yes":"No"]})}),S&&(0,i.jsxs)(f.A,{variant:"danger",className:"mb-3",children:[(0,i.jsx)("i",{className:"bi bi-exclamation-triangle me-2"}),S]}),"granted"===u&&b&&(0,i.jsxs)(f.A,{variant:"success",className:"mb-3",children:[(0,i.jsx)("i",{className:"bi bi-check-circle me-2"}),"Notifications are enabled and working!"]}),(0,i.jsxs)("div",{className:"d-flex gap-2 flex-wrap",children:["default"===u&&(0,i.jsx)(m.A,{variant:"primary",onClick:O,disabled:c||!j.A.isSupported(),className:"d-flex align-items-center",children:c?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(x.A,{animation:"border",size:"sm",className:"me-2"}),"Enabling..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("i",{className:"bi bi-bell me-2"}),"Enable Notifications"]})}),"granted"===u&&t&&(0,i.jsxs)(m.A,{variant:"outline-primary",onClick:I,disabled:c,className:"d-flex align-items-center",children:[c?(0,i.jsx)(x.A,{animation:"border",size:"sm",className:"me-2"}):(0,i.jsx)("i",{className:"bi bi-bell-fill me-2"}),"Test Notification"]}),"denied"===u&&(0,i.jsxs)(m.A,{variant:"outline-info",onClick:()=>T(!k),children:[(0,i.jsx)("i",{className:"bi bi-info-circle me-2"}),"Show Setup Instructions"]})]}),(0,i.jsx)(p.A,{in:k||"denied"===u,children:(0,i.jsx)("div",{className:"mt-3",children:(0,i.jsxs)(f.A,{variant:"info",children:[(0,i.jsxs)("h6",{children:[(0,i.jsx)("i",{className:"bi bi-info-circle me-2"}),"Setup Instructions"]}),(0,i.jsx)("ol",{className:"mb-0",children:P.map((e,s)=>(0,i.jsx)("li",{className:"mb-1",children:e},s))})]})})}),(null==A?void 0:A.recommendedMethod)==="unsupported"&&(0,i.jsxs)(f.A,{variant:"warning",className:"mt-3",children:[(0,i.jsx)("i",{className:"bi bi-exclamation-triangle me-2"}),(0,i.jsx)("strong",{children:"Limited Support"}),(0,i.jsx)("br",{}),"Push notifications are not fully supported on your device. You may still receive notifications when the app is open."]}),(null==A?void 0:A.isIOS)&&(0,i.jsxs)(f.A,{variant:"info",className:"mt-3",children:[(0,i.jsx)("i",{className:"bi bi-apple me-2"}),(0,i.jsx)("strong",{children:"iOS Notice"}),(0,i.jsx)("br",{}),"For best results on iOS, add this app to your home screen and open it from there. Web push notifications on iOS require Safari and iOS 16.4+."]}),b&&!1]})]})};var N=t(42851),b=t(15123);class y{async runAllTests(){return this.results=[],console.log("\uD83E\uDDEA Starting comprehensive notification tests..."),await this.testDeviceDetection(),await this.testServiceInitialization(),await this.testPermissionStatus(),await this.testIOSSpecific(),await this.testFCMSpecific(),await this.testUnifiedService(),console.log("\uD83E\uDDEA All tests completed:",this.results),this.results}async testDeviceDetection(){try{await j.A.initialize();let e=j.A.getDeviceInfo();this.addResult({test:"Device Detection",success:!!e,message:e?"Detected: ".concat(e.isIOS?"iOS":e.isAndroid?"Android":"Desktop"," - ").concat(e.recommendedMethod):"Failed to detect device",data:e})}catch(e){this.addResult({test:"Device Detection",success:!1,message:"Error: ".concat(e)})}}async testServiceInitialization(){try{let e=await j.A.initialize();this.addResult({test:"Unified Service Initialization",success:e,message:e?"Unified service initialized successfully":"Unified service failed to initialize"})}catch(e){this.addResult({test:"Unified Service Initialization",success:!1,message:"Error: ".concat(e)})}}async testPermissionStatus(){try{let e=j.A.getPermissionStatus(),s=j.A.getStatusMessage();this.addResult({test:"Permission Status",success:!0,message:"Permission: ".concat(e," - ").concat(s),data:{permission:e,statusMessage:s}})}catch(e){this.addResult({test:"Permission Status",success:!1,message:"Error: ".concat(e)})}}async testIOSSpecific(){try{let e=N.A.isSupported(),s=N.A.getPermissionStatus();this.addResult({test:"iOS Notification Support",success:!0,message:"iOS Support: ".concat(e,", Permission: ").concat(s),data:{isSupported:e,permission:s}})}catch(e){this.addResult({test:"iOS Notification Support",success:!1,message:"Error: ".concat(e)})}}async testFCMSpecific(){try{let e=await b.A.initialize();this.addResult({test:"FCM Service",success:!!e,message:e?"FCM service available":"FCM service not available",data:{hasMessaging:!!e}})}catch(e){this.addResult({test:"FCM Service",success:!1,message:"Error: ".concat(e)})}}async testUnifiedService(){try{let e=j.A.isSupported(),s=j.A.getSetupInstructions();this.addResult({test:"Unified Service Features",success:!0,message:"Supported: ".concat(e,", Instructions available: ").concat(s.length>0),data:{isSupported:e,instructionCount:s.length}})}catch(e){this.addResult({test:"Unified Service Features",success:!1,message:"Error: ".concat(e)})}}addResult(e){this.results.push(e),console.log("".concat(e.success?"✅":"❌"," ").concat(e.test,": ").concat(e.message))}async testPermissionRequest(){try{console.log("\uD83D\uDD14 Testing permission request...");let e=await j.A.requestPermission();return{test:"Permission Request",success:e,message:e?"Permission granted successfully":"Permission denied or failed",data:{hasPermission:e}}}catch(e){return{test:"Permission Request",success:!1,message:"Error: ".concat(e)}}}async testNotificationRegistration(){try{console.log("\uD83D\uDCDD Testing notification registration...");let e=await j.A.registerForNotifications();return{test:"Notification Registration",success:!!e,message:e?"Token generated: ".concat(e.substring(0,50),"..."):"Failed to generate token",data:{token:e?e.substring(0,50)+"...":null}}}catch(e){return{test:"Notification Registration",success:!1,message:"Error: ".concat(e)}}}async testShowNotification(){try{console.log("\uD83D\uDCF1 Testing show notification...");let e=await j.A.showTestNotification();return{test:"Show Test Notification",success:e,message:e?"Test notification shown successfully":"Failed to show test notification",data:{success:e}}}catch(e){return{test:"Show Test Notification",success:!1,message:"Error: ".concat(e)}}}getResults(){return this.results}clearResults(){this.results=[]}constructor(){this.results=[]}}let S=new y,v=()=>{let[e,s]=(0,n.useState)(null),[t,l]=(0,n.useState)([]),[d,u]=(0,n.useState)([]),[x,p]=(0,n.useState)(!1);(0,n.useEffect)(()=>{N()},[]);let N=async()=>{try{await j.A.initialize();let e=j.A.getDeviceInfo();s(e),b("Device detected: ".concat(JSON.stringify(e,null,2)))}catch(e){b("Error initializing: ".concat(e))}},b=e=>{let s=new Date().toLocaleTimeString();l(t=>["[".concat(s,"] ").concat(e),...t.slice(0,19)])},y=async()=>{try{b("Testing notification...");let e=await j.A.showTestNotification();b("Test notification result: ".concat(e?"Success":"Failed"))}catch(e){b("Test notification error: ".concat(e))}},v=async()=>{try{b("Showing custom notification...");let e=await j.A.showLocalNotification({title:"Custom Notification",body:"This is a custom notification with data",tag:"custom-test",data:{customData:"test-value"},url:"/notifications"});b("Custom notification result: ".concat(e?"Success":"Failed"))}catch(e){b("Custom notification error: ".concat(e))}},w=async()=>{p(!0),b("\uD83E\uDDEA Starting comprehensive notification tests...");try{let e=await S.runAllTests();u(e),b("✅ Tests completed: ".concat(e.filter(e=>e.success).length,"/").concat(e.length," passed")),e.forEach(e=>{b("".concat(e.success?"✅":"❌"," ").concat(e.test,": ").concat(e.message))})}catch(e){b("❌ Test error: ".concat(e))}finally{p(!1)}},C=async()=>{b("\uD83D\uDD14 Testing permission request...");try{let e=await S.testPermissionRequest();b("".concat(e.success?"✅":"❌"," ").concat(e.test,": ").concat(e.message))}catch(e){b("❌ Permission test error: ".concat(e))}},k=async()=>{b("\uD83D\uDCDD Testing notification registration...");try{let e=await S.testNotificationRegistration();b("".concat(e.success?"✅":"❌"," ").concat(e.test,": ").concat(e.message))}catch(e){b("❌ Registration test error: ".concat(e))}};return(0,i.jsx)(a.A,{className:"py-4",children:(0,i.jsx)(c.A,{children:(0,i.jsxs)(r.A,{md:8,className:"mx-auto",children:[(0,i.jsxs)("h2",{className:"text-center mb-4",children:[e?e.isIOS?"\uD83C\uDF4E":e.isAndroid?"\uD83E\uDD16":"\uD83D\uDCBB":"\uD83D\uDCF1"," Notification Test Page"]}),e&&(0,i.jsxs)(o.A,{className:"mb-4",children:[(0,i.jsx)(o.A.Header,{children:(0,i.jsx)("h5",{className:"mb-0",children:"Device Information"})}),(0,i.jsx)(o.A.Body,{children:(0,i.jsxs)(c.A,{children:[(0,i.jsxs)(r.A,{sm:6,children:[(0,i.jsx)("strong",{children:"Platform:"})," ",e.isIOS?"iOS":e.isAndroid?"Android":"Desktop"]}),(0,i.jsxs)(r.A,{sm:6,children:[(0,i.jsx)("strong",{children:"Browser:"})," ",e.browser]}),(0,i.jsxs)(r.A,{sm:6,children:[(0,i.jsx)("strong",{children:"PWA Mode:"})," ",e.isIOSPWA?"Yes":"No"]}),(0,i.jsxs)(r.A,{sm:6,children:[(0,i.jsx)("strong",{children:"Web Push Support:"})," ",e.supportsWebPush?"Yes":"No"]}),(0,i.jsxs)(r.A,{sm:12,className:"mt-2",children:[(0,i.jsx)("strong",{children:"Recommended Method:"})," ",(0,i.jsx)(h,{bg:(()=>{if(!e)return"secondary";switch(e.recommendedMethod){case"fcm":return"success";case"ios-web-push":return"info";case"local-only":return"warning";case"unsupported":return"danger";default:return"secondary"}})(),children:e.recommendedMethod.replace("-"," ").toUpperCase()})]})]})})]}),(0,i.jsx)("div",{className:"mb-4",children:(0,i.jsx)(A,{onTokenGenerated:e=>b("Token generated: ".concat(e.substring(0,50),"...")),showTestButton:!0,autoInitialize:!1})}),(0,i.jsxs)(o.A,{className:"mb-4",children:[(0,i.jsx)(o.A.Header,{children:(0,i.jsx)("h5",{className:"mb-0",children:"Test Controls"})}),(0,i.jsxs)(o.A.Body,{children:[(0,i.jsxs)("div",{className:"d-flex flex-wrap gap-2 mb-3",children:[(0,i.jsx)(m.A,{variant:"primary",onClick:y,children:"Test Notification"}),(0,i.jsx)(m.A,{variant:"secondary",onClick:v,children:"Custom Notification"}),(0,i.jsx)(m.A,{variant:"info",onClick:()=>{let e=j.A.getPermissionStatus(),s=j.A.getStatusMessage();b("Permission status: ".concat(e)),b("Status message: ".concat(s))},children:"Check Permission"}),(0,i.jsx)(m.A,{variant:"warning",onClick:()=>{let e=j.A.getSetupInstructions();b("Setup instructions:"),e.forEach((e,s)=>{b("  ".concat(s+1,". ").concat(e))})},children:"Get Instructions"})]}),(0,i.jsxs)("div",{className:"d-flex flex-wrap gap-2 mb-3",children:[(0,i.jsx)(m.A,{variant:"success",onClick:w,disabled:x,children:x?"Running Tests...":"Run All Tests"}),(0,i.jsx)(m.A,{variant:"outline-primary",onClick:C,children:"Test Permission"}),(0,i.jsx)(m.A,{variant:"outline-secondary",onClick:k,children:"Test Registration"}),(0,i.jsx)(m.A,{variant:"outline-danger",onClick:()=>{l([]),u([])},children:"Clear Logs"})]})]})]}),(null==e?void 0:e.isIOS)&&(0,i.jsxs)(f.A,{variant:"info",className:"mb-4",children:[(0,i.jsxs)("h6",{children:[(0,i.jsx)("i",{className:"bi bi-apple me-2"}),"iOS Specific Notes"]}),(0,i.jsxs)("ul",{className:"mb-0",children:[(0,i.jsx)("li",{children:"Web push notifications require iOS 16.4 or later"}),(0,i.jsx)("li",{children:"Must use Safari browser (not Chrome or others)"}),(0,i.jsx)("li",{children:"For PWA: Add to home screen and open from there"}),(0,i.jsx)("li",{children:"If notifications don't work, check Safari Settings → Websites → Notifications"}),e.isIOSPWA&&(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"PWA Mode Detected:"})," You're running in PWA mode - great for notifications!"]})]})]}),d.length>0&&(0,i.jsxs)(o.A,{className:"mb-4",children:[(0,i.jsx)(o.A.Header,{children:(0,i.jsx)("h5",{className:"mb-0",children:"Test Results"})}),(0,i.jsx)(o.A.Body,{children:(0,i.jsxs)(g,{striped:!0,bordered:!0,hover:!0,size:"sm",children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{children:"Test"}),(0,i.jsx)("th",{children:"Status"}),(0,i.jsx)("th",{children:"Message"})]})}),(0,i.jsx)("tbody",{children:d.map((e,s)=>(0,i.jsxs)("tr",{children:[(0,i.jsx)("td",{children:e.test}),(0,i.jsx)("td",{children:(0,i.jsx)(h,{bg:e.success?"success":"danger",children:e.success?"✅ Pass":"❌ Fail"})}),(0,i.jsx)("td",{className:"small",children:e.message})]},s))})]})})]}),(0,i.jsxs)(o.A,{children:[(0,i.jsxs)(o.A.Header,{className:"d-flex justify-content-between align-items-center",children:[(0,i.jsx)("h5",{className:"mb-0",children:"Debug Logs"}),(0,i.jsxs)(h,{bg:"secondary",children:[t.length," entries"]})]}),(0,i.jsx)(o.A.Body,{children:0===t.length?(0,i.jsx)("p",{className:"text-muted mb-0",children:"No logs yet. Try testing some notifications!"}):(0,i.jsx)("div",{style:{maxHeight:"300px",overflowY:"auto"},children:t.map((e,s)=>(0,i.jsx)("div",{className:"mb-1",children:(0,i.jsx)("code",{className:"small text-break",children:e})},s))})})]}),(0,i.jsxs)(o.A,{className:"mt-4",children:[(0,i.jsx)(o.A.Header,{children:(0,i.jsx)("h5",{className:"mb-0",children:"How to Test"})}),(0,i.jsxs)(o.A.Body,{children:[(0,i.jsxs)("ol",{children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Enable Notifications:"}),' Click "Enable Notifications" above']}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Test Basic:"}),' Click "Test Notification" to see if it works']}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Test Custom:"}),' Click "Custom Notification" to test with custom data']}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Check Status:"}),' Use "Check Permission" to see current state']}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Debug:"})," Watch the logs for detailed information"]})]}),(0,i.jsxs)(f.A,{variant:"warning",className:"mt-3",children:[(0,i.jsx)("strong",{children:"Note:"})," If you're testing on iOS and notifications don't work:",(0,i.jsxs)("ul",{className:"mb-0 mt-2",children:[(0,i.jsx)("li",{children:"Make sure you're using Safari (not Chrome)"}),(0,i.jsx)("li",{children:"Check your iOS version (needs 16.4+)"}),(0,i.jsx)("li",{children:"Try adding the app to home screen first"}),(0,i.jsx)("li",{children:"Check Safari Settings → Websites → Notifications"})]})]})]})]})]})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[4816,7666,4746,6222,8441,1684,7358],()=>s(86715)),_N_E=e.O()}]);