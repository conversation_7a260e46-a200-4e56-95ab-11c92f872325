(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3008],{21217:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var i=s(95155),l=s(9e4),a=s(38808),r=s(12115);let n=e=>{let{handleShowSetting:t,showSetting:s}=e,{theme:r,handleDarkModeToggle:n}=(0,l.D)(),{viewMode:c,handleRTLToggling:o}=(0,a.L)();return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{id:"setting-popup-overlay",className:s?"active":"",onClick:t}),(0,i.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(s?"active":""),id:"settingCard",children:(0,i.jsx)("div",{className:"card-body",children:(0,i.jsxs)("div",{className:"container",children:[(0,i.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,i.jsx)("p",{className:"mb-0",children:"Settings"}),(0,i.jsx)("div",{onClick:t,className:"btn-close",id:"settingCardClose"})]}),(0,i.jsx)("div",{className:"single-setting-panel",children:(0,i.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,i.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,i.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,i.jsx)("div",{className:"single-setting-panel",children:(0,i.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,i.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,i.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,i.jsx)("div",{className:"single-setting-panel",children:(0,i.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,i.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===r,onChange:n}),(0,i.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===r?"Light":"Dark"," mode"]})]})}),(0,i.jsx)("div",{className:"single-setting-panel",children:(0,i.jsxs)("div",{className:"form-check form-switch",children:[(0,i.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===c,onChange:o}),(0,i.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===c?"LTR":"RTL"," mode"]})]})})]})})})]})};var c=s(6874),o=s.n(c);let d=e=>{let{links:t,title:s}=e,[l,a]=(0,r.useState)(!1),c=()=>a(!l);return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"header-area",id:"headerArea",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,i.jsx)("div",{className:"back-button",children:(0,i.jsx)(o(),{href:"/".concat(t),children:(0,i.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,i.jsx)("div",{className:"page-heading",children:(0,i.jsx)("h6",{className:"mb-0",children:s})}),(0,i.jsx)("div",{className:"setting-wrapper",onClick:c,children:(0,i.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,i.jsx)("i",{className:"bi bi-gear"}),(0,i.jsx)("span",{})]})})]})})}),(0,i.jsx)(n,{showSetting:l,handleShowSetting:c})]})}},38808:(e,t,s)=>{"use strict";s.d(t,{L:()=>l});var i=s(12115);let l=()=>{let[e,t]=(0,i.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,i.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let s=()=>{t(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:s,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}s()}}}},38983:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var i=s(95155),l=s(6874),a=s.n(l);s(12115);let r=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],n=()=>(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,i.jsx)("div",{className:"container px-0",children:(0,i.jsx)("div",{className:"footer-nav position-relative",children:(0,i.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:r.map((e,t)=>(0,i.jsx)("li",{children:(0,i.jsxs)(a(),{href:"/".concat(e.link),children:[(0,i.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,i.jsx)("span",{children:e.title})]})},t))})})})})})},40844:function(e,t,s){e.exports=function(e,t,s,i){"use strict";let l=".bs.tab",a=`hide${l}`,r=`hidden${l}`,n=`show${l}`,c=`shown${l}`,o=`click${l}`,d=`keydown${l}`,h=`load${l}`,m="ArrowRight",u="ArrowDown",g="Home",b="active",f="fade",p="show",v=".dropdown-toggle",x=`:not(${v})`,k=`.nav-link${x}, .list-group-item${x}, [role="tab"]${x}`,N='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',_=`${k}, ${N}`,j=`.${b}[data-bs-toggle="tab"], .${b}[data-bs-toggle="pill"], .${b}[data-bs-toggle="list"]`;class y extends e{constructor(e){if(super(e),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),!this._parent)return;this._setInitialAttributes(this._parent,this._getChildren()),t.on(this._element,d,e=>this._keydown(e))}static get NAME(){return"tab"}show(){let e=this._element;if(this._elemIsActive(e))return;let s=this._getActiveElem(),i=s?t.trigger(s,a,{relatedTarget:e}):null;t.trigger(e,n,{relatedTarget:s}).defaultPrevented||i&&i.defaultPrevented||(this._deactivate(s,e),this._activate(e,s))}_activate(e,i){e&&(e.classList.add(b),this._activate(s.getElementFromSelector(e)),this._queueCallback(()=>{if("tab"!==e.getAttribute("role"))return void e.classList.add(p);e.removeAttribute("tabindex"),e.setAttribute("aria-selected",!0),this._toggleDropDown(e,!0),t.trigger(e,c,{relatedTarget:i})},e,e.classList.contains(f)))}_deactivate(e,i){e&&(e.classList.remove(b),e.blur(),this._deactivate(s.getElementFromSelector(e)),this._queueCallback(()=>{if("tab"!==e.getAttribute("role"))return void e.classList.remove(p);e.setAttribute("aria-selected",!1),e.setAttribute("tabindex","-1"),this._toggleDropDown(e,!1),t.trigger(e,r,{relatedTarget:i})},e,e.classList.contains(f)))}_keydown(e){let t;if(!["ArrowLeft",m,"ArrowUp",u,g,"End"].includes(e.key))return;e.stopPropagation(),e.preventDefault();let s=this._getChildren().filter(e=>!i.isDisabled(e));if([g,"End"].includes(e.key))t=s[e.key===g?0:s.length-1];else{let l=[m,u].includes(e.key);t=i.getNextActiveElement(s,e.target,l,!0)}t&&(t.focus({preventScroll:!0}),y.getOrCreateInstance(t).show())}_getChildren(){return s.find(_,this._parent)}_getActiveElem(){return this._getChildren().find(e=>this._elemIsActive(e))||null}_setInitialAttributes(e,t){for(let s of(this._setAttributeIfNotExists(e,"role","tablist"),t))this._setInitialAttributesOnChild(s)}_setInitialAttributesOnChild(e){e=this._getInnerElement(e);let t=this._elemIsActive(e),s=this._getOuterElement(e);e.setAttribute("aria-selected",t),s!==e&&this._setAttributeIfNotExists(s,"role","presentation"),t||e.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(e,"role","tab"),this._setInitialAttributesOnTargetPanel(e)}_setInitialAttributesOnTargetPanel(e){let t=s.getElementFromSelector(e);t&&(this._setAttributeIfNotExists(t,"role","tabpanel"),e.id&&this._setAttributeIfNotExists(t,"aria-labelledby",`${e.id}`))}_toggleDropDown(e,t){let i=this._getOuterElement(e);if(!i.classList.contains("dropdown"))return;let l=(e,l)=>{let a=s.findOne(e,i);a&&a.classList.toggle(l,t)};l(v,b),l(".dropdown-menu",p),i.setAttribute("aria-expanded",t)}_setAttributeIfNotExists(e,t,s){e.hasAttribute(t)||e.setAttribute(t,s)}_elemIsActive(e){return e.classList.contains(b)}_getInnerElement(e){return e.matches(_)?e:s.findOne(_,e)}_getOuterElement(e){return e.closest(".nav-item, .list-group-item")||e}static jQueryInterface(e){return this.each(function(){let t=y.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e]()}})}}return t.on(document,o,N,function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),i.isDisabled(this)||y.getOrCreateInstance(this).show()}),t.on(window,h,()=>{for(let e of s.find(j))y.getOrCreateInstance(e)}),i.defineJQueryPlugin(y),y}(s(19962),s(65613),s(27346),s(60250))},9e4:(e,t,s)=>{"use strict";s.d(t,{D:()=>l});var i=s(12115);let l=()=>{let[e,t]=(0,i.useState)("light"),[s,l]=(0,i.useState)(!1);(0,i.useEffect)(()=>{t(localStorage.getItem("theme")||"light"),l(!0)},[]),(0,i.useEffect)(()=>{s&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,s]);let a=(0,i.useCallback)(()=>{t(e=>"dark"===e?"light":"dark")},[]),r=(0,i.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}a()},[a]);return{theme:e,toggleTheme:a,handleDarkModeToggle:r}}}}]);