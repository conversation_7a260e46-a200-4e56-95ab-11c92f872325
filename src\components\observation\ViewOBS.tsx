import React, { useEffect, useState } from 'react';
import ImageComponent from '@/services/FileDownlodS3';
import moment from 'moment';
import API from '@/services/API';
import { USERS_URL } from '@/constant';

/** Types **/

interface User {
    id: string | number;
    firstName: string;
}

type ActionType = 'take_action' | 'reperform_action' | 'verify_action' | 'review';

interface ActionItem {
    id?: string | number;
    actionType?: ActionType;
    actionToBeTaken?: string;
    dueDate?: string;
    status?: string;
    assignedToId?: (string | number)[];
    actionTaken?: string;
    uploads?: string[];
    created?: string;
    comments?: string;
}

interface ReportData {
    isQR?: boolean;
    describeActionTaken?: string;
    describeAtRiskObservation?: string;
    describeSafekObservation?: string;
    description?: string;
    observationCategory?: string;
    observationType?: string;
    observationActOrCondition?: string;
    uploads?: string[];
    created?: string;
    qrRole?: string;
    hazardCategory?: { name?: string };
    hazardDescription?: { name?: string };
    reporterId?: string | number;
    reporter?: { firstName: string };
    locationOne?: { name?: string };
    locationTwo?: { name?: string };
    locationThree?: { name?: string };
    locationFour?: { name?: string };
    locationFive?: { name?: string };
    locationSix?: { name?: string };
    rectifiedOnSpot?: boolean;
    actionTaken?: string;
    evidence?: string[];
    maskId?: string;
    observationActions?: ActionItem[];
    reviewerId?: string | number;
}

interface ViewOBSProps {
    reportData: ReportData;
}

/** Small helper components **/

/**
 * Renders a list of images with an optional title.
 */
const ImageList: React.FC<{ images?: string[]; title?: string; showName?: boolean }> = ({
    images,
    title,
    showName = false,
}) => {
    if (!images || images.length === 0) return null;

    return (
        <div className="image-gallery mt-3">
            {title && <h6 className="obs-title">{title}</h6>}
            <div className="row">
                {images.map((file: string, idx: number) => (
                    <div key={idx} className="col-md-3 col-sm-4 col-6 p-2">
                        <div className="image-box shadow-sm p-2 rounded d-flex align-items-center justify-content-center">
                            <ImageComponent fileName={file} size={100} name={showName} />
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

/** Action Subcomponents **/

interface ActionProps {
    action: ActionItem;
    actionIndex?: number;
    getName?: (id?: string | number) => string;
    getMultipleNames: (ids?: (string | number)[]) => string;
    getClosedDate?: (action: ActionItem) => string;
    maskId?: string;
}

/**
 * Handles actionType = 'take_action' or 'reperform_action'
 */
const TakeOrReperformAction: React.FC<ActionProps> = ({
    action,
    actionIndex = 1,
    getMultipleNames,
    getClosedDate,
    maskId,
}) => {
    const isReassigned = action.actionType === 'reperform_action';
    const showAssignee =
        (action.status === 'Initiated') && action.assignedToId && action.assignedToId.length > 0;

    return (
        <div className="obs-section shadow p-3 mb-3">
            <div className="row mb-3">
                <div className="col-md-12">
                    {isReassigned ? (
                        <>
                            <h6 className="obs-title">
                                Action Verifier Comments &amp; Reassigned Action {maskId} - A{actionIndex}
                            </h6>
                            <p className="obs-content">{action.comments}</p>
                        </>
                    ) : (
                        <>
                            <h6 className="obs-title">
                                Assigned Action {maskId} - A{actionIndex}
                            </h6>
                            <p className="obs-content">{action.actionToBeTaken}</p>
                        </>
                    )}
                </div>
                {action.actionType !== 'reperform_action' &&
                    <div className="col-md-12">
                        <h6 className="obs-title">Due Date</h6>
                        <p className="obs-content">
                            {action?.dueDate && moment(action.dueDate, [
                                'DD-MM-YYYY',
                                'DD/MM/YYYY',
                                moment.ISO_8601,
                            ]).isValid()
                                ? moment(action.dueDate, [
                                    'DD-MM-YYYY',
                                    'DD/MM/YYYY',
                                    moment.ISO_8601,
                                ]).format('Do MMM YYYY')
                                : '-'}
                        </p>
                    </div>
                }
                {showAssignee && (
                    <div className="col-md-6">
                        <h6 className="obs-title">Action Assignee{action.assignedToId && action.assignedToId.length > 1 ? 's' : ''}</h6>
                        <p className="obs-content">{getMultipleNames(action.assignedToId)}</p>
                    </div>
                )}
            </div>

            {action.status === 'Completed' && (
                <>
                    <div className="row">
                        <div className="col-md-12">
                            <h6 className="obs-title">Action Taken</h6>
                            <p className="obs-content">{action.actionTaken}</p>
                        </div>
                    </div>

                    <div className="row">
                        <div className="col-md-6">
                            <h6 className="obs-title">Action Taken By</h6>
                            <p className="obs-content">{getMultipleNames(action.assignedToId)}</p>
                        </div>
                        <div className="col-md-6">
                            <h6 className="obs-title">Date</h6>
                            <p className="obs-content">{getClosedDate?.(action) || '-'}</p>
                        </div>
                    </div>

                    <ImageList images={action.uploads} title="Evidence" showName />
                </>
            )}
        </div>
    );
};

/**
 * Handles actionType = 'verify_action'
 */
const VerifyAction: React.FC<ActionProps> = ({ action, actionIndex = 1, getMultipleNames }) => {
    if (!action.assignedToId || action.assignedToId.length === 0) return null;

    return (
        <div className="obs-section shadow p-3 mt-3 mb-3">
            {action.status === 'Initiated' && (
                <div className="row mb-3">
                    <div className="col-md-12">
                        <h6 className="obs-title">Action Verifier{action.assignedToId && action.assignedToId.length > 1 ? 's' : ''} - A{actionIndex}</h6>
                        <p className="obs-content">{getMultipleNames(action.assignedToId)}</p>
                    </div>
                </div>
            )}

            {action.status === 'Completed' && (
                <div className="row mb-3">
                    <div className="col-md-12">
                        <h6 className="obs-title">Action Verified By</h6>
                        <p className="obs-content">{getMultipleNames(action.assignedToId)}</p>
                    </div>
                    <div className="col-md-12">
                        <h6 className="obs-title">Date</h6>
                        <p className="obs-content">
                            {moment(action.created).format('Do MMM YYYY, hh:mm:ss a')}
                        </p>
                    </div>
                    {action.comments &&
                        <div className="col-md-12">
                            <h6 className="obs-title">Comments</h6>
                            <p className="obs-content">{action.comments}</p>
                        </div>
                    }
                </div>
            )}
        </div>
    );
};

/**
 * Handles actionType = 'review'
 */
const ReviewAction: React.FC<ActionProps> = ({ action, getMultipleNames }) => {
    if (!action.assignedToId || action.assignedToId.length === 0) return null;

    const isInitiated = action.status === 'Initiated';
    const isCompleted = action.status === 'Completed';

    return (
        <div className="obs-section shadow p-3 mb-3">
            <div className="row mb-3">
                <div className="col-md-6">
                    <p className="obs-title">
                        {isInitiated ? 'Action Reviewer' : isCompleted ? 'Action Reviewed By' : 'Reviewer'}{action.assignedToId && action.assignedToId.length > 1 ? 's' : ''}
                    </p>
                    <p className="obs-content">{getMultipleNames(action.assignedToId)}</p>
                </div>

                <div className="col-md-6">
                    <p className="obs-title">Date</p>
                    <p className="obs-content">
                        {moment(action.created).format('Do MMM YYYY, hh:mm:ss a')}
                    </p>
                </div>
            </div>
        </div>
    );
};

/** Main ViewOBS Component **/

const ViewOBS: React.FC<ViewOBSProps> = ({ reportData }) => {
    const [users, setUsers] = useState<User[]>([]);

    useEffect(() => {
        getAllUsers();
    }, []);

    const getAllUsers = async () => {
        try {
            const response = await API.get(USERS_URL);
            setUsers(response.data);
        } catch (error) {
            console.error('Error fetching users:', error);
        }
    };

    /**
     * Safely returns the first name of the user with the given ID.
     */
    function getName(id?: string | number): string {
        if (!id) return 'No Assignee';
        const user = users.find((u) => u.id === id);
        return user ? user.firstName : 'Unknown User';
    }

    /**
     * Returns formatted names for multiple assignees
     */
    function getMultipleNames(ids?: (string | number)[]): string {
        if (!ids || ids.length === 0) return 'No Assignee';
        if (ids.length === 1) return getName(ids[0]);

        const names = ids.map(id => getName(id)).filter(name => name !== 'Unknown User');
        return names.length > 0 ? names.join(' & ') : 'Unknown Users';
    }

    /**
     * Returns a formatted date/time string from an action's createdDate.
     */
    function getClosedDate(action: ActionItem): string {
        if (!action?.created) return '-';
        return moment(action.created).format('Do MMM YYYY, hh:mm:ss a');
    }

    // We will track how many times we display a "take_action" or "reperform_action"
    // so it matches the "A1", "A2" labeling logic.
    let actionIndexCounter = 0;

    /**
     * Renders a single action based on its type. 
     */
    const renderAction = (action: ActionItem) => {
        if (action.actionType === 'take_action' || action.actionType === 'reperform_action') {
            actionIndexCounter++;
            return (
                <TakeOrReperformAction
                    key={action.id}
                    action={action}
                    actionIndex={actionIndexCounter}
                    getName={getName}
                    getMultipleNames={getMultipleNames}
                    getClosedDate={getClosedDate}
                    maskId={reportData.maskId}
                />
            );
        }
        if (action.actionType === 'verify_action') {
            return (
                <VerifyAction
                    key={action.id}
                    action={action}
                    actionIndex={actionIndexCounter}
                    getName={getName}
                    getMultipleNames={getMultipleNames}
                    getClosedDate={getClosedDate}
                    maskId={reportData.maskId}
                />
            );
        }
        if (action.actionType === 'review') {
            return (
                <ReviewAction
                    key={action.id}
                    action={action}
                    actionIndex={actionIndexCounter}
                    getName={getName}
                    getMultipleNames={getMultipleNames}
                    getClosedDate={getClosedDate}
                    maskId={reportData.maskId}
                />
            );
        }

        // Fallback if an unknown actionType
        return null;
    };

    // Render
    return (
        <div className="observation-report card shadow-sm p-3 rounded">
            {/* Description */}
            <div className="description-section">
                <h5 className="section-title">Description</h5>
                <p className="obs-dec text-muted">
                    {reportData.isQR
                        ? reportData.describeActionTaken ||
                        reportData.describeAtRiskObservation ||
                        reportData.describeSafekObservation ||
                        'No description available'
                        : reportData.description}
                </p>
            </div>

            {/* Basic info row */}
            <div className="row">
                <div className="col-md-6">
                    <h6 className="obs-title">Category</h6>
                    <p className="obs-content">{reportData.observationCategory || 'N/A'}</p>
                </div>
                <div className="col-md-6">
                    <h6 className="obs-title">Type</h6>
                    <p className="obs-content">
                        {reportData.observationType || ''} - {reportData.observationActOrCondition}
                    </p>
                </div>
            </div>

            {/* Initial Uploads */}
            <ImageList images={reportData.uploads} title="Images" />

            {/* Reported Date */}
            <div className="row">
                <div className="col-md-6">
                    <h6 className="obs-title">Reported Date</h6>
                    <p className="obs-content">
                        {reportData.created
                            ? moment(reportData.created).format('Do MMM YYYY, hh:mm:ss a')
                            : 'N/A'}
                    </p>
                </div>
            </div>

            {/* Reporter / hazard info */}
            <div className="row">
                {reportData.isQR ? (
                    <>
                        <div className="col-md-6">
                            <h6 className="obs-title">Role</h6>
                            <p className="obs-content">{reportData.qrRole || 'N/A'}</p>
                        </div>
                        <div className="col-md-6">
                            <h6 className="obs-title">Hazard Category</h6>
                            <p className="obs-content">{reportData.hazardCategory?.name || 'N/A'}</p>
                        </div>
                        <div className="col-md-6">
                            <h6 className="obs-title">Hazard Description</h6>
                            <p className="obs-content">{reportData.hazardDescription?.name || 'N/A'}</p>
                        </div>
                    </>
                ) : (
                    <div className="col-md-6">
                        <h6 className="obs-title">Reporter</h6>
                        <p className="obs-content">{reportData.reporter?.firstName || 'N/A'}</p>
                    </div>
                )}
            </div>

            {/* Location */}
            <div className="row">
                <div className="col-md-6">
                    <h6 className="obs-title">Location</h6>
                    <p className="obs-content">
                        {[
                            reportData.locationOne,
                            reportData.locationTwo,
                            reportData.locationThree,
                            reportData.locationFour,
                            reportData.locationFive,
                            reportData.locationSix,
                        ]
                            .filter((loc) => loc?.name)
                            .map((loc) => loc?.name ?? 'N/A')
                            .join(' > ') || 'N/A'}
                    </p>
                </div>
            </div>

            {/* If observationType is "Unsafe" then show the observation actions */}
            {reportData.observationType === 'Unsafe' && (
                <div className="obs-section">
                    {/* If not rectified on spot, show the actions list */}
                    {!reportData.rectifiedOnSpot && reportData.observationActions?.length ? (
                        <>
                            {reportData.observationActions.map((action) => renderAction(action))}
                        </>
                    ) : null}
                </div>
            )}

            {/* Rectified on spot actions */}
            {reportData.rectifiedOnSpot && (
                <div className="obs-section shadow p-3">
                    <div className="col-md-12">
                        <div className="row mb-3">
                            <div className="col-md-12">
                                <p className="obs-title">Action Taken</p>
                                <p className="obs-content">{reportData.actionTaken || 'N/A'}</p>
                            </div>
                        </div>
                        <ImageList images={reportData.evidence} title="Evidence" showName />
                    </div>
                </div>
            )}
        </div>
    );
};

export default ViewOBS;
