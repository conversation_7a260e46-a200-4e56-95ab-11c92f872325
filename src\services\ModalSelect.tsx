// ModalSelect.tsx

import React, { useState } from "react";
import { Modal, Button, Form } from "react-bootstrap";

// Define the shape of each option
export interface PickerOption {
  value: string;
  label: string;
}

// Props for the generic modal picker
interface ModalSelectProps {
  title: string;                     // e.g., "Action Owner"
  options: PickerOption[];           // List of { value, label }
  selectedValue?: string;
  disabled: boolean;         // Currently selected value
  onChange?: (newValue: string) => void;  // Callback when user selects an option (optional for multiSelect)
  placeholder?: string;              // e.g., "Select Action Owner"
  clearable?: boolean;               // Whether you allow clearing the selection
  multiSelect?: boolean;             // Whether to allow multiple selections
  selectedValues?: string[];         // For multi-select mode
  onMultiChange?: (newValues: string[]) => void; // Callback for multi-select
  maxSelections?: number;            // Maximum number of selections allowed
}

const ModalSelect: React.FC<ModalSelectProps> = ({
  title,
  options,
  selectedValue,
  disabled,
  onChange,
  placeholder = "Select Option",
  clearable = false,
  multiSelect = false,
  selectedValues = [],
  onMultiChange,
  maxSelections = 2,
}) => {
  const [show, setShow] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>("");

  const filteredOptions = options.filter((opt) =>
    opt.label.toLowerCase().includes(searchText.toLowerCase())
  );

  // Find label for the currently selected option (for the button)
  const selectedLabel = multiSelect
    ? (selectedValues.length > 0
        ? selectedValues.map(val => options.find(opt => opt.value === val)?.label).join(", ")
        : placeholder)
    : (options.find((opt) => opt.value === selectedValue)?.label || placeholder);

  const handleOpen = () => {
    setShow(true);
    setSearchText(""); // reset search text each time
  };

  const handleClose = () => {
    setShow(false);
  };

  const handleSelect = (value: string) => {
    if (multiSelect && onMultiChange) {
      const currentValues = selectedValues || [];
      if (currentValues.includes(value)) {
        // Remove if already selected
        const newValues = currentValues.filter(v => v !== value);
        onMultiChange(newValues);
      } else {
        // Add if not selected and under limit
        if (currentValues.length < maxSelections) {
          const newValues = [...currentValues, value];
          onMultiChange(newValues);
        }
      }
      // Don't close modal in multi-select mode
    } else {
      onChange?.(value);
      setShow(false);
    }
  };

  const clearSelection = () => {
    if (multiSelect && onMultiChange) {
      onMultiChange([]);
    } else {
      onChange?.("");
    }
  };

  return (
    <div className="mb-3">
      <Form.Label>{title}</Form.Label>
      <div className="d-flex gap-2">
        {/* Main Button - shows current selection, opens modal */}
        <Button
          disabled={disabled}
          variant="outline-secondary"
          className="flex-grow-1 text-start"
          onClick={handleOpen}
        >
          {selectedLabel}
        </Button>

        {clearable && (multiSelect ? selectedValues.length > 0 : selectedValue) && (
          <Button variant="outline-danger" onClick={clearSelection}>
            Clear
          </Button>
        )}
      </div>

      <Modal show={show} onHide={handleClose} centered>
        <Modal.Header closeButton>
          <Modal.Title>{title}</Modal.Title>
        </Modal.Header>

        <Modal.Body>
          {/* Search Input */}
          <Form.Control
            type="text"
            placeholder="Search..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            className="mb-3"
          />

          {/* Option List */}
          {filteredOptions.length > 0 ? (
            <div style={{ maxHeight: "300px", overflowY: "auto" }}>
              {filteredOptions.map((opt) => {
                const isSelected = multiSelect
                  ? selectedValues.includes(opt.value)
                  : opt.value === selectedValue;
                const isDisabled = multiSelect && !isSelected && selectedValues.length >= maxSelections;

                return (
                  <button
                    key={opt.value}
                    className={`list-group-item list-group-item-action mb-1 ${isSelected ? "active" : ""} ${isDisabled ? "disabled" : ""}`}
                    onClick={() => !isDisabled && handleSelect(opt.value)}
                    disabled={isDisabled}
                  >
                    {multiSelect && (
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => {}}
                        className="me-2"
                        disabled={isDisabled}
                      />
                    )}
                    {opt.label}
                  </button>
                );
              })}
            </div>
          ) : (
            <p className="text-muted">No options found.</p>
          )}

          {multiSelect && (
            <div className="mt-2 text-muted small">
              Selected: {selectedValues.length}/{maxSelections}
            </div>
          )}
        </Modal.Body>

        <Modal.Footer>
          {multiSelect && (
            <Button variant="primary" onClick={handleClose} className="me-auto">
              Done
            </Button>
          )}
          <Button variant="secondary" onClick={handleClose}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default ModalSelect;
