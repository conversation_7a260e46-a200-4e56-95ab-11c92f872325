import React from 'react';
import { NotificationItem } from './NotificationItem';

// Simple stub types
interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'error' | 'warning' | 'success' | 'info' | 'urgent';
  priority: 'urgent' | 'high' | 'medium' | 'low';
  timestamp: Date;
  isRead: boolean;
  category?: string;
  actionRequired?: boolean;
}

export function NotificationList() {
  // Simple stub data
  const notifications: Notification[] = [];
  const unreadCount = 0;

  const handleMarkAllAsRead = () => {
    console.log('Mark all as read clicked');
  };

  const renderHeader = () => (
    <div style={{
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: '16px 20px',
      borderBottom: '1px solid #f0f0f0'
    }}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <h3 style={{ margin: 0, fontSize: '18px', fontWeight: '600' }}>
          Notifications
        </h3>
        {unreadCount > 0 && (
          <div style={{
            backgroundColor: '#007AFF',
            color: 'white',
            borderRadius: '12px',
            padding: '2px 8px',
            fontSize: '12px',
            marginLeft: '8px'
          }}>
            {unreadCount}
          </div>
        )}
      </div>

      {unreadCount > 0 && (
        <button
          onClick={handleMarkAllAsRead}
          style={{
            display: 'flex',
            alignItems: 'center',
            background: 'none',
            border: 'none',
            color: '#007AFF',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          <span style={{ marginRight: '4px' }}>✓</span>
          Mark all read
        </button>
      )}
    </div>
  );

  const renderEmptyState = () => (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '60px 20px',
      textAlign: 'center'
    }}>
      <span style={{ fontSize: '48px', color: '#E5E5E5', marginBottom: '16px' }}>🔔</span>
      <h4 style={{ margin: '0 0 8px 0', color: '#666' }}>
        No notifications
      </h4>
      <p style={{ margin: 0, color: '#999', fontSize: '14px' }}>
        You&apos;re all caught up!
      </p>
    </div>
  );

  return (
    <div style={{ backgroundColor: 'white', minHeight: '100vh' }}>
      {renderHeader()}

      {notifications.length === 0 ? (
        renderEmptyState()
      ) : (
        <div style={{ overflowY: 'auto' }}>
          {notifications.map((notification, index) => (
            <div key={notification.id}>
              <NotificationItem notification={notification} />
              {index < notifications.length - 1 && (
                <div style={{ height: '1px', backgroundColor: '#f0f0f0' }} />
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}


