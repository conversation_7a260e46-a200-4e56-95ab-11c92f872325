(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5856],{8012:function(e,t,s){e.exports=function(e,t,s,i){"use strict";let a=".bs.toast",l=`mouseover${a}`,n=`mouseout${a}`,c=`focusin${a}`,r=`focusout${a}`,o=`hide${a}`,h=`hidden${a}`,d=`show${a}`,m=`shown${a}`,u="hide",g="show",f="showing",p={animation:"boolean",autohide:"boolean",delay:"number"},b={animation:!0,autohide:!0,delay:5e3};class _ extends e{constructor(e,t){super(e,t),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return b}static get DefaultType(){return p}static get NAME(){return"toast"}show(){t.trigger(this._element,d).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(u),i.reflow(this._element),this._element.classList.add(g,f),this._queueCallback(()=>{this._element.classList.remove(f),t.trigger(this._element,m),this._maybeScheduleHide()},this._element,this._config.animation))}hide(){this.isShown()&&!t.trigger(this._element,o).defaultPrevented&&(this._element.classList.add(f),this._queueCallback(()=>{this._element.classList.add(u),this._element.classList.remove(f,g),t.trigger(this._element,h)},this._element,this._config.animation))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(g),super.dispose()}isShown(){return this._element.classList.contains(g)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(e,t){switch(e.type){case"mouseover":case"mouseout":this._hasMouseInteraction=t;break;case"focusin":case"focusout":this._hasKeyboardInteraction=t}if(t)return void this._clearTimeout();let s=e.relatedTarget;this._element===s||this._element.contains(s)||this._maybeScheduleHide()}_setListeners(){t.on(this._element,l,e=>this._onInteraction(e,!0)),t.on(this._element,n,e=>this._onInteraction(e,!1)),t.on(this._element,c,e=>this._onInteraction(e,!0)),t.on(this._element,r,e=>this._onInteraction(e,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each(function(){let t=_.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw TypeError(`No method named "${e}"`);t[e](this)}})}}return s.enableDismissTrigger(_),i.defineJQueryPlugin(_),_}(s(19962),s(65613),s(12281),s(60250))},12281:function(e,t,s){(function(e,t,s,i){"use strict";e.enableDismissTrigger=(e,a="hide")=>{let l=`click.dismiss${e.EVENT_KEY}`,n=e.NAME;t.on(document,l,`[data-bs-dismiss="${n}"]`,function(t){if(["A","AREA"].includes(this.tagName)&&t.preventDefault(),i.isDisabled(this))return;let l=s.getElementFromSelector(this)||this.closest(`.${n}`);e.getOrCreateInstance(l)[a]()})},Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})})(t,s(65613),s(27346),s(60250))},21217:(e,t,s)=>{"use strict";s.d(t,{default:()=>h});var i=s(95155),a=s(9e4),l=s(38808),n=s(12115);let c=e=>{let{handleShowSetting:t,showSetting:s}=e,{theme:n,handleDarkModeToggle:c}=(0,a.D)(),{viewMode:r,handleRTLToggling:o}=(0,l.L)();return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{id:"setting-popup-overlay",className:s?"active":"",onClick:t}),(0,i.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(s?"active":""),id:"settingCard",children:(0,i.jsx)("div",{className:"card-body",children:(0,i.jsxs)("div",{className:"container",children:[(0,i.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,i.jsx)("p",{className:"mb-0",children:"Settings"}),(0,i.jsx)("div",{onClick:t,className:"btn-close",id:"settingCardClose"})]}),(0,i.jsx)("div",{className:"single-setting-panel",children:(0,i.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,i.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,i.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,i.jsx)("div",{className:"single-setting-panel",children:(0,i.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,i.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,i.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,i.jsx)("div",{className:"single-setting-panel",children:(0,i.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,i.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===n,onChange:c}),(0,i.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===n?"Light":"Dark"," mode"]})]})}),(0,i.jsx)("div",{className:"single-setting-panel",children:(0,i.jsxs)("div",{className:"form-check form-switch",children:[(0,i.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===r,onChange:o}),(0,i.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===r?"LTR":"RTL"," mode"]})]})})]})})})]})};var r=s(6874),o=s.n(r);let h=e=>{let{links:t,title:s}=e,[a,l]=(0,n.useState)(!1),r=()=>l(!a);return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"header-area",id:"headerArea",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,i.jsx)("div",{className:"back-button",children:(0,i.jsx)(o(),{href:"/".concat(t),children:(0,i.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,i.jsx)("div",{className:"page-heading",children:(0,i.jsx)("h6",{className:"mb-0",children:s})}),(0,i.jsx)("div",{className:"setting-wrapper",onClick:r,children:(0,i.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,i.jsx)("i",{className:"bi bi-gear"}),(0,i.jsx)("span",{})]})})]})})}),(0,i.jsx)(c,{showSetting:a,handleShowSetting:r})]})}},38808:(e,t,s)=>{"use strict";s.d(t,{L:()=>a});var i=s(12115);let a=()=>{let[e,t]=(0,i.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,i.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let s=()=>{t(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:s,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}s()}}}},38983:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var i=s(95155),a=s(6874),l=s.n(a);s(12115);let n=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],c=()=>(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,i.jsx)("div",{className:"container px-0",children:(0,i.jsx)("div",{className:"footer-nav position-relative",children:(0,i.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:n.map((e,t)=>(0,i.jsx)("li",{children:(0,i.jsxs)(l(),{href:"/".concat(e.link),children:[(0,i.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,i.jsx)("span",{children:e.title})]})},t))})})})})})},9e4:(e,t,s)=>{"use strict";s.d(t,{D:()=>a});var i=s(12115);let a=()=>{let[e,t]=(0,i.useState)("light"),[s,a]=(0,i.useState)(!1);(0,i.useEffect)(()=>{t(localStorage.getItem("theme")||"light"),a(!0)},[]),(0,i.useEffect)(()=>{s&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,s]);let l=(0,i.useCallback)(()=>{t(e=>"dark"===e?"light":"dark")},[]),n=(0,i.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}l()},[l]);return{theme:e,toggleTheme:l,handleDarkModeToggle:n}}}}]);