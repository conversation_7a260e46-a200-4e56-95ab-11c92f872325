
import New from "@/components/observation/New";
import HeaderSeven from "@/layouts/headers/HeaderSeven";
import React, { useState } from "react";
import OfflineAwareLayout from "@/components/offline/OfflineAwareLayout";


export const metadata = {
    title: "<PERSON><PERSON>an <PERSON>s - PWA Mobile Next.js Template",
};

const Observation: React.FC = () => {

    return (
        <>
            <HeaderSeven heading={'Observation'} />
            <OfflineAwareLayout
                pageTitle="Observation"
                requiresOnline={true}
            >
                <div className="page-content-wrapper">
                    <New />
                </div>
            </OfflineAwareLayout>
        </>
    );
};

export default Observation;
