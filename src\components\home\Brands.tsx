"use client";

import React, { useEffect, useState } from "react";
import { SERVICE_DETAILS } from "@/constant";
import API from "@/services/API";
import { serviceActions } from "@/store/services";
import { useDispatch } from "react-redux";
import Link from "next/link";

interface ServiceType {
  id: string;
  name: string;
  description: string;
  maskName: string;
  applicability: string;
  status: boolean;
  url: string;
  created: string;
  updated: string;
  mobileShortName: string;
}

const iconMapping: Record<string, string> = {
  "RA": "bi bi-shield-check",
  "EPTW-GEN": "bi bi-file-lock",
  "IR": "bi bi-exclamation-triangle",
  "OBS": "bi bi-eye",
  "INC": "bi bi-clipboard-check",
  "KNOWLEDGE": "bi bi-book",
  "TBT": "bi bi-chat-dots",
  "OTT": "bi bi-list-task",
  "INCINV": "bi bi-briefcase",
  "INS": "bi bi-clipboard-check",
  "DOC": "bi bi-file-earmark-text",   // Document Management
  "GC": "bi bi-hand-thumbs-up"        // Good Catch
};

const Brands: React.FC = () => {
  const [services, setServices] = useState<ServiceType[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const dispatch = useDispatch();

  // Check if RA service exists in the services array
  const hasRAService = services.some(service => service.maskName === "RA");

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const response = await API.get<ServiceType[]>(SERVICE_DETAILS);
      if (response.status === 200 && Array.isArray(response.data)) {
        setTimeout(() => {
          setServices(response.data);
          dispatch(serviceActions.setService(response.data));
          setLoading(false);
        }, 500); // Smooth transition delay
      }
    } catch (error) {
      console.error("Error fetching services:", error);
      setLoading(false);
    }
  };
  const removeAppsPrefix = (url: string) => {
    // If the URL starts with "/apps/", remove that portion
    // e.g. "/apps/observation" -> "/observation"
    return url.replace(/^\/apps\//, "/");
  };
  return (
    <>
      <div className="pt-3"></div>



      <div className=" container direction-rtl  ">

        <div className="element-heading mt-4 d-flex justify-content-between align-items-center mb-4">
          <h6>My Applications</h6>
        </div>

        <div className="shadow bg-white rounded m-1">
          {loading ? (
            <div
              className="d-flex justify-content-center align-items-center"
              style={{ height: "50vh", transition: "opacity 0.5s ease-in-out" }}
            >
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          ) : (
            <div
              className="row row-cols-4 g-3 p-2 "
              style={{ opacity: loading ? 0 : 1, transition: "opacity 0.5s ease-in-out" }}
            >
              {services.map((service) => (
                <div key={service.id} className="col">
                  <Link href={removeAppsPrefix(service.url)} className="feature-card mx-auto text-center text-decoration-none">
                    <div className="shadow p-3 bg-white rounded card mx-auto">
                      <i
                        className={`${iconMapping[service.maskName] || "bi bi-file"
                          } fs-2 text-danger`}
                      ></i>
                    </div>
                    <p className="mb-0">{service.maskName === "EPTW-GEN" ? "ePTW" : service.mobileShortName}</p>
                  </Link>
                </div>
              ))}

              {/* Only show TBT if RA service exists */}
              {hasRAService && (
                <div className="col">
                  <Link href={'/tbt'} className="feature-card mx-auto text-center text-decoration-none">
                    <div className="shadow p-3 bg-white rounded card mx-auto">
                      <i
                        className={`${iconMapping['TBT'] || "bi bi-file"
                          } fs-2 text-danger`}
                      ></i>
                    </div>
                    <p className="mb-0">{'TBT'}</p>
                  </Link>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

    </>
  );
};

export default Brands;