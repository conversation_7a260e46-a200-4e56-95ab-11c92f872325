(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4005],{38983:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});var i=t(95155),c=t(6874),l=t.n(c);t(12115);let a=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],r=()=>(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,i.jsx)("div",{className:"container px-0",children:(0,i.jsx)("div",{className:"footer-nav position-relative",children:(0,i.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:a.map((e,s)=>(0,i.jsx)("li",{children:(0,i.jsxs)(l(),{href:"/".concat(e.link),children:[(0,i.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,i.jsx)("span",{children:e.title})]})},s))})})})})})},85015:(e,s,t)=>{"use strict";t.d(s,{default:()=>n});var i=t(95155);t(12115);var c=t(6874),l=t.n(c),a=t(38983),r=t(91727);let n=()=>(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(r.default,{links:"pages",title:"Cart"}),(0,i.jsx)("div",{className:"page-content-wrapper py-3",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("div",{className:"cart-wrapper-area",children:(0,i.jsxs)("div",{className:"cart-table card mb-3",children:[(0,i.jsx)("div",{className:"table-responsive card-body",children:(0,i.jsxs)("table",{className:"table mb-0 text-center rk_table",children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{scope:"col",children:"Image"}),(0,i.jsx)("th",{scope:"col",children:"Description"}),(0,i.jsx)("th",{scope:"col",children:"Quantity"}),(0,i.jsx)("th",{scope:"col",children:"Remove"})]})}),(0,i.jsxs)("tbody",{children:[(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{scope:"row",children:(0,i.jsx)("img",{src:"/assets/img/bg-img/p1.jpg",alt:""})}),(0,i.jsxs)("td",{children:[(0,i.jsx)("h6",{className:"mb-1",children:"Wooden Tool"}),(0,i.jsx)("span",{children:"$9.89 \xd7 1"})]}),(0,i.jsx)("td",{children:(0,i.jsx)("div",{className:"quantity",children:(0,i.jsx)("input",{className:"qty-text",type:"text",value:"1"})})}),(0,i.jsx)("td",{children:(0,i.jsx)("a",{className:"remove-product",href:"#",children:(0,i.jsx)("i",{className:"bi bi-x-lg"})})})]}),(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{scope:"row",children:(0,i.jsx)("img",{src:"/assets/img/bg-img/p3.jpg",alt:""})}),(0,i.jsxs)("td",{children:[(0,i.jsx)("h6",{className:"mb-1",children:"Black T-shirt"}),(0,i.jsx)("span",{children:"$10.99 \xd7 2"})]}),(0,i.jsx)("td",{children:(0,i.jsx)("div",{className:"quantity",children:(0,i.jsx)("input",{className:"qty-text",type:"text",value:"2"})})}),(0,i.jsx)("td",{children:(0,i.jsx)("a",{className:"remove-product",href:"#",children:(0,i.jsx)("i",{className:"bi bi-x-lg"})})})]}),(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{scope:"row",children:(0,i.jsx)("img",{src:"/assets/img/bg-img/p5.jpg",alt:""})}),(0,i.jsxs)("td",{children:[(0,i.jsx)("h6",{className:"mb-1",children:"Crispy Biscuit"}),(0,i.jsx)("span",{children:"$0.78 \xd7 9"})]}),(0,i.jsx)("td",{children:(0,i.jsx)("div",{className:"quantity",children:(0,i.jsx)("input",{className:"qty-text",type:"text",value:"9"})})}),(0,i.jsx)("td",{children:(0,i.jsx)("a",{className:"remove-product",href:"#",children:(0,i.jsx)("i",{className:"bi bi-x-lg"})})})]})]})]})}),(0,i.jsx)("div",{className:"card-body border-top",children:(0,i.jsxs)("div",{className:"apply-coupon",children:[(0,i.jsx)("h6",{className:"mb-0",children:"Have a coupon?"}),(0,i.jsx)("p",{className:"mb-2",children:"Enter your coupon code here & get awesome discounts!"}),(0,i.jsx)("div",{className:"coupon-form",children:(0,i.jsxs)("form",{onSubmit:e=>e.preventDefault(),children:[(0,i.jsx)("div",{className:"form-group",children:(0,i.jsxs)("div",{className:"input-group",children:[(0,i.jsx)("input",{className:"form-control input-group-text text-start",type:"text",placeholder:"OFFER30"}),(0,i.jsx)("button",{className:"btn btn-primary",type:"submit",children:"Apply"})]})}),(0,i.jsx)(l(),{href:"/checkout",children:(0,i.jsx)("button",{className:"btn btn-danger w-100 mt-3",children:"$38.89 & Pay"})})]})})]})})]})})})}),(0,i.jsx)(a.default,{})]})},97541:(e,s,t)=>{Promise.resolve().then(t.bind(t,85015))}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,1955,1531,1727,8441,1684,7358],()=>s(97541)),_N_E=e.O()}]);