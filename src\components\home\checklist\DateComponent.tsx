"use client";

import React from "react";
import { Card, Form } from "react-bootstrap";
import DatePicker from "react-datepicker";
import { ChecklistComponent, DateData, ErrorBuckets } from "../types/ChecklistTypes";

interface DateComponentProps {
    component: ChecklistComponent;
    componentIndex: number;
    checklistData: ChecklistComponent[];
    setChecklistData: React.Dispatch<React.SetStateAction<ChecklistComponent[]>>;
    errorMap: ErrorBuckets;
}

const DateComponent: React.FC<DateComponentProps> = ({
    component,
    componentIndex,
    checklistData,
    setChecklistData,
    errorMap,
}) => {
    const dateData = component.data as DateData;
    
    return (
        <Card className="mb-3 shadow-sm">
            <Card.Body className="p-3">
                <Form.Group>
                    <Form.Label className="fw-semibold d-flex align-items-center mb-2">
                        <i className="bi bi-calendar3 me-2 text-primary"></i>
                        {dateData.label}
                        {dateData.required && <span className="text-danger ms-1"> *</span>}
                    </Form.Label>
                    <div
                        className={
                            errorMap.checklist[`${componentIndex}-date`]
                                ? "border border-danger rounded"
                                : "border rounded"
                        }
                        style={{ padding: "0.5rem" }}
                    >
                        <DatePicker
                            selected={component.selectedDate ? new Date(component.selectedDate) : null}
                            onChange={(d: Date | null | undefined) => {
                                const updated = [...checklistData];
                                updated[componentIndex].selectedDate = d ? d.toISOString() : null;
                                setChecklistData(updated);
                            }}
                            placeholderText="Select date"
                            dateFormat="dd-MM-yyyy"
                            className="form-control border-0"
                            popperClassName="datepicker-high-zindex"
                        />
                    </div>
                    {errorMap.checklist[`${componentIndex}-date`] && (
                        <div className="small mt-1 text-danger">
                            {errorMap.checklist[`${componentIndex}-date`]}
                        </div>
                    )}
                </Form.Group>
            </Card.Body>
        </Card>
    );
};

export default DateComponent;
