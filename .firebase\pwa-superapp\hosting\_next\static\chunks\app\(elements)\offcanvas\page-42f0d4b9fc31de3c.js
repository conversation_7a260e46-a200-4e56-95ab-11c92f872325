(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9133],{18726:(e,s,a)=>{"use strict";a.d(s,{default:()=>n});var t=a(95155);a(12115);var c=a(6874),l=a.n(c),i=a(9e4);let n=()=>{a(81531);let{theme:e,handleDarkModeToggle:s}=(0,i.D)();return(0,t.jsx)(t.Fragment,{children:(0,t.jsxs)("div",{className:"page-content-wrapper py-3",children:[(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("div",{className:"element-heading",children:(0,t.jsx)("h6",{children:"Left offcanvas"})})}),(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("button",{className:"btn btn-primary",type:"button","data-bs-toggle":"offcanvas","data-bs-target":"#offcanvasleft","aria-controls":"offcanvasLeft",children:"Toggle left offcanvas"}),(0,t.jsxs)("div",{className:"offcanvas offcanvas-start",id:"offcanvasleft","data-bs-scroll":"true",tabIndex:-1,"aria-labelledby":"affanOffcanvsLabel",children:[(0,t.jsx)("button",{className:"btn-close btn-close-white text-reset",type:"button","data-bs-dismiss":"offcanvas","aria-label":"Close"}),(0,t.jsx)("div",{className:"offcanvas-body p-0",children:(0,t.jsxs)("div",{className:"sidenav-wrapper",children:[(0,t.jsxs)("div",{className:"sidenav-profile bg-gradient",children:[(0,t.jsx)("div",{className:"sidenav-style1"}),(0,t.jsx)("div",{className:"user-profile",children:(0,t.jsx)("img",{src:"/assets/img/bg-img/2.jpg",alt:""})}),(0,t.jsxs)("div",{className:"user-info",children:[(0,t.jsx)("h6",{className:"user-name mb-0",children:"Affan Islam"}),(0,t.jsx)("span",{children:"CEO, Designing World"})]})]}),(0,t.jsxs)("ul",{className:"sidenav-nav ps-0",children:[(0,t.jsx)("li",{children:(0,t.jsxs)(l(),{href:"/home",children:[(0,t.jsx)("i",{className:"bi bi-house-door"})," Home"]})}),(0,t.jsx)("li",{children:(0,t.jsxs)(l(),{href:"/elements",children:[(0,t.jsx)("i",{className:"bi bi-folder2-open"})," Elements",(0,t.jsx)("span",{className:"badge bg-danger rounded-pill ms-2",children:"220+"})]})}),(0,t.jsx)("li",{children:(0,t.jsxs)(l(),{href:"/pages",children:[(0,t.jsx)("i",{className:"bi bi-collection"})," Pages",(0,t.jsx)("span",{className:"badge bg-success rounded-pill ms-2",children:"100+"})]})}),(0,t.jsxs)("li",{children:[(0,t.jsxs)("a",{href:"#",children:[(0,t.jsx)("i",{className:"bi bi-cart-check"})," Shop"]}),(0,t.jsxs)("ul",{children:[(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/shop-grid",children:"Shop Grid"})}),(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/shop-list",children:"Shop List"})}),(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/shop-details",children:"Shop Details"})}),(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/cart",children:" Cart"})}),(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/checkout",children:"Checkout"})})]})]}),(0,t.jsx)("li",{children:(0,t.jsxs)(l(),{href:"/settings",children:[(0,t.jsx)("i",{className:"bi bi-gear"})," Settings"]})}),(0,t.jsx)("li",{children:(0,t.jsxs)("div",{className:"night-mode-nav",style:{color:"#8480AE"},children:[(0,t.jsx)("i",{className:"bi bi-moon"}),"dark"===e?"Light":"Dark"," Mode",(0,t.jsx)("div",{className:"form-check form-switch",children:(0,t.jsx)("input",{className:"form-check-input form-check-success",id:"darkSwitch2",type:"checkbox",checked:"dark"===e,onChange:s})})]})}),(0,t.jsx)("li",{children:(0,t.jsxs)(l(),{href:"/login",children:[(0,t.jsx)("i",{className:"bi bi-box-arrow-right"}),"Logout"]})})]}),(0,t.jsxs)("div",{className:"social-info-wrap",children:[(0,t.jsx)("a",{href:"#",children:(0,t.jsx)("i",{className:"bi bi-facebook"})}),(0,t.jsx)("a",{href:"#",children:(0,t.jsx)("i",{className:"bi bi-twitter"})}),(0,t.jsx)("a",{href:"#",children:(0,t.jsx)("i",{className:"bi bi-linkedin"})})]}),(0,t.jsx)("div",{className:"copyright-info",children:(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{id:"copyrightYear"}),"copyright Made by ",(0,t.jsx)("a",{href:"#",children:"Designing World"})]})})]})})]})]})})}),(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("div",{className:"element-heading mt-3",children:(0,t.jsx)("h6",{children:"Top offcanvas"})})}),(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("button",{className:"btn btn-success",type:"button","data-bs-toggle":"offcanvas","data-bs-target":"#offcanvasTop","aria-controls":"offcanvasTop",children:"Toggle top offcanvas"}),(0,t.jsxs)("div",{className:"offcanvas offcanvas-top",id:"offcanvasTop",tabIndex:-1,"aria-labelledby":"offcanvasTopLabel",children:[(0,t.jsx)("button",{className:"btn-close text-reset",type:"button","data-bs-dismiss":"offcanvas","aria-label":"Close"}),(0,t.jsxs)("div",{className:"offcanvas-body p-4",children:[(0,t.jsx)("h5",{children:"Placement"}),(0,t.jsx)("p",{children:"Theres no default placement for offcanvas components, so you must add one of the modifier classNamees below;"}),(0,t.jsxs)("ul",{className:"ps-0 mb-4",children:[(0,t.jsxs)("li",{className:"fz-14 pb-2",children:[(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas-start"}),"places offcanvas on the left of the viewport."]}),(0,t.jsxs)("li",{className:"fz-14 pb-2",children:[(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas-end"}),"places offcanvas on the right of the viewport."]}),(0,t.jsxs)("li",{className:"fz-14 pb-2",children:[(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas-top"}),"places offcanvas on the top of the viewport."]}),(0,t.jsxs)("li",{className:"fz-14",children:[(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas-bottom"}),"places offcanvas on the bottom of the viewport."]})]}),(0,t.jsx)("h5",{children:"Hide & Show"}),(0,t.jsxs)("p",{children:["Use the buttons below to show and hide an offcanvas element via JavaScript that toggles the",(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".show"}),"className on an element with the",(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas"}),"className."]}),(0,t.jsxs)("ul",{className:"ps-0",children:[(0,t.jsxs)("li",{className:"fz-14 pb-2",children:[(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas"}),"hides content (by default)."]}),(0,t.jsxs)("li",{className:"fz-14 pb-2",children:[(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas.show"}),"shows content."]})]})]})]})]})})}),(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("div",{className:"element-heading mt-3",children:(0,t.jsx)("h6",{children:"Right offcanvas"})})}),(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("button",{className:"btn btn-warning",type:"button","data-bs-toggle":"offcanvas","data-bs-target":"#offcanvasRight","aria-controls":"offcanvasRight",children:"Toggle right offcanvas"}),(0,t.jsxs)("div",{className:"offcanvas offcanvas-end",id:"offcanvasRight",tabIndex:-1,"aria-labelledby":"offcanvasRightLabel",children:[(0,t.jsx)("button",{className:"btn-close text-reset",type:"button","data-bs-dismiss":"offcanvas","aria-label":"Close"}),(0,t.jsxs)("div",{className:"offcanvas-body p-4 text-end",children:[(0,t.jsx)("h5",{children:"Placement"}),(0,t.jsx)("p",{children:"Theres no default placement for offcanvas components, so you must add one of the modifier classNamees below;"}),(0,t.jsxs)("ul",{className:"ps-0 mb-4",children:[(0,t.jsxs)("li",{className:"fz-14 pb-2",children:[(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas-start"}),"places offcanvas on the left of the viewport."]}),(0,t.jsxs)("li",{className:"fz-14 pb-2",children:[(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas-end"}),"places offcanvas on the right of the viewport."]}),(0,t.jsxs)("li",{className:"fz-14 pb-2",children:[(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas-top"}),"places offcanvas on the top of the viewport."]}),(0,t.jsxs)("li",{className:"fz-14",children:[(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas-bottom"}),"places offcanvas on the bottom of the viewport."]})]}),(0,t.jsx)("h5",{children:"Hide & Show"}),(0,t.jsxs)("p",{children:["Use the buttons below to show and hide an offcanvas element via JavaScript that toggles the",(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".show"}),"className on an element with the",(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas"}),"className."]}),(0,t.jsxs)("ul",{className:"ps-0",children:[(0,t.jsxs)("li",{className:"fz-14 pb-2",children:[(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas"}),"hides content (by default)."]}),(0,t.jsxs)("li",{className:"fz-14 pb-2",children:[(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas.show"}),"shows content."]})]})]})]})]})})}),(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("div",{className:"element-heading mt-3",children:(0,t.jsx)("h6",{children:"Bottom Offcanvas"})})}),(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("button",{className:"btn btn-info",type:"button","data-bs-toggle":"offcanvas","data-bs-target":"#offcanvasBottom","aria-controls":"offcanvasBottom",children:"Toggle bottom offcanvas"}),(0,t.jsxs)("div",{className:"offcanvas offcanvas-bottom",id:"offcanvasBottom",tabIndex:-1,"aria-labelledby":"offcanvasBottomLabel",children:[(0,t.jsx)("button",{className:"btn-close text-reset",type:"button","data-bs-dismiss":"offcanvas","aria-label":"Close"}),(0,t.jsxs)("div",{className:"offcanvas-body p-4",children:[(0,t.jsx)("h5",{children:"Placement"}),(0,t.jsx)("p",{children:"Theres no default placement for offcanvas components, so you must add one of the modifier classNamees below;"}),(0,t.jsxs)("ul",{className:"ps-0 mb-4",children:[(0,t.jsxs)("li",{className:"fz-14 pb-2",children:[(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas-start"}),"places offcanvas on the left of the viewport."]}),(0,t.jsxs)("li",{className:"fz-14 pb-2",children:[(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas-end"}),"places offcanvas on the right of the viewport."]}),(0,t.jsxs)("li",{className:"fz-14 pb-2",children:[(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas-top"}),"places offcanvas on the top of the viewport."]}),(0,t.jsxs)("li",{className:"fz-14",children:[(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas-bottom"}),"places offcanvas on the bottom of the viewport."]})]}),(0,t.jsx)("h5",{children:"Hide & Show"}),(0,t.jsxs)("p",{children:["Use the buttons below to show and hide an offcanvas element via JavaScript that toggles the",(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".show"}),"className on an element with the",(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas"}),"className."]}),(0,t.jsxs)("ul",{className:"ps-0",children:[(0,t.jsxs)("li",{className:"fz-14 pb-2",children:[(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas"}),"hides content (by default)."]}),(0,t.jsxs)("li",{className:"fz-14 pb-2",children:[(0,t.jsx)("code",{className:"bg-dark text-white rounded p-1 me-1",children:".offcanvas.show"}),"shows content."]})]})]})]})]})})})]})})}},21217:(e,s,a)=>{"use strict";a.d(s,{default:()=>r});var t=a(95155),c=a(9e4),l=a(38808),i=a(12115);let n=e=>{let{handleShowSetting:s,showSetting:a}=e,{theme:i,handleDarkModeToggle:n}=(0,c.D)(),{viewMode:d,handleRTLToggling:o}=(0,l.L)();return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{id:"setting-popup-overlay",className:a?"active":"",onClick:s}),(0,t.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(a?"active":""),id:"settingCard",children:(0,t.jsx)("div",{className:"card-body",children:(0,t.jsxs)("div",{className:"container",children:[(0,t.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,t.jsx)("p",{className:"mb-0",children:"Settings"}),(0,t.jsx)("div",{onClick:s,className:"btn-close",id:"settingCardClose"})]}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,t.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,t.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===i,onChange:n}),(0,t.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===i?"Light":"Dark"," mode"]})]})}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===d,onChange:o}),(0,t.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===d?"LTR":"RTL"," mode"]})]})})]})})})]})};var d=a(6874),o=a.n(d);let r=e=>{let{links:s,title:a}=e,[c,l]=(0,i.useState)(!1),d=()=>l(!c);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"header-area",id:"headerArea",children:(0,t.jsx)("div",{className:"container",children:(0,t.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,t.jsx)("div",{className:"back-button",children:(0,t.jsx)(o(),{href:"/".concat(s),children:(0,t.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,t.jsx)("div",{className:"page-heading",children:(0,t.jsx)("h6",{className:"mb-0",children:a})}),(0,t.jsx)("div",{className:"setting-wrapper",onClick:d,children:(0,t.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,t.jsx)("i",{className:"bi bi-gear"}),(0,t.jsx)("span",{})]})})]})})}),(0,t.jsx)(n,{showSetting:c,handleShowSetting:d})]})}},29900:(e,s,a)=>{Promise.resolve().then(a.bind(a,18726)),Promise.resolve().then(a.bind(a,38983)),Promise.resolve().then(a.bind(a,21217))},38808:(e,s,a)=>{"use strict";a.d(s,{L:()=>c});var t=a(12115);let c=()=>{let[e,s]=(0,t.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,t.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let a=()=>{s(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:a,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let s=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(s),e.style.display="none")},20)},1e3))}a()}}}},38983:(e,s,a)=>{"use strict";a.d(s,{default:()=>n});var t=a(95155),c=a(6874),l=a.n(c);a(12115);let i=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],n=()=>(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,t.jsx)("div",{className:"container px-0",children:(0,t.jsx)("div",{className:"footer-nav position-relative",children:(0,t.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:i.map((e,s)=>(0,t.jsx)("li",{children:(0,t.jsxs)(l(),{href:"/".concat(e.link),children:[(0,t.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,t.jsx)("span",{children:e.title})]})},s))})})})})})},9e4:(e,s,a)=>{"use strict";a.d(s,{D:()=>c});var t=a(12115);let c=()=>{let[e,s]=(0,t.useState)("light"),[a,c]=(0,t.useState)(!1);(0,t.useEffect)(()=>{s(localStorage.getItem("theme")||"light"),c(!0)},[]),(0,t.useEffect)(()=>{a&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,a]);let l=(0,t.useCallback)(()=>{s(e=>"dark"===e?"light":"dark")},[]),i=(0,t.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let s=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(s),e.style.display="none")},20)},1e3))}l()},[l]);return{theme:e,toggleTheme:l,handleDarkModeToggle:i}}}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,1955,1531,8441,1684,7358],()=>s(29900)),_N_E=e.O()}]);