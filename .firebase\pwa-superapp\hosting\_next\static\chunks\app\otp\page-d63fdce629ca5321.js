(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7632],{19019:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var s=n(95155),r=n(12115),a=["mousedown","touchstart"];let l=function(e,t,n){void 0===n&&(n=a);var s=(0,r.useRef)(t);(0,r.useEffect)(function(){s.current=t},[t]),(0,r.useEffect)(function(){for(var t=function(t){var n=e.current;n&&!n.contains(t.target)&&s.current(t)},r=0,a=n;r<a.length;r++)!function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];e&&e.addEventListener&&e.addEventListener.apply(e,t)}(document,a[r],t);return function(){for(var e=0,s=n;e<s.length;e++)!function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];e&&e.removeEventListener&&e.removeEventListener.apply(e,t)}(document,s[e],t)}},[n,e])},c=e=>{let{options:t,defaultCurrent:n,placeholder:a,className:c,onChange:i,name:o}=e,[u,d]=(0,r.useState)(!1),[m,h]=(0,r.useState)(t[n]),p=(0,r.useCallback)(()=>{d(!1)},[]),v=(0,r.useRef)(null);l(v,p);let x=e=>{h(e),i(e,o),p()};return(0,s.jsxs)("div",{className:"nice-select form-select-lg mb-3 ".concat(c||""," ").concat(u?"open":""),role:"button",tabIndex:0,onClick:()=>d(e=>!e),onKeyDown:e=>e,ref:v,children:[(0,s.jsx)("span",{className:"current",children:(null==m?void 0:m.text)||a}),(0,s.jsx)("ul",{className:"list",role:"menubar",onClick:e=>e.stopPropagation(),onKeyDown:e=>e.stopPropagation(),children:null==t?void 0:t.map((e,t)=>(0,s.jsx)("li",{"data-value":e.value,className:"option ".concat(e.value===(null==m?void 0:m.value)?"selected focus":""),style:{fontSize:"14px"},role:"menuitem",onClick:()=>x(e),onKeyDown:e=>e,children:e.text},t))})]})}},48078:(e,t,n)=>{Promise.resolve().then(n.bind(n,93610))},78093:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var s=n(95155),r=n(6874),a=n.n(r);n(12115);let l=e=>{let{links:t}=e;return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"login-back-button",children:(0,s.jsx)(a(),{href:"/".concat(t),children:(0,s.jsx)("i",{className:"bi bi-arrow-left-short"})})})})}},93610:(e,t,n)=>{"use strict";n.d(t,{default:()=>i});var s=n(95155),r=n(78093),a=n(6874),l=n.n(a);n(12115);var c=n(19019);let i=()=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(r.A,{links:"register"}),(0,s.jsx)("div",{className:"login-wrapper d-flex align-items-center justify-content-center",children:(0,s.jsxs)("div",{className:"custom-container",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("img",{className:"mx-auto mb-4 d-block",src:"/assets/img/bg-img/36.png",alt:""}),(0,s.jsx)("h3",{children:"Phone Verification"}),(0,s.jsx)("p",{children:"We will send you an OTP code on this phone number."})]}),(0,s.jsx)("div",{className:"otp-form mt-4",children:(0,s.jsxs)("form",{action:"/otp-confirm",onSubmit:e=>e.preventDefault(),children:[(0,s.jsxs)("div",{className:"input-group mb-3",children:[(0,s.jsx)(c.A,{className:"input-group-text d-flex",options:[{value:"01",text:"+880"},{value:"02",text:"+920"},{value:"03",text:"+061"}],defaultCurrent:0,onChange:e=>{},placeholder:"Select an option",name:"myNiceSelect"}),(0,s.jsx)("input",{className:"form-control",type:"text",placeholder:"Enter phone number"})]}),(0,s.jsx)("button",{className:"btn btn-primary w-100",type:"submit",children:"Send Now"})]})}),(0,s.jsx)("div",{className:"login-meta-data px-4 text-center",children:(0,s.jsxs)("p",{className:"mt-3 mb-0",children:["By providing my phone number, I hereby agree the",(0,s.jsxs)(l(),{className:"stretched-link",href:"/privacy-policy",children:[" ","Term of Services"]}),"and",(0,s.jsxs)(l(),{className:"stretched-link",href:"/privacy-policy",children:[" "," ","Privacy Policy."]})]})})]})})]})}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,8441,1684,7358],()=>t(48078)),_N_E=e.O()}]);