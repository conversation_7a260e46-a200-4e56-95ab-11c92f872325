"use client";

import unifiedNotificationService from '@/services/unifiedNotificationService';
import iosNotificationService from '@/services/iosNotificationService';
import fcmService from '@/services/fcmService';

/**
 * Utility functions for testing notification functionality
 */

export interface TestResult {
  test: string;
  success: boolean;
  message: string;
  data?: any;
}

export class NotificationTester {
  private results: TestResult[] = [];

  /**
   * Run comprehensive notification tests
   */
  async runAllTests(): Promise<TestResult[]> {
    this.results = [];
    
    console.log('🧪 Starting comprehensive notification tests...');
    
    // Test 1: Device Detection
    await this.testDeviceDetection();
    
    // Test 2: Service Initialization
    await this.testServiceInitialization();
    
    // Test 3: Permission Status
    await this.testPermissionStatus();
    
    // Test 4: iOS Specific Tests
    await this.testIOSSpecific();
    
    // Test 5: FCM Specific Tests
    await this.testFCMSpecific();
    
    // Test 6: Unified Service Tests
    await this.testUnifiedService();
    
    console.log('🧪 All tests completed:', this.results);
    return this.results;
  }

  private async testDeviceDetection(): Promise<void> {
    try {
      await unifiedNotificationService.initialize();
      const deviceInfo = unifiedNotificationService.getDeviceInfo();
      
      this.addResult({
        test: 'Device Detection',
        success: !!deviceInfo,
        message: deviceInfo ? 
          `Detected: ${deviceInfo.isIOS ? 'iOS' : deviceInfo.isAndroid ? 'Android' : 'Desktop'} - ${deviceInfo.recommendedMethod}` :
          'Failed to detect device',
        data: deviceInfo
      });
    } catch (error) {
      this.addResult({
        test: 'Device Detection',
        success: false,
        message: `Error: ${error}`,
      });
    }
  }

  private async testServiceInitialization(): Promise<void> {
    try {
      const unifiedSuccess = await unifiedNotificationService.initialize();
      
      this.addResult({
        test: 'Unified Service Initialization',
        success: unifiedSuccess,
        message: unifiedSuccess ? 'Unified service initialized successfully' : 'Unified service failed to initialize'
      });
    } catch (error) {
      this.addResult({
        test: 'Unified Service Initialization',
        success: false,
        message: `Error: ${error}`,
      });
    }
  }

  private async testPermissionStatus(): Promise<void> {
    try {
      const permission = unifiedNotificationService.getPermissionStatus();
      const statusMessage = unifiedNotificationService.getStatusMessage();
      
      this.addResult({
        test: 'Permission Status',
        success: true,
        message: `Permission: ${permission} - ${statusMessage}`,
        data: { permission, statusMessage }
      });
    } catch (error) {
      this.addResult({
        test: 'Permission Status',
        success: false,
        message: `Error: ${error}`,
      });
    }
  }

  private async testIOSSpecific(): Promise<void> {
    try {
      const isSupported = iosNotificationService.isSupported();
      const permission = iosNotificationService.getPermissionStatus();
      
      this.addResult({
        test: 'iOS Notification Support',
        success: true,
        message: `iOS Support: ${isSupported}, Permission: ${permission}`,
        data: { isSupported, permission }
      });
    } catch (error) {
      this.addResult({
        test: 'iOS Notification Support',
        success: false,
        message: `Error: ${error}`,
      });
    }
  }

  private async testFCMSpecific(): Promise<void> {
    try {
      // Test FCM initialization without actually requesting permissions
      const messaging = await fcmService.initialize();
      
      this.addResult({
        test: 'FCM Service',
        success: !!messaging,
        message: messaging ? 'FCM service available' : 'FCM service not available',
        data: { hasMessaging: !!messaging }
      });
    } catch (error) {
      this.addResult({
        test: 'FCM Service',
        success: false,
        message: `Error: ${error}`,
      });
    }
  }

  private async testUnifiedService(): Promise<void> {
    try {
      const isSupported = unifiedNotificationService.isSupported();
      const instructions = unifiedNotificationService.getSetupInstructions();
      
      this.addResult({
        test: 'Unified Service Features',
        success: true,
        message: `Supported: ${isSupported}, Instructions available: ${instructions.length > 0}`,
        data: { isSupported, instructionCount: instructions.length }
      });
    } catch (error) {
      this.addResult({
        test: 'Unified Service Features',
        success: false,
        message: `Error: ${error}`,
      });
    }
  }

  private addResult(result: TestResult): void {
    this.results.push(result);
    console.log(`${result.success ? '✅' : '❌'} ${result.test}: ${result.message}`);
  }

  /**
   * Test notification permission request (requires user interaction)
   */
  async testPermissionRequest(): Promise<TestResult> {
    try {
      console.log('🔔 Testing permission request...');
      const hasPermission = await unifiedNotificationService.requestPermission();
      
      return {
        test: 'Permission Request',
        success: hasPermission,
        message: hasPermission ? 'Permission granted successfully' : 'Permission denied or failed',
        data: { hasPermission }
      };
    } catch (error) {
      return {
        test: 'Permission Request',
        success: false,
        message: `Error: ${error}`,
      };
    }
  }

  /**
   * Test notification registration (requires permission)
   */
  async testNotificationRegistration(): Promise<TestResult> {
    try {
      console.log('📝 Testing notification registration...');
      const token = await unifiedNotificationService.registerForNotifications();
      
      return {
        test: 'Notification Registration',
        success: !!token,
        message: token ? `Token generated: ${token.substring(0, 50)}...` : 'Failed to generate token',
        data: { token: token ? token.substring(0, 50) + '...' : null }
      };
    } catch (error) {
      return {
        test: 'Notification Registration',
        success: false,
        message: `Error: ${error}`,
      };
    }
  }

  /**
   * Test showing a notification (requires permission)
   */
  async testShowNotification(): Promise<TestResult> {
    try {
      console.log('📱 Testing show notification...');
      const success = await unifiedNotificationService.showTestNotification();
      
      return {
        test: 'Show Test Notification',
        success,
        message: success ? 'Test notification shown successfully' : 'Failed to show test notification',
        data: { success }
      };
    } catch (error) {
      return {
        test: 'Show Test Notification',
        success: false,
        message: `Error: ${error}`,
      };
    }
  }

  /**
   * Get test results
   */
  getResults(): TestResult[] {
    return this.results;
  }

  /**
   * Clear test results
   */
  clearResults(): void {
    this.results = [];
  }
}

// Export singleton instance
export const notificationTester = new NotificationTester();
export default notificationTester;
