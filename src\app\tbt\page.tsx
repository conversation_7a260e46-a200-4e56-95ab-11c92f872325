


import New from "@/components/tbt/New";
import HeaderSeven from "@/layouts/headers/HeaderSeven";
import React from "react";
import OfflineAwareLayout from "@/components/offline/OfflineAwareLayout";


// export const metadata = {
//     title: "A<PERSON>an <PERSON>s - <PERSON>WA Mobile Next.js Template",
// };

const Observation: React.FC = () => {

    return (
        <>
            <HeaderSeven heading={'Risk Communication'} />
            <OfflineAwareLayout
                pageTitle="Risk Communication"
                requiresOnline={true}
            >
                <div className="page-content-wrapper">
                    <New />
                </div>
            </OfflineAwareLayout>
        </>
    );
};

export default Observation;
