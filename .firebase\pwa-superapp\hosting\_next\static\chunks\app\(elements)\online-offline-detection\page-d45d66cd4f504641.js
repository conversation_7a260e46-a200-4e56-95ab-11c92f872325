(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8503],{45528:(e,n,t)=>{"use strict";t.d(n,{default:()=>l});var s=t(95155),i=t(12115),c=t(38983),o=t(21217);let l=()=>{t(8012);let[e,n]=(0,i.useState)(!1),[l,r]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{e?r(!1):l&&n(!1);let t=setTimeout(()=>{n(!1),r(!1)},5e3);return()=>clearTimeout(t)},[e,l]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.default,{links:"elements",title:"Offline Detection"}),(0,s.jsxs)("div",{className:"page-content-wrapper py-3",children:[(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"card offline-online-card",children:(0,s.jsxs)("div",{className:"card-body text-center",children:[(0,s.jsx)("h6",{className:"border-bottom pb-2",children:"Offline/Online Detection"}),(0,s.jsx)("p",{children:"Press the button to view offline/online notifications. These buttons are used for demo purposes."}),(0,s.jsx)("a",{className:"mx-1 offline-detection btn btn-danger",style:{cursor:"pointer"},onClick:()=>{r(!0)},children:"Offline Detect"}),(0,s.jsx)("a",{className:"mx-1 online-detection btn btn-success",style:{cursor:"pointer"},onClick:()=>{n(!0)},children:"Online Detect"})]})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"card mt-4 offline-online-card",children:(0,s.jsxs)("div",{className:"card-body",children:[(0,s.jsx)("h6",{children:"How it works?"}),(0,s.jsxs)("p",{className:"mb-2",children:["Offline / online detection",(0,s.jsx)("strong",{className:"mx-1 text-primary",children:"works automatically."}),"No coding required, it works by default on each page."]}),(0,s.jsx)("p",{className:"mb-0",children:"Disconnect your WiFi or mobile internet connection, you will be notified. When your connection returns, you will be notified again."})]})})}),(0,s.jsx)("div",{className:"internet-connection-status",id:"internetStatus",style:{display:e?"block":"none",backgroundColor:"rgb(0, 184, 148)"},children:"Your internet connection is back."}),(0,s.jsx)("div",{className:"internet-connection-status",id:"internetStatus",style:{display:l?"block":"none",backgroundColor:"rgb(234, 76, 98)"},children:"Oops! No internet connection."})]}),(0,s.jsx)(c.default,{})]})}},54836:(e,n,t)=>{Promise.resolve().then(t.bind(t,45528))}},e=>{var n=n=>e(e.s=n);e.O(0,[6874,2703,5856,8441,1684,7358],()=>n(54836)),_N_E=e.O()}]);