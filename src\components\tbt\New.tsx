"use client";

import HeaderSeven from "@/layouts/headers/HeaderSeven";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import API from "@/services/API"; // Import API service
import { FILE_DOWNLOAD, MY_TOOLBOX_TALKS, OBSERVATION_ARCHIVED_URL, TOOLBOX_TALKS, USERS_URL } from "@/constant"; // Your API endpoint
import { Modal, Button } from "react-bootstrap"; // Import Bootstrap Modal
import moment from 'moment'
import ImageComponent from "@/services/FileDownlodS3";
import MyLogoComponent from "@/services/MyLogoComponet";
import axios from "axios";
import ViewTBT from "./ViewTBT";



interface User {
    id: string | number;
    firstName: string;
}
interface ObservationAction {
    actionType: string;
    // Add more fields if necessary
}
const New: React.FC = () => {
    const router = useRouter();
    const [activeTab, setActiveTab] = useState("submitted");
    let k = 0;
    // State for Archived Data
    const [archivedData, setArchivedData] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [reportData, setReportData] = useState<any | null>(null);
    const [showModal, setShowModal] = useState(false);
    const [users, setUsers] = useState([])
    const [logo, setLogo] = useState('')
    // Function to Fetch Archived Data
    const fetchArchivedData = async () => {
        setLoading(true);
        setError(null);
        try {
            const uriString = {
                include: [
                    { relation: "locationOne" },
                    { relation: "locationTwo" },
                    { relation: "locationThree" },
                    { relation: "locationFour" },
                    { relation: "locationFive" },
                    { relation: "locationSix" },
                    { relation: "locationSix" },
                    {
                        relation: "toolboxSignStatuses",
                        scope: {
                            include: [{ relation: "signedBy"}]
                        }
                    },
                    { relation: "conductedBy" },
                    {
                        relation: "riskAssessment",
                        scope: {
                            include: [{ relation: "workActivity" }]
                        }
                    },
                    // { relation: "reviewer" },
                    // { relation: "observationActions" },
                ]

            };
            const url = `${MY_TOOLBOX_TALKS}?filter=${encodeURIComponent(
                JSON.stringify(uriString)
            )}`;
            const response = await API.get(url);
            if (response.status === 200) {
                setArchivedData(response.data.reverse()); // Assuming response.data is an array
            }
        } catch (err) {
            setError("Failed to fetch archived data.");
            console.error("Error fetching archived data:", err);
        } finally {
            setLoading(false);
        }
    };

    // Fetch data when Archived tab is selected
    useEffect(() => {
        if (activeTab === "submitted") {
            fetchArchivedData();
            getAllUsers()
        }
    }, [activeTab]);

    const getAllUsers = async () => {
        const response = await API.get(USERS_URL);
        setUsers(response.data)
    }
    useEffect(() => {
        getFetchLogo()
    }, [])
    const getFetchLogo = async () => {
        try {
            const response = await API.get(FILE_DOWNLOAD(localStorage.getItem('logo')), {
                headers: {
                    'Content-Type': 'application/json'
                },
            });
            const data = response.data;
            const logoUrl = data // Assuming the API returns an object with a `url` field
            setLogo(logoUrl);

        } catch (error) {
            console.error('Error fetching logo:', error);
        }
    }
    const handleObservationClick = (obs: any) => {
        setReportData(obs);
        setShowModal(true);
    };

    return (
        <>
            <div className="page-content-wrapper" style={{ backgroundColor: "#f8fafc", minHeight: "100vh" }}>
                <div className="container-fluid px-3 py-4">

                    {/* Simple Action Button */}
                    <button
                        className="btn btn-danger w-100 py-3 mb-4 d-flex align-items-center justify-content-between"
                        style={{
                            borderRadius: "12px",
                            fontSize: "16px",
                            fontWeight: "600",
                            border: "none",
                            boxShadow: "0 2px 8px rgba(220, 38, 38, 0.2)"
                        }}
                        onClick={() => router.push("/tbt/new")}
                    >
                        <div className="d-flex align-items-center">
                            <div className="bg-white bg-opacity-20 rounded-circle p-2 me-3 d-flex align-items-center justify-content-center" style={{ width: "40px", height: "40px" }}>
                                <span className="text-white fw-bold" style={{ fontSize: "18px" }}>📢</span>
                            </div>
                            <span>Conduct Toolbox Talk</span>
                        </div>
                        <span className="text-white fw-bold" style={{ fontSize: "18px" }}>→</span>
                    </button>

                    {/* Enhanced Tab Navigation */}
                    <div className="card border-0 shadow-sm mb-4">
                        <div className="card-body p-3">
                            <div className="d-flex gap-2">
                                {[
                                    { id: "submitted", label: "Submitted Talks", icon: "✅", count: archivedData.length }
                                ].map((tab) => (
                                    <button
                                        key={tab.id}
                                        className={`btn flex-fill py-3 rounded-3 d-flex align-items-center justify-content-center gap-2 ${
                                            activeTab === tab.id
                                                ? "btn-danger text-white shadow-sm"
                                                : "btn-light text-muted"
                                        }`}
                                        style={{
                                            border: "none",
                                            fontWeight: "600",
                                            fontSize: "14px",
                                            transition: "all 0.3s ease"
                                        }}
                                        onClick={() => setActiveTab(tab.id)}
                                    >
                                        <span style={{ fontSize: "16px" }}>{tab.icon}</span>
                                        <span>{tab.label}</span>
                                        {tab.count > 0 && (
                                            <span className={`badge ${
                                                activeTab === tab.id ? 'bg-white text-danger' : 'bg-danger text-white'
                                            } ms-1`} style={{ fontSize: "10px" }}>
                                                {tab.count}
                                            </span>
                                        )}
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Enhanced Content Area */}
                    <div>
                        {activeTab === "submitted" && (
                            <div>
                                {/* Enhanced Header */}
                                <div className="card border-0 shadow-sm mb-4">
                                    <div className="card-body py-3">
                                        <div className="d-flex justify-content-between align-items-center">
                                            <div className="d-flex align-items-center gap-3">
                                                <div className="p-2 rounded-circle d-flex align-items-center justify-content-center" style={{ backgroundColor: "#fef2f2", width: "40px", height: "40px" }}>
                                                    <span style={{ fontSize: "20px" }}>📢</span>
                                                </div>
                                                <div>
                                                    <h6 className="mb-0" style={{ color: "#1f2937", fontWeight: "600" }}>
                                                        Submitted Toolbox Talks
                                                    </h6>
                                                    <small className="text-muted">Safety briefings and team communications</small>
                                                </div>
                                            </div>
                                            <div className="text-end">
                                                <span className="badge bg-danger px-3 py-2" style={{ fontSize: "12px" }}>
                                                    {archivedData.length} talks
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Content */}
                                <div style={{ maxHeight: "calc(100vh - 400px)", overflowY: "auto" }} className="custom-scrollbar">
                                    {/* Enhanced Loading State */}
                                    {loading && (
                                        <div className="text-center py-5">
                                            <div className="d-flex flex-column align-items-center">
                                                <div className="spinner-border text-danger mb-3" role="status">
                                                    <span className="visually-hidden">Loading...</span>
                                                </div>
                                                <p className="text-muted mb-0">Loading toolbox talks...</p>
                                            </div>
                                        </div>
                                    )}

                                    {/* Enhanced Error State */}
                                    {error && (
                                        <div className="alert alert-danger border-0 shadow-sm" role="alert">
                                            <div className="d-flex align-items-center">
                                                <i className="bi bi-exclamation-triangle-fill me-2"></i>
                                                {error}
                                            </div>
                                        </div>
                                    )}

                                    {/* Enhanced Data List */}
                                    {archivedData.length > 0 ? (
                                        <div className="row g-3">
                                            {archivedData.map((obs, index) => (
                                                <div key={index} className="col-12">
                                                    <div
                                                        className="card border-0 h-100"
                                                        style={{
                                                            cursor: "pointer",
                                                            transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                                                            boxShadow: "0 4px 12px rgba(0,0,0,0.08)",
                                                            borderRadius: "16px",
                                                            backgroundColor: "#FFFFFF",
                                                            borderLeft: "4px solid #dc2626"
                                                        }}
                                                        onClick={() => handleObservationClick(obs)}
                                                        onMouseEnter={(e) => {
                                                            e.currentTarget.style.transform = "translateY(-4px)";
                                                            e.currentTarget.style.boxShadow = "0 12px 32px rgba(220, 38, 38, 0.15)";
                                                        }}
                                                        onMouseLeave={(e) => {
                                                            e.currentTarget.style.transform = "translateY(0)";
                                                            e.currentTarget.style.boxShadow = "0 4px 12px rgba(0,0,0,0.08)";
                                                        }}
                                                    >
                                                        <div className="card-body p-4">
                                                            <div className="d-flex justify-content-between align-items-start mb-3">
                                                                <div className="d-flex align-items-center gap-2">
                                                                    <div className="p-2 rounded-circle d-flex align-items-center justify-content-center" style={{ backgroundColor: "#fef2f2", width: "32px", height: "32px" }}>
                                                                        <span style={{ fontSize: "14px" }}>📢</span>
                                                                    </div>
                                                                    <span className="text-muted fw-medium" style={{ fontSize: "13px" }}>#{obs.maskId}</span>
                                                                </div>
                                                                <span className={`badge px-3 py-1 ${
                                                                    obs.status === "Published" ? "bg-success" :
                                                                    obs.status === "Pending" ? "bg-warning text-dark" :
                                                                    obs.status === "Submitted" ? "bg-info" : "bg-secondary"
                                                                }`} style={{ fontSize: "11px", fontWeight: "600" }}>
                                                                    {obs.status}
                                                                </span>
                                                            </div>

                                                            <h6 className="mb-3 fw-semibold" style={{ color: "#1f2937", lineHeight: "1.4", fontSize: "15px" }}>
                                                                {obs.riskAssessment?.workActivity?.name || "Toolbox Talk Session"}
                                                            </h6>

                                                            <div className="d-flex justify-content-between align-items-center">
                                                                <div className="d-flex align-items-center gap-1 text-muted">
                                                                    <span style={{ fontSize: "12px" }}>📅</span>
                                                                    <small style={{ fontSize: "12px" }}>
                                                                        {new Date(obs.created).toLocaleDateString()}
                                                                    </small>
                                                                </div>
                                                                <div className="d-flex align-items-center gap-2">
                                                                    <span className="badge bg-danger text-white px-2 py-1" style={{ fontSize: "10px", fontWeight: "600" }}>
                                                                        👥 {obs.noOfPersonsParticipated || 0} participants
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        !loading && (
                                            <div className="text-center py-5">
                                                <div className="d-flex flex-column align-items-center">
                                                    <div className="p-4 rounded-circle mb-3 d-flex align-items-center justify-content-center" style={{ backgroundColor: "#f3f4f6", width: "80px", height: "80px" }}>
                                                        <span style={{ fontSize: "40px" }}>📢</span>
                                                    </div>
                                                    <h6 className="text-muted mb-2">No Toolbox Talks Found</h6>
                                                    <p className="text-muted small mb-0">No submitted toolbox talks available at the moment.</p>
                                                </div>
                                            </div>
                                        )
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Enhanced Modal */}
            <Modal show={showModal} onHide={() => setShowModal(false)} centered size="lg">
                <Modal.Header closeButton className="border-0 pb-0">
                    {reportData && (
                        <div className="w-100">
                            <div className="d-flex align-items-center justify-content-between">
                                <div className="d-flex align-items-center gap-3">
                                    <MyLogoComponent logo={logo} />
                                    <div>
                                        <h5 className="mb-1 fw-semibold">Toolbox Talk Details</h5>
                                        <div className="d-flex align-items-center gap-2">
                                            <span className="text-muted small">#{reportData.maskId || ''}</span>
                                            <span className={`badge px-2 py-1 ${
                                                reportData.status === "Published" ? "bg-success" :
                                                reportData.status === "Pending" ? "bg-warning text-dark" :
                                                reportData.status === "Submitted" ? "bg-info" : "bg-secondary"
                                            }`} style={{ fontSize: "11px" }}>
                                                {reportData.status}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </Modal.Header>
                <Modal.Body className="pt-2">
                    <ViewTBT formData={reportData} />
                </Modal.Body>
                <Modal.Footer className="border-0 pt-0">
                    <Button
                        variant="outline-secondary"
                        onClick={() => setShowModal(false)}
                        className="px-4"
                    >
                        ✕ Close
                    </Button>
                </Modal.Footer>
            </Modal>

            <style jsx>{`
                .observation-report {
                  background: #fff;
                  border-radius: 10px;
                  padding: 20px;
                }
                .section-title {
                  font-size: 1.1rem;
                  font-weight: bold;
                  color: #333;
                }
                .obs-title {
                  font-size: 0.9rem;
                  font-weight: bold;
                  color: #555;
                  margin-bottom: 5px;
                }
                .obs-content {
                  font-size: 0.9rem;
                  color: #777;
                }
                .image-box {
                  border: 1px solid #ddd;
                  background: #f8f9fa;
                  height: 100px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                }
                .custom-scrollbar {
                  scrollbar-width: thin;
                  scrollbar-color: #cbd5e1 #f1f5f9;
                }
                .custom-scrollbar::-webkit-scrollbar {
                  width: 6px;
                }
                .custom-scrollbar::-webkit-scrollbar-track {
                  background: #f1f5f9;
                  border-radius: 3px;
                }
                .custom-scrollbar::-webkit-scrollbar-thumb {
                  background: #cbd5e1;
                  border-radius: 3px;
                }
                .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                  background: #94a3b8;
                }
                @media (max-width: 768px) {
                  .card-body {
                    padding: 1rem !important;
                  }
                }
                @keyframes pulse {
                  0% {
                    transform: scale(1);
                    opacity: 0.3;
                  }
                  50% {
                    transform: scale(1.1);
                    opacity: 0.1;
                  }
                  100% {
                    transform: scale(1.2);
                    opacity: 0;
                  }
                }
            `}</style>
        </>
    );
};

export default New;
