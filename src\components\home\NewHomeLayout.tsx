"use client";

import React, { useEffect, useState, useCallback } from "react";
import { serviceActions } from "@/store/services";
import { loginActions } from "@/store/login-slice";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import Link from "next/link";
import Action from "./Action";
import OfflineIndicator from "@/components/offline/OfflineIndicator";
// import CachedDataBadge from "@/components/offline/CachedDataBadge";
import { offlineAPI } from "@/services/offlineAPI";
import API from "@/services/API";
import { FILE_DOWNLOAD, CURRENT_USER_URL } from "@/constant";

// Notification-related imports
interface Notification {
  id: string;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error" | "urgent";
  timestamp: string;
  isRead: boolean;
  category: string;
  priority: "low" | "medium" | "high" | "urgent";
  actionUrl?: string;
  data?: any;
}

interface ServiceType {
  id: string;
  name: string;
  description: string;
  maskName: string;
  applicability: string;
  status: boolean;
  url: string;
  created: string;
  updated: string;
  mobileShortName: string;
  color: string;
  icon: string;
}

interface ActionType {
  id: string;
  actionType: string;
  application: string;
  applicationId: string;
  counter: number;
  created: string;
  description: string;
  dueDate: string;
  maskId: string;
  observationCategory: string;
  observationType: string;
  status: string;
  actionToBeTaken: string;
  submittedBy?: { firstName?: string };
  applicationDetails?: any;
}

const iconMapping: Record<string, string> = {
  RA: "bi bi-shield-exclamation", // Risk Assessment - Shield with exclamation
  "EPTW-GEN": "bi bi-file-earmark", // E-Permit to Work - Document icon
  IR: "bi bi-exclamation-triangle-fill", // Incident Reporting - Warning triangle
  OBS: "bi bi-eye-fill", // Observation Reporting - Eye icon
  INC: "bi bi-exclamation-triangle-fill", // Incident - Warning triangle
  KNOWLEDGE: "bi bi-folder-fill", // Document Management - Folder icon
  TBT: "bi bi-chat-dots-fill", // TBT - Chat dots
  OTT: "bi bi-list-task", // Task list
  INCINV: "bi bi-briefcase-fill", // Investigation - Briefcase
  INS: "bi bi-clipboard-check-fill", // Inspection - Clipboard check
  DOC: "bi bi-folder-fill", // Document Management - Folder icon
  GC: "bi bi-hand-thumbs-up-fill", // Good catch
  AUDIT: "bi bi-check-circle-fill", // Audit - Checkmark circle
  STATS: "bi bi-bar-chart-fill", // Statistic Report - Chart icon
  TRACK: "bi bi-geo-alt-fill", // AZ Track - Location pin
};

// Note: hexToLightBackground function removed as we now use direct color + '20' approach

// Use actual service color from API or fallback to predefined colors
const getServiceIconColor = (service: ServiceType): string => {
  // First try to use the color from the API
  if (service.color && service.color.startsWith("#")) {
    return service.color;
  }

  // Fallback to predefined colors based on maskName
  const colorMap: Record<string, string> = {
    RA: "#FF6B6B", // Red for Risk Assessment
    "EPTW-GEN": "#4ECDC4", // Teal for E-Permit
    IR: "#FFE66D", // Yellow for Incident Reporting
    OBS: "#4DABF7", // Blue for Observation
    INC: "#FFE66D", // Yellow for Incident
    KNOWLEDGE: "#51CF66", // Green for Document Management
    TBT: "#9775FA", // Purple for TBT
    OTT: "#FF8CC8", // Pink for Task
    INCINV: "#FF9F43", // Orange for Investigation
    INS: "#51CF66", // Green for Inspection
    DOC: "#51CF66", // Green for Documents
    GC: "#51CF66", // Green for Good Catch
    AUDIT: "#FF8CC8", // Pink for Audit
    STATS: "#9775FA", // Purple for Statistics
    TRACK: "#4ECDC4", // Teal for Track
  };
  return colorMap[service.maskName] || "#667eea";
};

// Note: getServiceIconBackground function removed as we now use direct color + '20' approach

// Helper function to create a mock service object for TBT
const createTBTService = (): ServiceType => ({
  id: "tbt",
  name: "TBT",
  description: "Toolbox Talk",
  maskName: "TBT",
  applicability: "",
  status: true,
  url: "/tbt",
  created: "",
  updated: "",
  mobileShortName: "TBT",
  color: "#9775FA",
  icon: "bi bi-chat-dots-fill",
});

// Mock drafts for demonstration
const createMockDrafts = (): Draft[] => [
  {
    id: "1",
    title: "Safety Incident Report - Slip and Fall",
    description:
      "Employee slipped on wet floor in cafeteria area. Minor injury reported.",
    type: "incident",
    module: "Incident Reporting",
    progress: 75,
    lastModified: "2 hours ago",
    createdDate: "2024-01-15T09:00:00Z",
    status: "in-progress",
    data: {
      location: "Cafeteria",
      severity: "Minor",
      witnesses: ["John Doe", "Jane Smith"],
    },
  },
  {
    id: "2",
    title: "Chemical Storage Risk Assessment",
    description:
      "Quarterly assessment of chemical storage facility compliance and safety measures.",
    type: "risk-assessment",
    module: "Risk Assessment",
    progress: 45,
    lastModified: "1 day ago",
    createdDate: "2024-01-14T14:00:00Z",
    status: "in-progress",
    data: {
      facility: "Chemical Storage Unit A",
      assessor: "Safety Team",
      riskLevel: "Medium",
    },
  },
  {
    id: "3",
    title: "Monthly Safety Audit - Production Floor",
    description:
      "Comprehensive safety audit of production floor operations and equipment.",
    type: "audit",
    module: "Audit",
    progress: 20,
    lastModified: "3 days ago",
    createdDate: "2024-01-13T08:00:00Z",
    status: "draft",
    data: {
      area: "Production Floor",
      auditor: "Safety Inspector",
      scheduledDate: "2024-01-20",
    },
  },
  {
    id: "4",
    title: "Hot Work Permit - Welding Operations",
    description: "Permit application for welding work in maintenance area.",
    type: "e-permit",
    module: "E-Permit to Work",
    progress: 60,
    lastModified: "5 days ago",
    createdDate: "2024-01-12T13:00:00Z",
    status: "in-progress",
    data: {
      workType: "Welding",
      location: "Maintenance Bay 2",
      duration: "4 hours",
      supervisor: "Mike Johnson",
    },
  },
  {
    id: "5",
    title: "Near Miss Observation - Equipment Malfunction",
    description:
      "Observation report for equipment malfunction that could have caused injury.",
    type: "observation",
    module: "Observation Reporting",
    progress: 90,
    lastModified: "1 week ago",
    createdDate: "2024-01-11T08:45:00Z",
    status: "in-progress",
    data: {
      equipment: "Conveyor Belt #3",
      observer: "Line Supervisor",
      severity: "High",
    },
  },
  {
    id: "6",
    title: "Fire Safety Equipment Inspection",
    description:
      "Monthly inspection of fire extinguishers and emergency equipment.",
    type: "inspection",
    module: "Inspection Management",
    progress: 30,
    lastModified: "1 week ago",
    createdDate: "2024-01-10T14:00:00Z",
    status: "draft",
    data: {
      equipmentType: "Fire Safety",
      inspector: "Safety Officer",
      nextDue: "2024-02-10",
    },
  },
];

// Mock services for demonstration when API fails
const createMockServices = (): ServiceType[] => [
  {
    id: "1",
    name: "Risk Assessment",
    description: "Risk Assessment Module",
    maskName: "RA",
    applicability: "All",
    status: true,
    url: "/risk-assessment",
    created: "",
    updated: "",
    mobileShortName: "Risk Assessment",
    color: "#FF6B6B",
    icon: "bi bi-shield-exclamation",
  },
  {
    id: "2",
    name: "E-Permit to Work",
    description: "Electronic Permit to Work",
    maskName: "EPTW-GEN",
    applicability: "All",
    status: true,
    url: "/permit",
    created: "",
    updated: "",
    mobileShortName: "E-Permit",
    color: "#4ECDC4",
    icon: "bi bi-file-earmark",
  },
  {
    id: "3",
    name: "Incident Reporting",
    description: "Report Incidents",
    maskName: "IR",
    applicability: "All",
    status: true,
    url: "/incident",
    created: "",
    updated: "",
    mobileShortName: "Incident",
    color: "#FFE66D",
    icon: "bi bi-exclamation-triangle-fill",
  },
  {
    id: "4",
    name: "Observation",
    description: "Safety Observations",
    maskName: "OBS",
    applicability: "All",
    status: true,
    url: "/observation",
    created: "",
    updated: "",
    mobileShortName: "Observation",
    color: "#4DABF7",
    icon: "bi bi-eye-fill",
  },
  {
    id: "5",
    name: "Inspection",
    description: "Safety Inspections",
    maskName: "INS",
    applicability: "All",
    status: true,
    url: "/inspection",
    created: "",
    updated: "",
    mobileShortName: "Inspection",
    color: "#51CF66",
    icon: "bi bi-clipboard-check-fill",
  },
  {
    id: "6",
    name: "Documents",
    description: "Document Management",
    maskName: "DOC",
    applicability: "All",
    status: true,
    url: "/documents",
    created: "",
    updated: "",
    mobileShortName: "Documents",
    color: "#51CF66",
    icon: "bi bi-folder-fill",
  },
];

// Draft interface
interface Draft {
  id: string;
  title: string;
  description: string;
  type:
    | "incident"
    | "risk-assessment"
    | "audit"
    | "e-permit"
    | "observation"
    | "inspection";
  module: string;
  progress: number;
  lastModified: string;
  createdDate: string;
  status: "draft" | "in-progress" | "completed";
  data?: any;
}

const NewHomeLayout: React.FC = () => {
  const [services, setServices] = useState<ServiceType[]>([]);
  const [actions, setActions] = useState<ActionType[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [actionsLoading, setActionsLoading] = useState<boolean>(true);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<string>("All");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [logo, setLogo] = useState<string>("");
  const [username, setUsername] = useState<string>("");

  // Draft-related state
  const [activeTab, setActiveTab] = useState<"actions" | "drafts">("actions");
  const [drafts, setDrafts] = useState<Draft[]>([]);
  const [draftsSearchQuery, setDraftsSearchQuery] = useState<string>("");
  const [draftsFilterModule, setDraftsFilterModule] = useState<string>("All");
  const [showDraftsFilterModal, setShowDraftsFilterModal] = useState(false);

  // Notification state
  const [notificationCount, setNotificationCount] = useState<number>(3); // Mock notification count
  const [showNotificationPopup, setShowNotificationPopup] =
    useState<boolean>(false);
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: "1",
      title: "Safety Alert",
      message:
        "Emergency drill scheduled for tomorrow at 2 PM. All personnel must participate.",
      type: "warning",
      timestamp: "2024-01-15T14:30:00Z",
      isRead: false,
      category: "Safety",
      priority: "high",
      actionUrl: "/safety/emergency-drill",
    },
    {
      id: "2",
      title: "Incident Report Submitted",
      message:
        "Your incident report #IR-2024-001 has been successfully submitted and is under review.",
      type: "success",
      timestamp: "2024-01-15T10:15:00Z",
      isRead: false,
      category: "Incident",
      priority: "medium",
      actionUrl: "/incident/IR-2024-001",
    },
    {
      id: "3",
      title: "Risk Assessment Due",
      message:
        "Chemical storage risk assessment is due for renewal within 7 days.",
      type: "warning",
      timestamp: "2024-01-14T16:45:00Z",
      isRead: true,
      category: "Risk Assessment",
      priority: "medium",
      actionUrl: "/risk-assessment/chemical-storage",
    },
  ]);

  // Notification handler
  const handleNotificationPress = () => {
    console.log("🔔 Notification button clicked! Opening popup...");
    setShowNotificationPopup(true);
  };

  // Notification popup handlers
  const hideNotificationPopup = () => {
    setShowNotificationPopup(false);
  };

  const markNotificationAsRead = (id: string) => {
    setNotifications((prev) =>
      prev.map((notification) =>
        notification.id === id
          ? { ...notification, isRead: true }
          : notification
      )
    );
    // Update notification count
    const unreadCount = notifications.filter(
      (n) => !n.isRead && n.id !== id
    ).length;
    setNotificationCount(unreadCount);
  };

  // Helper functions for notifications
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "urgent":
        return "bi bi-exclamation-triangle-fill";
      case "warning":
        return "bi bi-exclamation-circle-fill";
      case "success":
        return "bi bi-check-circle-fill";
      case "error":
        return "bi bi-x-circle-fill";
      default:
        return "bi bi-info-circle-fill";
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case "urgent":
        return "#FF3B30";
      case "warning":
        return "#FF9500";
      case "success":
        return "#34C759";
      case "error":
        return "#FF3B30";
      default:
        return "#007AFF";
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60)
    );

    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const dispatch = useDispatch();
  const user = useSelector((state: RootState) => state.login.user);

  // Check if RA service exists in the services array
  const hasRAService = services.some((service) => service.maskName === "RA");

  const fetchServices = useCallback(async () => {
    try {
      console.log("🔄 Fetching services...");

      // Check authentication
      const token = localStorage.getItem("access_token");
      console.log("🔑 Auth token present:", !!token);

      const services = await offlineAPI.getServices();
      console.log("✅ Services fetched:", services);
      console.log("📊 Services count:", services.length);
      setServices(services);
      dispatch(serviceActions.setService(services));
      setLoading(false);
    } catch (error) {
      console.error("❌ Error fetching services:", error);
      console.error(
        "❌ Error details:",
        error instanceof Error ? error.message : String(error)
      );

      // Use mock services as fallback for demonstration
      console.log("🔄 Using mock services as fallback");
      const mockServices = createMockServices();
      setServices(mockServices);
      dispatch(serviceActions.setService(mockServices));

      setLoading(false);
    }
  }, [dispatch]);

  const fetchActions = useCallback(async () => {
    try {
      const actions = await offlineAPI.getActions();
      setActions(actions);
      setActionsLoading(false);
    } catch (error) {
      console.error("Error fetching actions:", error);
      setActionsLoading(false);
    }
  }, []);

  const getFetchLogo = useCallback(async () => {
    try {
      if (typeof window !== "undefined" && window.localStorage) {
        const logoKey = localStorage.getItem("logo");

        if (!logoKey) {
          console.warn("No logo key found in localStorage");
          return;
        }

        const response = await API.get(FILE_DOWNLOAD(logoKey), {
          headers: {
            "Content-Type": "application/json",
          },
        });

        const data = response.data;
        setLogo(data);
      } else {
        console.warn("localStorage is not available");
      }
    } catch (error) {
      console.error("Error fetching logo:", error);
    }
  }, []);

  // Function to get user information
  const getUsersInfo = useCallback(async () => {
    try {
      const response = await API.get(CURRENT_USER_URL);

      if (response.status === 200) {
        setUsername(response.data.firstName);
        dispatch(loginActions.setUser(response.data));
        console.log("✅ User data fetched:", response.data);
      } else {
        console.warn("Failed to fetch user data, status:", response.status);
      }
    } catch (e) {
      console.error("❌ Error fetching user data:", e);
    }
  }, [dispatch]);

  useEffect(() => {
    fetchServices();
    fetchActions();
    getFetchLogo();
    getUsersInfo(); // Fetch user data
    // Initialize drafts with mock data
    setDrafts(createMockDrafts());

    // Initialize notification count based on unread notifications
    const unreadCount = notifications.filter((n) => !n.isRead).length;
    setNotificationCount(unreadCount);

    // Simulate periodic notification updates (for demo purposes)
    const notificationInterval = setInterval(() => {
      // Randomly add notifications (10% chance every 30 seconds)
      if (Math.random() < 0.1) {
        setNotificationCount((prev) => Math.min(99, prev + 1));
      }
    }, 30000); // 30 seconds

    return () => clearInterval(notificationInterval);
  }, [fetchServices, fetchActions, getFetchLogo, getUsersInfo, notifications]);

  const removeAppsPrefix = (url: string) => {
    return url.replace(/^\/apps\//, "/");
  };

  const getActionCount = () => {
    return actions.length;
  };

  const getDraftCount = () => {
    return drafts.length;
  };

  // Module filter options
  const moduleFilters = [
    "All",
    "Risk Assessment",
    "Observation Reporting",
    "E-Permit to Work",
    "Incident Reporting",
    "Document Management",
    "Audit",
    "Inspection Management",
  ];

  // Filter functions for drafts
  const filteredDrafts = drafts.filter((draft) => {
    const matchesSearch =
      draft.title.toLowerCase().includes(draftsSearchQuery.toLowerCase()) ||
      draft.description.toLowerCase().includes(draftsSearchQuery.toLowerCase());
    const matchesFilter =
      draftsFilterModule === "All" || draft.module === draftsFilterModule;
    return matchesSearch && matchesFilter;
  });

  // Helper function to get draft type color
  const getDraftTypeColor = (type: string) => {
    switch (type) {
      case "incident":
        return "#FF3B30";
      case "risk-assessment":
        return "#FF9500";
      case "audit":
        return "#FF2D92";
      case "e-permit":
        return "#34C759";
      case "observation":
        return "#007AFF";
      case "inspection":
        return "#32D74B";
      default:
        return "#007AFF";
    }
  };

  // Helper function to get draft type background color
  const getDraftTypeBackgroundColor = (type: string) => {
    switch (type) {
      case "incident":
        return "#FFE5E5";
      case "risk-assessment":
        return "#FFF3E0";
      case "audit":
        return "#FCE4EC";
      case "e-permit":
        return "#E8F5E9";
      case "observation":
        return "#E3F2FD";
      case "inspection":
        return "#E8F5E9";
      default:
        return "#E3F2FD";
    }
  };

  return (
    <div
      className="d-flex flex-column"
      style={{
        backgroundColor: "#FAFBFC",
        height: "100vh",
        overflow: "hidden",
      }}
    >
      {/* Fixed Header Section */}
      <div
        style={{
          backgroundColor: "#FFFFFF",
          borderRadius: "20px",
          borderTopLeftRadius: 0,
          borderTopRightRadius: 0,
          padding: "20px",
          // marginBottom: '24px',
          boxShadow: "0 6px 12px rgba(0,0,0,0.1)",
          border: "1px solid #F0F0F0",
        }}
      >
        {/* Header Content */}
        <div className="d-flex justify-content-between align-items-center mb-3">
          {/* Company Logo */}
          <div className="flex-grow-1">
            {logo ? (
              <img
                src={logo}
                alt="Company Logo"
                style={{
                  maxWidth: "240px",
                  height: "60px",
                  objectFit: "contain",
                  borderRadius: "8px",
                }}
              />
            ) : (
              <img
                src="/assets/img/core-img/logo.png"
                alt="Company Logo"
                style={{
                  maxWidth: "240px",
                  height: "60px",
                  objectFit: "contain",
                  borderRadius: "8px",
                }}
              />
            )}
          </div>

          {/* Right side - Notification Icon */}
          <div className="position-relative">
            <button
              className="btn p-0 border-0 bg-transparent position-relative"
              onClick={handleNotificationPress}
              style={{
                cursor: "pointer",
                transition: "all 0.2s ease",
              }}
              onMouseDown={(e) => {
                e.currentTarget.style.opacity = "0.7";
                e.currentTarget.style.transform = "scale(0.95)";
              }}
              onMouseUp={(e) => {
                e.currentTarget.style.opacity = "1";
                e.currentTarget.style.transform = "scale(1)";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.opacity = "1";
                e.currentTarget.style.transform = "scale(1)";
              }}
            >
              <i
                className="bi bi-bell-fill"
                style={{ fontSize: "24px", color: "#6B7280" }}
              ></i>
              {/* Notification Badge - Clean count display */}
              {notificationCount > 0 && (
                <span
                  className="position-absolute badge d-flex align-items-center justify-content-center"
                  style={{
                    top: "-8px",
                    right: "-8px",
                    width: "20px",
                    height: "20px",
                    borderRadius: "10px",
                    fontSize: "11px",
                    fontWeight: "700",
                    backgroundColor: "#EF4444",
                    color: "white",
                    border: "2px solid white",
                    boxShadow: "0 2px 4px rgba(239, 68, 68, 0.3)",
                    lineHeight: "14px",
                    textAlign: "center",
                  }}
                >
                  {notificationCount > 99 ? "99+" : notificationCount}
                </span>
              )}
            </button>
          </div>
        </div>

        {/* User Greeting Row with Company Name Watermark */}
        <div className="d-flex justify-content-between align-items-center">
          {/* User Greeting */}
          <h6
            className="mb-0"
            style={{
              color: "#1F2937",
              fontSize: "14px", // smaller welcome text
              fontWeight: "500",
              letterSpacing: "0.3px",
              fontFamily: "'Raleway', sans-serif",
            }}
          >
            AcuiZen WorkHub welcomes,{" "}
            <span
              style={{
                fontSize: "14px", // bigger than welcome text
                fontWeight: "600", // bolder username
                fontFamily: "'Raleway', sans-serif"
              }}
            >
              {username || user?.firstName || "User"}
            </span>
          </h6>
        </div>
      </div>
      <div
        className="flex-shrink-0"
        style={{ padding: "20px", paddingBottom: "10px" }}
      >
        {/* Enhanced Header Card - Matching RN Design */}

        {/* Quick Access Section - Enhanced Mobile App Design */}
        <div style={{ marginBottom: "16px", padding: "0 4px" }}>
          <div className="d-flex justify-content-between align-items-center mb-3">
            <h6
              className="mb-0"
              style={{
                fontSize: "18px",
                fontWeight: "500",
                color: "#464646ff",
              }}
            >
              My Applications
            </h6>
            <Link
              href="/services"
              className="text-decoration-none d-flex align-items-center"
              style={{
                color: "#007AFF",
                fontSize: "12px",
                fontWeight: "600",
                padding: "4px 8px",
                borderRadius: "6px",
                backgroundColor: "rgba(0, 122, 255, 0.1)",
                transition: "all 0.2s ease",
              }}
            >
              Show All
              <i
                className="bi bi-chevron-right ms-1"
                style={{ fontSize: "12px" }}
              ></i>
            </Link>
          </div>

          {/* Quick Access Grid - Matching RN Design */}
          <div
            className="d-flex flex-wrap justify-content-between"
            style={{ gap: "8px" }}
          >
            {loading ? (
              Array.from({ length: 8 }).map((_, index) => (
                <div key={index} style={{ width: "23%", aspectRatio: "1" }}>
                  <div
                    className="bg-white d-flex flex-column align-items-center justify-content-center"
                    style={{
                      width: "100%",
                      height: "100%",
                      padding: "8px",
                      borderRadius: "12px",
                      border: "1px solid #E5E7EB",
                      boxShadow: "0 1px 2px rgba(0,0,0,0.05)",
                    }}
                  >
                    {/* skeleton circle */}
                    <div
                      style={{
                        width: 32,
                        height: 32,
                        borderRadius: "50%",
                        background: "#f2f4f7",
                        marginBottom: "6px",
                        animation: "pulse 1.5s ease-in-out infinite",
                      }}
                    />
                    <div
                      style={{
                        width: "80%",
                        height: 8,
                        backgroundColor: "#f2f4f7",
                        borderRadius: 4,
                        animation: "pulse 1.5s ease-in-out infinite",
                      }}
                    />
                  </div>
                </div>
              ))
            ) : (
              <>
                {services.slice(0, 8).map((service) => (
                  <div
                    key={service.id}
                    style={{ width: "23%", aspectRatio: "1" }}
                  >
                    <Link
                      href={removeAppsPrefix(service.url)}
                      className="text-decoration-none"
                    >
                      <div
                        className="bg-white d-flex flex-column align-items-center justify-content-center"
                        style={{
                          width: "100%",
                          height: "100%",
                          padding: "8px",
                          borderRadius: "12px",
                          border: "1px solid #E5E7EB",
                          boxShadow: "0 1px 2px rgba(0,0,0,0.05)",
                          transition: "all 0.2s ease",
                        }}
                      >
                        {/* Icon container with service color background */}
                        <div
                          className="d-flex align-items-center justify-content-center"
                          style={{
                            width: 35,
                            height: 35,
                            borderRadius: 16,
                            backgroundColor:
                              getServiceIconColor(service) + "20", // 20% opacity of service color
                            marginBottom: "6px",
                          }}
                        >
                          <i
                            className={`${
                              iconMapping[service.maskName] ||
                              "bi bi-file-earmark"
                            }`}
                            style={{
                              fontSize: 20,
                              color: getServiceIconColor(service),
                            }}
                          />
                        </div>

                        {/* Service title */}
                        <div
                          className="text-center"
                          style={{
                            fontSize: 10,
                            lineHeight: 1.2,
                            color: "#1A1A1A",
                            fontWeight: 600,
                            display: "-webkit-box",
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: "vertical",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: "100%",
                          }}
                        >
                          {service.name}
                        </div>
                      </div>
                    </Link>
                  </div>
                ))}

                {/* Show TBT tile if RA exists and tiles < 8 */}
                {hasRAService && services.length < 8 && (
                  <div style={{ width: "23%", aspectRatio: "1" }}>
                    <Link href="/tbt" className="text-decoration-none">
                      <div
                        className="bg-white d-flex flex-column align-items-center justify-content-center"
                        style={{
                          width: "100%",
                          height: "100%",
                          padding: "8px",
                          borderRadius: "12px",
                          border: "1px solid #E5E7EB",
                          boxShadow: "0 1px 2px rgba(0,0,0,0.05)",
                          transition: "all 0.2s ease",
                        }}
                      >
                        {/* TBT Icon container with service color background */}
                        <div
                          className="d-flex align-items-center justify-content-center"
                          style={{
                            width: 35,
                            height: 35,
                            borderRadius: 16,
                            backgroundColor:
                              getServiceIconColor(createTBTService()) + "20", // 20% opacity
                            marginBottom: "6px",
                          }}
                        >
                          <i
                            className="bi bi-chat-dots-fill"
                            style={{
                              fontSize: 20,
                              color: getServiceIconColor(createTBTService()),
                            }}
                          />
                        </div>
                        <div
                          className="text-center"
                          style={{
                            fontSize: 10,
                            lineHeight: 1.2,
                            color: "#1A1A1A",
                            fontWeight: 600,
                          }}
                        >
                          TBT
                        </div>
                      </div>
                    </Link>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      <div
        className="flex-grow-1 d-flex flex-column px-3"
        style={{
          overflow: "hidden",
        }}
      >
        {/* Tab Navigation - Matching RN Underline Style */}
        <div className="mb-3 flex-shrink-0">
          <div className="d-flex" style={{ borderBottom: "1px solid #E5E7EB" }}>
            {/* My Actions Tab */}
            <div className="position-relative flex-grow-1">
              <button
                className="btn w-100 border-0 bg-transparent"
                onClick={() => setActiveTab("actions")}
                style={{ padding: "16px 12px" }}
              >
                <div className="d-flex align-items-center justify-content-center">
                  <span
                    style={{
                      fontSize: "16px",
                      fontWeight: activeTab === "actions" ? "600" : "500",
                      color: activeTab === "actions" ? "#1F2937" : "#9CA3AF",
                      textAlign: "center",
                      letterSpacing: "0.1px",
                      fontFamily: "system-ui, -apple-system, sans-serif",
                    }}
                  >
                    My Actions
                  </span>
                  {getActionCount() > 0 && (
                    <span
                      className="badge rounded-pill d-flex align-items-center justify-content-center ms-2"
                      style={{
                        fontSize: "11px",
                        minWidth: "20px",
                        height: "20px",
                        padding: "0 6px",
                        fontWeight: "700",
                        lineHeight: "13px",
                        backgroundColor: "#EF4444",
                        color: "#FFFFFF",
                      }}
                    >
                      {getActionCount()}
                    </span>
                  )}
                </div>
              </button>
              {/* Active tab indicator */}
              {activeTab === "actions" && (
                <div
                  style={{
                    position: "absolute",
                    bottom: "-1px",
                    left: "0",
                    right: "0",
                    height: "2px",
                    backgroundColor: "#1976D2",
                    borderRadius: "1px",
                  }}
                ></div>
              )}
            </div>

            {/* Draft Tab */}
            <div className="position-relative flex-grow-1">
              <button
                className="btn w-100 border-0 bg-transparent"
                onClick={() => setActiveTab("drafts")}
                style={{ padding: "16px 12px" }}
              >
                <div className="d-flex align-items-center justify-content-center">
                  <span
                    style={{
                      fontSize: "16px",
                      fontWeight: activeTab === "drafts" ? "600" : "500",
                      color: activeTab === "drafts" ? "#1F2937" : "#9CA3AF",
                      textAlign: "center",
                      letterSpacing: "0.1px",
                      fontFamily: "system-ui, -apple-system, sans-serif",
                    }}
                  >
                    Draft
                  </span>
                  {getDraftCount() > 0 && (
                    <span
                      className="badge rounded-pill d-flex align-items-center justify-content-center ms-2"
                      style={{
                        fontSize: "11px",
                        minWidth: "20px",
                        height: "20px",
                        padding: "0 6px",
                        fontWeight: "700",
                        lineHeight: "13px",
                        backgroundColor: "#EF4444",
                        color: "#FFFFFF",
                      }}
                    >
                      {getDraftCount()}
                    </span>
                  )}
                </div>
              </button>
              {/* Active tab indicator */}
              {activeTab === "drafts" && (
                <div
                  style={{
                    position: "absolute",
                    bottom: "-1px",
                    left: "0",
                    right: "0",
                    height: "2px",
                    backgroundColor: "#1976D2",
                    borderRadius: "1px",
                  }}
                ></div>
              )}
            </div>
          </div>
        </div>

        {/* Search Bar - Dynamic based on active tab */}
        <div
          className="d-flex align-items-center mb-3 flex-shrink-0"
          style={{ gap: "12px" }}
        >
          <div className="flex-grow-1">
            <div
              className="d-flex align-items-center"
              style={{
                backgroundColor: "#F2F2F7",
                borderRadius: "10px",
                padding: "8px 12px",
              }}
            >
              <i
                className="bi bi-search me-2"
                style={{
                  fontSize: "20px",
                  color: "#8E8E93",
                }}
              ></i>
              <input
                type="text"
                className="form-control border-0 bg-transparent p-0"
                placeholder={
                  activeTab === "actions"
                    ? "Search actions..."
                    : "Search drafts..."
                }
                value={
                  activeTab === "actions" ? searchQuery : draftsSearchQuery
                }
                onChange={(e) => {
                  if (activeTab === "actions") {
                    setSearchQuery(e.target.value);
                  } else {
                    setDraftsSearchQuery(e.target.value);
                  }
                }}
                style={{
                  fontSize: "16px",
                  color: "#000000",
                  fontFamily: "system-ui, -apple-system, sans-serif",
                }}
              />
            </div>
          </div>
          <button
            className="btn d-flex align-items-center justify-content-center"
            onClick={() => {
              if (activeTab === "actions") {
                setShowFilterModal(true);
              } else {
                setShowDraftsFilterModal(true);
              }
            }}
            style={{
              width: "44px",
              height: "44px",
              borderRadius: "10px",
              border: `1px solid ${
                (activeTab === "actions"
                  ? selectedFilter
                  : draftsFilterModule) !== "All"
                  ? "#007AFF"
                  : "#E5E7EB"
              }`,
              backgroundColor:
                (activeTab === "actions"
                  ? selectedFilter
                  : draftsFilterModule) !== "All"
                  ? "#007AFF"
                  : "#F2F2F7",
              transition: "all 0.2s ease",
            }}
          >
            <i
              className="bi bi-sliders"
              style={{
                fontSize: "18px",
                color:
                  (activeTab === "actions"
                    ? selectedFilter
                    : draftsFilterModule) !== "All"
                    ? "#FFFFFF"
                    : "#007AFF",
              }}
            ></i>
          </button>
        </div>

        {/* Compact Filter and Search Indicators - Dynamic based on active tab */}
        {((activeTab === "actions" &&
          (selectedFilter !== "All" ||
            (searchQuery && searchQuery.trim() !== ""))) ||
          (activeTab === "drafts" &&
            (draftsFilterModule !== "All" ||
              (draftsSearchQuery && draftsSearchQuery.trim() !== "")))) && (
          <div className="d-flex align-items-center justify-content-between mt-2 mb-2 px-1 flex-shrink-0">
            <div className="d-flex align-items-center gap-2">
              {((activeTab === "actions" && selectedFilter !== "All") ||
                (activeTab === "drafts" && draftsFilterModule !== "All")) && (
                <span
                  className="badge rounded-pill d-flex align-items-center px-2 py-1"
                  style={{
                    backgroundColor: "#E3F2FD",
                    color: "#2196F3",
                    fontSize: "11px",
                    fontWeight: "500",
                  }}
                >
                  <i
                    className="bi bi-funnel-fill me-1"
                    style={{ fontSize: "10px" }}
                  ></i>
                  {activeTab === "actions"
                    ? selectedFilter
                    : draftsFilterModule}
                </span>
              )}
              {((activeTab === "actions" &&
                searchQuery &&
                searchQuery.trim() !== "") ||
                (activeTab === "drafts" &&
                  draftsSearchQuery &&
                  draftsSearchQuery.trim() !== "")) && (
                <span
                  className="badge rounded-pill d-flex align-items-center px-2 py-1"
                  style={{
                    backgroundColor: "#FFF3E0",
                    color: "#FF9800",
                    fontSize: "11px",
                    fontWeight: "500",
                  }}
                >
                  <i
                    className="bi bi-search me-1"
                    style={{ fontSize: "10px" }}
                  ></i>
                  &ldquo;
                  {activeTab === "actions" ? searchQuery : draftsSearchQuery}
                  &rdquo;
                </span>
              )}
            </div>
            <button
              className="btn btn-link p-0 text-decoration-none d-flex align-items-center"
              onClick={() => {
                if (activeTab === "actions") {
                  setSelectedFilter("All");
                  setSearchQuery("");
                } else {
                  setDraftsFilterModule("All");
                  setDraftsSearchQuery("");
                }
              }}
              style={{
                color: "#757575",
                fontSize: "11px",
              }}
            >
              <i className="bi bi-x" style={{ fontSize: "12px" }}></i>
            </button>
          </div>
        )}

        {/* Scrollable Content - Actions or Drafts */}
        <div
          className="flex-grow-1"
          style={{
            overflowY: "auto",
            paddingBottom: "80px",
          }}
        >
          {activeTab === "actions" ? (
            <Action
              externalShowModal={showFilterModal}
              externalSetShowModal={setShowFilterModal}
              selectedFilter={selectedFilter}
              setSelectedFilter={setSelectedFilter}
              searchQuery={searchQuery}
            />
          ) : (
            <div>
              {/* Draft Items */}
              {filteredDrafts.length === 0 ? (
                <div className="text-center py-5">
                  <i
                    className="bi bi-file-earmark-text"
                    style={{ fontSize: "48px", color: "#8E8E93" }}
                  ></i>
                  <p
                    className="mt-3 mb-0"
                    style={{ color: "#8E8E93", fontSize: "16px" }}
                  >
                    No drafts found
                  </p>
                </div>
              ) : (
                filteredDrafts.map((draft) => (
                  <div
                    key={draft.id}
                    className="bg-white border rounded-3 p-3 mb-3 position-relative"
                    style={{
                      borderColor: "#E5E7EB",
                      boxShadow: "0 1px 2px rgba(0,0,0,0.05)",
                      overflow: "hidden",
                    }}
                  >
                    {/* Draft Type Indicator Strip */}
                    <div
                      style={{
                        position: "absolute",
                        left: 0,
                        top: 0,
                        bottom: 0,
                        width: "4px",
                        backgroundColor: getDraftTypeColor(draft.type),
                      }}
                    ></div>

                    {/* Main Content */}
                    <div style={{ marginLeft: "8px" }}>
                      {/* Title and Progress Row */}
                      <div className="d-flex justify-content-between align-items-start mb-2">
                        <h6
                          className="mb-0 flex-grow-1"
                          style={{
                            fontSize: "16px",
                            fontWeight: "600",
                            color: "#1A1A1A",
                            lineHeight: "20px",
                          }}
                        >
                          {draft.title}
                        </h6>
                        <div
                          className="d-flex align-items-center ms-3"
                          style={{ minWidth: "80px" }}
                        >
                          {/* Progress Bar */}
                          <div
                            style={{
                              width: "40px",
                              height: "4px",
                              backgroundColor: "#E5E5E5",
                              borderRadius: "2px",
                              marginRight: "6px",
                              overflow: "hidden",
                            }}
                          >
                            <div
                              style={{
                                width: `${draft.progress}%`,
                                height: "100%",
                                backgroundColor: getDraftTypeColor(draft.type),
                                borderRadius: "2px",
                              }}
                            ></div>
                          </div>
                          {/* Progress Text */}
                          <span
                            className="badge"
                            style={{
                              fontSize: "11px",
                              fontWeight: "600",
                              minWidth: "32px",
                              padding: "2px 6px",
                              borderRadius: "10px",
                              backgroundColor: getDraftTypeBackgroundColor(
                                draft.type
                              ),
                              color: getDraftTypeColor(draft.type),
                            }}
                          >
                            {draft.progress}%
                          </span>
                        </div>
                      </div>

                      {/* Description */}
                      <p
                        className="mb-2"
                        style={{
                          fontSize: "14px",
                          color: "#666666",
                          lineHeight: "18px",
                        }}
                      >
                        {draft.description}
                      </p>

                      {/* Footer Row */}
                      <div className="d-flex justify-content-between align-items-center">
                        <small
                          style={{
                            fontSize: "12px",
                            color: "#666666",
                            fontWeight: "500",
                          }}
                        >
                          Last modified: {draft.lastModified}
                        </small>
                        <button
                          className="btn btn-sm d-flex align-items-center"
                          style={{
                            fontSize: "11px",
                            color: "#007AFF",
                            fontWeight: "600",
                            backgroundColor: "#F0F8FF",
                            border: "none",
                            borderRadius: "6px",
                            padding: "4px 8px",
                          }}
                        >
                          Continue
                          <i
                            className="bi bi-chevron-right ms-1"
                            style={{ fontSize: "10px" }}
                          ></i>
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>
      </div>

      {/* Fixed Footer Navigation */}
      <div className="flex-shrink-0 bg-white border-top shadow-sm">
        <div className="container-fluid">
          <div className="row text-center py-2">
            <div className="col">
              <Link
                href="/dashboard"
                className="text-decoration-none text-muted"
              >
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-grid-3x3-gap fs-5 mb-1"></i>
                  <small style={{ fontSize: "0.7rem" }}>Dashboard</small>
                </div>
              </Link>
            </div>
            <div className="col">
              <Link
                href="/services"
                className="text-decoration-none text-muted"
              >
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-grid fs-5 mb-1"></i>
                  <small style={{ fontSize: "0.7rem" }}>Services</small>
                </div>
              </Link>
            </div>
            <div className="col">
              <Link href="/home" className="text-decoration-none text-primary">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-house-fill fs-5 mb-1"></i>
                  <small style={{ fontSize: "0.7rem" }}>Home</small>
                </div>
              </Link>
            </div>
            <div className="col">
              <Link href="/history" className="text-decoration-none text-muted">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-clock-history fs-5 mb-1"></i>
                  <small style={{ fontSize: "0.7rem" }}>History</small>
                </div>
              </Link>
            </div>
            <div className="col">
              <Link href="/profile" className="text-decoration-none text-muted">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-person fs-5 mb-1"></i>
                  <small style={{ fontSize: "0.7rem" }}>Profile</small>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Draft Filter Modal */}
      {showDraftsFilterModal && (
        <div
          className="modal fade show d-block"
          style={{ backgroundColor: "rgba(0,0,0,0.5)" }}
          onClick={() => setShowDraftsFilterModal(false)}
        >
          <div
            className="modal-dialog modal-dialog-centered"
            onClick={(e) => e.stopPropagation()}
          >
            <div
              className="modal-content"
              style={{ borderRadius: "16px", border: "none" }}
            >
              <div
                className="modal-header border-bottom"
                style={{ borderColor: "#F0F0F0 !important" }}
              >
                <h5
                  className="modal-title"
                  style={{ fontSize: "18px", fontWeight: "600" }}
                >
                  Filter Drafts
                </h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowDraftsFilterModal(false)}
                ></button>
              </div>
              <div className="modal-body" style={{ padding: "16px" }}>
                {moduleFilters.map((filter) => (
                  <button
                    key={filter}
                    className="btn w-100 d-flex justify-content-between align-items-center mb-2"
                    style={{
                      padding: "16px",
                      borderRadius: "12px",
                      border: "1px solid #E5E7EB",
                      backgroundColor:
                        draftsFilterModule === filter
                          ? "rgba(0, 122, 255, 0.1)"
                          : "transparent",
                      color:
                        draftsFilterModule === filter ? "#007AFF" : "#000000",
                      textAlign: "left",
                    }}
                    onClick={() => {
                      setDraftsFilterModule(filter);
                      setShowDraftsFilterModal(false);
                    }}
                  >
                    <span style={{ fontSize: "16px", fontWeight: "500" }}>
                      {filter}
                    </span>
                    {draftsFilterModule === filter && (
                      <i
                        className="bi bi-check-circle-fill"
                        style={{ color: "#007AFF", fontSize: "20px" }}
                      ></i>
                    )}
                  </button>
                ))}

                {/* Clear All Filters Option */}
                {draftsFilterModule !== "All" && (
                  <button
                    className="btn w-100 d-flex align-items-center justify-content-center mt-3"
                    style={{
                      padding: "16px",
                      borderRadius: "12px",
                      backgroundColor: "#FFF2F2",
                      border: "1px solid #FFE5E5",
                      color: "#FF3B30",
                    }}
                    onClick={() => {
                      setDraftsFilterModule("All");
                      setShowDraftsFilterModal(false);
                    }}
                  >
                    <i
                      className="bi bi-x-circle me-2"
                      style={{ fontSize: "20px" }}
                    ></i>
                    <span style={{ fontSize: "16px", fontWeight: "600" }}>
                      Clear All Filters
                    </span>
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Notification Popup */}
      {showNotificationPopup && (
        <div
          className="modal fade show d-block"
          style={{
            backgroundColor: "rgba(0,0,0,0.5)",
            zIndex: 1050,
          }}
          onClick={hideNotificationPopup}
        >
          <div
            className="modal-dialog modal-dialog-centered modal-dialog-scrollable"
            style={{ maxWidth: "400px" }}
            onClick={(e) => e.stopPropagation()}
          >
            <div
              className="modal-content"
              style={{
                borderRadius: "16px",
                border: "none",
                maxHeight: "80vh",
              }}
            >
              {/* Header */}
              <div
                className="modal-header border-bottom"
                style={{ borderColor: "#F0F0F0 !important", padding: "20px" }}
              >
                <div className="d-flex align-items-center justify-content-between w-100">
                  <h5
                    className="modal-title mb-0"
                    style={{ fontSize: "20px", fontWeight: "600" }}
                  >
                    Notifications
                  </h5>
                  <div className="d-flex align-items-center gap-3">
                    {notificationCount > 0 && (
                      <span
                        className="badge rounded-pill"
                        style={{
                          backgroundColor: "#EF4444",
                          color: "white",
                          fontSize: "12px",
                          fontWeight: "600",
                          padding: "4px 8px",
                        }}
                      >
                        {notificationCount}
                      </span>
                    )}
                    <button
                      type="button"
                      className="btn-close"
                      onClick={hideNotificationPopup}
                      style={{ fontSize: "12px" }}
                    ></button>
                  </div>
                </div>
              </div>

              {/* Notifications List */}
              <div
                className="modal-body"
                style={{ padding: "0", maxHeight: "60vh", overflowY: "auto" }}
              >
                {notifications.length === 0 ? (
                  <div className="text-center py-5">
                    <i
                      className="bi bi-bell-slash"
                      style={{ fontSize: "48px", color: "#8E8E93" }}
                    ></i>
                    <p
                      className="mt-3 mb-0"
                      style={{ color: "#8E8E93", fontSize: "16px" }}
                    >
                      No notifications yet
                    </p>
                  </div>
                ) : (
                  notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className="border-bottom p-3"
                      style={{
                        backgroundColor: notification.isRead
                          ? "transparent"
                          : "#F8F9FA",
                        borderColor: "#E5E7EB !important",
                        cursor: "pointer",
                      }}
                      onClick={() => markNotificationAsRead(notification.id)}
                    >
                      <div className="d-flex align-items-start gap-3">
                        {/* Notification Icon */}
                        <div
                          className="d-flex align-items-center justify-content-center flex-shrink-0"
                          style={{
                            width: "40px",
                            height: "40px",
                            borderRadius: "20px",
                            backgroundColor:
                              getNotificationColor(notification.type) + "20",
                          }}
                        >
                          <i
                            className={getNotificationIcon(notification.type)}
                            style={{
                              fontSize: "18px",
                              color: getNotificationColor(notification.type),
                            }}
                          ></i>
                        </div>

                        {/* Notification Content */}
                        <div className="flex-grow-1">
                          <div className="d-flex justify-content-between align-items-start mb-1">
                            <h6
                              className="mb-0"
                              style={{
                                fontSize: "16px",
                                fontWeight: "600",
                                color: "#1A1A1A",
                              }}
                            >
                              {notification.title}
                            </h6>
                            {!notification.isRead && (
                              <div
                                style={{
                                  width: "8px",
                                  height: "8px",
                                  borderRadius: "4px",
                                  backgroundColor: "#007AFF",
                                  marginTop: "4px",
                                }}
                              ></div>
                            )}
                          </div>
                          <p
                            className="mb-2"
                            style={{
                              fontSize: "14px",
                              color: "#666666",
                              lineHeight: "1.4",
                            }}
                          >
                            {notification.message}
                          </p>
                          <div className="d-flex justify-content-between align-items-center">
                            <small
                              style={{
                                fontSize: "12px",
                                color: "#8E8E93",
                                fontWeight: "500",
                              }}
                            >
                              {notification.category}
                            </small>
                            <small
                              style={{ fontSize: "12px", color: "#8E8E93" }}
                            >
                              {formatTimestamp(notification.timestamp)}
                            </small>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>

              {/* Footer */}
              {notifications.length > 0 && (
                <div
                  className="modal-footer border-top"
                  style={{
                    borderColor: "#F0F0F0 !important",
                    padding: "15px 20px",
                  }}
                >
                  <button
                    className="btn btn-link text-decoration-none w-100"
                    style={{ color: "#007AFF", fontWeight: "500" }}
                    onClick={hideNotificationPopup}
                  >
                    View All Notifications
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NewHomeLayout;
