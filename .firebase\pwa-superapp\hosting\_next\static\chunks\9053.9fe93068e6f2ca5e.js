(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9053],{33248:function(e,t,r){var s;let n;"undefined"!=typeof self&&self,n=r(12115),e.exports=(()=>{"use strict";var e={156:e=>{e.exports=n}},t={};function r(s){var n=t[s];if(void 0!==n)return n.exports;var o=t[s]={exports:{}};return e[s](o,o.exports,r),o.exports}r.d=(e,t)=>{for(var s in t)r.o(t,s)&&!r.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var s={};return(()=>{r.r(s),r.d(s,{default:()=>c,useStopwatch:()=>l,useTime:()=>u,useTimer:()=>i});var e=r(156);class t{static expiryTimestamp(e){let t=new Date(e).getTime()>0;return t||console.warn("react-timer-hook: { useTimer } Invalid expiryTimestamp settings",e),t}static onExpire(e){let t=e&&"function"==typeof e;return e&&!t&&console.warn("react-timer-hook: { useTimer } Invalid onExpire settings function",e),t}}class n{static getTimeFromSeconds(e){let t=Math.ceil(e),r=Math.floor(t/86400),s=Math.floor(t%86400/3600),n=Math.floor(t%3600/60);return{totalSeconds:t,seconds:Math.floor(t%60),minutes:n,hours:s,days:r}}static getSecondsFromExpiry(e,t){let r=e-(new Date).getTime();if(r>0){let e=r/1e3;return t?Math.round(e):e}return 0}static getSecondsFromPrevTime(e,t){let r=(new Date).getTime()-e;if(r>0){let e=r/1e3;return t?Math.round(e):e}return 0}static getSecondsFromTimeNow(){let e=new Date;return e.getTime()/1e3-60*e.getTimezoneOffset()}static getFormattedTimeFromSeconds(e,t){let{seconds:r,minutes:s,hours:o}=n.getTimeFromSeconds(e),a="",i=o;return"12-hour"===t&&(a=o>=12?"pm":"am",i=o%12),{seconds:r,minutes:s,hours:i,ampm:a}}}function o(t,r){let s=(0,e.useRef)();(0,e.useEffect)(()=>{s.current=t}),(0,e.useEffect)(()=>{if(!r)return()=>{};let e=setInterval(()=>{s.current&&s.current()},r);return()=>clearInterval(e)},[r])}function a(e){if(!t.expiryTimestamp(e))return null;let r=n.getSecondsFromExpiry(e),s=Math.floor(1e3*(r-Math.floor(r)));return s>0?s:1e3}function i(){let{expiryTimestamp:r,onExpire:s,autoStart:i=!0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[l,u]=(0,e.useState)(r),[c,m]=(0,e.useState)(n.getSecondsFromExpiry(l)),[d,p]=(0,e.useState)(i),[f,g]=(0,e.useState)(i),[S,h]=(0,e.useState)(a(l)),x=(0,e.useCallback)(()=>{t.onExpire(s)&&s(),p(!1),h(null)},[s]),T=(0,e.useCallback)(()=>{p(!1)},[]),v=(0,e.useCallback)(function(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];h(a(e)),g(t),p(t),u(e),m(n.getSecondsFromExpiry(e))},[]),y=(0,e.useCallback)(()=>{let e=new Date;e.setMilliseconds(e.getMilliseconds()+1e3*c),v(e)},[c,v]),F=(0,e.useCallback)(()=>{f?(m(n.getSecondsFromExpiry(l)),p(!0)):y()},[l,f,y]);return o(()=>{1e3!==S&&h(1e3);let e=n.getSecondsFromExpiry(l);m(e),e<=0&&x()},d?S:null),{...n.getTimeFromSeconds(c),start:F,pause:T,resume:y,restart:v,isRunning:d}}function l(){let{autoStart:t,offsetTimestamp:r}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[s,a]=(0,e.useState)(n.getSecondsFromExpiry(r,!0)||0),[i,l]=(0,e.useState)(new Date),[u,c]=(0,e.useState)(s+n.getSecondsFromPrevTime(i||0,!0)),[m,d]=(0,e.useState)(t);o(()=>{c(s+n.getSecondsFromPrevTime(i,!0))},m?1e3:null);let p=(0,e.useCallback)(()=>{let e=new Date;l(e),d(!0),c(s+n.getSecondsFromPrevTime(e,!0))},[s]),f=(0,e.useCallback)(()=>{a(u),d(!1)},[u]),g=(0,e.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=n.getSecondsFromExpiry(e,!0)||0,s=new Date;l(s),a(r),d(t),c(r+n.getSecondsFromPrevTime(s,!0))},[]);return{...n.getTimeFromSeconds(u),start:p,pause:f,reset:g,isRunning:m}}function u(){let{format:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[r,s]=(0,e.useState)(n.getSecondsFromTimeNow());return o(()=>{s(n.getSecondsFromTimeNow())},1e3),{...n.getFormattedTimeFromSeconds(r,t)}}function c(t){if((0,e.useEffect)(()=>{console.warn("react-timer-hook: default export useTimer is deprecated, use named exports { useTimer, useStopwatch, useTime } instead")},[]),t.expiryTimestamp){let e=i(t);return{...e,startTimer:e.start,stopTimer:e.pause,resetTimer:()=>{}}}let r=l(t);return{...r,startTimer:r.start,stopTimer:r.pause,resetTimer:r.reset}}})(),s})()},39053:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(95155);r(12115);var n=r(33248);let o=()=>{let e=new Date("2025-12-30"),{seconds:t,minutes:r,hours:o,days:a}=(0,n.useTimer)({expiryTimestamp:e});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"days",children:a}),(0,s.jsx)("div",{className:"smalltext",children:"Days"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"hours",children:o}),(0,s.jsx)("div",{className:"smalltext",children:"Hours"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"minutes",children:r}),(0,s.jsx)("div",{className:"smalltext",children:"Minutes"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"seconds",children:t}),(0,s.jsx)("div",{className:"smalltext",children:"Seconds"})]})]})}}}]);