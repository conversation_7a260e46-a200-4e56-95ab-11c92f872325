"use client";

import fcmService from './fcmService';
import iosNotificationService from './iosNotificationService';

/**
 * Unified notification service that handles both FCM (Android/Web) 
 * and iOS-specific push notifications
 */

interface NotificationConfig {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  tag?: string;
  data?: any;
  url?: string;
}

interface DeviceInfo {
  isIOS: boolean;
  isIOSPWA: boolean;
  isAndroid: boolean;
  browser: string;
  supportsWebPush: boolean;
  recommendedMethod: 'fcm' | 'ios-web-push' | 'local-only' | 'unsupported';
}

class UnifiedNotificationService {
  private deviceInfo: DeviceInfo | null = null;

  /**
   * Initialize the unified notification service
   */
  async initialize(): Promise<boolean> {
    console.log('🔄 Initializing Unified Notification Service...');
    
    // Detect device and capabilities
    this.deviceInfo = this.detectDevice();
    
    console.log('📱 Device Info:', this.deviceInfo);

    try {
      switch (this.deviceInfo.recommendedMethod) {
        case 'fcm':
          console.log('🚀 Using FCM for notifications');
          await fcmService.initialize();
          return true;

        case 'ios-web-push':
          console.log('🍎 Using iOS Web Push for notifications');
          return iosNotificationService.isSupported();

        case 'local-only':
          console.log('📱 Using local notifications only');
          return true;

        case 'unsupported':
          console.log('❌ Push notifications not supported on this device');
          return false;

        default:
          console.log('⚠️ Unknown notification method, falling back to FCM');
          await fcmService.initialize();
          return true;
      }
    } catch (error) {
      console.error('❌ Error initializing unified notification service:', error);
      return false;
    }
  }

  /**
   * Detect device capabilities and recommend best notification method
   */
  private detectDevice(): DeviceInfo {
    const userAgent = navigator.userAgent.toLowerCase();
    
    const isIOS = /ipad|iphone|ipod/.test(userAgent);
    const isIOSPWA = isIOS && (window.navigator as any).standalone === true;
    const isAndroid = /android/.test(userAgent);
    
    let browser = 'unknown';
    if (userAgent.includes('chrome')) browser = 'chrome';
    else if (userAgent.includes('firefox')) browser = 'firefox';
    else if (userAgent.includes('safari')) browser = 'safari';
    else if (userAgent.includes('edge')) browser = 'edge';

    const supportsWebPush = 'serviceWorker' in navigator && 
                           'PushManager' in window && 
                           'Notification' in window;

    let recommendedMethod: DeviceInfo['recommendedMethod'] = 'unsupported';

    if (isIOS) {
      // iOS version check
      const iosVersionMatch = userAgent.match(/os (\d+)_(\d+)_?(\d+)?/);
      const iosVersion = iosVersionMatch ? parseInt(iosVersionMatch[1], 10) : 0;
      
      if (iosVersion >= 16 && supportsWebPush) {
        recommendedMethod = 'ios-web-push';
      } else if (supportsWebPush) {
        recommendedMethod = 'local-only';
      } else {
        recommendedMethod = 'unsupported';
      }
    } else if (isAndroid || browser === 'chrome' || browser === 'firefox' || browser === 'edge') {
      recommendedMethod = 'fcm';
    } else if (supportsWebPush) {
      recommendedMethod = 'fcm';
    }

    return {
      isIOS,
      isIOSPWA,
      isAndroid,
      browser,
      supportsWebPush,
      recommendedMethod
    };
  }

  /**
   * Request notification permission using the appropriate method
   */
  async requestPermission(): Promise<boolean> {
    if (!this.deviceInfo) {
      await this.initialize();
    }

    console.log(`🔔 Requesting permission using: ${this.deviceInfo?.recommendedMethod}`);

    try {
      switch (this.deviceInfo?.recommendedMethod) {
        case 'fcm':
          return await fcmService.requestNotificationPermission();

        case 'ios-web-push':
          return await iosNotificationService.requestPermission();

        case 'local-only':
          if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            return permission === 'granted';
          }
          return false;

        case 'unsupported':
          console.log('❌ Notifications not supported on this device');
          this.showUnsupportedMessage();
          return false;

        default:
          return false;
      }
    } catch (error) {
      console.error('❌ Error requesting notification permission:', error);
      return false;
    }
  }

  /**
   * Generate/register for push notifications
   */
  async registerForNotifications(): Promise<string | null> {
    if (!this.deviceInfo) {
      await this.initialize();
    }

    try {
      switch (this.deviceInfo?.recommendedMethod) {
        case 'fcm':
          return await fcmService.generateFCMToken();

        case 'ios-web-push':
          return await iosNotificationService.registerForPushNotifications();

        case 'local-only':
          // For local-only, we just return a dummy token to indicate success
          if (Notification.permission === 'granted') {
            return 'local-notifications-enabled';
          }
          return null;

        case 'unsupported':
          return null;

        default:
          return null;
      }
    } catch (error) {
      console.error('❌ Error registering for notifications:', error);
      return null;
    }
  }

  /**
   * Show a test notification
   */
  async showTestNotification(): Promise<boolean> {
    const config: NotificationConfig = {
      title: 'Test Notification',
      body: 'Your notifications are working correctly!',
      tag: 'test-notification',
      data: { test: true }
    };

    return await this.showLocalNotification(config);
  }

  /**
   * Show a local notification (for foreground or testing)
   */
  async showLocalNotification(config: NotificationConfig): Promise<boolean> {
    if (!this.deviceInfo) {
      await this.initialize();
    }

    if (Notification.permission !== 'granted') {
      console.log('❌ Cannot show notification: permission not granted');
      return false;
    }

    try {
      switch (this.deviceInfo?.recommendedMethod) {
        case 'ios-web-push':
        case 'local-only':
          return await iosNotificationService.showLocalNotification(config);

        case 'fcm':
          // Use FCM's local notification method
          fcmService.showNotification({
            notification: {
              title: config.title,
              body: config.body,
              icon: config.icon
            },
            data: config.data
          });
          return true;

        default:
          // Fallback to basic Notification API
          const notification = new Notification(config.title, {
            body: config.body,
            icon: config.icon || '/assets/icons/Icon-192.png',
            tag: config.tag
          });

          notification.onclick = () => {
            if (config.url) {
              window.open(config.url, '_blank');
            }
            notification.close();
          };

          return true;
      }
    } catch (error) {
      console.error('❌ Error showing local notification:', error);
      return false;
    }
  }

  /**
   * Get current permission status
   */
  getPermissionStatus(): NotificationPermission {
    if (typeof window === 'undefined' || !('Notification' in window)) {
      return 'denied';
    }
    return Notification.permission;
  }

  /**
   * Check if notifications are supported
   */
  isSupported(): boolean {
    return this.deviceInfo?.recommendedMethod !== 'unsupported';
  }

  /**
   * Get device information
   */
  getDeviceInfo(): DeviceInfo | null {
    return this.deviceInfo;
  }

  /**
   * Show message for unsupported devices
   */
  private showUnsupportedMessage(): void {
    console.log('📱 Notification Support Information:');
    console.log('Your device/browser combination does not support push notifications.');
    console.log('Supported combinations:');
    console.log('• Android: Chrome, Firefox, Edge');
    console.log('• iOS: Safari 16.4+ (limited support)');
    console.log('• Desktop: Chrome, Firefox, Edge, Safari');
  }

  /**
   * Get user-friendly status message
   */
  getStatusMessage(): string {
    if (!this.deviceInfo) {
      return 'Checking notification support...';
    }

    const permission = this.getPermissionStatus();
    
    if (permission === 'granted') {
      return `Notifications enabled (${this.deviceInfo.recommendedMethod})`;
    } else if (permission === 'denied') {
      return 'Notifications blocked - please enable in browser settings';
    } else if (this.deviceInfo.recommendedMethod === 'unsupported') {
      return 'Notifications not supported on this device';
    } else {
      return 'Click to enable notifications';
    }
  }

  /**
   * Get setup instructions for current device
   */
  getSetupInstructions(): string[] {
    if (!this.deviceInfo) {
      return ['Checking device compatibility...'];
    }

    const instructions: string[] = [];

    if (this.deviceInfo.isIOS) {
      instructions.push('iOS Setup:');
      instructions.push('1. Ensure you have iOS 16.4 or later');
      instructions.push('2. Open this site in Safari (not Chrome or other browsers)');
      instructions.push('3. Tap the share button and "Add to Home Screen"');
      instructions.push('4. Open the app from your home screen');
      instructions.push('5. Allow notifications when prompted');
    } else if (this.deviceInfo.isAndroid) {
      instructions.push('Android Setup:');
      instructions.push('1. Allow notifications when prompted');
      instructions.push('2. If blocked, go to browser settings > Site settings > Notifications');
      instructions.push('3. Find this site and set to "Allow"');
    } else {
      instructions.push('Desktop Setup:');
      instructions.push('1. Allow notifications when prompted');
      instructions.push('2. If blocked, click the lock icon in address bar');
      instructions.push('3. Set notifications to "Allow"');
    }

    return instructions;
  }
}

// Export singleton instance
export const unifiedNotificationService = new UnifiedNotificationService();
export default unifiedNotificationService;
