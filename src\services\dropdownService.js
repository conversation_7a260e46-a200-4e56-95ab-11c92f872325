import API from "./API";
import { DROPDOWNS, ADMINDRO<PERSON><PERSON>NS } from '../constants'

export const fetchDropdownData = async (maskId, setState, additionalCondition,) => {
    try {
        // Prepare the base URI
        let uriString = {
            where: { maskId },
            include: [{ relation: "dropdownItems" }]
        };

        // Check for the 'General' condition
        if (additionalCondition?.type === 'General') {
            uriString.where = { ...uriString.where, ...additionalCondition };
        }

        // Determine URL based on condition type
        const url = `${additionalCondition?.type === 'General' ? DROPDOWNS : ADMINDROPDOWNS}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
        const response = await API.get(url);

        if (response.status === 200) {
            // Map the response to dropdown items
            const data = response.data[0].dropdownItems.map((item) => ({
                label: item.name,
                value: item.id,
                desc: item.description
            }));
            setState(data);

        }
    } catch (error) {
        console.error(`Error fetching ${maskId} list:`, error);
        // Optionally, add user feedback (e.g., notification)
    }
};
