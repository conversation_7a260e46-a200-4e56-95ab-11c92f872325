"use client";

import { FILE_DOWNLOAD, CURRENT_USER_URL } from "@/constant";
import { useDarkMode } from "@/hooks/useDarkMode";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import axios from 'axios'
import API from '../../services/API'
import { useDispatch } from "react-redux";
import { loginActions } from "@/store/login-slice";
import { useRouter } from "next/navigation";
import Image from "next/image";
const HeaderTwo = () => {
	if (typeof window !== "undefined") {
		require("bootstrap/dist/js/bootstrap");
	}
	const [logo, setLogo] = useState('')
	const [userName, setUsername] = useState('')
	const dispatch = useDispatch()
	const router = useRouter();

	const { theme, handleDarkModeToggle } = useDarkMode();

	useEffect(() => {
		getFetchLogo()
		getUsersInfo()

	}, [])

	const getFetchLogo = async () => {
		try {
			if (typeof window !== "undefined" && window.localStorage) { // Ensure localStorage is available
				const logoKey = localStorage.getItem('logo');
			

				if (!logoKey) {
					console.warn("No logo key found in localStorage");
					return;
				}

				const response = await API.get(FILE_DOWNLOAD(logoKey), {
					headers: {
						'Content-Type': 'application/json'
					},
				});

				const data = response.data;
				setLogo(data); // Assuming the API response is the correct format
			} else {
				console.warn("localStorage is not available");
			}
		} catch (error) {
			console.error('Error fetching logo:', error);
		}
	};


	const getUsersInfo = async () => {
		try {
			const response = await API.get(CURRENT_USER_URL);

			if (response.status === 200) {
				setUsername(response.data.firstName)
				dispatch(loginActions.setUser(response.data))

			} else {
				router.push('/')
			}
		} catch (e) {
			console.log(e)
		}
	}
	const handleLogout = () => {
		// 2) Remove the desired items from localStorage
		localStorage.removeItem("access_token");
		localStorage.removeItem("refresh_token");
		localStorage.removeItem("COGNITO_USER_DOMAIN");
		localStorage.removeItem("enterprise_id");

		// 3) Optionally, redirect the user to login or home
		router.push("/");
	};
	return (
		<>

			<div className="header-area" id="headerArea">
				<div className="container">

					<div className="header-content position-relative d-flex align-items-center justify-content-between">
						{/* <!-- Navbar Toggler --> */}
						<div className="navbar--toggler"
							id="affanNavbarToggler"
							data-bs-toggle="offcanvas"
							data-bs-target="#affanOffcanvas"
							aria-controls="affanOffcanvas">
							<span className="d-block"></span>
							<span className="d-block"></span>
							<span className="d-block"></span>
						</div>

						{/* <!-- Logo Wrapper --> */}
						<div className="logo-wrapper text-center">
							<Link href="/home">
								<img src={logo} alt="" />
							</Link>
							<div className="element-heading">
								<h6>AcuiZen WorkHub</h6>
							</div>

						</div>

						{/* <!-- Settings --> */}
						<div className="setting-wrapper">
							<div className="setting-trigger-btn" id="settingTriggerBtn2">
								<i className="bi bi-bell"></i>
								<span></span>
							</div>
						</div>
					</div>

				</div>
			</div>

			<div
				className="offcanvas offcanvas-start"
				id="affanOffcanvas"
				data-bs-scroll="true"
				tabIndex={-1}
				aria-labelledby="affanOffcanvsLabel"
			>
				<button
					className="btn-close btn-close-white text-reset"
					type="button"
					data-bs-dismiss="offcanvas"
					aria-label="Close"
				></button>

				<div className="offcanvas-body p-0">
					<div className="sidenav-wrapper">
						<div className="sidenav-profile bg-gradient">
							<div className="sidenav-style1"></div>

							<div className="user-profile">
								{/* <Image src="/assets/img/bg-img/2.jpg" alt=""  width={100} height={100}/> */}
							</div>

							<div className="user-info">
								<h6 className="user-name mb-0">{userName}</h6>
								{/* <span>CEO, Designing World</span> */}
							</div>
						</div>

						<ul className="sidenav-nav ps-0">
							<li>
								<Link href="/home">
									<i className="bi bi-house-door"></i> Home
								</Link>
							</li>

							<li>
								<div className="night-mode-nav">
									<i className="bi bi-moon"></i>
									{theme === "dark" ? "Light" : "Dark"} Mode
									<div className="form-check form-switch">
										<input
											className="form-check-input form-check-success"
											id="darkSwitch"
											type="checkbox"
											checked={theme === "dark"}
											onChange={handleDarkModeToggle}
										/>
									</div>
								</div>
							</li>
							<li>
								<button
									className="btn w-100 text-start"
									onClick={handleLogout}
									style={{ border: "none", background: "none",padding:'10px 22px ' }}
								>
									<i className="bi bi-box-arrow-right" style={{fontSize:'20px',paddingRight:10}}></i> Logout
								</button>
							</li>
						</ul>



						<div className="copyright-info">
							<p>
								<span id="copyrightYear"></span>
								{new Date().getFullYear()}	© Made by <a target="_blank" href="https://www.acuizen.com">AcuiZen</a>
							</p>
						</div>
					</div>
				</div>
			</div>

		</>
	);
};

export default HeaderTwo;