"use client";

import React from 'react';
import Link from 'next/link';

interface OfflineFallbackProps {
  title?: string;
  message?: string;
  showHomeLink?: boolean;
  className?: string;
}

const OfflineFallback: React.FC<OfflineFallbackProps> = ({
  title = "Page Unavailable Offline",
  message = "This page requires an internet connection. Please check your connection and try again.",
  showHomeLink = true,
  className = ''
}) => {
  return (
    <div className={`container-fluid px-3 py-5 ${className}`}>
      <div className="text-center">
        {/* Offline Icon */}
        <div
          className="mx-auto mb-4 d-flex align-items-center justify-content-center"
          style={{
            width: '80px',
            height: '80px',
            borderRadius: '50%',
            backgroundColor: '#fff3e0',
            color: '#f57c00'
          }}
        >
          <i className="bi bi-wifi-off" style={{ fontSize: '32px' }}></i>
        </div>

        {/* Title */}
        <h4 className="mb-3" style={{ fontWeight: '600', color: '#333' }}>
          {title}
        </h4>

        {/* Message */}
        <p className="text-muted mb-4" style={{ fontSize: '14px', lineHeight: '1.6' }}>
          {message}
        </p>

        {/* Available Offline Features */}
        <div className="bg-light rounded-3 p-3 mb-4">
          <h6 className="mb-3" style={{ fontSize: '14px', fontWeight: '600' }}>
            Available Offline:
          </h6>
          <div className="row g-2">
            <div className="col-6">
              <Link href="/home" className="text-decoration-none">
                <div className="bg-white rounded-2 p-2 text-center">
                  <i className="bi bi-house text-primary mb-1" style={{ fontSize: '16px' }}></i>
                  <div style={{ fontSize: '12px', color: '#333' }}>Home</div>
                </div>
              </Link>
            </div>
            <div className="col-6">
              <Link href="/services" className="text-decoration-none">
                <div className="bg-white rounded-2 p-2 text-center">
                  <i className="bi bi-grid text-success mb-1" style={{ fontSize: '16px' }}></i>
                  <div style={{ fontSize: '12px', color: '#333' }}>Services</div>
                </div>
              </Link>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="d-flex flex-column gap-2">
          <button
            className="btn btn-primary"
            onClick={() => window.location.reload()}
            style={{ borderRadius: '12px' }}
          >
            <i className="bi bi-arrow-clockwise me-2"></i>
            Try Again
          </button>
          
          {showHomeLink && (
            <Link href="/home" className="btn btn-outline-primary" style={{ borderRadius: '12px' }}>
              <i className="bi bi-house me-2"></i>
              Go to Home
            </Link>
          )}
        </div>

        {/* Connection Status */}
        <div className="mt-4 pt-3 border-top">
          <div className="d-flex align-items-center justify-content-center">
            <div
              className="rounded-circle d-flex align-items-center justify-content-center me-2"
              style={{
                width: '16px',
                height: '16px',
                backgroundColor: '#f44336',
                color: 'white'
              }}
            >
              <i className="bi bi-wifi-off" style={{ fontSize: '8px' }}></i>
            </div>
            <small className="text-muted">
              No internet connection
            </small>
          </div>
        </div>
      </div>

      {/* Auto-reload when online */}
      <script
        dangerouslySetInnerHTML={{
          __html: `
            window.addEventListener('online', function() {
              console.log('🌐 Back online! Reloading...');
              window.location.reload();
            });
          `
        }}
      />
    </div>
  );
};

export default OfflineFallback;
