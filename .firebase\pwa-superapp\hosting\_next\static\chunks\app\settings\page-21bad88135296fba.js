(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4662],{6043:(e,s,i)=>{"use strict";i.d(s,{default:()=>b});var o=i(95155),t=i(9e4),n=i(38808),r=i(38983),a=i(46554),l=i(12115),c=i(57111),d=i(60902),g=i(91867),m=i(94016),u=i(15123);let h=e=>{let{onTokenGenerated:s,showCard:i=!0}=e,[t,n]=(0,l.useState)("default"),[r,a]=(0,l.useState)(null),[h,p]=(0,l.useState)(!1),[f,b]=(0,l.useState)(null),[v,k]=(0,l.useState)(!0);(0,l.useEffect)(()=>{x(),j(),y()},[]);let x=()=>{"Notification"in window||(k(!1),b("Push notifications are not supported in this browser"))},j=()=>{"Notification"in window&&n(Notification.permission)},y=async()=>{try{let e=await u.A.getStoredToken();e&&(a(e),null==s||s(e))}catch(e){console.error("Error loading stored token:",e)}},w=async()=>{if(v){p(!0),b(null);try{await u.A.initialize();let e=await u.A.generateFCMToken();e?(a(e),n("granted"),null==s||s(e),console.log("✅ Push notifications enabled successfully!")):b("Failed to generate FCM token. Please try again.")}catch(e){console.error("Error enabling notifications:",e),b(e.message||"Failed to enable push notifications")}finally{p(!1)}}},N=async()=>{p(!0),b(null);try{let e=await u.A.refreshToken();e?(a(e),null==s||s(e),console.log("✅ FCM token refreshed successfully!")):b("Failed to refresh FCM token")}catch(e){console.error("Error refreshing token:",e),b(e.message||"Failed to refresh token")}finally{p(!1)}},S=()=>{r&&(navigator.clipboard.writeText(r),console.log("\uD83D\uDCCB FCM token copied to clipboard"))},F=()=>(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("div",{className:"d-flex align-items-center justify-content-between mb-3",children:[(0,o.jsx)("h6",{className:"mb-0",children:"Push Notifications"}),(0,o.jsxs)("div",{className:"d-flex align-items-center",children:["granted"===t&&(0,o.jsxs)("span",{className:"badge bg-success me-2",children:[(0,o.jsx)("i",{className:"bi bi-check-circle me-1"}),"Enabled"]}),"denied"===t&&(0,o.jsxs)("span",{className:"badge bg-danger me-2",children:[(0,o.jsx)("i",{className:"bi bi-x-circle me-1"}),"Blocked"]}),"default"===t&&(0,o.jsxs)("span",{className:"badge bg-warning me-2",children:[(0,o.jsx)("i",{className:"bi bi-question-circle me-1"}),"Not Set"]})]})]}),!v&&(0,o.jsxs)(c.A,{variant:"warning",children:[(0,o.jsx)("i",{className:"bi bi-exclamation-triangle me-2"}),"Push notifications are not supported in this browser."]}),f&&(0,o.jsxs)(c.A,{variant:"danger",dismissible:!0,onClose:()=>b(null),children:[(0,o.jsx)("i",{className:"bi bi-exclamation-circle me-2"}),f]}),v&&"default"===t&&(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("p",{className:"text-muted mb-3",children:"Enable push notifications to receive important updates and alerts."}),(0,o.jsx)(d.A,{variant:"primary",onClick:w,disabled:h,className:"d-flex align-items-center mx-auto",children:h?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(g.A,{animation:"border",size:"sm",className:"me-2"}),"Enabling..."]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"bi bi-bell me-2"}),"Enable Notifications"]})})]}),"denied"===t&&(0,o.jsxs)(c.A,{variant:"info",children:[(0,o.jsx)("i",{className:"bi bi-info-circle me-2"}),"Notifications are blocked. Please enable them in your browser settings and refresh the page."]}),"granted"===t&&r&&(0,o.jsxs)("div",{children:[(0,o.jsxs)(c.A,{variant:"success",children:[(0,o.jsx)("i",{className:"bi bi-check-circle me-2"}),"Push notifications are enabled! You'll receive important updates."]}),(0,o.jsxs)("div",{className:"d-flex gap-2 mb-3",children:[(0,o.jsx)(d.A,{variant:"outline-secondary",size:"sm",onClick:N,disabled:h,children:h?(0,o.jsx)(g.A,{animation:"border",size:"sm"}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"bi bi-arrow-clockwise me-1"}),"Refresh Token"]})}),(0,o.jsxs)(d.A,{variant:"outline-info",size:"sm",onClick:S,children:[(0,o.jsx)("i",{className:"bi bi-clipboard me-1"}),"Copy Token"]})]}),(0,o.jsxs)("details",{className:"mt-3",children:[(0,o.jsx)("summary",{className:"text-muted",style:{cursor:"pointer"},children:(0,o.jsx)("small",{children:"View FCM Token (for developers)"})}),(0,o.jsx)("div",{className:"mt-2 p-2 bg-light rounded",children:(0,o.jsx)("small",{className:"font-monospace text-break",children:r})})]})]})]});return i?(0,o.jsx)(m.A,{className:"notification-permission-card",children:(0,o.jsx)(m.A.Body,{children:F()})}):(0,o.jsx)("div",{className:"notification-permission",children:F()})};var p=i(6874),f=i.n(p);let b=()=>{let{theme:e,handleDarkModeToggle:s}=(0,t.D)(),{viewMode:i,handleRTLToggling:l}=(0,n.L)();return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(a.default,{heading:""}),(0,o.jsx)("div",{className:"page-content-wrapper py-3",children:(0,o.jsxs)("div",{className:"container",children:[!1,(0,o.jsx)(h,{onTokenGenerated:e=>{console.log("FCM Token generated in Settings:",e)}}),(0,o.jsx)("div",{className:"card mb-3 shadow-sm",children:(0,o.jsxs)("div",{className:"card-body direction-rtl",children:[(0,o.jsx)("p",{className:"mb-2",children:"Settings"}),(0,o.jsx)("div",{className:"single-setting-panel",children:(0,o.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,o.jsx)("input",{className:"form-check-input",type:"checkbox",id:"flexSwitchCheckDefault",defaultChecked:!0}),(0,o.jsx)("label",{className:"form-check-label",htmlFor:"flexSwitchCheckDefault",children:"Availability Status"})]})}),(0,o.jsx)("div",{className:"single-setting-panel",children:(0,o.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,o.jsx)("input",{className:"form-check-input",type:"checkbox",id:"flexSwitchCheckDefault2",defaultChecked:!0}),(0,o.jsx)("label",{className:"form-check-label",htmlFor:"flexSwitchCheckDefault2",children:"Send Me Notifications"})]})}),(0,o.jsx)("div",{className:"single-setting-panel",children:(0,o.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,o.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===e,onChange:s}),(0,o.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===e?"Light":"Dark"," mode"]})]})}),(0,o.jsx)("div",{className:"single-setting-panel",children:(0,o.jsxs)("div",{className:"form-check form-switch",children:[(0,o.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===i,onChange:l}),(0,o.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===i?"LTR":"RTL"," mode"]})]})})]})}),(0,o.jsx)("div",{className:"card mb-3 shadow-sm",children:(0,o.jsxs)("div",{className:"card-body direction-rtl",children:[(0,o.jsx)("p",{className:"mb-2",children:"Account Setup"}),(0,o.jsx)("div",{className:"single-setting-panel",children:(0,o.jsxs)(f(),{href:"/user-profile",children:[(0,o.jsx)("div",{className:"icon-wrapper",children:(0,o.jsx)("i",{className:"bi bi-person"})}),"Update Profile"]})}),(0,o.jsx)("div",{className:"single-setting-panel",children:(0,o.jsxs)(f(),{href:"/user-profile",children:[(0,o.jsx)("div",{className:"icon-wrapper bg-warning",children:(0,o.jsx)("i",{className:"bi bi-pencil"})}),"Update Bio"]})}),(0,o.jsx)("div",{className:"single-setting-panel",children:(0,o.jsxs)(f(),{href:"/change-password",children:[(0,o.jsx)("div",{className:"icon-wrapper bg-info",children:(0,o.jsx)("i",{className:"bi bi-lock"})}),"Change Password"]})}),(0,o.jsx)("div",{className:"single-setting-panel",children:(0,o.jsxs)(f(),{href:"/language",children:[(0,o.jsx)("div",{className:"icon-wrapper bg-success",children:(0,o.jsx)("i",{className:"bi bi-globe2"})}),"Language"]})}),(0,o.jsx)("div",{className:"single-setting-panel",children:(0,o.jsxs)(f(),{href:"/privacy-policy",children:[(0,o.jsx)("div",{className:"icon-wrapper bg-danger",children:(0,o.jsx)("i",{className:"bi bi-shield-lock"})}),"Privacy Policy"]})})]})}),(0,o.jsx)("div",{className:"card shadow-sm",children:(0,o.jsxs)("div",{className:"card-body direction-rtl",children:[(0,o.jsx)("p",{className:"mb-2",children:"Register & Logout"}),(0,o.jsx)("div",{className:"single-setting-panel",children:(0,o.jsxs)(f(),{href:"/register",children:[(0,o.jsx)("div",{className:"icon-wrapper bg-primary",children:(0,o.jsx)("i",{className:"bi bi-person"})}),"Create New Account"]})}),(0,o.jsx)("div",{className:"single-setting-panel",children:(0,o.jsxs)(f(),{href:"/login",children:[(0,o.jsx)("div",{className:"icon-wrapper bg-danger",children:(0,o.jsx)("i",{className:"bi bi-box-arrow-right"})}),"Logout"]})})]})})]})}),(0,o.jsx)(r.default,{})]})}},15123:(e,s,i)=>{"use strict";i.d(s,{A:()=>g});var o=i(23915),t=i(20996);let n={apiKey:"AIzaSyCvHAIHZPnzl9xMJu9NpPQ8J-iL8zKDh9k",authDomain:"pwa-superapp.firebaseapp.com",projectId:"pwa-superapp",storageBucket:"pwa-superapp.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:e809373bd1190e6a8bf4e3"},r=(0,o.Wp)(n),a=null,l=async()=>{try{console.log("\uD83D\uDD04 Checking Firebase messaging support..."),console.log("\uD83D\uDD0D Firebase config check:"),console.log("  - API Key:",n.apiKey?"Set":"Missing"),console.log("  - Project ID:",n.projectId),console.log("  - Sender ID:",n.messagingSenderId),console.log("  - App ID:",n.appId?"Set":"Missing");let e=await (0,t.TT)();if(console.log("\uD83D\uDD0D Firebase messaging supported:",e),!e)return console.log("❌ Firebase messaging is not supported in this browser"),console.log("This could be due to:"),console.log("  - Browser doesn't support service workers"),console.log("  - Browser doesn't support push notifications"),console.log("  - Running in incognito/private mode"),null;if(console.log("\uD83D\uDD04 Initializing Firebase messaging..."),"serviceWorker"in navigator)try{await navigator.serviceWorker.ready,console.log("✅ Service worker is ready for Firebase messaging")}catch(e){console.warn("⚠️ Service worker not ready:",e)}return a=(0,t.dG)(r),console.log("✅ Firebase messaging initialized successfully"),console.log("  - Messaging instance:",!!a),a}catch(e){if(console.error("❌ Error initializing Firebase messaging:",e),console.error("Error details:",{name:e.name,message:e.message,code:e.code}),e.code)switch(e.code){case"app/invalid-api-key":console.error("\uD83D\uDD0D Invalid Firebase API key");break;case"app/invalid-app-id":console.error("\uD83D\uDD0D Invalid Firebase App ID");break;case"messaging/unsupported-browser":console.error("\uD83D\uDD0D Browser not supported for messaging");break;default:console.error("\uD83D\uDD0D Unknown Firebase error:",e.code)}return null}},c="BDukpatijXC4YB_g4W7FvW-UjBfYtMAM7epw7MJFkcb4ICU8udWq7UYAQlk-hFJ9WmZ-EutlgybC5-HIAkaMSnU";class d{async initialize(){if(this.isInitialized)return console.log("✅ FCM Service already initialized"),this.messaging;try{return console.log("\uD83D\uDD04 Initializing FCM Service..."),console.log("Browser environment check:",!0),console.log("Notification support:","Notification"in window),console.log("Service Worker support:","serviceWorker"in navigator),this.messaging=await l(),this.isInitialized=!0,this.messaging?(this.setupForegroundMessageListener(),console.log("✅ FCM Service initialized successfully"),console.log("Messaging instance:",!!this.messaging)):console.log("⚠️ FCM messaging not supported or failed to initialize"),this.messaging}catch(e){return console.error("❌ Error initializing FCM service:",e),console.error("Error details:",{name:e.name,message:e.message,code:e.code}),null}}async requestNotificationPermission(){try{let e=await Notification.requestPermission();if("granted"===e)return console.log("✅ Notification permission granted"),!0;return console.log("❌ Notification permission denied"),!1}catch(e){return console.error("Error requesting notification permission:",e),!1}}async generateFCMToken(){console.log("\uD83D\uDD04 Starting FCM token generation..."),console.log("\uD83D\uDD0D Environment check:"),console.log("  - Window available:",!0),console.log("  - HTTPS/Localhost:","https:"===window.location.protocol||"localhost"===window.location.hostname),console.log("  - Current URL:",window.location.href),console.log("\uD83D\uDD0D Environment variables:"),console.log("  - API Key:","Set"),console.log("  - Project ID:","pwa-superapp"),console.log("  - Sender ID:","************"),console.log("  - VAPID Key:","Set");try{if(this.messaging||(console.log("\uD83D\uDD04 Step 1: Initializing FCM messaging..."),await this.initialize()),!this.messaging)return console.error("❌ Step 1 FAILED: FCM messaging not available"),console.error("This usually means:"),console.error("  - Firebase configuration is incorrect"),console.error("  - Browser doesn't support FCM"),console.error("  - Network connectivity issues"),null;if(console.log("✅ Step 1: FCM messaging initialized"),console.log("\uD83D\uDD04 Step 2: Checking service worker..."),"serviceWorker"in navigator)try{let e=await navigator.serviceWorker.ready;console.log("✅ Step 2: Service Worker is ready"),console.log("  - Scope:",e.scope),console.log("  - Active:",!!e.active)}catch(e){console.warn("⚠️ Step 2: Service Worker not ready:",e)}else console.warn("⚠️ Step 2: Service Worker not supported");if(console.log("\uD83D\uDD04 Step 3: Checking notification permission..."),console.log("  - Current permission:",Notification.permission),!await this.requestNotificationPermission())return console.log("❌ Step 3 FAILED: Cannot generate token without notification permission"),null;console.log("✅ Step 3: Notification permission granted"),console.log("\uD83D\uDD04 Step 4: Generating FCM token..."),console.log("  - Using VAPID key:",c.substring(0,20)+"..."),console.log("  - Messaging instance type:",typeof this.messaging);let e=await (0,t.gf)(this.messaging,{vapidKey:c});if(e)return console.log("✅ Step 4: FCM Token generated successfully!"),console.log("  - Token length:",e.length),console.log("  - Token preview:",e.substring(0,50)+"..."),console.log("  - Full token:",e),localStorage.setItem("fcm_token",e),console.log("✅ Token stored in localStorage"),console.log("\uD83D\uDD04 Step 5: Sending token to server..."),await this.sendTokenToServer(e),e;return console.log("❌ Step 4 FAILED: No registration token available"),console.log("This could be due to:"),console.log("  - Browser blocking notifications"),console.log("  - Invalid VAPID key"),console.log("  - Firebase configuration issues"),console.log("  - Network connectivity issues"),console.log("  - Service worker not properly configured"),null}catch(e){if(console.error("❌ CRITICAL ERROR in FCM token generation:",e),console.error("Error details:",{name:e.name,message:e.message,code:e.code,stack:e.stack}),e.code)switch(e.code){case"messaging/unsupported-browser":console.error("\uD83D\uDD0D Browser not supported for FCM");break;case"messaging/permission-blocked":console.error("\uD83D\uDD0D Notification permission blocked");break;case"messaging/vapid-key-required":console.error("\uD83D\uDD0D VAPID key is required but missing");break;case"messaging/invalid-vapid-key":console.error("\uD83D\uDD0D VAPID key is invalid");break;default:console.error("\uD83D\uDD0D Unknown Firebase error code:",e.code)}return null}}async sendTokenToServer(e){try{let s=localStorage.getItem("access_token")||localStorage.getItem("token")||localStorage.getItem("authToken");if(!s)return void console.log("⚠️ No authorization token found, skipping token registration");let i=await fetch("".concat("https://client-api.acuizen.com","/users/me"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)},body:JSON.stringify({deviceToken:e})});if(i.ok){console.log("✅ FCM token sent to server successfully");let e=await i.json();console.log("Server response:",e)}else{let e=await i.text();console.log("⚠️ Failed to send FCM token to server:",i.status,e)}}catch(e){console.error("❌ Error sending token to server:",e)}}setupForegroundMessageListener(){this.messaging&&(0,t.xD)(this.messaging,e=>{console.log("\uD83D\uDCF1 Foreground message received:",e),this.showNotification(e)})}showNotification(e){let{notification:s,data:i}=e;if(s){let e=s.title||"New Notification",o={body:s.body||"",icon:s.icon||"/assets/icons/Icon-192.png",badge:"/assets/icons/Icon-72.png",tag:(null==i?void 0:i.tag)||"default",data:i||{},requireInteraction:!0,actions:[{action:"view",title:"View",icon:"/assets/icons/Icon-72.png"},{action:"dismiss",title:"Dismiss"}]};"serviceWorker"in navigator&&"showNotification"in ServiceWorkerRegistration.prototype?navigator.serviceWorker.ready.then(s=>{s.showNotification(e,o)}):new Notification(e,o)}}async getStoredToken(){return localStorage.getItem("fcm_token")}async refreshToken(){return localStorage.removeItem("fcm_token"),await this.generateFCMToken()}async deleteToken(){try{this.messaging&&(localStorage.removeItem("fcm_token"),console.log("✅ FCM token cleared from local storage"))}catch(e){console.error("Error deleting FCM token:",e)}}constructor(){this.messaging=null,this.isInitialized=!1}}let g=new d},26957:(e,s,i)=>{"use strict";i.d(s,{AM:()=>a,Dp:()=>d,F4:()=>T,FI:()=>C,H$:()=>o,J9:()=>h,Jo:()=>f,K9:()=>l,M6:()=>m,MO:()=>g,OT:()=>M,P4:()=>j,UR:()=>F,WD:()=>S,WH:()=>k,WU:()=>N,_i:()=>r,bW:()=>y,dG:()=>t,dm:()=>I,iJ:()=>A,mh:()=>P,oo:()=>u,pZ:()=>v,u3:()=>c,x2:()=>x,xE:()=>n,xo:()=>w,yo:()=>b,zP:()=>p});let o="https://client-api.acuizen.com",t=o+"/login-configs",n=o+"/services",r=e=>o+"/files/"+e+"/presigned-url",a=o+"/users/me",l=o+"/dynamic-titles",c=o+"/users/get_users",d=o+"/files",g=o+"/observation-reports",m=o+"/my-observation-reports",u=o+"/dropdowns",h=o+"/get-blob",p=o+"/permit-reports",f=o+"/users",b=o+"/toolbox-talks",v=o+"/my-toolbox-talks",k=e=>o+"/my-assigned-actions/"+e,x=e=>o+"/inspection-checklist-submit/"+e,j=e=>o+"/observation-reports/"+e,y=e=>o+"/inspection-task-submit/"+e,w=e=>o+"/inspections/"+e,N=e=>o+"/permit-report-submit/"+e,S=e=>o+"/permit-reports-acknowledge/"+e,F=e=>o+"/permit-reports-update-status/"+e,C=e=>o+"/observation-action-submit/"+e,I=o+"/risk-assessments",A=e=>o+"/risk-assessments/"+e,T=e=>o+"/ra-team-member-submit-signature/"+e,M=o+"/permit-reports",P=e=>o+"/permit-reports/"+e},35695:(e,s,i)=>{"use strict";var o=i(18999);i.o(o,"useRouter")&&i.d(s,{useRouter:function(){return o.useRouter}}),i.o(o,"useSearchParams")&&i.d(s,{useSearchParams:function(){return o.useSearchParams}})},38336:(e,s,i)=>{"use strict";i.d(s,{A:()=>r});var o=i(96078),t=i(26957);let n=o.A.create({baseURL:t.H$,headers:{Accept:"application/json","Content-Type":"application/json"},mode:"no-cors"});n.interceptors.request.use(async e=>(e.metadata={requestTime:Date.now()},e),e=>Promise.reject(e)),n.interceptors.response.use(e=>{var s;return e.headers["x-request-time"]=null==(s=e.config.metadata)?void 0:s.requestTime,e},async e=>{let{offlineQueue:s}=await i.e(8836).then(i.bind(i,48836)),{offlineStorage:o}=await i.e(58).then(i.bind(i,60058));if(s.shouldQueue(e)){var t,n,r,a;let i=e.config;if(await s.addRequest(i.url,(null==(t=i.method)?void 0:t.toUpperCase())||"GET",i.data,i.headers),(null==(n=i.method)?void 0:n.toLowerCase())==="get")try{if(null==(r=i.url)?void 0:r.includes("/services")){let e=await o.getServices();if(e.length>0)return console.log("\uD83D\uDCF1 Serving services from offline cache"),{data:e,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:i}}if(null==(a=i.url)?void 0:a.includes("assigned-actions")){let e=new URLSearchParams(i.url.split("?")[1]).get("filter"),s="All";if(e)try{JSON.parse(decodeURIComponent(e))}catch(t){let e=i.url.split("/"),o=e.findIndex(e=>"assigned-actions"===e);-1!==o&&e[o+1]&&(s=e[o+1])}let t=await o.getActions(s);if(t.length>0)return console.log("\uD83D\uDCF1 Serving actions from offline cache"),{data:t,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:i}}}catch(e){console.error("❌ Error serving from cache:",e)}}return Promise.reject(e)});let r=n},38808:(e,s,i)=>{"use strict";i.d(s,{L:()=>t});var o=i(12115);let t=()=>{let[e,s]=(0,o.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,o.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let i=()=>{s(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:i,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let s=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(s),e.style.display="none")},20)},1e3))}i()}}}},38983:(e,s,i)=>{"use strict";i.d(s,{default:()=>a});var o=i(95155),t=i(6874),n=i.n(t);i(12115);let r=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],a=()=>(0,o.jsx)(o.Fragment,{children:(0,o.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,o.jsx)("div",{className:"container px-0",children:(0,o.jsx)("div",{className:"footer-nav position-relative",children:(0,o.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:r.map((e,s)=>(0,o.jsx)("li",{children:(0,o.jsxs)(n(),{href:"/".concat(e.link),children:[(0,o.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,o.jsx)("span",{children:e.title})]})},s))})})})})})},46554:(e,s,i)=>{"use strict";i.d(s,{default:()=>d});var o=i(95155),t=i(12115),n=i(35695),r=i(38336),a=i(26957),l=i(34540),c=i(81359);let d=e=>{let{heading:s}=e,i=(0,n.useRouter)(),[d,g]=(0,t.useState)(""),m=(0,l.wA)();t.useEffect(()=>{u()},[]);let u=async()=>{try{let e=await r.A.get(a.AM);200===e.status?(g(e.data.firstName),m(c.l.setUser(e.data))):i.push("/")}catch(e){console.log(e)}};return(0,o.jsx)(o.Fragment,{children:(0,o.jsx)("div",{className:"header-area",id:"headerArea",children:(0,o.jsx)("div",{className:"container",children:(0,o.jsxs)("div",{className:"header-content header-style-four position-relative d-flex align-items-center justify-content-between",children:[(0,o.jsx)("div",{className:"back-button",children:(0,o.jsx)("button",{onClick:()=>i.back(),className:"border-0 bg-transparent p-0",style:{cursor:"pointer"},children:(0,o.jsx)("i",{className:"bi bi-arrow-left-short fs-3"})})}),(0,o.jsx)("div",{className:"page-heading",children:(0,o.jsx)("h6",{className:"mb-0",children:s})}),(0,o.jsx)("div",{className:"user-profile-wrapper"})]})})})})}},67970:(e,s,i)=>{Promise.resolve().then(i.bind(i,6043))},79630:(e,s,i)=>{"use strict";function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var i=arguments[s];for(var o in i)({}).hasOwnProperty.call(i,o)&&(e[o]=i[o])}return e}).apply(null,arguments)}i.d(s,{A:()=>o})},81359:(e,s,i)=>{"use strict";i.d(s,{A:()=>n,l:()=>t});let o=(0,i(51990).Z0)({name:"login",initialState:{isLogin:!1,user:null},reducers:{setLogin(e){e.isLogin=!0},setLogout(e){e.isLogin=!1,e.user=null},setUser(e,s){e.user=s.payload}}}),t=o.actions,n=o},9e4:(e,s,i)=>{"use strict";i.d(s,{D:()=>t});var o=i(12115);let t=()=>{let[e,s]=(0,o.useState)("light"),[i,t]=(0,o.useState)(!1);(0,o.useEffect)(()=>{s(localStorage.getItem("theme")||"light"),t(!0)},[]),(0,o.useEffect)(()=>{i&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,i]);let n=(0,o.useCallback)(()=>{s(e=>"dark"===e?"light":"dark")},[]),r=(0,o.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let s=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(s),e.style.display="none")},20)},1e3))}n()},[n]);return{theme:e,toggleTheme:n,handleDarkModeToggle:r}}},91867:(e,s,i)=>{"use strict";i.d(s,{A:()=>c});var o=i(29300),t=i.n(o),n=i(12115),r=i(97390),a=i(95155);let l=n.forwardRef((e,s)=>{let{bsPrefix:i,variant:o,animation:n="border",size:l,as:c="div",className:d,...g}=e;i=(0,r.oU)(i,"spinner");let m="".concat(i,"-").concat(n);return(0,a.jsx)(c,{ref:s,...g,className:t()(d,m,l&&"".concat(m,"-").concat(l),o&&"text-".concat(o))})});l.displayName="Spinner";let c=l}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,6078,635,4816,7666,4746,8441,1684,7358],()=>s(67970)),_N_E=e.O()}]);