"use client";

import React, { useState } from 'react';
import AttachmentUploader from '@/components/home/<USER>/AttachmentUploader';

const TestAttachmentPage = () => {
  const [files, setFiles] = useState<string[]>([]);
  
  // Test configurations
  const imageCameraAndGalleryConfig = {
    image: { enabled: true, galleryUploads: true },
    video: { enabled: false, galleryUploads: false },
    documents: { enabled: false }
  };

  const imageCameraOnlyConfig = {
    image: { enabled: true, galleryUploads: false },
    video: { enabled: false, galleryUploads: false },
    documents: { enabled: false }
  };

  const imageGalleryOnlyConfig = {
    image: { enabled: false, galleryUploads: true },
    video: { enabled: false, galleryUploads: false },
    documents: { enabled: false }
  };

  const documentOnlyConfig = {
    image: { enabled: false, galleryUploads: false },
    video: { enabled: false, galleryUploads: false },
    documents: { enabled: true }
  };

  const videoFullConfig = {
    image: { enabled: false, galleryUploads: false },
    video: { enabled: true, galleryUploads: true },
    documents: { enabled: false }
  };

  const allTypesConfig = {
    image: { enabled: true, galleryUploads: true },
    video: { enabled: true, galleryUploads: true },
    documents: { enabled: true }
  };

  const noTypesConfig = {
    image: { enabled: false, galleryUploads: false },
    video: { enabled: false, galleryUploads: false },
    documents: { enabled: false }
  };

  return (
    <div className="container py-5">
      <h1 className="mb-4">Attachment Uploader Test</h1>
      
      <div className="card mb-4">
        <div className="card-header bg-primary text-white">
          <h5 className="mb-0">Images: Camera + Gallery</h5>
        </div>
        <div className="card-body">
          <AttachmentUploader
            attachmentConfig={imageCameraAndGalleryConfig}
            onFilesSelected={setFiles}
            files={files}
            label="Take photos or upload from gallery"
          />
        </div>
      </div>

      <div className="card mb-4">
        <div className="card-header bg-secondary text-white">
          <h5 className="mb-0">Images: Camera Only</h5>
        </div>
        <div className="card-body">
          <AttachmentUploader
            attachmentConfig={imageCameraOnlyConfig}
            onFilesSelected={setFiles}
            files={files}
            label="Take photos only (no gallery)"
          />
        </div>
      </div>

      <div className="card mb-4">
        <div className="card-header bg-info text-white">
          <h5 className="mb-0">Images: Gallery Only</h5>
        </div>
        <div className="card-body">
          <AttachmentUploader
            attachmentConfig={imageGalleryOnlyConfig}
            onFilesSelected={setFiles}
            files={files}
            label="Upload images from gallery only"
          />
        </div>
      </div>

      <div className="card mb-4">
        <div className="card-header bg-success text-white">
          <h5 className="mb-0">Documents Only</h5>
        </div>
        <div className="card-body">
          <AttachmentUploader
            attachmentConfig={documentOnlyConfig}
            onFilesSelected={setFiles}
            files={files}
            label="Upload Documents Only"
          />
        </div>
      </div>

      <div className="card mb-4">
        <div className="card-header bg-warning text-dark">
          <h5 className="mb-0">Video: Camera + Gallery</h5>
        </div>
        <div className="card-body">
          <AttachmentUploader
            attachmentConfig={videoFullConfig}
            onFilesSelected={setFiles}
            files={files}
            label="Record videos or upload from gallery"
          />
        </div>
      </div>

      <div className="card mb-4">
        <div className="card-header bg-dark text-white">
          <h5 className="mb-0">All Types Allowed</h5>
        </div>
        <div className="card-body">
          <AttachmentUploader
            attachmentConfig={allTypesConfig}
            onFilesSelected={setFiles}
            files={files}
            label="All options available"
          />
        </div>
      </div>
      
      <div className="card mb-4">
        <div className="card-header bg-warning text-dark">
          <h5 className="mb-0">No Types Allowed</h5>
        </div>
        <div className="card-body">
          <AttachmentUploader
            attachmentConfig={noTypesConfig}
            onFilesSelected={setFiles}
            files={files}
            label="No Uploads Allowed"
          />
        </div>
      </div>
    </div>
  );
};

export default TestAttachmentPage;
