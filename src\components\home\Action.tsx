"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import API from "@/services/API";
import { offlineAPI } from "@/services/offlineAPI";
import { ActionType as OfflineActionType } from "@/services/offlineStorage";

import {
  ASSIGNED_ACTION_URL_BY_ID,
  INSPECTION_REPORT_WITH_ID,
  OBSERVATION_REPORT_WITH_ID,
  PERMIT_REPORT_WITH_ID,
  RISKASSESSMENT_LIST_WITH_ID,
} from "@/constant";
import ActionModal from "./ActionModal";
import RaModal from "./RaModal";
import EPTWModal from "./EPTWModal";
import InspectionModal from "./InspectionModal";
import ConductAction from "./ConductInspection";

type ActionType =
  | "take_action"
  | "reperform_action"
  | "perform_task"
  | "review"
  | "conduct_inspection"
  | "verify_task"
  | "reperform_task"
  | "verify_action"
  | "sign";

interface ShowItem {
  id: string;
  actionType: ActionType | null;

}


// ------------- INTERFACES --------------
interface ServiceType {
  id: string;
  name: string;
  maskName: string;
}

/**
 * Basic shape for each item in `actions`. 
 * Adjust fields to match your real data.
 */
interface Observation extends ShowItem {
  maskId: string;
  observationCategory: string;
  description: string;
  status: string;
  observationType: string;
  created: string;
  actionToBeTaken: string;
  application: string;      // "OBS" | "RA" | "EPTW-GEN" | "INS"
  applicationId: string;
  counter: number;
  submittedBy?: { firstName?: string };
  applicationDetails?: any
  dueDate: string;
}

interface ExtendedApplicationDetail extends ApplicationDetail {
  type: string;
  description: string;
  tasks: any; // assuming TaskType is defined
  hazardName: string;
}
/**
 * The shape for `applicationDetails` returned from your GET request.
 * Adjust fields to match what's actually in `response.data`.
 */
interface ApplicationDetail {
  maskId?: string;
  status?: string;
  // Add any other fields returned by your API
}

interface ActionProps {
  externalShowModal?: boolean;
  externalSetShowModal?: (show: boolean) => void;
  selectedFilter?: string;
  setSelectedFilter?: (filter: string) => void;
  searchQuery?: string;
}

// Helper function to determine due date status
const getDueDateStatus = (dueDate: string) => {
  const now = new Date();
  const due = new Date(dueDate);
  const diffInHours = (due.getTime() - now.getTime()) / (1000 * 60 * 60);
  const diffInDays = diffInHours / 24;

  if (diffInHours < 0) {
    return {
      status: "Overdue",
      color: "#dc3545", // Red
      bgColor: "#f8d7da",
      textColor: "#721c24"
    };
  } else if (diffInDays <= 1) {
    return {
      status: "Due Soon",
      color: "#fd7e14", // Orange
      bgColor: "#fff3cd",
      textColor: "#856404"
    };
  } else {
    return {
      status: "Upcoming",
      color: "#198754", // Green
      bgColor: "#d1e7dd",
      textColor: "#0f5132"
    };
  }
};

function Action({ externalShowModal, externalSetShowModal, selectedFilter: externalSelectedFilter, setSelectedFilter: externalSetSelectedFilter, searchQuery }: ActionProps = {}) {
  const [showModal, setShowModal] = useState<boolean>(false);
  const [viewModal, setViewModal] = useState<boolean>(false);

  // Use external modal control if provided, otherwise use internal state
  const modalShow = externalShowModal !== undefined ? externalShowModal : showModal;
  const setModalShow = externalSetShowModal || setShowModal;

  // Use external filter state if provided, otherwise use internal state
  const [internalSelectedFilter, setInternalSelectedFilter] = useState<string>("All");
  const selectedFilter = externalSelectedFilter !== undefined ? externalSelectedFilter : internalSelectedFilter;
  const setSelectedFilter = externalSetSelectedFilter || setInternalSelectedFilter;
  const [actions, setActions] = useState<OfflineActionType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // The currently selected observation item (or null if none)
  const [showItem, setShowItem] = useState<OfflineActionType | null>(null);

  // The application details fetched from server (or null if not loaded)
  const [applicationDetails, setApplicationDetails] = useState<ApplicationDetail | null>(null);

  // Get services from Redux state
  const services = useSelector((state: RootState) => state.service.service);

  const taskTypes = ["perform_task", "reperform_task", "verify_task"] as const;
  const isTask = showItem ? taskTypes.includes(showItem.actionType as any) : false;
  // Fetch actions whenever `selectedFilter` changes
  useEffect(() => {
    fetchActions(selectedFilter);
  }, [viewModal, selectedFilter])

  /**
   * Fetch actions from server based on filter with offline support
   */
  const fetchActions = async (filter: string) => {
    try {
      setLoading(true);
      const actions = await offlineAPI.getActions(filter);
      setActions(actions || []);
    } catch (error) {
      console.error("Error fetching actions:", error);
      setActions([]);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Called when the user clicks on a specific observation card
   */
  const handleObservationClick = async (action: OfflineActionType) => {
    try {
      const uriString = {
        include: [
          { relation: "locationOne" },
          { relation: "locationTwo" },
          { relation: "locationThree" },
          { relation: "locationFour" },
          { relation: "locationFive" },
          { relation: "locationSix" },
          { relation: "reporter" },
          { relation: "actionOwner" },
          { relation: "reviewer" },
          { "relation": "observationActions" }
        ],
      };

      const url = `${OBSERVATION_REPORT_WITH_ID(action.applicationId)}?filter=${encodeURIComponent(
        JSON.stringify(uriString)
      )}`;

      const response = await API.get(url);
      if (response.status === 200) {
        // Assume response.data is a single object matching ApplicationDetail
        setApplicationDetails(response.data);
      } else {
        console.error("Unexpected response status:", response.status);
      }
    } catch (error) {
      console.error("Error fetching incidents:", error);
    }

    setShowItem(action);
    setViewModal(true);
  };

  const handleRAClick = async (action: OfflineActionType) => {
    try {
      const uriString = {
        include: [
          { relation: "department" },
          { relation: "teamLeader" },
          { relation: "workActivity" },
          {
            relation: "raTeamMembers",
            scope: { include: [{ relation: "user" }] }
          }
        ]
      };

      const url = `${RISKASSESSMENT_LIST_WITH_ID(action.applicationId)}?filter=${encodeURIComponent(
        JSON.stringify(uriString)
      )}`;

      const response = await API.get(url);
      if (response.status === 200) {
        // Assume response.data is a single object matching ApplicationDetail
        setApplicationDetails(response.data);
      } else {
        console.error("Unexpected response status:", response.status);
      }
    } catch (error) {
      console.error("Error fetching incidents:", error);
    }

    setShowItem(action);
    setViewModal(true);
  }


  const handleEPTWClick = async (action: OfflineActionType) => {

    try {
      const uriString = {
        include: [
          { relation: "locationOne" },
          { relation: "locationTwo" },
          { relation: "locationThree" },
          { relation: "locationFour" },
          { relation: "locationFive" },
          { relation: "locationSix" },
          { relation: "applicant" },
          { relation: "assessor" },
          { relation: "approver" },
          { relation: "reviewer" },
        ]
      };

      const url = `${PERMIT_REPORT_WITH_ID(action.applicationId)}?filter=${encodeURIComponent(
        JSON.stringify(uriString)
      )}`;

      const response = await API.get(url);
      if (response.status === 200) {
        // Assume response.data is a single object matching ApplicationDetail
        setApplicationDetails(response.data);
      } else {
        console.error("Unexpected response status:", response.status);
      }
    } catch (error) {
      console.error("Error fetching incidents:", error);
    }

    setShowItem(action);
    setViewModal(true);
  }

  const handleInspectionClick = async (action: OfflineActionType) => {
    try {
      const uriString = {
        include: [
          { relation: "locationOne" },
          { relation: "locationTwo" },
          { relation: "locationThree" },
          { relation: "locationFour" },
          { relation: "locationFive" },
          { relation: "locationSix" },
          { relation: "inspector" },
          { relation: "assignedBy" },
          { relation: "checklist" },
        ]
      };

      const url = `${INSPECTION_REPORT_WITH_ID(action.applicationId)}?filter=${encodeURIComponent(
        JSON.stringify(uriString)
      )}`;

      const response = await API.get(url);
      if (response.status === 200) {
        // Assume response.data is a single object matching ApplicationDetail
        setApplicationDetails(response.data);
      } else {
        console.error("Unexpected response status:", response.status);
      }
    } catch (error) {
      console.error("Error fetching incidents:", error);
    }

    setShowItem(action);
    setViewModal(true);
  }

  // Filter actions based on search query
  const filteredActions = actions.filter(action => {
    if (!searchQuery || searchQuery.trim() === '') {
      return true;
    }

    const query = searchQuery.toLowerCase();
    return (
      action.maskId?.toLowerCase().includes(query) ||
      action.description?.toLowerCase().includes(query) ||
      action.actionToBeTaken?.toLowerCase().includes(query) ||
      action.application?.toLowerCase().includes(query) ||
      action.actionType?.toLowerCase().includes(query)
    );
  });

  return (
    <div className="container-fluid px-0">
      {/* Actions List */}
      <div className="bg-transparent">
        <div className="p-0">
          <div>
            {loading ? (
              <p className="m-2">Loading...</p>
            ) : filteredActions.length > 0 ? (
              filteredActions.map((obs, index) => {
                // Only render if obs.application === "OBS"
                if (obs.application === "OBS") {
                  const dueDateInfo = getDueDateStatus(obs.dueDate);

                  return (
                    <div
                      key={index}
                      className="bg-white rounded-3 mb-3 position-relative shadow-sm"
                      style={{
                        cursor: "pointer",
                        border: "none"
                      }}
                      onClick={() => handleObservationClick(obs)}
                    >
                      {/* Left colored border */}
                      <div
                        className="position-absolute h-100 rounded-start"
                        style={{
                          left: 0,
                          top: 0,
                          width: "4px",
                          backgroundColor: dueDateInfo.color
                        }}
                      ></div>

                      <div className="p-3" style={{ paddingLeft: '20px' }}>
                        <div className="d-flex justify-content-between align-items-start mb-2">
                          <div className="flex-grow-1">
                            <h6 className="mb-1 fw-medium" style={{ fontSize: '16px', color: '#212121' }}>
                               {obs.maskId}
                            </h6>

                             <p className="mb-2 text-muted" style={{ fontSize: '14px' }}>
                              {obs.description}
                            </p>
                          </div>
                          <span
                            className="badge rounded-pill px-2 py-1"
                            style={{
                              backgroundColor: "#fff3e0",
                              color: "#f57c00",
                              fontSize: '12px'
                            }}
                          >
                        {obs.actionType === "take_action" ? "Take Action" : obs.actionType === 'verify_action' ? "Verify Action" : obs.actionType === 'review' ? "In Review" : obs.actionType === 'reperform_action' ? "Reperform Action" : ""}
                          </span>
                        </div>

                        <div className="d-flex justify-content-between align-items-center">
                          <span
                            className="badge rounded-pill px-2 py-1"
                            style={{
                              backgroundColor: dueDateInfo.bgColor,
                              color: dueDateInfo.textColor,
                              fontSize: '11px',
                              fontWeight: '500'
                            }}
                          >
                            {dueDateInfo.status}
                          </span>
                          <button
                            className="btn btn-link p-0 text-decoration-none d-flex align-items-center small"
                            style={{
                              color: '#1976d2'
                            }}
                          >
                            View Details <i className="bi bi-chevron-right ms-1"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                }

                // Handle RA application type
                else if (obs.application === "RA") {
                  const dueDateInfo = getDueDateStatus(obs.dueDate);

                  return (
                    <div
                      key={index}
                      className="bg-white rounded-3 mb-3 position-relative shadow-sm"
                      style={{
                        cursor: "pointer",
                        border: "none"
                      }}
                      onClick={() => handleRAClick(obs)}
                    >
                      <div
                        className="position-absolute h-100 rounded-start"
                        style={{
                          left: 0,
                          top: 0,
                          width: "4px",
                          backgroundColor: dueDateInfo.color
                        }}
                      ></div>

                      <div className="p-3" style={{ paddingLeft: '20px' }}>
                        <div className="d-flex justify-content-between align-items-start mb-2">
                          <div className="flex-grow-1">
                            <h6 className="mb-1 fw-medium" style={{ fontSize: '16px', color: '#212121' }}>
                              {obs.description}
                            </h6>
                            <p className="mb-2 text-muted" style={{ fontSize: '14px' }}>
                              {obs.maskId} 
                            </p>
                          </div>
                          <span
                            className="badge rounded-pill px-2 py-1"
                            style={{
                              backgroundColor: "#e3f2fd",
                              color: "#1565c0",
                              fontSize: '12px'
                            }}
                          >
                            Pending
                          </span>
                        </div>

                        <div className="d-flex justify-content-between align-items-center">
                          <span
                            className="badge rounded-pill px-2 py-1"
                            style={{
                              backgroundColor: dueDateInfo.bgColor,
                              color: dueDateInfo.textColor,
                              fontSize: '11px',
                              fontWeight: '500'
                            }}
                          >
                            {dueDateInfo.status}
                          </span>
                          <button
                            className="btn btn-link p-0 text-decoration-none d-flex align-items-center small"
                            style={{
                              color: '#1976d2'
                            }}
                          >
                            View Details <i className="bi bi-chevron-right ms-1"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                }

                // Handle EPTW applications
                else if (obs.application === "EPTW-GEN") {
                  const dueDateInfo = getDueDateStatus(obs.dueDate);

                  return (
                    <div
                      key={index}
                      className="bg-white rounded-3 mb-3 position-relative"
                      style={{
                        cursor: "pointer",
                        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                        border: "none"
                      }}
                      onClick={() => handleEPTWClick(obs)}
                    >
                      <div
                        className="position-absolute h-100 rounded-start"
                        style={{
                          left: 0,
                          top: 0,
                          width: "4px",
                          backgroundColor: dueDateInfo.color
                        }}
                      ></div>

                      <div className="p-4" style={{ paddingLeft: '20px' }}>
                        <div className="d-flex justify-content-between align-items-start mb-2">
                          <div className="flex-grow-1">
                            <h6 className="mb-1 fw-medium" style={{ fontSize: '16px', color: '#212121', lineHeight: '1.4' }}>
                              {obs.description}
                            </h6>
                            <p className="mb-2" style={{ fontSize: '14px', color: '#757575', lineHeight: '1.4' }}>
                              {obs.maskId} - {obs.actionToBeTaken}
                            </p>
                          </div>
                          <span
                            className="badge rounded-pill px-3 py-1"
                            style={{
                              backgroundColor: "#f3e5f5",
                              color: "#7b1fa2",
                              fontSize: '12px',
                              fontWeight: '500'
                            }}
                          >
                            Pending
                          </span>
                        </div>

                        <div className="d-flex justify-content-between align-items-center">
                          <span
                            className="badge rounded-pill px-2 py-1"
                            style={{
                              backgroundColor: dueDateInfo.bgColor,
                              color: dueDateInfo.textColor,
                              fontSize: '11px',
                              fontWeight: '500'
                            }}
                          >
                            {dueDateInfo.status}
                          </span>
                          <button
                            className="btn btn-link p-0 text-decoration-none d-flex align-items-center"
                            style={{
                              fontSize: '14px',
                              color: '#1976d2',
                              fontWeight: '500'
                            }}
                          >
                            View Details <i className="bi bi-chevron-right ms-1"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                }

                // Handle Inspection applications
                else if (obs.application === "INS") {
                  const dueDateInfo = getDueDateStatus(obs.dueDate);

                  return (
                    <div
                      key={index}
                      className="bg-white rounded-3 mb-3 position-relative"
                      style={{
                        cursor: "pointer",
                        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                        border: "none"
                      }}
                      onClick={() => handleInspectionClick(obs)}
                    >
                      <div
                        className="position-absolute h-100 rounded-start"
                        style={{
                          left: 0,
                          top: 0,
                          width: "4px",
                          backgroundColor: dueDateInfo.color
                        }}
                      ></div>

                      <div className="p-4" style={{ paddingLeft: '20px' }}>
                        <div className="d-flex justify-content-between align-items-start mb-2">
                          <div className="flex-grow-1">
                            <h6 className="mb-1 fw-medium" style={{ fontSize: '16px', color: '#212121', lineHeight: '1.4' }}>
                               {obs.actionType === "conduct_inspection" ? "Conduct Inspection" : obs.actionType === "perform_task" ? "Take Action" : obs.actionType === "verify_task" ? "Verify Action" : obs.actionType === "reperform_task" ? "Retake Action" : obs.actionToBeTaken
                          }
                            </h6>
                            <p className="mb-2" style={{ fontSize: '14px', color: '#757575', lineHeight: '1.4' }}>
                             {obs.actionType === "conduct_inspection" ? obs.maskId : obs.maskId + '-IA-' + (obs.counter + 1)}
                            </p>
                            <p className="m-0">{obs.description}</p>
                          </div>
                          <span
                            className="badge rounded-pill px-3 py-1"
                            style={{
                              backgroundColor: "#e8f5e8",
                              color: "#2e7d32",
                              fontSize: '12px',
                              fontWeight: '500'
                            }}
                          >
                            In Progress
                          </span>
                        </div>

                        <div className="d-flex justify-content-between align-items-center">
                          <span
                            className="badge rounded-pill px-2 py-1"
                            style={{
                              backgroundColor: dueDateInfo.bgColor,
                              color: dueDateInfo.textColor,
                              fontSize: '11px',
                              fontWeight: '500'
                            }}
                          >
                            {dueDateInfo.status}
                          </span>
                          <button
                            className="btn btn-link p-0 text-decoration-none d-flex align-items-center"
                            style={{
                              fontSize: '14px',
                              color: '#1976d2',
                              fontWeight: '500'
                            }}
                          >
                            View Details <i className="bi bi-chevron-right ms-1"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                }

                return null; // Don't render if application type doesn't match
              })
            ) : (
              <div className="text-center py-4">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-search text-muted mb-2" style={{ fontSize: '24px' }}></i>
                  <p className="text-muted mb-1">
                    {searchQuery && searchQuery.trim() !== '' ? 'No actions match your search' : 'No actions found'}
                  </p>
                  {searchQuery && searchQuery.trim() !== '' && (
                    <small className="text-muted">
                      Try adjusting your search terms
                    </small>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Filter Modal - Draft Filter Modal Design */}
      {modalShow && (
        <div
          className="modal fade show d-block"
          style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}
          onClick={() => setModalShow(false)}
        >
          <div className="modal-dialog modal-dialog-centered" onClick={(e) => e.stopPropagation()}>
            <div className="modal-content" style={{ borderRadius: '16px', border: 'none' }}>
              <div className="modal-header border-bottom" style={{ borderColor: '#F0F0F0 !important' }}>
                <h5 className="modal-title" style={{ fontSize: '18px', fontWeight: '600' }}>
                  Filter Actions
                </h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setModalShow(false)}
                ></button>
              </div>
              <div className="modal-body" style={{ padding: '16px' }}>
                {/* All Actions Option */}
                <button
                  className="btn w-100 d-flex justify-content-between align-items-center mb-2"
                  style={{
                    padding: '16px',
                    borderRadius: '12px',
                    border: '1px solid #E5E7EB',
                    backgroundColor: selectedFilter === 'All' ? 'rgba(0, 122, 255, 0.1)' : 'transparent',
                    color: selectedFilter === 'All' ? '#007AFF' : '#000000',
                    textAlign: 'left'
                  }}
                  onClick={() => {
                    setSelectedFilter('All');
                    setModalShow(false);
                  }}
                >
                  <span style={{ fontSize: '16px', fontWeight: '500' }}>All Actions</span>
                  {selectedFilter === 'All' && (
                    <i className="bi bi-check-circle-fill" style={{ color: '#007AFF', fontSize: '20px' }}></i>
                  )}
                </button>

                {/* Service Filter Options */}
                {services && services.map((service: ServiceType) => (
                  <button
                    key={service.id}
                    className="btn w-100 d-flex justify-content-between align-items-center mb-2"
                    style={{
                      padding: '16px',
                      borderRadius: '12px',
                      border: '1px solid #E5E7EB',
                      backgroundColor: selectedFilter === service.maskName ? 'rgba(0, 122, 255, 0.1)' : 'transparent',
                      color: selectedFilter === service.maskName ? '#007AFF' : '#000000',
                      textAlign: 'left'
                    }}
                    onClick={() => {
                      setSelectedFilter(service.maskName);
                      setModalShow(false);
                    }}
                  >
                    <span style={{ fontSize: '16px', fontWeight: '500' }}>{service.name}</span>
                    {selectedFilter === service.maskName && (
                      <i className="bi bi-check-circle-fill" style={{ color: '#007AFF', fontSize: '20px' }}></i>
                    )}
                  </button>
                ))}

                {/* Clear All Filters Option */}
                {selectedFilter !== 'All' && (
                  <button
                    className="btn w-100 d-flex align-items-center justify-content-center mt-3"
                    style={{
                      padding: '16px',
                      borderRadius: '12px',
                      backgroundColor: '#FFF2F2',
                      border: '1px solid #FFE5E5',
                      color: '#FF3B30'
                    }}
                    onClick={() => {
                      setSelectedFilter('All');
                      setModalShow(false);
                    }}
                  >
                    <i className="bi bi-x-circle me-2" style={{ fontSize: '20px' }}></i>
                    <span style={{ fontSize: '16px', fontWeight: '600' }}>Clear All Filters</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* The Action Modal for Observations */}
      {viewModal && showItem && showItem.application === "OBS" && (
        <ActionModal
          show={viewModal}
          applicationDetails={applicationDetails || undefined}
          showItem={showItem as any}
          closeModal={() => setViewModal(false)}
        />
      )}

      {viewModal && showItem && showItem.application === "RA" && (
        <RaModal
          show={viewModal}
          applicationDetails={applicationDetails as unknown as ExtendedApplicationDetail}
          showItem={showItem as any}
          closeModal={() => setViewModal(false)}
        />
      )}
      {viewModal && showItem && showItem.application === "EPTW-GEN" && (
        <EPTWModal
          show={viewModal}
          applicationDetails={applicationDetails as unknown as ExtendedApplicationDetail}
          showItem={showItem as any}
          closeModal={() => setViewModal(false)}
        />
      )}

      {viewModal && showItem && showItem.application === "INS" && (
       isTask ? (
            <InspectionModal
              show={viewModal}
              applicationDetails={applicationDetails as unknown as ExtendedApplicationDetail}
              showItem={showItem as any}
              closeModal={() => setViewModal(false)}
            />
          ) : (
            <ConductAction
              show={viewModal}
              applicationDetails={applicationDetails as unknown as ExtendedApplicationDetail}
              showItem={showItem as any}
              closeModal={() => setViewModal(false)}
            />
          )
        

      )}

      {/* <InspectionModal
           show={viewModal}
           applicationDetails={applicationDetails as unknown as ExtendedApplicationDetail}
          showItem={showItem as ShowItem}
           closeModal={() => setViewModal(false)}
         /> */}

      {/* {viewModal && showItem && showItem.application === "INS" && showItem.actionType === "conduct_inspection" && (
        <ConductAction
          show={viewModal}
          applicationDetails={applicationDetails as unknown as ExtendedApplicationDetail}
          showItem={showItem as ShowItem}
          closeModal={() => setViewModal(false)}
        />
      )} */}

      {/* Custom styles for the tick-mark radio buttons */}
      <style jsx>{`
        .tick-container {
          display: inline-flex;
          align-items: center;
          position: relative;
          padding-left: 32px;
          margin-bottom: 12px;
          cursor: pointer;
          user-select: none;
          font-size: 1rem;
          gap: 8px;
          width: 100%;
        }

        /* Hide the default radio */
        .tick-container input {
          position: absolute;
          opacity: 0;
          cursor: pointer;
          height: 0;
          width: 0;
        }

        /* Create a custom checkmark */
        .checkmark {
          position: absolute;
          left: 0;
          top: 0;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          border: 2px solid #007bff;
          background-color: #ffffff;
          box-sizing: border-box;
        }

        .tick-container:hover input ~ .checkmark {
          background-color: #f0f8ff;
        }

        .tick-container input:checked ~ .checkmark {
          background-color: #007bff;
        }

        .checkmark:after {
          content: "";
          position: absolute;
          display: none;
        }

        .tick-container input:checked ~ .checkmark:after {
          display: block;
        }

        .tick-container .checkmark:after {
          left: 5px;
          top: 2px;
          width: 5px;
          height: 10px;
          border: solid #fff;
          border-width: 0 2px 2px 0;
          transform: rotate(45deg);
        }
      `}</style>
    </div>
  );
}

export default Action;
