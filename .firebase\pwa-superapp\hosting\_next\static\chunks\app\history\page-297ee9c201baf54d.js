(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3429],{26957:(e,s,t)=>{"use strict";t.d(s,{AM:()=>o,Dp:()=>d,F4:()=>R,FI:()=>A,H$:()=>i,J9:()=>p,Jo:()=>b,K9:()=>n,M6:()=>x,MO:()=>m,OT:()=>z,P4:()=>v,UR:()=>S,WD:()=>w,WH:()=>y,WU:()=>C,_i:()=>a,bW:()=>N,dG:()=>r,dm:()=>k,iJ:()=>E,mh:()=>P,oo:()=>u,pZ:()=>g,u3:()=>c,x2:()=>j,xE:()=>l,xo:()=>F,yo:()=>f,zP:()=>h});let i="https://client-api.acuizen.com",r=i+"/login-configs",l=i+"/services",a=e=>i+"/files/"+e+"/presigned-url",o=i+"/users/me",n=i+"/dynamic-titles",c=i+"/users/get_users",d=i+"/files",m=i+"/observation-reports",x=i+"/my-observation-reports",u=i+"/dropdowns",p=i+"/get-blob",h=i+"/permit-reports",b=i+"/users",f=i+"/toolbox-talks",g=i+"/my-toolbox-talks",y=e=>i+"/my-assigned-actions/"+e,j=e=>i+"/inspection-checklist-submit/"+e,v=e=>i+"/observation-reports/"+e,N=e=>i+"/inspection-task-submit/"+e,F=e=>i+"/inspections/"+e,C=e=>i+"/permit-report-submit/"+e,w=e=>i+"/permit-reports-acknowledge/"+e,S=e=>i+"/permit-reports-update-status/"+e,A=e=>i+"/observation-action-submit/"+e,k=i+"/risk-assessments",E=e=>i+"/risk-assessments/"+e,R=e=>i+"/ra-team-member-submit-signature/"+e,z=i+"/permit-reports",P=e=>i+"/permit-reports/"+e},35695:(e,s,t)=>{"use strict";var i=t(18999);t.o(i,"useRouter")&&t.d(s,{useRouter:function(){return i.useRouter}}),t.o(i,"useSearchParams")&&t.d(s,{useSearchParams:function(){return i.useSearchParams}})},38336:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var i=t(96078),r=t(26957);let l=i.A.create({baseURL:r.H$,headers:{Accept:"application/json","Content-Type":"application/json"},mode:"no-cors"});l.interceptors.request.use(async e=>(e.metadata={requestTime:Date.now()},e),e=>Promise.reject(e)),l.interceptors.response.use(e=>{var s;return e.headers["x-request-time"]=null==(s=e.config.metadata)?void 0:s.requestTime,e},async e=>{let{offlineQueue:s}=await t.e(8836).then(t.bind(t,48836)),{offlineStorage:i}=await t.e(58).then(t.bind(t,60058));if(s.shouldQueue(e)){var r,l,a,o;let t=e.config;if(await s.addRequest(t.url,(null==(r=t.method)?void 0:r.toUpperCase())||"GET",t.data,t.headers),(null==(l=t.method)?void 0:l.toLowerCase())==="get")try{if(null==(a=t.url)?void 0:a.includes("/services")){let e=await i.getServices();if(e.length>0)return console.log("\uD83D\uDCF1 Serving services from offline cache"),{data:e,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:t}}if(null==(o=t.url)?void 0:o.includes("assigned-actions")){let e=new URLSearchParams(t.url.split("?")[1]).get("filter"),s="All";if(e)try{JSON.parse(decodeURIComponent(e))}catch(r){let e=t.url.split("/"),i=e.findIndex(e=>"assigned-actions"===e);-1!==i&&e[i+1]&&(s=e[i+1])}let r=await i.getActions(s);if(r.length>0)return console.log("\uD83D\uDCF1 Serving actions from offline cache"),{data:r,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:t}}}catch(e){console.error("❌ Error serving from cache:",e)}}return Promise.reject(e)});let a=l},46554:(e,s,t)=>{"use strict";t.d(s,{default:()=>d});var i=t(95155),r=t(12115),l=t(35695),a=t(38336),o=t(26957),n=t(34540),c=t(81359);let d=e=>{let{heading:s}=e,t=(0,l.useRouter)(),[d,m]=(0,r.useState)(""),x=(0,n.wA)();r.useEffect(()=>{u()},[]);let u=async()=>{try{let e=await a.A.get(o.AM);200===e.status?(m(e.data.firstName),x(c.l.setUser(e.data))):t.push("/")}catch(e){console.log(e)}};return(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("div",{className:"header-area",id:"headerArea",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"header-content header-style-four position-relative d-flex align-items-center justify-content-between",children:[(0,i.jsx)("div",{className:"back-button",children:(0,i.jsx)("button",{onClick:()=>t.back(),className:"border-0 bg-transparent p-0",style:{cursor:"pointer"},children:(0,i.jsx)("i",{className:"bi bi-arrow-left-short fs-3"})})}),(0,i.jsx)("div",{className:"page-heading",children:(0,i.jsx)("h6",{className:"mb-0",children:s})}),(0,i.jsx)("div",{className:"user-profile-wrapper"})]})})})})}},57643:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var i=t(95155),r=t(12115),l=t(46554),a=t(6874),o=t.n(a);function n(){let[e,s]=(0,r.useState)("All"),[t,a]=(0,r.useState)(""),[n,c]=(0,r.useState)(!1),d=[{id:"IR-2024-001",type:"Incident Report",title:"Slip and Fall - Warehouse A",date:"2024-01-15",status:"Completed",icon:"bi bi-exclamation-triangle-fill",color:"#FF3B30"},{id:"RA-2024-003",type:"Risk Assessment",title:"Chemical Storage Area Review",date:"2024-01-14",status:"In Progress",icon:"bi bi-shield-fill",color:"#FF9500"},{id:"PTW-2024-012",type:"E-Permit",title:"Hot Work - Maintenance Bay",date:"2024-01-13",status:"Approved",icon:"bi bi-file-text-fill",color:"#34C759"},{id:"OBS-2024-008",type:"Observation",title:"Unsafe Ladder Usage",date:"2024-01-12",status:"Closed",icon:"bi bi-eye-fill",color:"#007AFF"},{id:"AUD-2024-002",type:"Safety Audit",title:"Monthly Safety Inspection",date:"2024-01-10",status:"Completed",icon:"bi bi-patch-check-fill",color:"#5856D6"},{id:"DOC-2024-005",type:"Document",title:"Safety Procedure Update",date:"2024-01-09",status:"Published",icon:"bi bi-folder-fill",color:"#AF52DE"}],m=d.filter(s=>{let i=s.title.toLowerCase().includes(t.toLowerCase())||s.type.toLowerCase().includes(t.toLowerCase())||s.id.toLowerCase().includes(t.toLowerCase()),r="All"===e||"Reports"===e&&(s.type.includes("Report")||s.type.includes("Observation"))||"Permits"===e&&s.type.includes("Permit")||"Assessments"===e&&s.type.includes("Assessment")||"Audits"===e&&s.type.includes("Audit");return i&&r}),x=e=>{switch(e){case"Completed":case"Approved":case"Closed":case"Published":return"#2E7D32";case"In Progress":return"#F9A825";case"Pending":return"#F57C00";default:return"#666666"}},u=e=>{switch(e){case"Completed":case"Approved":case"Closed":case"Published":return"#E8F5E9";case"In Progress":return"#FFF8E1";case"Pending":return"#FFF3E0";default:return"#F5F5F5"}};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(l.default,{heading:"History"}),(0,i.jsx)("div",{className:"page-content-wrapper",style:{minHeight:"100vh",backgroundColor:"#f8f9fa"},children:(0,i.jsxs)("div",{className:"container-fluid px-3",style:{padding:"16px"},children:[(0,i.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,i.jsx)("p",{className:"text-muted mb-0",style:{opacity:.7,fontSize:"16px"},children:"View your activity history and records"}),(0,i.jsx)("div",{style:{marginTop:"16px"},children:(0,i.jsxs)("div",{className:"d-flex align-items-center gap-3 mb-3",children:[(0,i.jsxs)("div",{className:"d-flex align-items-center flex-grow-1",style:{backgroundColor:"#F2F2F7",borderRadius:"10px",padding:"8px 12px"},children:[(0,i.jsx)("i",{className:"bi bi-search me-2",style:{fontSize:"20px",color:"#8E8E93"}}),(0,i.jsx)("input",{type:"text",className:"form-control border-0 bg-transparent p-0",placeholder:"Search history...",value:t,onChange:e=>a(e.target.value),style:{fontSize:"16px",color:"#000000"}})]}),(0,i.jsx)("button",{className:"btn d-flex align-items-center justify-content-center",onClick:()=>c(!0),style:{width:"44px",height:"44px",borderRadius:"10px",borderWidth:"1px",borderColor:"All"!==e?"#1976D2":"#E5E7EB",backgroundColor:"All"!==e?"#1976D2":"#F2F2F7"},children:(0,i.jsx)("i",{className:"bi bi-sliders",style:{fontSize:"18px",color:"All"!==e?"#FFFFFF":"#1976D2"}})}),"All"!==e&&(0,i.jsx)("button",{className:"btn d-flex align-items-center justify-content-center",onClick:()=>s("All"),style:{width:"32px",height:"32px",borderRadius:"16px",backgroundColor:"#FFF2F2",border:"none"},children:(0,i.jsx)("i",{className:"bi bi-x-circle-fill",style:{fontSize:"20px",color:"#FF3B30"}})})]})})]}),(0,i.jsx)("div",{style:{marginBottom:"24px"},children:m.map(e=>(0,i.jsx)("div",{className:"bg-white border rounded-3 p-3 mb-3",style:{borderRadius:"12px",borderWidth:"1px",borderColor:"#E5E7EB",transition:"all 0.2s ease",cursor:"pointer"},onMouseEnter:e=>{e.currentTarget.style.transform="translateY(-2px)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0,0,0,0.1)"},onMouseLeave:e=>{e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="none"},children:(0,i.jsxs)("div",{className:"d-flex align-items-center",children:[(0,i.jsx)("div",{className:"d-flex align-items-center justify-content-center me-3",style:{width:"40px",height:"40px",borderRadius:"20px",backgroundColor:e.color+"20"},children:(0,i.jsx)("i",{className:e.icon,style:{fontSize:"20px",color:e.color}})}),(0,i.jsxs)("div",{style:{flex:1},children:[(0,i.jsx)("h6",{className:"fw-bold text-dark mb-1",style:{fontSize:"16px",marginBottom:"4px"},children:e.title}),(0,i.jsxs)("p",{className:"text-muted mb-0",style:{fontSize:"14px",opacity:.7},children:[e.type," • ",e.id]})]}),(0,i.jsxs)("div",{className:"text-end",children:[(0,i.jsx)("div",{className:"badge rounded-pill mb-1",style:{backgroundColor:u(e.status),color:x(e.status),fontSize:"12px",fontWeight:"600",padding:"4px 8px"},children:e.status}),(0,i.jsx)("div",{style:{fontSize:"12px",opacity:.6,color:"#666666"},children:e.date})]})]})},e.id))}),(0,i.jsx)("div",{className:"text-center py-4",style:{paddingBottom:"100px"},children:(0,i.jsxs)("p",{className:"text-muted mb-0",style:{fontSize:"14px",opacity:.6},children:["Showing ",m.length," of ",d.length," items"]})}),n&&(0,i.jsx)("div",{className:"modal fade show d-block",style:{backgroundColor:"rgba(0,0,0,0.5)",zIndex:1050},onClick:()=>c(!1),children:(0,i.jsx)("div",{className:"modal-dialog modal-dialog-centered",style:{maxWidth:"320px"},onClick:e=>e.stopPropagation(),children:(0,i.jsxs)("div",{className:"modal-content",style:{borderRadius:"16px",border:"1px solid #E5E7EB",boxShadow:"0 4px 12px rgba(0,0,0,0.15)"},children:[(0,i.jsx)("div",{className:"modal-header border-bottom",style:{borderColor:"#F0F0F0 !important",padding:"20px"},children:(0,i.jsxs)("div",{className:"d-flex justify-content-between align-items-center w-100",children:[(0,i.jsx)("h5",{className:"modal-title mb-0",style:{fontSize:"18px",fontWeight:"600"},children:"Filter History"}),(0,i.jsx)("button",{type:"button",className:"btn-close",onClick:()=>c(!1),style:{fontSize:"12px"}})]})}),(0,i.jsxs)("div",{className:"modal-body",style:{padding:"16px"},children:[["All","Reports","Permits","Assessments","Audits"].map(t=>(0,i.jsxs)("button",{className:"btn w-100 d-flex justify-content-between align-items-center mb-2",onClick:()=>{s(t),c(!1)},style:{backgroundColor:e===t?"#E3F2FD":"transparent",borderColor:"#E5E7EB",borderWidth:"1px",borderRadius:"12px",padding:"16px",textAlign:"left"},children:[(0,i.jsx)("span",{style:{fontSize:"16px",fontWeight:"500",color:e===t?"#1976D2":"#000000"},children:t}),e===t&&(0,i.jsx)("i",{className:"bi bi-check-circle-fill",style:{fontSize:"20px",color:"#1976D2"}})]},t)),"All"!==e&&(0,i.jsxs)("button",{className:"btn w-100 d-flex align-items-center mt-3",onClick:()=>{s("All"),c(!1)},style:{backgroundColor:"#FFF2F2",borderColor:"#FFE5E5",borderWidth:"1px",borderRadius:"12px",padding:"16px"},children:[(0,i.jsx)("i",{className:"bi bi-x-circle-fill",style:{fontSize:"20px",color:"#FF3B30",marginRight:"12px"}}),(0,i.jsx)("span",{style:{fontSize:"16px",fontWeight:"600",color:"#FF3B30"},children:"Clear All Filters"})]})]})]})})})]})}),(0,i.jsx)("div",{className:"fixed-bottom bg-white border-top shadow-sm",children:(0,i.jsx)("div",{className:"container-fluid",children:(0,i.jsxs)("div",{className:"row text-center py-2",children:[(0,i.jsx)("div",{className:"col",children:(0,i.jsx)(o(),{href:"/dashboard",className:"text-decoration-none text-muted",children:(0,i.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,i.jsx)("i",{className:"bi bi-grid-3x3-gap fs-5 mb-1"}),(0,i.jsx)("small",{style:{fontSize:"0.7rem"},children:"Dashboard"})]})})}),(0,i.jsx)("div",{className:"col",children:(0,i.jsx)(o(),{href:"/services",className:"text-decoration-none text-muted",children:(0,i.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,i.jsx)("i",{className:"bi bi-grid fs-5 mb-1"}),(0,i.jsx)("small",{style:{fontSize:"0.7rem"},children:"Services"})]})})}),(0,i.jsx)("div",{className:"col",children:(0,i.jsx)(o(),{href:"/home",className:"text-decoration-none text-muted",children:(0,i.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,i.jsx)("i",{className:"bi bi-house fs-5 mb-1"}),(0,i.jsx)("small",{style:{fontSize:"0.7rem"},children:"Home"})]})})}),(0,i.jsx)("div",{className:"col",children:(0,i.jsx)(o(),{href:"/history",className:"text-decoration-none text-primary",children:(0,i.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,i.jsx)("i",{className:"bi bi-clock-fill fs-5 mb-1"}),(0,i.jsx)("small",{style:{fontSize:"0.7rem"},children:"History"})]})})}),(0,i.jsx)("div",{className:"col",children:(0,i.jsx)(o(),{href:"/profile",className:"text-decoration-none text-muted",children:(0,i.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,i.jsx)("i",{className:"bi bi-person fs-5 mb-1"}),(0,i.jsx)("small",{style:{fontSize:"0.7rem"},children:"Profile"})]})})})]})})})]})}},75006:(e,s,t)=>{Promise.resolve().then(t.bind(t,57643))},81359:(e,s,t)=>{"use strict";t.d(s,{A:()=>l,l:()=>r});let i=(0,t(51990).Z0)({name:"login",initialState:{isLogin:!1,user:null},reducers:{setLogin(e){e.isLogin=!0},setLogout(e){e.isLogin=!1,e.user=null},setUser(e,s){e.user=s.payload}}}),r=i.actions,l=i}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,6078,635,8441,1684,7358],()=>s(75006)),_N_E=e.O()}]);