(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2587],{42528:(a,e,t)=>{Promise.resolve().then(t.bind(t,45362)),Promise.resolve().then(t.bind(t,38983)),Promise.resolve().then(t.bind(t,21217))},45362:(a,e,t)=>{"use strict";t.d(e,{default:()=>i});var s=t(95155);t(12115);let i=()=>(t(40844),(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"page-content-wrapper py-3",children:[(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"element-heading",children:(0,s.jsx)("h6",{children:"Standard Tab"})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"card",children:(0,s.jsx)("div",{className:"card-body",children:(0,s.jsxs)("div",{className:"standard-tab",children:[(0,s.jsxs)("ul",{className:"nav rounded-lg mb-2 p-2 shadow-sm",id:"affanTabs1",role:"tablist",children:[(0,s.jsx)("li",{className:"nav-item",role:"presentation",children:(0,s.jsx)("button",{className:"btn active",id:"bootstrap-tab","data-bs-toggle":"tab","data-bs-target":"#bootstrap",type:"button",role:"tab","aria-controls":"bootstrap","aria-selected":"true",children:"RTL"})}),(0,s.jsx)("li",{className:"nav-item",role:"presentation",children:(0,s.jsx)("button",{className:"btn",id:"pwa-tab","data-bs-toggle":"tab","data-bs-target":"#pwa",type:"button",role:"tab","aria-controls":"pwa","aria-selected":"false",children:"PWA"})}),(0,s.jsx)("li",{className:"nav-item",role:"presentation",children:(0,s.jsx)("button",{className:"btn",id:"dark-tab","data-bs-toggle":"tab","data-bs-target":"#dark",type:"button",role:"tab","aria-controls":"dark","aria-selected":"false",children:"Dark"})})]}),(0,s.jsxs)("div",{className:"tab-content rounded-lg p-3 shadow-sm",id:"affanTabs1Content",children:[(0,s.jsxs)("div",{className:"tab-pane fade show active",id:"bootstrap",role:"tabpanel","aria-labelledby":"bootstrap-tab",children:[(0,s.jsx)("h6",{children:"RTL Ready"}),(0,s.jsx)("p",{className:"mb-0",children:"Lorem ipsum, dolor sit amet consectetur adipisicing elit."})]}),(0,s.jsxs)("div",{className:"tab-pane fade",id:"pwa",role:"tabpanel","aria-labelledby":"pwa-tab",children:[(0,s.jsx)("h6",{children:"PWA Ready"}),(0,s.jsx)("p",{className:"mb-0",children:"Lorem ipsum, dolor sit amet consectetur adipisicing elit."})]}),(0,s.jsxs)("div",{className:"tab-pane fade",id:"dark",role:"tabpanel","aria-labelledby":"dark-tab",children:[(0,s.jsx)("h6",{children:"Dark Mode"}),(0,s.jsx)("p",{className:"mb-0",children:"Lorem ipsum, dolor sit amet consectetur adipisicing elit."})]})]})]})})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"element-heading mt-3",children:(0,s.jsx)("h6",{children:"Colorful Tab"})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"card bg-primary bg-gradient",children:(0,s.jsx)("div",{className:"card-body",children:(0,s.jsxs)("div",{className:"colorful-tab",children:[(0,s.jsxs)("ul",{className:"nav p-1 mb-3 shadow-sm",id:"affanTab3",role:"tablist",children:[(0,s.jsx)("li",{className:"nav-item",role:"presentation",children:(0,s.jsx)("button",{className:"btn btn-primary active",id:"creative-tab","data-bs-toggle":"tab","data-bs-target":"#creative",type:"button",role:"tab","aria-controls":"creative","aria-selected":"true",children:"Creative"})}),(0,s.jsx)("li",{className:"nav-item",role:"presentation",children:(0,s.jsx)("button",{className:"btn btn-primary",id:"modern-tab","data-bs-toggle":"tab","data-bs-target":"#modern",type:"button",role:"tab","aria-controls":"modern","aria-selected":"false",children:"Modern"})}),(0,s.jsx)("li",{className:"nav-item",role:"presentation",children:(0,s.jsx)("button",{className:"btn btn-primary",id:"latest-tab","data-bs-toggle":"tab","data-bs-target":"#latest",type:"button",role:"tab","aria-controls":"latest","aria-selected":"false",children:"Latest"})})]}),(0,s.jsxs)("div",{className:"tab-content shadow-sm p-3",id:"affanTab3Content",children:[(0,s.jsxs)("div",{className:"tab-pane fade show active",id:"creative",role:"tabpanel","aria-labelledby":"creative-tab",children:[(0,s.jsx)("h6",{className:"text-white",children:"Creative design."}),(0,s.jsx)("p",{className:"mb-0 text-white",children:"Lorem ipsum, dolor sit amet consectetur adipisicing elit."})]}),(0,s.jsxs)("div",{className:"tab-pane fade",id:"modern",role:"tabpanel","aria-labelledby":"modern-tab",children:[(0,s.jsx)("h6",{className:"text-white",children:"Modern trends."}),(0,s.jsx)("p",{className:"mb-0 text-white",children:"Lorem ipsum, dolor sit amet consectetur adipisicing elit."})]}),(0,s.jsxs)("div",{className:"tab-pane fade",id:"latest",role:"tabpanel","aria-labelledby":"latest-tab",children:[(0,s.jsx)("h6",{className:"text-white",children:"Latest technology."}),(0,s.jsx)("p",{className:"mb-0 text-white",children:"Lorem ipsum, dolor sit amet consectetur adipisicing elit."})]})]})]})})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"element-heading mt-3",children:(0,s.jsx)("h6",{children:"Minimal Tab"})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"card",children:(0,s.jsx)("div",{className:"card-body",children:(0,s.jsxs)("div",{className:"minimal-tab",children:[(0,s.jsxs)("ul",{className:"nav nav-tabs mb-3",id:"affanTab2",role:"tablist",children:[(0,s.jsx)("li",{className:"nav-item",role:"presentation",children:(0,s.jsx)("button",{className:"btn active",id:"sass-tab","data-bs-toggle":"tab","data-bs-target":"#sass",type:"button",role:"tab","aria-controls":"sass","aria-selected":"true",children:"Sass"})}),(0,s.jsx)("li",{className:"nav-item",role:"presentation",children:(0,s.jsx)("button",{className:"btn",id:"npm-tab","data-bs-toggle":"tab","data-bs-target":"#npm",type:"button",role:"tab","aria-controls":"npm","aria-selected":"false",children:"NPM"})}),(0,s.jsx)("li",{className:"nav-item",role:"presentation",children:(0,s.jsx)("button",{className:"btn",id:"gulp-tab","data-bs-toggle":"tab","data-bs-target":"#gulp",type:"button",role:"tab","aria-controls":"gulp","aria-selected":"false",children:"Gulp"})})]}),(0,s.jsxs)("div",{className:"tab-content rounded-lg p-3",id:"affanTab2Content",children:[(0,s.jsxs)("div",{className:"tab-pane fade show active",id:"sass",role:"tabpanel","aria-labelledby":"sass-tab",children:[(0,s.jsx)("h6",{children:"Built with SASS"}),(0,s.jsx)("p",{className:"mb-0",children:"Lorem ipsum, dolor sit amet consectetur adipisicing elit."})]}),(0,s.jsxs)("div",{className:"tab-pane fade",id:"npm",role:"tabpanel","aria-labelledby":"npm-tab",children:[(0,s.jsx)("h6",{children:"Built with NPM"}),(0,s.jsx)("p",{className:"mb-0",children:"Lorem ipsum, dolor sit amet consectetur adipisicing elit."})]}),(0,s.jsxs)("div",{className:"tab-pane fade",id:"gulp",role:"tabpanel","aria-labelledby":"gulp-tab",children:[(0,s.jsx)("h6",{children:"Built with Gulp js"}),(0,s.jsx)("p",{className:"mb-0",children:"Lorem ipsum, dolor sit amet consectetur adipisicing elit."})]})]})]})})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"element-heading mt-3",children:(0,s.jsx)("h6",{children:"Bootstrap Tab"})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"card",children:(0,s.jsxs)("div",{className:"card-body",children:[(0,s.jsxs)("ul",{className:"nav nav-tabs",id:"bootstrapTab",role:"tablist",children:[(0,s.jsx)("li",{className:"nav-item me-2",role:"presentation",children:(0,s.jsx)("button",{className:"nav-link active",id:"home-tab","data-bs-toggle":"tab","data-bs-target":"#home",type:"button",role:"tab","aria-controls":"home","aria-selected":"true",children:"Home"})}),(0,s.jsx)("li",{className:"nav-item me-2",role:"presentation",children:(0,s.jsx)("button",{className:"nav-link",id:"profile-tab","data-bs-toggle":"tab","data-bs-target":"#profile",type:"button",role:"tab","aria-controls":"profile","aria-selected":"false",children:"Profile"})}),(0,s.jsx)("li",{className:"nav-item",role:"presentation",children:(0,s.jsx)("button",{className:"nav-link",id:"contact-tab","data-bs-toggle":"tab","data-bs-target":"#contact",type:"button",role:"tab","aria-controls":"contact","aria-selected":"false",children:"Contact"})})]}),(0,s.jsxs)("div",{className:"tab-content p-3 border-top-0",id:"bootstrapTabContent",children:[(0,s.jsxs)("div",{className:"tab-pane fade show active",id:"home",role:"tabpanel","aria-labelledby":"home-tab",children:[(0,s.jsx)("h6",{children:"Im tab one!"}),(0,s.jsx)("p",{className:"mb-0",children:"Lorem ipsum, dolor sit amet consectetur adipisicing elit. Nam, impedit natus itaque fuga aperiam qui eos ut."})]}),(0,s.jsxs)("div",{className:"tab-pane fade",id:"profile",role:"tabpanel","aria-labelledby":"profile-tab",children:[(0,s.jsx)("h6",{children:"Im tab two!"}),(0,s.jsx)("p",{className:"mb-0",children:"Lorem ipsum, dolor sit amet consectetur adipisicing elit. Nam, impedit natus itaque fuga aperiam qui eos ut."})]}),(0,s.jsxs)("div",{className:"tab-pane fade",id:"contact",role:"tabpanel","aria-labelledby":"contact-tab",children:[(0,s.jsx)("h6",{children:"Im tab three!"}),(0,s.jsx)("p",{className:"mb-0",children:"Lorem ipsum, dolor sit amet consectetur adipisicing elit. Nam, impedit natus itaque fuga aperiam qui eos ut."})]})]})]})})})]})}))}},a=>{var e=e=>a(a.s=e);a.O(0,[6874,2703,3008,8441,1684,7358],()=>e(42528)),_N_E=a.O()}]);