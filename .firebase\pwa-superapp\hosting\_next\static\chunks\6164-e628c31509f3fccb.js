(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6164],{7111:function(t,e,i){t.exports=function(t,e,i,n){"use strict";let s={allowList:i.DefaultAllowlist,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},o={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},r={entry:"(string|element|function|null)",selector:"(string|element)"};class l extends e{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return s}static get DefaultType(){return o}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map(t=>this._resolvePossibleFunction(t)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){let t=document.createElement("div");for(let[e,i]of(t.innerHTML=this._maybeSanitize(this._config.template),Object.entries(this._config.content)))this._setContent(t,i,e);let e=t.children[0],i=this._resolvePossibleFunction(this._config.extraClass);return i&&e.classList.add(...i.split(" ")),e}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(let[e,i]of Object.entries(t))super._typeCheckConfig({selector:e,entry:i},r)}_setContent(e,i,s){let o=t.findOne(s,e);if(o){if(!(i=this._resolvePossibleFunction(i)))return void o.remove();if(n.isElement(i))return void this._putElementInTemplate(n.getElement(i),o);if(this._config.html){o.innerHTML=this._maybeSanitize(i);return}o.textContent=i}}_maybeSanitize(t){return this._config.sanitize?i.sanitizeHtml(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return n.execute(t,[this])}_putElementInTemplate(t,e){if(this._config.html){e.innerHTML="",e.append(t);return}e.textContent=t.textContent}}return l}(i(27346),i(91408),i(76949),i(60250))},76949:function(t,e){(function(t){"use strict";let e=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),i=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,n=(t,n)=>{let s=t.nodeName.toLowerCase();return n.includes(s)?!e.has(s)||!!i.test(t.nodeValue):n.filter(t=>t instanceof RegExp).some(t=>t.test(s))};t.DefaultAllowlist={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},t.sanitizeHtml=function(t,e,i){if(!t.length)return t;if(i&&"function"==typeof i)return i(t);let s=new window.DOMParser().parseFromString(t,"text/html");for(let t of[].concat(...s.body.querySelectorAll("*"))){let i=t.nodeName.toLowerCase();if(!Object.keys(e).includes(i)){t.remove();continue}let s=[].concat(...t.attributes),o=[].concat(e["*"]||[],e[i]||[]);for(let e of s)n(e,o)||t.removeAttribute(e.nodeName)}return s.body.innerHTML},Object.defineProperty(t,Symbol.toStringTag,{value:"Module"})})(e)},96164:function(t,e,i){t.exports=function(t,e,i,n,s,o,r){"use strict";let l=function(t){let e=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(t){for(let i in t)if("default"!==i){let n=Object.getOwnPropertyDescriptor(t,i);Object.defineProperty(e,i,n.get?n:{enumerable:!0,get:()=>t[i]})}}return e.default=t,Object.freeze(e)}(t),a=new Set(["sanitize","allowList","sanitizeFn"]),c="fade",h="show",u=".modal",_="hide.bs.modal",g="hover",p="focus",m={AUTO:"auto",TOP:"top",RIGHT:s.isRTL()?"left":"right",BOTTOM:"bottom",LEFT:s.isRTL()?"right":"left"},f={allowList:o.DefaultAllowlist,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},d={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class b extends e{constructor(t,e){if(void 0===l)throw TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(t,e),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return f}static get DefaultType(){return d}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){if(this._isEnabled){if(this._activeTrigger.click=!this._activeTrigger.click,this._isShown())return void this._leave();this._enter()}}dispose(){clearTimeout(this._timeout),i.off(this._element.closest(u),_,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw Error("Please use show on visible elements");if(!(this._isWithContent()&&this._isEnabled))return;let t=i.trigger(this._element,this.constructor.eventName("show")),e=(s.findShadowRoot(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!e)return;this._disposePopper();let n=this._getTipElement();this._element.setAttribute("aria-describedby",n.getAttribute("id"));let{container:o}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(o.append(n),i.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(n),n.classList.add(h),"ontouchstart"in document.documentElement)for(let t of[].concat(...document.body.children))i.on(t,"mouseover",s.noop);this._queueCallback(()=>{i.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1},this.tip,this._isAnimated())}hide(){if(this._isShown()&&!i.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(h),"ontouchstart"in document.documentElement)for(let t of[].concat(...document.body.children))i.off(t,"mouseover",s.noop);this._activeTrigger.click=!1,this._activeTrigger[p]=!1,this._activeTrigger[g]=!1,this._isHovered=null,this._queueCallback(()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),i.trigger(this._element,this.constructor.eventName("hidden")))},this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){let e=this._getTemplateFactory(t).toHtml();if(!e)return null;e.classList.remove(c,h),e.classList.add(`bs-${this.constructor.NAME}-auto`);let i=s.getUID(this.constructor.NAME).toString();return e.setAttribute("id",i),this._isAnimated()&&e.classList.add(c),e}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new r({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{".tooltip-inner":this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(c)}_isShown(){return this.tip&&this.tip.classList.contains(h)}_createPopper(t){let e=m[s.execute(this._config.placement,[this,t,this._element]).toUpperCase()];return l.createPopper(this._element,t,this._getPopperConfig(e))}_getOffset(){let{offset:t}=this._config;return"string"==typeof t?t.split(",").map(t=>Number.parseInt(t,10)):"function"==typeof t?e=>t(e,this._element):t}_resolvePossibleFunction(t){return s.execute(t,[this._element])}_getPopperConfig(t){let e={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:t=>{this._getTipElement().setAttribute("data-popper-placement",t.state.placement)}}]};return{...e,...s.execute(this._config.popperConfig,[e])}}_setListeners(){for(let t of this._config.trigger.split(" "))if("click"===t)i.on(this._element,this.constructor.eventName("click"),this._config.selector,t=>{this._initializeOnDelegatedTarget(t).toggle()});else if("manual"!==t){let e=t===g?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),n=t===g?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");i.on(this._element,e,this._config.selector,t=>{let e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusin"===t.type?p:g]=!0,e._enter()}),i.on(this._element,n,this._config.selector,t=>{let e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusout"===t.type?p:g]=e._element.contains(t.relatedTarget),e._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},i.on(this._element.closest(u),_,this._hideModalHandler)}_fixTitle(){let t=this._element.getAttribute("title");t&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){if(this._isShown()||this._isHovered){this._isHovered=!0;return}this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show)}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){let e=n.getDataAttributes(this._element);for(let t of Object.keys(e))a.has(t)&&delete e[t];return t={...e,..."object"==typeof t&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=!1===t.container?document.body:s.getElement(t.container),"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),t}_getDelegateConfig(){let t={};for(let[e,i]of Object.entries(this._config))this.constructor.Default[e]!==i&&(t[e]=i);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each(function(){let e=b.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw TypeError(`No method named "${t}"`);e[t]()}})}}return s.defineJQueryPlugin(b),b}(i(57984),i(19962),i(65613),i(41380),i(60250),i(76949),i(7111))}}]);