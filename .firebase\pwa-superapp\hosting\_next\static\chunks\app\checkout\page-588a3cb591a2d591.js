(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8279],{19019:(e,a,t)=>{"use strict";t.d(a,{A:()=>c});var l=t(95155),s=t(12115),r=["mousedown","touchstart"];let n=function(e,a,t){void 0===t&&(t=r);var l=(0,s.useRef)(a);(0,s.useEffect)(function(){l.current=a},[a]),(0,s.useEffect)(function(){for(var a=function(a){var t=e.current;t&&!t.contains(a.target)&&l.current(a)},s=0,r=t;s<r.length;s++)!function(e){for(var a=[],t=1;t<arguments.length;t++)a[t-1]=arguments[t];e&&e.addEventListener&&e.addEventListener.apply(e,a)}(document,r[s],a);return function(){for(var e=0,l=t;e<l.length;e++)!function(e){for(var a=[],t=1;t<arguments.length;t++)a[t-1]=arguments[t];e&&e.removeEventListener&&e.removeEventListener.apply(e,a)}(document,l[e],a)}},[t,e])},c=e=>{let{options:a,defaultCurrent:t,placeholder:r,className:c,onChange:o,name:i}=e,[d,m]=(0,s.useState)(!1),[u,h]=(0,s.useState)(a[t]),p=(0,s.useCallback)(()=>{m(!1)},[]),x=(0,s.useRef)(null);n(x,p);let f=e=>{h(e),o(e,i),p()};return(0,l.jsxs)("div",{className:"nice-select form-select-lg mb-3 ".concat(c||""," ").concat(d?"open":""),role:"button",tabIndex:0,onClick:()=>m(e=>!e),onKeyDown:e=>e,ref:x,children:[(0,l.jsx)("span",{className:"current",children:(null==u?void 0:u.text)||r}),(0,l.jsx)("ul",{className:"list",role:"menubar",onClick:e=>e.stopPropagation(),onKeyDown:e=>e.stopPropagation(),children:null==a?void 0:a.map((e,a)=>(0,l.jsx)("li",{"data-value":e.value,className:"option ".concat(e.value===(null==u?void 0:u.value)?"selected focus":""),style:{fontSize:"14px"},role:"menuitem",onClick:()=>f(e),onKeyDown:e=>e,children:e.text},a))})]})}},38983:(e,a,t)=>{"use strict";t.d(a,{default:()=>c});var l=t(95155),s=t(6874),r=t.n(s);t(12115);let n=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],c=()=>(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,l.jsx)("div",{className:"container px-0",children:(0,l.jsx)("div",{className:"footer-nav position-relative",children:(0,l.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:n.map((e,a)=>(0,l.jsx)("li",{children:(0,l.jsxs)(r(),{href:"/".concat(e.link),children:[(0,l.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,l.jsx)("span",{children:e.title})]})},a))})})})})})},73759:(e,a,t)=>{Promise.resolve().then(t.bind(t,94465))},94465:(e,a,t)=>{"use strict";t.d(a,{default:()=>i});var l=t(95155);t(12115);var s=t(38983),r=t(91727),n=t(19019),c=t(6874),o=t.n(c);let i=()=>{let e=e=>{};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(r.default,{links:"pages",title:"Checkout"}),(0,l.jsx)("div",{className:"page-content-wrapper py-3",children:(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"checkout-wrapper-area",children:(0,l.jsx)("div",{className:"card",children:(0,l.jsxs)("div",{className:"card-body checkout-form",children:[(0,l.jsx)("h6",{className:"mb-3",children:"Enter your billing details"}),(0,l.jsxs)("form",{onSubmit:e=>e.preventDefault(),children:[(0,l.jsx)("div",{className:"form-group",children:(0,l.jsx)("input",{className:"form-control mb-3",type:"text",placeholder:"Your full name"})}),(0,l.jsx)("div",{className:"form-group",children:(0,l.jsx)("input",{className:"form-control mb-3",type:"text",placeholder:"Your company"})}),(0,l.jsx)("div",{className:"form-group",children:(0,l.jsx)("input",{className:"form-control mb-3",type:"email",placeholder:"Your email"})}),(0,l.jsx)("div",{className:"form-group",children:(0,l.jsx)("input",{className:"form-control mb-3",type:"text",placeholder:"Your mobile number"})}),(0,l.jsx)("div",{className:"form-group rk_select",children:(0,l.jsx)(n.A,{className:"form-select mb-3 d-flex align-items-center shop_grid_style checkout_select",options:[{value:"01",text:"Your Country"},{value:"02",text:"Bangladesh"},{value:"03",text:"India"},{value:"04",text:"United"},{value:"05",text:"United"}],defaultCurrent:0,onChange:e,placeholder:"Select an option",name:"myNiceSelect"})}),(0,l.jsx)("div",{className:"form-group rk_select",children:(0,l.jsx)(n.A,{className:"form-select mb-3 d-flex align-items-center shop_grid_style checkout_select",options:[{value:"01",text:"Your City"},{value:"02",text:"Dhaka"},{value:"03",text:"Barishal"},{value:"04",text:"Khulna"}],defaultCurrent:0,onChange:e,placeholder:"Select an option",name:"myNiceSelect"})}),(0,l.jsx)("div",{className:"form-group",children:(0,l.jsx)("input",{className:"form-control mb-3",type:"text",placeholder:"Street address"})}),(0,l.jsx)("div",{className:"form-group",children:(0,l.jsx)("input",{className:"form-control mb-3",type:"text",placeholder:"Postcode or ZIP"})}),(0,l.jsx)("div",{className:"form-group",children:(0,l.jsx)("input",{className:"form-control mb-3",type:"text",placeholder:"Postcode or ZIP"})}),(0,l.jsx)("div",{className:"form-group",children:(0,l.jsx)("textarea",{className:"form-control mb-3",id:"notes",name:"notes",cols:30,rows:10,placeholder:"Notes"})}),(0,l.jsxs)("div",{className:"form-check mb-2",children:[(0,l.jsx)("input",{className:"form-check-input",type:"radio",name:"exampleRadio",id:"darkRadio1",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"darkRadio1",children:"Regular Courier $0.49"})]}),(0,l.jsxs)("div",{className:"form-check mb-2",children:[(0,l.jsx)("input",{className:"form-check-input",type:"radio",name:"exampleRadio",id:"darkRadio2"}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"darkRadio2",children:"Free Shipping $0"})]}),(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input",type:"radio",name:"exampleRadio",id:"darkRadio3"}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"darkRadio3",children:"Emergency Delivery $2"})]}),(0,l.jsx)(o(),{href:"/payment-confirm",children:(0,l.jsx)("button",{className:"btn btn-danger mt-3 w-100",type:"submit",children:"Pay Now & Order"})})]})]})})})})}),(0,l.jsx)(s.default,{})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[6874,1955,1531,1727,8441,1684,7358],()=>a(73759)),_N_E=e.O()}]);