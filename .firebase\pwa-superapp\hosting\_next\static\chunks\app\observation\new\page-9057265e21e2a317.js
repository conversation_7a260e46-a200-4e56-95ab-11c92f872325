(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4110],{1704:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>I});var o=n(95155),a=n(12115),i=n(23327),r=n(16639),s=n(43630),l=n(60902),c=n(16344),d=n(54239);n(35279);var u=n(38336),m=n(26957),v=n(46554),b=n(24752),x=n.n(b),h=n(35695),p=n(27347);let w={observationCategory:"",observationType:"",observationActOrCondition:"",description:"",comments:"",dueDate:new Date().toISOString(),rectifiedOnSpot:!1,isQR:!1,actionToBeTaken:"",isReviewerRequired:!1,evidence:[],uploads:[],actionTaken:"",locationOneId:"",locationTwoId:"",locationThreeId:"",locationFourId:"",locationFiveId:"",locationSixId:"",actionOwnerId:"",multiActionOwnersIds:[],reviewerId:""};function I(){let[e,t]=(0,a.useState)(w),[n,b]=(0,a.useState)([]),[I,g]=(0,a.useState)([]),[O,j]=(0,a.useState)({}),f=(0,h.useRouter)();(0,a.useEffect)(()=>{A(),T()},[e]);let A=(0,a.useCallback)(async()=>{try{let t=await u.A.post(m.u3,{locationOneId:e.locationOneId||"",locationTwoId:e.locationTwoId||"",locationThreeId:e.locationThreeId||"",locationFourId:e.locationFourId||"",mode:"obsactionowner"});if(200===t.status){let e=t.data.map(e=>({label:e.firstName,value:e.id}));b(e)}}catch(e){console.error("Error fetching crew list:",e)}},[e.locationOneId,e.locationTwoId,e.locationThreeId,e.locationFourId]),T=(0,a.useCallback)(async()=>{try{let t=await u.A.post(m.u3,{locationOneId:e.locationOneId||"",locationTwoId:e.locationTwoId||"",locationThreeId:e.locationThreeId||"",locationFourId:e.locationFourId||"",mode:"obsreviewer"});if(200===t.status){let e=t.data.map(e=>({label:e.firstName,value:e.id}));g(e)}}catch(e){console.error("Error fetching crew list:",e)}},[e.locationOneId,e.locationTwoId,e.locationThreeId,e.locationFourId]),y=()=>{let t={};return e.locationOneId||(t.locationOneId="Location is required."),e.observationCategory||(t.observationCategory="Observation Category is required."),e.observationType||(t.observationType="Observation Type is required."),e.observationActOrCondition||(t.observationActOrCondition="Observation Act or Condition is required."),e.description.trim()||(t.description="Description is required."),e.uploads.length<1&&(t.uploads="Please attach at least 1 supporting documents/images."),"Unsafe"===e.observationType&&("boolean"!=typeof e.rectifiedOnSpot?t.rectifiedOnSpot="Rectified on Spot is required.":e.rectifiedOnSpot?(e.actionTaken.trim()||(t.actionTaken="Action Taken is required."),e.evidence.length<1&&(t.evidence="Please attach at least 1 evidence files.")):"boolean"!=typeof e.isReviewerRequired?t.isReviewerRequired="Please specify if Reviewer is required.":e.isReviewerRequired&&!e.reviewerId?t.reviewerId="Please select a Reviewer.":!e.isReviewerRequired&&(e.actionToBeTaken.trim()||(t.actionToBeTaken="Action to be Taken is required."),e.dueDate||(t.dueDate="Due Date is required."),e.actionOwnerId||0!==e.multiActionOwnersIds.length||(t.actionOwnerId="Action Owner is required."))),j(t),0===Object.keys(t).length},N=async t=>{if(t.preventDefault(),!y())return void x().fire({title:"Validation Error",text:"Please fill all required fields correctly.",icon:"warning",confirmButtonText:"OK"});try{let t={...e};1===e.multiActionOwnersIds.length?(t.actionOwnerId=e.multiActionOwnersIds[0],delete t.multiActionOwnersIds):2===e.multiActionOwnersIds.length?(t.multiActionOwnersIds=e.multiActionOwnersIds,t.actionOwnerId=""):(t.actionOwnerId="",delete t.multiActionOwnersIds);let n=await u.A.post(m.MO,t);200===n.status&&x().fire({title:"Observation Report",text:"Submitted Successfully",icon:"success",confirmButtonText:"OK"}).then(()=>{f.back()})}catch(e){console.error("Error submitting observation report:",e),x().fire({title:"Error",text:"Something went wrong. Please try again.",icon:"error"})}};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(v.default,{heading:"New Observation"}),(0,o.jsx)("div",{className:"page-content-wrapper py-3",children:(0,o.jsx)("div",{className:"container",children:(0,o.jsx)("div",{className:"card",children:(0,o.jsxs)("div",{className:"card-body p-3",children:[(0,o.jsxs)("div",{className:"mb-4",children:[(0,o.jsx)("h5",{className:"mb-3",children:"Location Details"}),(0,o.jsxs)("div",{className:"bg-light shadow-sm p-2",children:[(0,o.jsx)(i.A,{handleFilter:(e,n,o,a,i,r)=>{t(t=>({...t,locationOneId:e,locationTwoId:n,locationThreeId:o,locationFourId:a,locationFiveId:i,locationSixId:r}))},getLocation:e,disabled:!1}),O.locationOneId&&(0,o.jsx)("div",{className:"text-danger mt-1",children:O.locationOneId})]})]}),(0,o.jsxs)("div",{className:"mb-4",children:[(0,o.jsx)("h5",{className:"mb-3",children:"Observation Details"}),(0,o.jsxs)(r.A.Group,{controlId:"observationCategory",className:"mb-3",children:[(0,o.jsx)(r.A.Label,{children:"Category"}),(0,o.jsx)("div",{className:"d-flex gap-2",children:["Environment","HSE","Social"].map(n=>{let a=e.observationCategory===n,i="Environment"===n?"#dc3545":"HSE"===n?"#fd7e14":"#0d6efd";return(0,o.jsx)("button",{type:"button",className:"btn flex-grow-1 ".concat(a?"text-white":"btn-outline-secondary"),style:{backgroundColor:a?i:"transparent",borderColor:a?i:"#6c757d",transition:"all 0.3s ease-in-out"},onClick:()=>t({...e,observationCategory:n,observationType:"",observationActOrCondition:""}),children:n},n)})}),O.observationCategory&&(0,o.jsx)("div",{className:"text-danger mt-1",children:O.observationCategory})]}),(0,o.jsxs)(r.A.Group,{controlId:"observationType",className:"mb-3",children:[(0,o.jsxs)(r.A.Label,{children:["Observation Type ",(0,o.jsx)("span",{className:"text-danger",children:"*"})]}),(0,o.jsx)("div",{className:"d-flex gap-2",children:"Environment"===e.observationCategory||"Social"===e.observationCategory?[{label:"Positive",value:"Safe",color:"#198754"},{label:"Negative",value:"Unsafe",color:"#dc3545"}].map(n=>{let{label:a,value:i,color:r}=n,s=e.observationType===i;return(0,o.jsx)("button",{type:"button",className:"btn flex-grow-1 ".concat(s?"text-white":"btn-outline-secondary"),style:{backgroundColor:s?r:"transparent",borderColor:s?r:"#6c757d",transition:"all 0.3s ease-in-out"},onClick:()=>t({...e,observationType:i,observationActOrCondition:"",rectifiedOnSpot:!1,actionTaken:"",evidence:[],isReviewerRequired:!1,reviewerId:"",actionToBeTaken:"",dueDate:new Date().toISOString(),actionOwnerId:"",multiActionOwnersIds:[]}),children:a},i)}):[{label:"Safe",value:"Safe",color:"#198754"},{label:"Unsafe",value:"Unsafe",color:"#dc3545"}].map(n=>{let{label:a,value:i,color:r}=n,s=e.observationType===i;return(0,o.jsx)("button",{type:"button",className:"btn flex-grow-1 ".concat(s?"text-white":"btn-outline-secondary"),style:{backgroundColor:s?r:"transparent",borderColor:s?r:"#6c757d",transition:"all 0.3s ease-in-out"},onClick:()=>t({...e,observationType:i,observationActOrCondition:"",rectifiedOnSpot:!1,actionTaken:"",evidence:[],isReviewerRequired:!1,reviewerId:"",actionToBeTaken:"",dueDate:new Date().toISOString(),actionOwnerId:"",multiActionOwnersIds:[]}),children:a},i)})}),O.observationType&&(0,o.jsx)("div",{className:"text-danger mt-1",children:O.observationType})]}),(0,o.jsxs)(r.A.Group,{controlId:"observationActOrCondition",className:"mb-3",children:[(0,o.jsxs)(r.A.Label,{children:["Observation Act or Condition ",(0,o.jsx)("span",{className:"text-danger",children:"*"})]}),(0,o.jsx)("div",{className:"d-flex gap-2",children:[{label:"Act",value:"Act"},{label:"Condition",value:"Condition"}].map(n=>{let{label:a,value:i}=n,r=e.observationActOrCondition===i,s="Safe"===e.observationType?"#198754":"#dc3545";return(0,o.jsx)("button",{type:"button",className:"btn flex-grow-1 ".concat(r?"text-white":"btn-outline-secondary"),style:{backgroundColor:r?s:"transparent",borderColor:r?s:"#6c757d",transition:"all 0.3s ease-in-out"},onClick:()=>t({...e,observationActOrCondition:i}),children:a},i)})}),O.observationActOrCondition&&(0,o.jsx)("div",{className:"text-danger mt-1",children:O.observationActOrCondition})]}),(0,o.jsxs)(r.A.Group,{controlId:"description",className:"mb-3",children:[(0,o.jsx)(r.A.Label,{children:"Enter a detailed description of the observation"}),(0,o.jsx)(r.A.Control,{as:"textarea",rows:3,placeholder:"Provide details about what was observed...",value:e.description||"",onChange:e=>t(t=>({...t,description:e.target.value}))}),O.description&&(0,o.jsx)("div",{className:"text-danger mt-1",children:O.description})]}),(0,o.jsx)(s.A,{children:"Upload any relevant photos or documents. You can select multiple files."}),(0,o.jsxs)(r.A.Group,{controlId:"attachments",className:"mb-3",children:[(0,o.jsx)("div",{className:"mb-2",children:(0,o.jsx)(c.A,{onFilesSelected:e=>t(t=>({...t,uploads:e})),files:e.uploads,disabled:!1})}),(0,o.jsx)("small",{className:"text-muted"}),O.uploads&&(0,o.jsx)("div",{className:"text-danger mt-1",children:O.uploads})]}),"Unsafe"===e.observationType&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(r.A.Group,{controlId:"rectifiedOnSpot",className:"mb-3",children:[(0,o.jsx)(r.A.Label,{children:"Was the observation addressed on the spot?"}),(0,o.jsx)("div",{className:"d-flex gap-2",children:[{label:"Yes",value:!0,color:"#198754"},{label:"No",value:!1,color:"#dc3545"}].map(n=>{let{label:a,value:i,color:r}=n,s=e.rectifiedOnSpot===i;return(0,o.jsx)("button",{type:"button",className:"btn flex-grow-1 ".concat(s?"text-white":"btn-outline-secondary"),style:{backgroundColor:s?r:"transparent",borderColor:s?r:"#6c757d",transition:"all 0.3s ease-in-out"},onClick:()=>t({...e,rectifiedOnSpot:i,actionTaken:"",evidence:[],isReviewerRequired:!1,reviewerId:"",actionToBeTaken:"",dueDate:new Date().toISOString(),actionOwnerId:"",multiActionOwnersIds:[]}),children:a},a)})}),O.rectifiedOnSpot&&(0,o.jsx)("div",{className:"text-danger mt-1",children:O.rectifiedOnSpot})]}),e.rectifiedOnSpot?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(r.A.Group,{controlId:"actionTaken",className:"mt-3",children:[(0,o.jsx)(r.A.Label,{children:"Action Taken"}),(0,o.jsx)(r.A.Control,{type:"text",value:e.actionTaken||"",onChange:e=>t(t=>({...t,actionTaken:e.target.value}))}),O.actionTaken&&(0,o.jsx)("div",{className:"text-danger mt-1",children:O.actionTaken})]}),(0,o.jsxs)(r.A.Group,{controlId:"evidence",className:"mb-3",children:[(0,o.jsx)(r.A.Label,{children:"Upload evidence for the actions taken."}),(0,o.jsx)("div",{className:"mb-2",children:(0,o.jsx)(c.A,{onFilesSelected:e=>t(t=>({...t,evidence:e})),files:e.evidence,disabled:!1})}),O.evidence&&(0,o.jsx)("div",{className:"text-danger mt-1",children:O.evidence})]})]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(r.A.Group,{controlId:"isReviewerRequired",className:"mb-3",children:[(0,o.jsx)(r.A.Label,{children:"Do you Need to send this Observation to Reviewer?"}),(0,o.jsx)("div",{className:"d-flex gap-2",children:[{label:"Yes",value:!0,color:"#198754"},{label:"No",value:!1,color:"#dc3545"}].map(n=>{let{label:a,value:i,color:r}=n,s=e.isReviewerRequired===i;return(0,o.jsx)("button",{type:"button",className:"btn flex-grow-1 ".concat(s?"text-white":"btn-outline-secondary"),style:{backgroundColor:s?r:"transparent",borderColor:s?r:"#6c757d",transition:"all 0.3s ease-in-out"},onClick:()=>t({...e,isReviewerRequired:i,reviewerId:i?"":e.reviewerId,actionToBeTaken:i?"":e.actionToBeTaken,dueDate:i?new Date().toISOString():e.dueDate,actionOwnerId:i?"":e.actionOwnerId,multiActionOwnersIds:i?[]:e.multiActionOwnersIds}),children:a},a)})}),O.isReviewerRequired&&(0,o.jsx)("div",{className:"text-danger mt-1",children:O.isReviewerRequired})]}),e.isReviewerRequired?(0,o.jsx)(o.Fragment,{children:(0,o.jsxs)(r.A.Group,{className:"mb-3 mt-3",children:[(0,o.jsx)(p.A,{title:"Reviewer",options:I,selectedValue:e.reviewerId,onChange:e=>{t(t=>({...t,reviewerId:e}))},placeholder:"Select Reviewer",clearable:!0,disabled:!1}),O.reviewerId&&(0,o.jsx)("div",{className:"text-danger mt-1",children:O.reviewerId})]})}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(r.A.Group,{controlId:"actionToBeTaken",className:"mb-3 mt-3",children:[(0,o.jsx)(r.A.Label,{className:"mb-3",children:"Action to be Taken"}),(0,o.jsx)(r.A.Control,{type:"text",value:e.actionToBeTaken||"",onChange:e=>t(t=>({...t,actionToBeTaken:e.target.value}))}),O.actionToBeTaken&&(0,o.jsx)("div",{className:"text-danger mt-1",children:O.actionToBeTaken})]}),(0,o.jsxs)(r.A.Group,{controlId:"dueDate",className:"mb-3",children:[(0,o.jsx)(r.A.Label,{className:"mb-3",children:"Due Date"}),(0,o.jsx)("div",{className:"d-block d-flex",children:(0,o.jsx)(d.Ay,{selected:e.dueDate?new Date(e.dueDate):null,onChange:e=>t(t=>({...t,dueDate:e?e.toISOString():""})),dateFormat:"yyyy-MM-dd",className:"form-control d-flex mt-2 w-100",placeholderText:"Select a date",minDate:new Date})}),O.dueDate&&(0,o.jsx)("div",{className:"text-danger mt-1",children:O.dueDate})]}),(0,o.jsxs)(r.A.Group,{className:"mb-3",children:[(0,o.jsx)(p.A,{title:"Action Owner (Select 1 or 2)",options:n,multiSelect:!0,selectedValues:e.multiActionOwnersIds,onMultiChange:e=>{t(t=>({...t,multiActionOwnersIds:e,actionOwnerId:1===e.length?e[0]:""}))},placeholder:"Select Action Owner(s)",clearable:!0,disabled:!1,maxSelections:2}),O.actionOwnerId&&(0,o.jsx)("div",{className:"text-danger mt-1",children:O.actionOwnerId})]})]})]})]})]})]})})})}),(0,o.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,o.jsx)("div",{className:"container px-0",children:(0,o.jsx)("div",{className:"footer-nav position-relative",children:(0,o.jsx)("div",{className:"h-100 d-flex align-items-center justify-content-between ps-0 w-100",children:(0,o.jsx)(l.A,{variant:"primary",className:" w-100",onClick:N,children:"Submit"})})})})})]})}},88090:(e,t,n)=>{Promise.resolve().then(n.bind(n,1704))}},e=>{var t=t=>e(e.s=t);e.O(0,[3496,5302,8320,5579,2545,6078,635,4816,1205,5898,6639,9697,3678,124,3066,9314,8441,1684,7358],()=>t(88090)),_N_E=e.O()}]);