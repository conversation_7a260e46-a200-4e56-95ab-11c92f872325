"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1955],{57984:(e,t,n)=>{n.r(t),n.d(t,{afterMain:()=>O,afterRead:()=>b,afterWrite:()=>D,applyStyles:()=>B,arrow:()=>Z,auto:()=>s,basePlacements:()=>f,beforeMain:()=>w,beforeRead:()=>g,beforeWrite:()=>j,bottom:()=>o,clippingParents:()=>l,computeStyles:()=>en,createPopper:()=>ek,createPopperBase:()=>eP,createPopperLite:()=>eL,detectOverflow:()=>ev,end:()=>p,eventListeners:()=>eo,flip:()=>eg,hide:()=>ew,left:()=>a,main:()=>x,modifierPhases:()=>A,offset:()=>ex,placements:()=>v,popper:()=>d,popperGenerator:()=>eA,popperOffsets:()=>eO,preventOverflow:()=>ej,read:()=>y,reference:()=>h,right:()=>i,start:()=>c,top:()=>r,variationPlacements:()=>m,viewport:()=>u,write:()=>E});var r="top",o="bottom",i="right",a="left",s="auto",f=[r,o,i,a],c="start",p="end",l="clippingParents",u="viewport",d="popper",h="reference",m=f.reduce(function(e,t){return e.concat([t+"-"+c,t+"-"+p])},[]),v=[].concat(f,[s]).reduce(function(e,t){return e.concat([t,t+"-"+c,t+"-"+p])},[]),g="beforeRead",y="read",b="afterRead",w="beforeMain",x="main",O="afterMain",j="beforeWrite",E="write",D="afterWrite",A=[g,y,b,w,x,O,j,E,D];function P(e){return e?(e.nodeName||"").toLowerCase():null}function k(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function L(e){var t=k(e).Element;return e instanceof t||e instanceof Element}function M(e){var t=k(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function W(e){if("undefined"==typeof ShadowRoot)return!1;var t=k(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}let B={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},o=t.elements[e];M(o)&&P(o)&&(Object.assign(o.style,n),Object.keys(r).forEach(function(e){var t=r[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(e){var r=t.elements[e],o=t.attributes[e]||{},i=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce(function(e,t){return e[t]="",e},{});M(r)&&P(r)&&(Object.assign(r.style,i),Object.keys(o).forEach(function(e){r.removeAttribute(e)}))})}},requires:["computeStyles"]};function H(e){return e.split("-")[0]}var R=Math.max,T=Math.min,S=Math.round;function C(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function V(){return!/^((?!chrome|android).)*safari/i.test(C())}function q(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),o=1,i=1;t&&M(e)&&(o=e.offsetWidth>0&&S(r.width)/e.offsetWidth||1,i=e.offsetHeight>0&&S(r.height)/e.offsetHeight||1);var a=(L(e)?k(e):window).visualViewport,s=!V()&&n,f=(r.left+(s&&a?a.offsetLeft:0))/o,c=(r.top+(s&&a?a.offsetTop:0))/i,p=r.width/o,l=r.height/i;return{width:p,height:l,top:c,right:f+p,bottom:c+l,left:f,x:f,y:c}}function N(e){var t=q(e),n=e.offsetWidth,r=e.offsetHeight;return 1>=Math.abs(t.width-n)&&(n=t.width),1>=Math.abs(t.height-r)&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function _(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&W(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function I(e){return k(e).getComputedStyle(e)}function F(e){return((L(e)?e.ownerDocument:e.document)||window.document).documentElement}function U(e){return"html"===P(e)?e:e.assignedSlot||e.parentNode||(W(e)?e.host:null)||F(e)}function z(e){return M(e)&&"fixed"!==I(e).position?e.offsetParent:null}function X(e){for(var t=k(e),n=z(e);n&&["table","td","th"].indexOf(P(n))>=0&&"static"===I(n).position;)n=z(n);return n&&("html"===P(n)||"body"===P(n)&&"static"===I(n).position)?t:n||function(e){var t=/firefox/i.test(C());if(/Trident/i.test(C())&&M(e)&&"fixed"===I(e).position)return null;var n=U(e);for(W(n)&&(n=n.host);M(n)&&0>["html","body"].indexOf(P(n));){var r=I(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}function Y(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function G(e,t,n){return R(e,T(t,n))}function J(){return{top:0,right:0,bottom:0,left:0}}function K(e){return Object.assign({},J(),e)}function Q(e,t){return t.reduce(function(t,n){return t[n]=e,t},{})}let Z={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,s=e.name,c=e.options,p=n.elements.arrow,l=n.modifiersData.popperOffsets,u=H(n.placement),d=Y(u),h=[a,i].indexOf(u)>=0?"height":"width";if(p&&l){var m,v=(m=c.padding,K("number"!=typeof(m="function"==typeof m?m(Object.assign({},n.rects,{placement:n.placement})):m)?m:Q(m,f))),g=N(p),y="y"===d?r:a,b="y"===d?o:i,w=n.rects.reference[h]+n.rects.reference[d]-l[d]-n.rects.popper[h],x=l[d]-n.rects.reference[d],O=X(p),j=O?"y"===d?O.clientHeight||0:O.clientWidth||0:0,E=v[y],D=j-g[h]-v[b],A=j/2-g[h]/2+(w/2-x/2),P=G(E,A,D);n.modifiersData[s]=((t={})[d]=P,t.centerOffset=P-A,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;if(null!=r)("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&_(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function $(e){return e.split("-")[1]}var ee={top:"auto",right:"auto",bottom:"auto",left:"auto"};function et(e){var t,n,s,f,c,l,u,d=e.popper,h=e.popperRect,m=e.placement,v=e.variation,g=e.offsets,y=e.position,b=e.gpuAcceleration,w=e.adaptive,x=e.roundOffsets,O=e.isFixed,j=g.x,E=void 0===j?0:j,D=g.y,A=void 0===D?0:D,P="function"==typeof x?x({x:E,y:A}):{x:E,y:A};E=P.x,A=P.y;var L=g.hasOwnProperty("x"),M=g.hasOwnProperty("y"),W=a,B=r,H=window;if(w){var R=X(d),T="clientHeight",C="clientWidth";R===k(d)&&"static"!==I(R=F(d)).position&&"absolute"===y&&(T="scrollHeight",C="scrollWidth"),(m===r||(m===a||m===i)&&v===p)&&(B=o,A-=(O&&R===H&&H.visualViewport?H.visualViewport.height:R[T])-h.height,A*=b?1:-1),(m===a||(m===r||m===o)&&v===p)&&(W=i,E-=(O&&R===H&&H.visualViewport?H.visualViewport.width:R[C])-h.width,E*=b?1:-1)}var V=Object.assign({position:y},w&&ee),q=!0===x?(t={x:E,y:A},n=k(d),s=t.x,f=t.y,{x:S(s*(c=n.devicePixelRatio||1))/c||0,y:S(f*c)/c||0}):{x:E,y:A};return(E=q.x,A=q.y,b)?Object.assign({},V,((u={})[B]=M?"0":"",u[W]=L?"0":"",u.transform=1>=(H.devicePixelRatio||1)?"translate("+E+"px, "+A+"px)":"translate3d("+E+"px, "+A+"px, 0)",u)):Object.assign({},V,((l={})[B]=M?A+"px":"",l[W]=L?E+"px":"",l.transform="",l))}let en={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=n.adaptive,i=n.roundOffsets,a=void 0===i||i,s={placement:H(t.placement),variation:$(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:void 0===r||r,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,et(Object.assign({},s,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:void 0===o||o,roundOffsets:a})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,et(Object.assign({},s,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:a})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var er={passive:!0};let eo={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,o=r.scroll,i=void 0===o||o,a=r.resize,s=void 0===a||a,f=k(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&c.forEach(function(e){e.addEventListener("scroll",n.update,er)}),s&&f.addEventListener("resize",n.update,er),function(){i&&c.forEach(function(e){e.removeEventListener("scroll",n.update,er)}),s&&f.removeEventListener("resize",n.update,er)}},data:{}};var ei={left:"right",right:"left",bottom:"top",top:"bottom"};function ea(e){return e.replace(/left|right|bottom|top/g,function(e){return ei[e]})}var es={start:"end",end:"start"};function ef(e){return e.replace(/start|end/g,function(e){return es[e]})}function ec(e){var t=k(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function ep(e){return q(F(e)).left+ec(e).scrollLeft}function el(e){var t=I(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function eu(e,t){void 0===t&&(t=[]);var n,r=function e(t){return["html","body","#document"].indexOf(P(t))>=0?t.ownerDocument.body:M(t)&&el(t)?t:e(U(t))}(e),o=r===(null==(n=e.ownerDocument)?void 0:n.body),i=k(r),a=o?[i].concat(i.visualViewport||[],el(r)?r:[]):r,s=t.concat(a);return o?s:s.concat(eu(U(a)))}function ed(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function eh(e,t,n){var r,o,i,a,s,f,c,p,l,d;return t===u?ed(function(e,t){var n=k(e),r=F(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,s=0,f=0;if(o){i=o.width,a=o.height;var c=V();(c||!c&&"fixed"===t)&&(s=o.offsetLeft,f=o.offsetTop)}return{width:i,height:a,x:s+ep(e),y:f}}(e,n)):L(t)?((r=q(t,!1,"fixed"===n)).top=r.top+t.clientTop,r.left=r.left+t.clientLeft,r.bottom=r.top+t.clientHeight,r.right=r.left+t.clientWidth,r.width=t.clientWidth,r.height=t.clientHeight,r.x=r.left,r.y=r.top,r):ed((o=F(e),a=F(o),s=ec(o),f=null==(i=o.ownerDocument)?void 0:i.body,c=R(a.scrollWidth,a.clientWidth,f?f.scrollWidth:0,f?f.clientWidth:0),p=R(a.scrollHeight,a.clientHeight,f?f.scrollHeight:0,f?f.clientHeight:0),l=-s.scrollLeft+ep(o),d=-s.scrollTop,"rtl"===I(f||a).direction&&(l+=R(a.clientWidth,f?f.clientWidth:0)-c),{width:c,height:p,x:l,y:d}))}function em(e){var t,n=e.reference,s=e.element,f=e.placement,l=f?H(f):null,u=f?$(f):null,d=n.x+n.width/2-s.width/2,h=n.y+n.height/2-s.height/2;switch(l){case r:t={x:d,y:n.y-s.height};break;case o:t={x:d,y:n.y+n.height};break;case i:t={x:n.x+n.width,y:h};break;case a:t={x:n.x-s.width,y:h};break;default:t={x:n.x,y:n.y}}var m=l?Y(l):null;if(null!=m){var v="y"===m?"height":"width";switch(u){case c:t[m]=t[m]-(n[v]/2-s[v]/2);break;case p:t[m]=t[m]+(n[v]/2-s[v]/2)}}return t}function ev(e,t){void 0===t&&(t={});var n,a,s,c,p,m,v,g,y=t,b=y.placement,w=void 0===b?e.placement:b,x=y.strategy,O=void 0===x?e.strategy:x,j=y.boundary,E=y.rootBoundary,D=y.elementContext,A=void 0===D?d:D,k=y.altBoundary,W=y.padding,B=void 0===W?0:W,H=K("number"!=typeof B?B:Q(B,f)),S=e.rects.popper,C=e.elements[void 0!==k&&k?A===d?h:d:A],V=(n=L(C)?C:C.contextElement||F(e.elements.popper),a=void 0===j?l:j,s=void 0===E?u:E,v=(m=[].concat("clippingParents"===a?(c=eu(U(n)),!L(p=["absolute","fixed"].indexOf(I(n).position)>=0&&M(n)?X(n):n)?[]:c.filter(function(e){return L(e)&&_(e,p)&&"body"!==P(e)})):[].concat(a),[s]))[0],(g=m.reduce(function(e,t){var r=eh(n,t,O);return e.top=R(r.top,e.top),e.right=T(r.right,e.right),e.bottom=T(r.bottom,e.bottom),e.left=R(r.left,e.left),e},eh(n,v,O))).width=g.right-g.left,g.height=g.bottom-g.top,g.x=g.left,g.y=g.top,g),N=q(e.elements.reference),z=em({reference:N,element:S,strategy:"absolute",placement:w}),Y=ed(Object.assign({},S,z)),G=A===d?Y:N,J={top:V.top-G.top+H.top,bottom:G.bottom-V.bottom+H.bottom,left:V.left-G.left+H.left,right:G.right-V.right+H.right},Z=e.modifiersData.offset;if(A===d&&Z){var $=Z[w];Object.keys(J).forEach(function(e){var t=[i,o].indexOf(e)>=0?1:-1,n=[r,o].indexOf(e)>=0?"y":"x";J[e]+=$[n]*t})}return J}let eg={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,p=e.name;if(!t.modifiersData[p]._skip){for(var l=n.mainAxis,u=void 0===l||l,d=n.altAxis,h=void 0===d||d,g=n.fallbackPlacements,y=n.padding,b=n.boundary,w=n.rootBoundary,x=n.altBoundary,O=n.flipVariations,j=void 0===O||O,E=n.allowedAutoPlacements,D=t.options.placement,A=H(D)===D,P=g||(A||!j?[ea(D)]:function(e){if(H(e)===s)return[];var t=ea(e);return[ef(e),t,ef(t)]}(D)),k=[D].concat(P).reduce(function(e,n){var r,o,i,a,c,p,l,u,d,h,g,x;return e.concat(H(n)===s?(o=(r={placement:n,boundary:b,rootBoundary:w,padding:y,flipVariations:j,allowedAutoPlacements:E}).placement,i=r.boundary,a=r.rootBoundary,c=r.padding,p=r.flipVariations,u=void 0===(l=r.allowedAutoPlacements)?v:l,0===(g=(h=(d=$(o))?p?m:m.filter(function(e){return $(e)===d}):f).filter(function(e){return u.indexOf(e)>=0})).length&&(g=h),Object.keys(x=g.reduce(function(e,n){return e[n]=ev(t,{placement:n,boundary:i,rootBoundary:a,padding:c})[H(n)],e},{})).sort(function(e,t){return x[e]-x[t]})):n)},[]),L=t.rects.reference,M=t.rects.popper,W=new Map,B=!0,R=k[0],T=0;T<k.length;T++){var S=k[T],C=H(S),V=$(S)===c,q=[r,o].indexOf(C)>=0,N=q?"width":"height",_=ev(t,{placement:S,boundary:b,rootBoundary:w,altBoundary:x,padding:y}),I=q?V?i:a:V?o:r;L[N]>M[N]&&(I=ea(I));var F=ea(I),U=[];if(u&&U.push(_[C]<=0),h&&U.push(_[I]<=0,_[F]<=0),U.every(function(e){return e})){R=S,B=!1;break}W.set(S,U)}if(B)for(var z=j?3:1,X=function(e){var t=k.find(function(t){var n=W.get(t);if(n)return n.slice(0,e).every(function(e){return e})});if(t)return R=t,"break"},Y=z;Y>0&&"break"!==X(Y);Y--);t.placement!==R&&(t.modifiersData[p]._skip=!0,t.placement=R,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function ey(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function eb(e){return[r,i,o,a].some(function(t){return e[t]>=0})}let ew={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,i=t.modifiersData.preventOverflow,a=ev(t,{elementContext:"reference"}),s=ev(t,{altBoundary:!0}),f=ey(a,r),c=ey(s,o,i),p=eb(f),l=eb(c);t.modifiersData[n]={referenceClippingOffsets:f,popperEscapeOffsets:c,isReferenceHidden:p,hasPopperEscaped:l},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":p,"data-popper-escaped":l})}},ex={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,o=e.name,s=n.offset,f=void 0===s?[0,0]:s,c=v.reduce(function(e,n){var o,s,c,p,l,u;return e[n]=(o=t.rects,c=[a,r].indexOf(s=H(n))>=0?-1:1,l=(p="function"==typeof f?f(Object.assign({},o,{placement:n})):f)[0],u=p[1],l=l||0,u=(u||0)*c,[a,i].indexOf(s)>=0?{x:u,y:l}:{x:l,y:u}),e},{}),p=c[t.placement],l=p.x,u=p.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=u),t.modifiersData[o]=c}},eO={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=em({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},ej={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,s=e.name,f=n.mainAxis,p=n.altAxis,l=n.boundary,u=n.rootBoundary,d=n.altBoundary,h=n.padding,m=n.tether,v=void 0===m||m,g=n.tetherOffset,y=void 0===g?0:g,b=ev(t,{boundary:l,rootBoundary:u,padding:h,altBoundary:d}),w=H(t.placement),x=$(t.placement),O=!x,j=Y(w),E="x"===j?"y":"x",D=t.modifiersData.popperOffsets,A=t.rects.reference,P=t.rects.popper,k="function"==typeof y?y(Object.assign({},t.rects,{placement:t.placement})):y,L="number"==typeof k?{mainAxis:k,altAxis:k}:Object.assign({mainAxis:0,altAxis:0},k),M=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,W={x:0,y:0};if(D){if(void 0===f||f){var B,S="y"===j?r:a,C="y"===j?o:i,V="y"===j?"height":"width",q=D[j],_=q+b[S],I=q-b[C],F=v?-P[V]/2:0,U=x===c?A[V]:P[V],z=x===c?-P[V]:-A[V],K=t.elements.arrow,Q=v&&K?N(K):{width:0,height:0},Z=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:J(),ee=Z[S],et=Z[C],en=G(0,A[V],Q[V]),er=O?A[V]/2-F-en-ee-L.mainAxis:U-en-ee-L.mainAxis,eo=O?-A[V]/2+F+en+et+L.mainAxis:z+en+et+L.mainAxis,ei=t.elements.arrow&&X(t.elements.arrow),ea=ei?"y"===j?ei.clientTop||0:ei.clientLeft||0:0,es=null!=(B=null==M?void 0:M[j])?B:0,ef=G(v?T(_,q+er-es-ea):_,q,v?R(I,q+eo-es):I);D[j]=ef,W[j]=ef-q}if(void 0!==p&&p){var ec,ep,el="x"===j?r:a,eu="x"===j?o:i,ed=D[E],eh="y"===E?"height":"width",em=ed+b[el],eg=ed-b[eu],ey=-1!==[r,a].indexOf(w),eb=null!=(ep=null==M?void 0:M[E])?ep:0,ew=ey?em:ed-A[eh]-P[eh]-eb+L.altAxis,ex=ey?ed+A[eh]+P[eh]-eb-L.altAxis:eg,eO=v&&ey?(ec=G(ew,ed,ex))>ex?ex:ec:G(v?ew:em,ed,v?ex:eg);D[E]=eO,W[E]=eO-ed}t.modifiersData[s]=W}},requiresIfExists:["offset"]};var eE={placement:"bottom",modifiers:[],strategy:"absolute"};function eD(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}function eA(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,o=t.defaultOptions,i=void 0===o?eE:o;return function(e,t,n){void 0===n&&(n=i);var o,a,s={placement:"bottom",orderedModifiers:[],options:Object.assign({},eE,i),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},f=[],c=!1,p={state:s,setOptions:function(n){var o,a,c,u,d,h,m="function"==typeof n?n(s.options):n;l(),s.options=Object.assign({},i,s.options,m),s.scrollParents={reference:L(e)?eu(e):e.contextElement?eu(e.contextElement):[],popper:eu(t)};var v=(a=Object.keys(o=[].concat(r,s.options.modifiers).reduce(function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e},{})).map(function(e){return o[e]}),c=new Map,u=new Set,d=[],a.forEach(function(e){c.set(e.name,e)}),a.forEach(function(e){u.has(e.name)||function e(t){u.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){if(!u.has(t)){var n=c.get(t);n&&e(n)}}),d.push(t)}(e)}),h=d,A.reduce(function(e,t){return e.concat(h.filter(function(e){return e.phase===t}))},[]));return s.orderedModifiers=v.filter(function(e){return e.enabled}),s.orderedModifiers.forEach(function(e){var t=e.name,n=e.options,r=e.effect;if("function"==typeof r){var o=r({state:s,name:t,instance:p,options:void 0===n?{}:n});f.push(o||function(){})}}),p.update()},forceUpdate:function(){if(!c){var e=s.elements,t=e.reference,n=e.popper;if(eD(t,n)){s.rects={reference:(r=X(n),o="fixed"===s.options.strategy,i=M(r),u=M(r)&&(f=S((a=r.getBoundingClientRect()).width)/r.offsetWidth||1,l=S(a.height)/r.offsetHeight||1,1!==f||1!==l),d=F(r),h=q(t,u,o),m={scrollLeft:0,scrollTop:0},v={x:0,y:0},(i||!i&&!o)&&(("body"!==P(r)||el(d))&&(m=function(e){return e!==k(e)&&M(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:ec(e)}(r)),M(r)?(v=q(r,!0),v.x+=r.clientLeft,v.y+=r.clientTop):d&&(v.x=ep(d))),{x:h.left+m.scrollLeft-v.x,y:h.top+m.scrollTop-v.y,width:h.width,height:h.height}),popper:N(n)},s.reset=!1,s.placement=s.options.placement,s.orderedModifiers.forEach(function(e){return s.modifiersData[e.name]=Object.assign({},e.data)});for(var r,o,i,a,f,l,u,d,h,m,v,g=0;g<s.orderedModifiers.length;g++){if(!0===s.reset){s.reset=!1,g=-1;continue}var y=s.orderedModifiers[g],b=y.fn,w=y.options,x=void 0===w?{}:w,O=y.name;"function"==typeof b&&(s=b({state:s,options:x,name:O,instance:p})||s)}}}},update:(o=function(){return new Promise(function(e){p.forceUpdate(),e(s)})},function(){return a||(a=new Promise(function(e){Promise.resolve().then(function(){a=void 0,e(o())})})),a}),destroy:function(){l(),c=!0}};if(!eD(e,t))return p;function l(){f.forEach(function(e){return e()}),f=[]}return p.setOptions(n).then(function(e){!c&&n.onFirstUpdate&&n.onFirstUpdate(e)}),p}}var eP=eA(),ek=eA({defaultModifiers:[eo,eO,en,B,ex,eg,ej,Z,ew]}),eL=eA({defaultModifiers:[eo,eO,en,B]})}}]);