(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4065],{8495:(e,t,r)=>{Promise.resolve().then(r.bind(r,91767))},91767:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var a=r(95155),i=r(12115),s=r(23327),n=r(16639),o=r(60902),l=r(16344),c=r(16129);r(35279);var d=r(38336),p=r(26957),m=r(46554),u=r(24752),h=r.n(u),x=r(35695),v=r(24952),g=r(27347),N=r(34540),f=r(64044);r(3898),r(97165),r(38619);let j={uploads:[],supportingDocuments:[],permitStartDate:"",permitEndDate:"",workDescription:"",nameOfSiteSupervisor:"",applicantContactNo:"",supervisorContactNo:"",workOrderNo:"",noOfWorkers:0,permitWorkType:"",permitType:[],permitTag:"",status:"",permitRiskControl:[],riskAssessmentId:"",assessorId:"",approverId:"",locationFiveId:"",locationFourId:"",locationOneId:"",locationSixId:"",locationThreeId:"",locationTwoId:"",reviewerId:"",applicantStatus:{signature:""}};function b(){let[e,t]=(0,i.useState)(j),[r,u]=(0,i.useState)([]),[b,S]=(0,i.useState)([]),[k,y]=(0,i.useState)({}),w=(0,x.useRouter)(),I=(0,N.d4)(e=>e.login.user),A=new Date,C=(0,f.L)(A,48);console.log(I);let[D,T]=(0,i.useState)({}),O=(0,i.useRef)(null),[R,E]=(0,i.useState)([]),[W,_]=(0,i.useState)([]),F=r=>{let a=r?r.toISOString():"";t({...e,permitStartDate:a,permitEndDate:""})},L=r=>{let a=r?r.toISOString():"";t({...e,permitEndDate:a})},P=(0,i.useCallback)(async(t,r)=>{if(e.locationOneId)try{let a=await d.A.post(p.u3,{locationOneId:e.locationOneId,locationTwoId:e.locationTwoId,locationThreeId:e.locationThreeId,locationFourId:e.locationFourId,mode:t});if(200===a.status){let e=a.data.map(e=>{var t;return{label:"".concat(e.firstName," ").concat(null!=(t=e.lastName)?t:"").trim(),value:e.id}});r(e)}}catch(e){console.error("Error fetching ".concat(t,":"),e)}},[e.locationOneId,e.locationTwoId,e.locationThreeId,e.locationFourId]);(0,i.useEffect)(()=>{P("eptwReviewer",S),P("eptwAssessor",E)},[e]);let q=(e,t)=>{for(var r=atob(e.split(",")[1]),a=e.split(",")[0].split(":")[1].split(";")[0],i=new ArrayBuffer(r.length),s=new DataView(i),n=0;n<r.length;n++)s.setUint8(n,r.charCodeAt(n));return new File([i],t,{type:a})},G=async()=>{var e;let t="".concat(new Date().getTime(),"_captin_sign.png"),r=new FormData,a=q(null==(e=O.current)?void 0:e.toDataURL("image/png"),t);r.append("file",a);try{let e=await d.A.post(p.Dp,r,{headers:{"Content-Type":"multipart/form-data"}});if(e&&200===e.status)return e.data.files[0].originalname;throw Error("File upload failed.")}catch(e){throw console.error("File upload error:",e),e}},z=()=>{var t;let r={};return e.permitStartDate||(r.permitStartDate="Start date is required."),e.permitEndDate||(r.permitEndDate="End date is required."),e.workDescription||(r.workDescription="Work description is required."),e.nameOfSiteSupervisor||(r.nameOfSiteSupervisor="Site supervisor name is required."),e.applicantContactNo||(r.applicantContactNo="Applicant Contact number is required."),e.supervisorContactNo||(r.supervisorContactNo="Supervisor Contact number is required."),e.noOfWorkers||(r.noOfWorkers="Number of workers is required."),e.permitWorkType||(r.permitWorkType="Permit Type is required."),e.permitType&&0!==e.permitType.length||(r.permitType="Checkpoint selection is required."),(!e.supportingDocuments||e.supportingDocuments.length<2)&&(r.supportingDocuments="Minimum 2 files must be uploaded."),(null==I?void 0:I.type)!=="External"||e.reviewerId||(r.reviewerId="Reviewer is required."),(null==I?void 0:I.type)!=="Internal"||e.assessorId||(r.assessorId="Assessor is required."),O.current&&O.current.isEmpty()&&(r.signature="Applicant signature is required."),null==(t=e.permitRiskControl)||t.forEach((e,t)=>{var a;e.value||(r["control_value_".concat(t)]="Select Yes, No, or Not Applicable."),"No"!==e.value&&"Not Applicable"!==e.value||(null==(a=e.remarks)?void 0:a.trim())||(r["control_remarks_".concat(t)]="Remarks required for No or Not Applicable.")}),console.log(r),T(r),0===Object.keys(r).length},H=async()=>{if(z())try{let t=await G(),r={...e,applicantStatus:{...e.applicantStatus,signature:t,status:!0,comments:"",signedDate:new Date().toISOString()},noOfWorkers:Number(e.noOfWorkers)};console.log(r);let a=await d.A.post(p.OT,r);200===a.status&&(h().fire("Permit","Submitted Successfully","success"),w.back())}catch(e){var t,r;console.error("Error saving permit:",e),h().fire({icon:"error",title:"Save Error",text:(null==e||null==(r=e.response)||null==(t=r.data)?void 0:t.message)||"An error occurred while saving the permit. Please try again."})}else h().fire({icon:"error",title:"Save Error",text:"Please fill all required fields and try again."})},M=async e=>{try{let t=(await d.A.get("/".concat(e))).data.map(e=>({value:e.hazardName,label:e.hazardName,id:e.riskAssessmentId,controls:e.controls}));_(t)}catch(e){console.error("Error fetching permit type options:",e)}};(0,i.useEffect)(()=>{"High-Risk Hazard"===e.permitWorkType?M("high-risk-hazard-list"):"Routine"===e.permitWorkType?M("routine-list"):"Non Routine"===e.permitWorkType&&M("non-routine-list")},[e.permitWorkType]),(0,i.useEffect)(()=>{t({...e,permitWorkType:"High-Risk Hazard",permitType:[],permitRiskControl:[]})},[]);let U=e.permitRiskControl.reduce((e,t,r)=>((e[t.permitType]=e[t.permitType]||[]).push({...t,controlIndex:r}),e),{}),B=(e,r,a)=>{t(t=>{let i=[...t.permitRiskControl];return i[e][r]=a,{...t,permitRiskControl:i}})},V=(e,t)=>{let r="btn rounded-pill px-4 me-2 mb-2",a=t===e;return"Yes"===e?"".concat(r," ").concat(a?"btn-success text-white":"btn-outline-success"):"No"===e?"".concat(r," ").concat(a?"btn-danger text-white":"btn-outline-danger"):"Not Applicable"===e?"".concat(r," ").concat(a?"btn-warning text-white":"btn-outline-warning"):r},Y=(e,t)=>{B(e,"value",t)},J=async(e,r)=>{t(t=>{let a=[...t.permitRiskControl];return Array.isArray(a[e].evidence)||(a[e].evidence=[]),a[e].evidence=r,{...t,permitRiskControl:a}})};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.default,{heading:"New Permit"}),(0,a.jsx)("div",{className:"page-content-wrapper py-3",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"card",children:(0,a.jsxs)("div",{className:"card-body p-3",children:[(0,a.jsxs)(n.A.Group,{className:"mb-3",children:[(0,a.jsx)(n.A.Label,{children:"Permit Work Type *"}),(0,a.jsxs)(n.A.Select,{value:e.permitWorkType,onChange:e=>t(t=>({...t,permitWorkType:e.target.value,permitType:[],permitRiskControl:[]})),children:[(0,a.jsx)("option",{value:"",children:"Select Permit Work Type"}),(0,a.jsx)("option",{value:"High-Risk Hazard",children:"High-Risk Hazard Permit"}),(0,a.jsx)("option",{value:"Routine",children:"Routine Work Activity"}),(0,a.jsx)("option",{value:"Non Routine",children:"Non Routine Work Activity"})]}),D.permitWorkType&&(0,a.jsx)("p",{className:"text-danger",children:D.permitWorkType})]}),e.permitWorkType&&(0,a.jsxs)(n.A.Group,{className:"mb-3",children:[(0,a.jsx)(n.A.Label,{children:"Checkpoints *"}),(0,a.jsx)(c.Ay,{options:W,value:W.filter(t=>{var r;return null==(r=e.permitType)?void 0:r.includes(t.value)}),onChange:e=>{if(!e)return void t(e=>({...e,permitType:[],permitRiskControl:[],riskAssessmentId:""}));e.length>1&&(h().fire({icon:"error",title:"Selection Error",text:"Only one checkpoint can be selected for this Permit Work Type."}),e=e.slice(0,1));let r=e.map(e=>e.value),a=e.flatMap(e=>e.controls.map(t=>({remarks:"",evidence:[],description:t.value,currentType:t.current_type,files:t.files,method:t.method,permitType:e.label,value:""})));t(t=>{var i;return{...t,permitType:r,permitRiskControl:a,riskAssessmentId:(null==(i=e[0])?void 0:i.id)||""}})},placeholder:"Select Check Point(s)",isMulti:!0,isClearable:!0,isDisabled:!e.permitWorkType}),D.permitType&&(0,a.jsx)("p",{className:"text-danger",children:D.permitType})]}),(0,a.jsxs)(n.A.Group,{className:"row mb-3",children:[(0,a.jsxs)("div",{className:"col-md-12",children:[(0,a.jsx)(n.A.Label,{className:"d-flex",children:"Permit Start Date and Time *"}),(0,a.jsx)(n.A.Control,{type:"datetime-local",value:e.permitStartDate?new Date(e.permitStartDate).toISOString().slice(0,16):"",onChange:e=>{if(e.target.value){let t=new Date(e.target.value),r=t>=A,a=t<=C;if(r&&a)F(t);else{let e="Please select a valid date and time.";r?a||(e="Start date/time must be within 48 hours from now."):e="Start date/time cannot be in the past.",h().fire({icon:"error",title:"Invalid Date/Time",text:e})}}else F(null)},min:A.toISOString().slice(0,16),max:C.toISOString().slice(0,16)}),D.permitStartDate&&(0,a.jsx)("p",{className:"text-danger",children:D.permitStartDate})]}),(0,a.jsxs)("div",{className:"col-md-12",children:[(0,a.jsx)(n.A.Label,{className:"d-flex",children:"Permit End Date and Time *"}),(0,a.jsx)(n.A.Control,{type:"datetime-local",value:e.permitEndDate?new Date(e.permitEndDate).toISOString().slice(0,16):"",onChange:t=>{if(t.target.value){let r=new Date(t.target.value);if(e.permitStartDate){let t=new Date(e.permitStartDate),a=r>=t,i=r<=(0,f.L)(t,48);if(a&&i)L(r);else{let e="Please select a valid end date/time.";a?i||(e="End date/time must be within 48 hours of start date/time."):e="End date/time must be after start date/time.",h().fire({icon:"error",title:"Invalid Date/Time",text:e})}}else L(r)}else L(null)},disabled:!e.permitStartDate,min:e.permitStartDate?new Date(e.permitStartDate).toISOString().slice(0,16):A.toISOString().slice(0,16),max:e.permitStartDate?(0,f.L)(new Date(e.permitStartDate),48).toISOString().slice(0,16):(0,f.L)(A,48).toISOString().slice(0,16)}),D.permitEndDate&&(0,a.jsx)("p",{className:"text-danger",children:D.permitEndDate})]})]}),(0,a.jsxs)(n.A.Group,{controlId:"description",className:"mt-3",children:[(0,a.jsxs)(n.A.Label,{children:["Description ",(0,a.jsx)("span",{className:"text-danger",children:"*"})]}),(0,a.jsx)(n.A.Control,{as:"textarea",rows:3,value:e.workDescription||"",onChange:e=>t(t=>({...t,workDescription:e.target.value}))}),D.workDescription&&(0,a.jsx)("div",{className:"text-danger",children:D.workDescription})]}),(0,a.jsxs)("div",{className:"bg-light shadow-sm p-2 mt-3 mb-3",children:[(0,a.jsx)(s.A,{handleFilter:(e,r,a,i,s,n)=>{t(t=>({...t,locationOneId:e,locationTwoId:r,locationThreeId:a,locationFourId:i,locationFiveId:s,locationSixId:n}))},getLocation:e,disabled:!1}),k.locationOneId&&(0,a.jsx)("div",{className:"text-danger mt-1",children:k.locationOneId})]}),(0,a.jsxs)(n.A.Group,{className:"mb-3",children:[(0,a.jsx)(n.A.Label,{children:"Responsible Site / Job Supervisor"}),(0,a.jsx)(n.A.Control,{type:"text",name:"nameOfSiteSupervisor",value:e.nameOfSiteSupervisor||"",onChange:e=>t(t=>({...t,nameOfSiteSupervisor:e.target.value}))}),D.nameOfSiteSupervisor&&(0,a.jsx)("p",{className:"text-danger",children:D.nameOfSiteSupervisor})]}),(0,a.jsxs)(n.A.Group,{className:"mb-3",children:[(0,a.jsx)(n.A.Label,{children:"Number of Workers"}),(0,a.jsx)(n.A.Control,{type:"text",name:"nameOfSiteSupervisor",value:e.noOfWorkers||"",onChange:e=>t(t=>({...t,noOfWorkers:Number(e.target.value)}))}),D.noOfWorkers&&(0,a.jsx)("p",{className:"text-danger",children:D.noOfWorkers})]}),(0,a.jsxs)(n.A.Group,{className:"mb-3",children:[(0,a.jsx)(n.A.Label,{children:"Work Order / Job Number"}),(0,a.jsx)(n.A.Control,{type:"text",name:"nameOfSiteSupervisor",value:e.workOrderNo||"",onChange:e=>t(t=>({...t,workOrderNo:e.target.value}))})]}),(0,a.jsxs)(n.A.Group,{className:"mb-3",children:[(0,a.jsx)(n.A.Label,{children:"Applicant Contact Number"}),(0,a.jsx)(n.A.Control,{type:"text",name:"nameOfSiteSupervisor",value:e.applicantContactNo||"",onChange:e=>t(t=>({...t,applicantContactNo:e.target.value}))}),D.applicantContactNo&&(0,a.jsx)("p",{className:"text-danger",children:D.applicantContactNo})]}),(0,a.jsxs)(n.A.Group,{className:"mb-3",children:[(0,a.jsx)(n.A.Label,{children:"Supervisor Contact Number"}),(0,a.jsx)(n.A.Control,{type:"text",name:"nameOfSiteSupervisor",value:e.supervisorContactNo||"",onChange:e=>t(t=>({...t,supervisorContactNo:e.target.value}))}),D.supervisorContactNo&&(0,a.jsx)("p",{className:"text-danger",children:D.supervisorContactNo})]}),(0,a.jsx)("label",{htmlFor:"incidentImages",className:"mb-2",children:"Add/Upload images of the work location and authorized workers list (Min 2 attachment is mandatory) *"}),(0,a.jsx)(l.A,{onFilesSelected:e=>t(t=>({...t,supportingDocuments:e})),files:e.supportingDocuments,disabled:!1}),D.supportingDocuments&&(0,a.jsx)("p",{className:"text-danger",children:D.supportingDocuments}),Object.entries(U).map(e=>{let[t,r]=e;return(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h5",{className:"mt-4 mb-3 permit-head",children:t}),r.map(e=>(0,a.jsxs)("div",{className:"border p-3 mb-3 rounded",children:[(0,a.jsxs)("p",{children:[(0,a.jsxs)("strong",{children:[e.controlIndex+1,"."]})," ",e.description]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Control Type:"})," ",e.currentType]}),(0,a.jsxs)(n.A.Group,{className:"mb-3",children:[(0,a.jsx)("div",{className:"btn-group d-flex flex-wrap",role:"group","aria-label":"Risk Control Options",children:["Yes","No","Not Applicable"].map(t=>(0,a.jsx)("button",{type:"button",className:V(t,e.value),onClick:()=>Y(e.controlIndex,t),children:t},"".concat(t,"-").concat(e.controlIndex)))}),D["control_value_".concat(e.controlIndex)]&&(0,a.jsx)("div",{className:"text-danger mt-1",children:D["control_value_".concat(e.controlIndex)]})]}),(0,a.jsxs)(n.A.Group,{className:"mb-3",children:[(0,a.jsx)(n.A.Label,{children:"Remarks"}),(0,a.jsx)(n.A.Control,{type:"text",value:e.remarks,onChange:t=>B(e.controlIndex,"remarks",t.target.value)}),D["control_remarks_".concat(e.controlIndex)]&&(0,a.jsx)("div",{className:"text-danger",children:D["control_remarks_".concat(e.controlIndex)]})]}),(0,a.jsxs)("div",{className:"col-12 mt-3 mb-2",children:[(0,a.jsx)("label",{htmlFor:"incidentImages",className:"mb-2",children:"Add/Upload Evidence *"}),(0,a.jsx)(l.A,{onFilesSelected:t=>J(e.controlIndex,t),files:e.evidence,disabled:!1}),D["control_evidence_".concat(e.controlIndex)]&&(0,a.jsx)("div",{className:"text-danger",children:D["control_evidence_".concat(e.controlIndex)]})]})]},e.controlIndex))]},t)}),(0,a.jsx)("div",{className:"row mb-4 text-center",children:(0,a.jsxs)("div",{className:"d-flex flex-column col-12",children:[(0,a.jsx)(n.A.Label,{className:"fw-bold",children:"Applicant Sign"}),(0,a.jsx)("i",{children:"I confirm that all required fields are accurately completed, and I acknowledge responsibility for adhering to the specified safety controls for this work activity."}),(0,a.jsxs)("div",{className:"row mt-2",children:[(0,a.jsxs)("div",{className:"col-12",children:[(0,a.jsx)(v.A,{penColor:"#1F3BB3",backgroundColor:"white",canvasProps:{width:300,height:120,className:"sigCanvas",style:{boxShadow:"0px 0px 10px 3px rgb(189 189 189)"}},ref:O}),(0,a.jsx)("i",{className:"fa fa-undo undo",onClick:()=>{var e;return null==(e=O.current)?void 0:e.clear()}}),(0,a.jsx)("p",{children:null==I?void 0:I.firstName})]}),D.signature&&(0,a.jsx)("div",{className:"text-danger",children:D.signature})]})]})}),(null==I?void 0:I.type)==="External"?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{title:"Reviewer",options:b,selectedValue:e.reviewerId,onChange:e=>{t(t=>({...t,reviewerId:e}))},placeholder:"Select Reviewer",clearable:!0,disabled:!1}),D.reviewerId&&(0,a.jsx)("p",{className:"text-danger",children:D.reviewerId})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{title:"Assessor",options:R,selectedValue:e.assessorId,onChange:e=>{t(t=>({...t,assessorId:e}))},placeholder:"Select Assessor",clearable:!0,disabled:!1}),D.assessorId&&(0,a.jsx)("p",{className:"text-danger",children:D.assessorId})]})]})})})}),(0,a.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,a.jsx)("div",{className:"container px-0",children:(0,a.jsx)("div",{className:"footer-nav position-relative",children:(0,a.jsx)("div",{className:"h-100 d-flex align-items-center justify-content-between ps-0 w-100",children:(0,a.jsx)(o.A,{variant:"primary",className:" w-100",onClick:H,children:"Submit"})})})})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3496,5302,8320,6078,635,4816,1205,5898,6639,9697,3678,4952,6129,3066,9314,8441,1684,7358],()=>t(8495)),_N_E=e.O()}]);