"use client";

import React, { useState, useRef, useEffect, useCallback } from "react";
import {
    <PERSON><PERSON>,
    But<PERSON>,
} from "react-bootstrap";
import SignatureCanvas from "react-signature-canvas";
import {
    SUBMIT_CHECKLIST_INSPECTION,
    FILE_URL,
    GET_USER_ROLE_BY_MODE,
} from "@/constant";
import Swal from "sweetalert2";
import API from "@/services/API";

import ChecklistComponentRenderer from "./checklist/ChecklistComponentRenderer";
import PostActionsComponent from "./checklist/PostActionsComponent";
import MetaDetailsComponent from "./checklist/MetaDetailsComponent";

/* ──────────────── Types ───────────────── */
import {
    ApplicationDetails,
    OptionType,
    ChecklistComponent,
    CheckpointGroupData,
    PostAction,
    ErrorBuckets,
    ConductActionProps
} from "./types/ChecklistTypes";

/* ─────────────── Component ─────────────── */

const ConductAction: React.FC<ConductActionProps> = ({
    show,
    applicationDetails,
    showItem,
    closeModal,
}) => {
    /**  Signature references */
    const signRefs = useRef<{ [key: string]: SignatureCanvas | null }>({});

    /* ─────────────── state ─────────────── */
    const [assessor, setAssessor] = useState<OptionType[]>([]);
    const [checklistData, setChecklistData] = useState<ChecklistComponent[]>([]);
    const [postActions, setPostActions] = useState<PostAction[]>([]);
    const [showPostActions, setShowPostActions] = useState(false);

    const [errorMap, setErrorMap] = useState<ErrorBuckets>({
        group: {},
        checklist: {},
        post: {},
    });

    /* ─────────────── crew list ─────────────── */
    const getCrewList = useCallback(async (type: string) => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: applicationDetails?.locationOne?.id || "",
                locationTwoId: applicationDetails?.locationTwo?.id || "",
                locationThreeId: applicationDetails?.locationThree?.id || "",
                locationFourId: applicationDetails?.locationFour?.id || "",
                mode: type,
            });

            if (response.status === 200) {
                const data: OptionType[] = response.data.map((item: any) => ({
                    label: item.firstName,
                    value: item.id,
                }));
                setAssessor(data);
            }
        } catch (error) {
            console.error("Error fetching crew list:", error);
        }
    }, [
        applicationDetails?.locationOne?.id,
        applicationDetails?.locationTwo?.id,
        applicationDetails?.locationThree?.id,
        applicationDetails?.locationFour?.id
    ]);

    /* ─────────────── init ─────────────── */
    useEffect(() => {
        if (applicationDetails?.checklist?.value?.components) {
            // deep‑clone to break reference with props and initialize response data
            const components = JSON.parse(JSON.stringify(applicationDetails.checklist.value.components)) as ChecklistComponent[];

            // Initialize response data for each component
            const initializedComponents = components.map(component => {
                const initialized = { ...component };

                if (component.type === 'checkpoint-group') {
                    initialized.groupAnswer = "";
                    initialized.checkpoints = (component.data as CheckpointGroupData).checkpoints.map(cp => ({
                        id: cp.id,
                        text: cp.text,
                        selected: "",
                        remarks: "",
                        actionToBeTaken: "",
                        dueDate: null,
                        assignee: "",
                        uploads: []
                    }));
                } else if (component.type === 'checkpoint') {
                    initialized.selected = "";
                    initialized.remarks = "";
                    initialized.actionToBeTaken = "";
                    initialized.dueDate = null;
                    initialized.assignee = "";
                    initialized.uploads = [];
                } else if (component.type === 'date') {
                    initialized.selectedDate = null;
                } else if (component.type === 'sign') {
                    initialized.signature = "";
                } else if (component.type === 'text-input') {
                    initialized.textValue = "";
                } else if (component.type === 'image-input') {
                    // Handle existing uploads from data object or initialize empty
                    const existingUploads = (component.data as any).uploads || [];
                    initialized.imageFiles = existingUploads;
                    // Also initialize uploads in the data object for backend compatibility
                    (initialized.data as any).uploads = existingUploads;
                }

                return initialized;
            });

            // Sort components by position and set the data
            setChecklistData(initializedComponents.sort((a, b) => a.position - b.position));
        }
    }, [applicationDetails]);

    useEffect(() => {
        getCrewList("ins_action_owner");
    }, [showItem, getCrewList]);

    /* ─────────────── helpers ─────────────── */
    const handlePostActionChange = (
        index: number,
        field: keyof PostAction,
        value: string | Date | null
    ) => {
        const updated = [...postActions];
        (updated[index] as any)[field] = value;
        setPostActions(updated);
    };

    const handlePostFileUpload = async (files: string[], index: number) => {

        const updated = [...postActions];
        updated[index].uploads = files;
        setPostActions(updated);

    };



    const addNewPostAction = () => {
        setPostActions([
            ...postActions,
            { actionToBeTaken: "", dueDate: null, uploads: [], assignee: "" },
        ]);
    };

    const handleFileUpload = async (
        files: string[],
        componentIndex: number,
        checkpointIndex?: number
    ) => {
        if (!files.length) return;

        const updated = [...checklistData];
        const component = updated[componentIndex];

        if (component.type === 'checkpoint-group' && checkpointIndex !== undefined && component.checkpoints) {
            component.checkpoints[checkpointIndex].uploads = files;
        } else if (component.type === 'checkpoint') {
            component.uploads = files;
        }

        setChecklistData(updated);
    };



    const handleRemovePostAction = (idx: number) => {
        const updated = [...postActions];
        updated.splice(idx, 1);
        setPostActions(updated);
    };

    /* ─────────────── signature handlers ─────────────── */
    const handleSaveSignature = async (componentId: string, cIdx: number) => {
        const componentKey = `${componentId}-${cIdx}`;
        const canvas = signRefs.current[componentKey];

        console.log('Save signature called:', { componentId, cIdx, componentKey, canvas });

        if (!canvas) {
            Swal.fire("Error", "Signature canvas not found", "error");
            return;
        }

        if (canvas.isEmpty()) {
            Swal.fire("Error", "Please provide a signature before saving", "error");
            return;
        }

        try {
            // Convert canvas to blob with high quality
            const dataURL = canvas.toDataURL('image/png', 1.0);
            console.log('DataURL generated:', dataURL.substring(0, 50) + '...');

            const response = await fetch(dataURL);
            const blob = await response.blob();
            console.log('Blob created:', blob.size, 'bytes');

            // Create FormData and upload
            const formData = new FormData();
            const fileName = `signature_${componentId}_${Date.now()}.png`;
            formData.append('file', blob, fileName);
            console.log('FormData created with filename:', fileName);

            const uploadResponse = await API.post(FILE_URL, formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });
            console.log('Upload response:', uploadResponse);

            if (uploadResponse?.data?.files?.[0]?.originalname) {
                const uploaded = uploadResponse.data.files[0].originalname;
                console.log('File uploaded successfully:', uploaded);

                // Update component data with single signature file
                const updated = [...checklistData];
                updated[cIdx].signature = uploaded;
                setChecklistData(updated);

                Swal.fire("Success", "Signature saved successfully", "success");
            } else {
                console.error('Upload response missing files array:', uploadResponse);
                Swal.fire("Error", "Upload response was invalid", "error");
            }
        } catch (error) {
            console.error('Signature save error:', error);
            Swal.fire("Error", "Failed to save signature", "error");
        }
    };

    const handleClearSignature = (componentId: string, cIdx: number) => {
        const componentKey = `${componentId}-${cIdx}`;
        const canvas = signRefs.current[componentKey];
        if (canvas) {
            canvas.clear();
        }
    };

    /* ─────────────── validation ─────────────── */
    const runValidation = (): boolean => {
        const nErr: ErrorBuckets = { group: {}, checklist: {}, post: {} };

        // ▸ component-level validation
        checklistData.forEach((component, cIdx) => {
            const componentData = component.data as any;

            // Validate checkpoint-group
            if (component.type === 'checkpoint-group') {
                const answerKey = `${cIdx}-answer`;
                if (!component.groupAnswer) {
                    nErr.group[answerKey] = "Select Yes or No";
                    return;
                }

                // when YES, validate each checkpoint
                if (component.groupAnswer === "Yes" && component.checkpoints) {
                    component.checkpoints.forEach((checkpoint, cpIdx) => {
                        const base = `${cIdx}-${cpIdx}`;
                        if (!checkpoint.selected) {
                            nErr.checklist[`${base}-sel`] = "Select an option";
                        } else {
                            if ((checkpoint.selected === "No" || checkpoint.selected === "N/A") && !checkpoint.remarks) {
                                nErr.checklist[`${base}-remarks`] = "Required";
                            }
                            if (checkpoint.selected === "No") {
                                if (!checkpoint.actionToBeTaken)
                                    nErr.checklist[`${base}-action`] = "Required";
                                if (!checkpoint.dueDate) nErr.checklist[`${base}-due`] = "Required";
                                if (!checkpoint.assignee) nErr.checklist[`${base}-own`] = "Required";
                            }
                        }
                    });
                }
            }

            // Validate individual checkpoint
            else if (component.type === 'checkpoint') {
                if (componentData.required && !component.selected) {
                    nErr.checklist[`${cIdx}-sel`] = "Select an option";
                } else if (component.selected) {
                    if ((component.selected === "No" || component.selected === "N/A") && !component.remarks) {
                        nErr.checklist[`${cIdx}-remarks`] = "Required";
                    }
                    if (component.selected === "No") {
                        if (!component.actionToBeTaken)
                            nErr.checklist[`${cIdx}-action`] = "Required";
                        if (!component.dueDate) nErr.checklist[`${cIdx}-due`] = "Required";
                        if (!component.assignee) nErr.checklist[`${cIdx}-own`] = "Required";
                    }
                }
            }

            // Validate date component
            else if (component.type === 'date') {
                if (componentData.required && !component.selectedDate) {
                    nErr.checklist[`${cIdx}-date`] = "Date is required";
                }
            }

            // Validate signature component
            else if (component.type === 'sign') {
                if (componentData.required && !component.signature) {
                    nErr.checklist[`${cIdx}-sign`] = "Signature is required";
                }
            }

            // Validate text-input component
            else if (component.type === 'text-input') {
                if (componentData.required && !component.textValue?.trim()) {
                    nErr.checklist[`${cIdx}-text`] = "This field is required";
                }
            }

            // Validate image-input component
            else if (component.type === 'image-input') {
                const hasFiles = (component.imageFiles && component.imageFiles.length > 0) ||
                                (componentData.uploads && componentData.uploads.length > 0);
                if (componentData.required && !hasFiles) {
                    nErr.checklist[`${cIdx}-image`] = "At least one image is required";
                }
            }

            // Validate attachment-input component
            else if (component.type === 'attachment-input') {
                const hasFiles = (component.uploads && component.uploads.length > 0) ||
                                (componentData.uploads && componentData.uploads.length > 0);
                if (componentData.required && !hasFiles) {
                    nErr.checklist[`${cIdx}-attachment`] = "At least one attachment is required";
                }
            }
        });

        // ▸ post actions
        postActions.forEach((a, idx) => {
            if (!a.actionToBeTaken) nErr.post[`${idx}-action`] = "Required";
            if (!a.dueDate) nErr.post[`${idx}-due`] = "Required";
            if (!a.assignee) nErr.post[`${idx}-own`] = "Required";
        });

        setErrorMap(nErr);
        const hasErrors =
            Object.keys(nErr.group).length +
            Object.keys(nErr.checklist).length +
            Object.keys(nErr.post).length >
            0;
        return !hasErrors;
    };

    /* ─────────────── data transformation ─────────────── */
    const transformCheckpointGroupData = (component: ChecklistComponent) => {
        if (component.type !== "checkpoint-group") {
            return component;
        }

        const groupData = component.data as CheckpointGroupData;

        // Transform checkpoints to the desired format
        const transformedCheckpoints = component.checkpoints?.map(checkpoint => ({
            id: checkpoint.id,
            text: checkpoint.text,
            selected: checkpoint.selected || "",
            remarks: checkpoint.remarks || "",
            actionToBeTaken: checkpoint.actionToBeTaken || "",
            dueDate: checkpoint.dueDate || null,
            assignee: checkpoint.assignee || "",
            uploads: checkpoint.uploads || []
        })) || [];

        return {
            id: component.id,
            type: component.type,
            position: component.position,
            data: {
                id: groupData.id,
                type: groupData.type,
                position: groupData.position,
                required: groupData.required,
                title: groupData.title,
                checkpoints: transformedCheckpoints
            },
            validation: component.validation,
            isChecked: component.groupAnswer === "Yes",
            groupAnswer: component.groupAnswer || "",
            reason: component.reason || ""
        };
    };

    /* ─────────────── submit ─────────────── */
    const handleSubmit = async () => {
        if (!runValidation()) {
            Swal.fire("Validation", "Please fix the highlighted errors.", "error");
            return;
        }

        try {
            // Transform checkpoint group data to the desired format
            const transformedChecklistData = checklistData.map(component => {
                if (component.type === "checkpoint-group") {
                    return transformCheckpointGroupData(component);
                }
                return component;
            });

            const payload = {
                checklist: transformedChecklistData,
                postActions: postActions,
            };

            console.log("Transformed payload:", JSON.stringify(payload, null, 2));

            // Log checkpoint groups specifically for verification
            const checkpointGroups = transformedChecklistData.filter(c => c.type === "checkpoint-group");
            if (checkpointGroups.length > 0) {
                console.log("Checkpoint Groups in desired format:", JSON.stringify(checkpointGroups, null, 2));
            }

            const response = await API.patch(
                SUBMIT_CHECKLIST_INSPECTION(showItem.id),
                payload
            );

            if (response.status === 204) {
                Swal.fire("Inspection", "Submitted Successfully", "success");
                closeModal();
            }
        } catch (error) {
            console.error("Error:", error);
        }
    };

    /* ─────────────── render ─────────────── */
    return (
        <Modal show={show} size="lg" onHide={() => closeModal()} aria-labelledby="conduct-action">
            <Modal.Header closeButton>
                {applicationDetails && (
                    <div className="w-100">
                        <h4>Inspection</h4>
                        <div className="d-flex align-items-center">
                            <p className="me-2 mb-0">#{applicationDetails.maskId || ""}</p>
                            <p className="card-eptw mb-0">{applicationDetails.status}</p>
                        </div>
                    </div>
                )}
            </Modal.Header>

            <Modal.Body style={{ maxHeight: "70vh", overflowY: "auto" }}>
                {/* Meta Details */}
                <MetaDetailsComponent applicationDetails={applicationDetails} />

                {/* Checklist Components */}
                <div className="px-2 px-md-0">
                    {Array.isArray(checklistData) &&
                        checklistData.map((component, cIdx) => (
                            <div key={cIdx} className="mb-3 mb-md-4">
                                <ChecklistComponentRenderer
                                    component={component}
                                    componentIndex={cIdx}
                                    checklistData={checklistData}
                                    setChecklistData={setChecklistData}
                                    errorMap={errorMap}
                                    assessor={assessor}
                                    signRefs={signRefs}
                                    handleFileUpload={handleFileUpload}
                                    handleSaveSignature={handleSaveSignature}
                                    handleClearSignature={handleClearSignature}
                                />
                            </div>
                        ))}
                </div>





                {/* ───── Post Actions ───── */}
                <PostActionsComponent
                    postActions={postActions}
                    setPostActions={setPostActions}
                    showPostActions={showPostActions}
                    setShowPostActions={setShowPostActions}
                    errorMap={errorMap}
                    assessor={assessor}
                    handlePostActionChange={handlePostActionChange}
                    handlePostFileUpload={handlePostFileUpload}
                    handleRemovePostAction={handleRemovePostAction}
                    addNewPostAction={addNewPostAction}
                />
            </Modal.Body>

            <Modal.Footer>
                <Button type="button" variant="primary" onClick={handleSubmit}>
                    Submit
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default ConductAction;
