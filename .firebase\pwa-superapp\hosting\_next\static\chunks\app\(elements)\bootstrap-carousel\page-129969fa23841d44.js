(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[903],{10463:(e,s,a)=>{"use strict";a.d(s,{default:()=>l});var t=a(95155);a(12115);let l=()=>(a(81531),(0,t.jsx)(t.Fragment,{children:(0,t.jsxs)("div",{className:"page-content-wrapper py-3",children:[(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("div",{className:"element-heading",children:(0,t.jsx)("h6",{children:"Bootstrap Carousel"})})}),(0,t.jsxs)("div",{className:"carousel slide",id:"bootstrapCarousel","data-bs-ride":"carousel",children:[(0,t.jsxs)("div",{className:"carousel-indicators",children:[(0,t.jsx)("button",{className:"active",type:"button","data-bs-target":"#bootstrapCarousel","data-bs-slide-to":"0","aria-current":"true","aria-label":"Slide 1"}),(0,t.jsx)("button",{type:"button","data-bs-target":"#bootstrapCarousel","data-bs-slide-to":"1","aria-label":"Slide 2"}),(0,t.jsx)("button",{type:"button","data-bs-target":"#bootstrapCarousel","data-bs-slide-to":"2","aria-label":"Slide 3"})]}),(0,t.jsxs)("div",{className:"carousel-inner",children:[(0,t.jsx)("div",{className:"carousel-item active",children:(0,t.jsx)("img",{className:"d-block w-100",src:"/assets/img/bg-img/23.jpg",alt:""})}),(0,t.jsx)("div",{className:"carousel-item",children:(0,t.jsx)("img",{className:"d-block w-100",src:"/assets/img/bg-img/24.jpg",alt:""})}),(0,t.jsx)("div",{className:"carousel-item",children:(0,t.jsx)("img",{className:"d-block w-100",src:"/assets/img/bg-img/25.jpg",alt:""})})]}),(0,t.jsxs)("button",{className:"carousel-control-prev","data-bs-target":"#bootstrapCarousel",type:"button","data-bs-slide":"prev",children:[(0,t.jsx)("span",{className:"carousel-control-prev-icon","aria-hidden":"true"}),(0,t.jsx)("span",{className:"visually-hidden",children:"Previous"})]}),(0,t.jsxs)("button",{className:"carousel-control-next","data-bs-target":"#bootstrapCarousel",type:"button","data-bs-slide":"next",children:[(0,t.jsx)("span",{className:"carousel-control-next-icon","aria-hidden":"true"}),(0,t.jsx)("span",{className:"visually-hidden",children:"Next"})]})]}),(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("div",{className:"element-heading mt-3",children:(0,t.jsx)("h6",{children:"Bootstrap Carousel Crossfade"})})}),(0,t.jsxs)("div",{className:"carousel slide carousel-fade",id:"bootstrapCarouselFade","data-bs-ride":"carousel",children:[(0,t.jsxs)("div",{className:"carousel-indicators",children:[(0,t.jsx)("button",{className:"active",type:"button","data-bs-target":"#bootstrapCarouselFade","data-bs-slide-to":"0","aria-current":"true","aria-label":"Slide 1"}),(0,t.jsx)("button",{type:"button","data-bs-target":"#bootstrapCarouselFade","data-bs-slide-to":"1","aria-label":"Slide 2"}),(0,t.jsx)("button",{type:"button","data-bs-target":"#bootstrapCarouselFade","data-bs-slide-to":"2","aria-label":"Slide 3"})]}),(0,t.jsxs)("div",{className:"carousel-inner",children:[(0,t.jsx)("div",{className:"carousel-item active",children:(0,t.jsx)("img",{className:"d-block w-100",src:"/assets/img/bg-img/26.jpg",alt:""})}),(0,t.jsx)("div",{className:"carousel-item",children:(0,t.jsx)("img",{className:"d-block w-100",src:"/assets/img/bg-img/27.jpg",alt:""})}),(0,t.jsx)("div",{className:"carousel-item",children:(0,t.jsx)("img",{className:"d-block w-100",src:"/assets/img/bg-img/28.jpg",alt:""})})]}),(0,t.jsxs)("button",{className:"carousel-control-prev","data-bs-target":"#bootstrapCarouselFade",type:"button","data-bs-slide":"prev",children:[(0,t.jsx)("span",{className:"carousel-control-prev-icon","aria-hidden":"true"}),(0,t.jsx)("span",{className:"visually-hidden",children:"Previous"})]}),(0,t.jsxs)("button",{className:"carousel-control-next","data-bs-target":"#bootstrapCarouselFade",type:"button","data-bs-slide":"next",children:[(0,t.jsx)("span",{className:"carousel-control-next-icon","aria-hidden":"true"}),(0,t.jsx)("span",{className:"visually-hidden",children:"Next"})]})]})]})}))},21217:(e,s,a)=>{"use strict";a.d(s,{default:()=>o});var t=a(95155),l=a(9e4),i=a(38808),c=a(12115);let r=e=>{let{handleShowSetting:s,showSetting:a}=e,{theme:c,handleDarkModeToggle:r}=(0,l.D)(),{viewMode:n,handleRTLToggling:d}=(0,i.L)();return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{id:"setting-popup-overlay",className:a?"active":"",onClick:s}),(0,t.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(a?"active":""),id:"settingCard",children:(0,t.jsx)("div",{className:"card-body",children:(0,t.jsxs)("div",{className:"container",children:[(0,t.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,t.jsx)("p",{className:"mb-0",children:"Settings"}),(0,t.jsx)("div",{onClick:s,className:"btn-close",id:"settingCardClose"})]}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,t.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,t.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===c,onChange:r}),(0,t.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===c?"Light":"Dark"," mode"]})]})}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===n,onChange:d}),(0,t.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===n?"LTR":"RTL"," mode"]})]})})]})})})]})};var n=a(6874),d=a.n(n);let o=e=>{let{links:s,title:a}=e,[l,i]=(0,c.useState)(!1),n=()=>i(!l);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"header-area",id:"headerArea",children:(0,t.jsx)("div",{className:"container",children:(0,t.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,t.jsx)("div",{className:"back-button",children:(0,t.jsx)(d(),{href:"/".concat(s),children:(0,t.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,t.jsx)("div",{className:"page-heading",children:(0,t.jsx)("h6",{className:"mb-0",children:a})}),(0,t.jsx)("div",{className:"setting-wrapper",onClick:n,children:(0,t.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,t.jsx)("i",{className:"bi bi-gear"}),(0,t.jsx)("span",{})]})})]})})}),(0,t.jsx)(r,{showSetting:l,handleShowSetting:n})]})}},38808:(e,s,a)=>{"use strict";a.d(s,{L:()=>l});var t=a(12115);let l=()=>{let[e,s]=(0,t.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,t.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let a=()=>{s(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:a,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let s=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(s),e.style.display="none")},20)},1e3))}a()}}}},38983:(e,s,a)=>{"use strict";a.d(s,{default:()=>r});var t=a(95155),l=a(6874),i=a.n(l);a(12115);let c=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],r=()=>(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,t.jsx)("div",{className:"container px-0",children:(0,t.jsx)("div",{className:"footer-nav position-relative",children:(0,t.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:c.map((e,s)=>(0,t.jsx)("li",{children:(0,t.jsxs)(i(),{href:"/".concat(e.link),children:[(0,t.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,t.jsx)("span",{children:e.title})]})},s))})})})})})},74729:(e,s,a)=>{Promise.resolve().then(a.bind(a,10463)),Promise.resolve().then(a.bind(a,38983)),Promise.resolve().then(a.bind(a,21217))},9e4:(e,s,a)=>{"use strict";a.d(s,{D:()=>l});var t=a(12115);let l=()=>{let[e,s]=(0,t.useState)("light"),[a,l]=(0,t.useState)(!1);(0,t.useEffect)(()=>{s(localStorage.getItem("theme")||"light"),l(!0)},[]),(0,t.useEffect)(()=>{a&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,a]);let i=(0,t.useCallback)(()=>{s(e=>"dark"===e?"light":"dark")},[]),c=(0,t.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let s=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(s),e.style.display="none")},20)},1e3))}i()},[i]);return{theme:e,toggleTheme:i,handleDarkModeToggle:c}}}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,1955,1531,8441,1684,7358],()=>s(74729)),_N_E=e.O()}]);