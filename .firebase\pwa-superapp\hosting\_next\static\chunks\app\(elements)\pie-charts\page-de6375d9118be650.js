(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8544],{10255:(e,t,s)=>{"use strict";function a(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return a}}),s(95155),s(47650),s(85744),s(20589)},17828:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return a}});let a=(0,s(64054).createAsyncLocalStorage)()},21217:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var a=s(95155),l=s(9e4),n=s(38808),i=s(12115);let r=e=>{let{handleShowSetting:t,showSetting:s}=e,{theme:i,handleDarkModeToggle:r}=(0,l.D)(),{viewMode:c,handleRTLToggling:d}=(0,n.L)();return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{id:"setting-popup-overlay",className:s?"active":"",onClick:t}),(0,a.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(s?"active":""),id:"settingCard",children:(0,a.jsx)("div",{className:"card-body",children:(0,a.jsxs)("div",{className:"container",children:[(0,a.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,a.jsx)("p",{className:"mb-0",children:"Settings"}),(0,a.jsx)("div",{onClick:t,className:"btn-close",id:"settingCardClose"})]}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,a.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,a.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===i,onChange:r}),(0,a.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===i?"Light":"Dark"," mode"]})]})}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===c,onChange:d}),(0,a.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===c?"LTR":"RTL"," mode"]})]})})]})})})]})};var c=s(6874),d=s.n(c);let o=e=>{let{links:t,title:s}=e,[l,n]=(0,i.useState)(!1),c=()=>n(!l);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"header-area",id:"headerArea",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,a.jsx)("div",{className:"back-button",children:(0,a.jsx)(d(),{href:"/".concat(t),children:(0,a.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,a.jsx)("div",{className:"page-heading",children:(0,a.jsx)("h6",{className:"mb-0",children:s})}),(0,a.jsx)("div",{className:"setting-wrapper",onClick:c,children:(0,a.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,a.jsx)("i",{className:"bi bi-gear"}),(0,a.jsx)("span",{})]})})]})})}),(0,a.jsx)(r,{showSetting:l,handleShowSetting:c})]})}},23075:(e,t,s)=>{Promise.resolve().then(s.bind(s,84519)),Promise.resolve().then(s.bind(s,38983)),Promise.resolve().then(s.bind(s,21217))},36645:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let a=s(88229)._(s(67357));function l(e,t){var s;let l={};"function"==typeof e&&(l.loader=e);let n={...l,...t};return(0,a.default)({...n,modules:null==(s=n.loadableGenerated)?void 0:s.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38808:(e,t,s)=>{"use strict";s.d(t,{L:()=>l});var a=s(12115);let l=()=>{let[e,t]=(0,a.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,a.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let s=()=>{t(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:s,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}s()}}}},38983:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});var a=s(95155),l=s(6874),n=s.n(l);s(12115);let i=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],r=()=>(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,a.jsx)("div",{className:"container px-0",children:(0,a.jsx)("div",{className:"footer-nav position-relative",children:(0,a.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:i.map((e,t)=>(0,a.jsx)("li",{children:(0,a.jsxs)(n(),{href:"/".concat(e.link),children:[(0,a.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,a.jsx)("span",{children:e.title})]})},t))})})})})})},55028:(e,t,s)=>{"use strict";s.d(t,{default:()=>l.a});var a=s(36645),l=s.n(a)},62146:(e,t,s)=>{"use strict";function a(e){let{reason:t,children:s}=e;return s}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return a}}),s(45262)},64054:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{bindSnapshot:function(){return i},createAsyncLocalStorage:function(){return n},createSnapshot:function(){return r}});let s=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class a{disable(){throw s}getStore(){}run(){throw s}exit(){throw s}enterWith(){throw s}static bind(e){return e}}let l="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function n(){return l?new l:new a}function i(e){return l?l.bind(e):a.bind(e)}function r(){return l?l.snapshot():function(e,...t){return e(...t)}}},67357:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let a=s(95155),l=s(12115),n=s(62146);function i(e){return{default:e&&"default"in e?e.default:e}}s(10255);let r={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},c=function(e){let t={...r,...e},s=(0,l.lazy)(()=>t.loader().then(i)),c=t.loading;function d(e){let i=c?(0,a.jsx)(c,{isLoading:!0,pastDelay:!0,error:null}):null,r=!t.ssr||!!t.loading,d=r?l.Suspense:l.Fragment,o=t.ssr?(0,a.jsxs)(a.Fragment,{children:[null,(0,a.jsx)(s,{...e})]}):(0,a.jsx)(n.BailoutToCSR,{reason:"next/dynamic",children:(0,a.jsx)(s,{...e})});return(0,a.jsx)(d,{...r?{fallback:i}:{},children:o})}return d.displayName="LoadableComponent",d}},84519:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});var a=s(95155);s(12115);let l=(0,s(55028).default)(()=>Promise.all([s.e(3104),s.e(8699)]).then(s.bind(s,78699)),{loadableGenerated:{webpack:()=>[78699]},ssr:!1}),n={chart:{width:280,type:"pie",sparkline:{enabled:!0},dropShadow:{enabled:!1}},colors:["#0134d4","#2ecc4a","#ea4c62","#1787b8"],series:[100,55,63,77],labels:["Business","Marketing","Admin","Ecommerce"]},i={chart:{width:280,type:"donut",sparkline:{enabled:!0},dropShadow:{enabled:!1}},colors:["#0134d4","#2ecc4a","#ea4c62","#1787b8"],series:[100,55,63,77],labels:["Business","Marketing","Admin","Ecommerce"]},r=()=>(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"page-content-wrapper py-3",children:[(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"element-heading",children:(0,a.jsx)("h6",{children:"Pie Chart"})})}),(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"card shadow-sm direction-rtl",children:(0,a.jsx)("div",{className:"card-body",children:(0,a.jsx)("div",{className:"chart-wrapper",children:(0,a.jsx)("div",{id:"pieChart",children:(0,a.jsx)(l,{options:n,series:n.series,type:"pie",width:280})})})})})}),(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"element-heading mt-3",children:(0,a.jsx)("h6",{children:"Donut Chart"})})}),(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"card shadow-sm direction-rtl",children:(0,a.jsx)("div",{className:"card-body",children:(0,a.jsx)("div",{className:"chart-wrapper",children:(0,a.jsx)("div",{id:"donutChart",children:(0,a.jsx)(l,{options:i,series:i.series,type:"donut",width:280})})})})})})]})})},85744:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return a.workAsyncStorageInstance}});let a=s(17828)},9e4:(e,t,s)=>{"use strict";s.d(t,{D:()=>l});var a=s(12115);let l=()=>{let[e,t]=(0,a.useState)("light"),[s,l]=(0,a.useState)(!1);(0,a.useEffect)(()=>{t(localStorage.getItem("theme")||"light"),l(!0)},[]),(0,a.useEffect)(()=>{s&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,s]);let n=(0,a.useCallback)(()=>{t(e=>"dark"===e?"light":"dark")},[]),i=(0,a.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}n()},[n]);return{theme:e,toggleTheme:n,handleDarkModeToggle:i}}}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,8441,1684,7358],()=>t(23075)),_N_E=e.O()}]);