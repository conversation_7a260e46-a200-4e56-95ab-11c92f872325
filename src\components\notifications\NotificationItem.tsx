import React from 'react';

// Simple stub types
interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'error' | 'warning' | 'success' | 'info' | 'urgent';
  priority: 'urgent' | 'high' | 'medium' | 'low';
  timestamp: Date;
  isRead: boolean;
  category?: string;
  actionRequired?: boolean;
}

interface NotificationItemProps {
  notification: Notification;
}

export function NotificationItem({ notification }: NotificationItemProps) {
  const handlePress = () => {
    // Simple stub - just log for now
    console.log('Notification clicked:', notification.id);
  };

  const getNotificationIcon = () => {
    switch (notification.type) {
      case 'error':
      case 'urgent':
        return 'exclamationmark.triangle.fill';
      case 'warning':
        return 'exclamationmark.circle.fill';
      case 'success':
        return 'checkmark.circle.fill';
      case 'info':
      default:
        return 'info.circle.fill';
    }
  };

  const getNotificationColor = () => {
    switch (notification.type) {
      case 'error':
      case 'urgent':
        return '#EF4444';
      case 'warning':
        return '#F59E0B';
      case 'success':
        return '#10B981';
      case 'info':
      default:
        return '#3B82F6';
    }
  };

  const getPriorityColor = () => {
    switch (notification.priority) {
      case 'urgent':
        return '#EF4444';
      case 'high':
        return '#F59E0B';
      case 'medium':
        return '#3B82F6';
      case 'low':
      default:
        return '#6B7280';
    }
  };

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return timestamp.toLocaleDateString();
  };

  return (
    <div
      onClick={handlePress}
      style={{
        cursor: 'pointer',
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'flex-start',
        padding: '16px 20px',
        backgroundColor: !notification.isRead ? '#F8F9FF' : 'white',
        position: 'relative',
        borderBottom: '1px solid #f0f0f0'
      }}
    >
      {/* Unread indicator */}
      {!notification.isRead && (
        <div style={{
          position: 'absolute',
          left: 0,
          top: 0,
          bottom: 0,
          width: 4,
          backgroundColor: '#007AFF'
        }} />
      )}

      {/* Icon */}
      <div style={{
        width: 36,
        height: 36,
        borderRadius: 18,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
        backgroundColor: getNotificationColor() + '20'
      }}>
        <span style={{ color: getNotificationColor(), fontSize: '20px' }}>
          {notification.type === 'error' || notification.type === 'urgent' ? '⚠️' :
           notification.type === 'warning' ? '⚠️' :
           notification.type === 'success' ? '✅' : 'ℹ️'}
        </span>
      </div>

      {/* Content */}
      <div style={{ flex: 1, marginRight: 8 }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
          marginBottom: 4
        }}>
          <div style={{ flex: 1, marginRight: 8 }}>
            <h6 style={{
              fontSize: 14,
              fontWeight: !notification.isRead ? '700' : '600',
              color: '#1A1A1A',
              marginBottom: 2,
              margin: 0
            }}>
              {notification.title}
            </h6>

            {/* Priority badge */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              padding: '2px 6px',
              borderRadius: 8,
              border: `1px solid ${getPriorityColor()}`,
              alignSelf: 'flex-start',
              marginTop: 4
            }}>
              <div style={{
                width: 4,
                height: 4,
                borderRadius: 2,
                marginRight: 4,
                backgroundColor: getPriorityColor()
              }} />
              <span style={{
                fontSize: 9,
                fontWeight: '600',
                color: getPriorityColor()
              }}>
                {notification.priority.toUpperCase()}
              </span>
            </div>
          </div>

          <span style={{
            fontSize: 11,
            color: '#8E8E93',
            fontWeight: '500'
          }}>
            {formatTimeAgo(notification.timestamp)}
          </span>
        </div>

        {/* Message */}
        <p style={{
          fontSize: 13,
          color: '#666666',
          lineHeight: '18px',
          marginBottom: 8,
          margin: '8px 0'
        }}>
          {notification.message}
        </p>

        {/* Footer */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: 8
        }}>
          {notification.category && (
            <div style={{
              padding: '2px 8px',
              backgroundColor: '#F0F0F0',
              borderRadius: 6
            }}>
              <span style={{
                fontSize: 10,
                color: '#666666',
                fontWeight: '500'
              }}>
                {notification.category.charAt(0).toUpperCase() + notification.category.slice(1)}
              </span>
            </div>
          )}

          {notification.actionRequired && (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              padding: '2px 6px',
              backgroundColor: '#FEF3C7',
              borderRadius: 6
            }}>
              <span style={{ color: '#F59E0B', fontSize: '10px', marginRight: 2 }}>⚠️</span>
              <span style={{
                fontSize: 10,
                color: '#F59E0B',
                fontWeight: '600'
              }}>Action Required</span>
            </div>
          )}
        </div>
      </div>

      {/* Chevron */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        paddingLeft: 4
      }}>
        <span style={{ color: '#C7C7CC', fontSize: '16px' }}>→</span>
      </div>
    </div>
  );
}


