(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3920],{21217:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var a=s(95155),l=s(9e4),i=s(38808),c=s(12115);let n=e=>{let{handleShowSetting:t,showSetting:s}=e,{theme:c,handleDarkModeToggle:n}=(0,l.D)(),{viewMode:r,handleRTLToggling:d}=(0,i.L)();return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{id:"setting-popup-overlay",className:s?"active":"",onClick:t}),(0,a.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(s?"active":""),id:"settingCard",children:(0,a.jsx)("div",{className:"card-body",children:(0,a.jsxs)("div",{className:"container",children:[(0,a.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,a.jsx)("p",{className:"mb-0",children:"Settings"}),(0,a.jsx)("div",{onClick:t,className:"btn-close",id:"settingCardClose"})]}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,a.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,a.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===c,onChange:n}),(0,a.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===c?"Light":"Dark"," mode"]})]})}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===r,onChange:d}),(0,a.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===r?"LTR":"RTL"," mode"]})]})})]})})})]})};var r=s(6874),d=s.n(r);let o=e=>{let{links:t,title:s}=e,[l,i]=(0,c.useState)(!1),r=()=>i(!l);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"header-area",id:"headerArea",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,a.jsx)("div",{className:"back-button",children:(0,a.jsx)(d(),{href:"/".concat(t),children:(0,a.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,a.jsx)("div",{className:"page-heading",children:(0,a.jsx)("h6",{className:"mb-0",children:s})}),(0,a.jsx)("div",{className:"setting-wrapper",onClick:r,children:(0,a.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,a.jsx)("i",{className:"bi bi-gear"}),(0,a.jsx)("span",{})]})})]})})}),(0,a.jsx)(n,{showSetting:l,handleShowSetting:r})]})}},38808:(e,t,s)=>{"use strict";s.d(t,{L:()=>l});var a=s(12115);let l=()=>{let[e,t]=(0,a.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,a.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let s=()=>{t(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:s,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}s()}}}},38983:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var a=s(95155),l=s(6874),i=s.n(l);s(12115);let c=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],n=()=>(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,a.jsx)("div",{className:"container px-0",children:(0,a.jsx)("div",{className:"footer-nav position-relative",children:(0,a.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:c.map((e,t)=>(0,a.jsx)("li",{children:(0,a.jsxs)(i(),{href:"/".concat(e.link),children:[(0,a.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,a.jsx)("span",{children:e.title})]})},t))})})})})})},59042:(e,t,s)=>{Promise.resolve().then(s.bind(s,59652)),Promise.resolve().then(s.bind(s,38983)),Promise.resolve().then(s.bind(s,21217))},59652:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var a=s(95155);let l=()=>(s(96164),(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"page-content-wrapper py-3",children:[(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"element-heading",children:(0,a.jsx)("h6",{children:"Tooltip on top"})})}),(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"card",children:(0,a.jsx)("div",{className:"card-body text-center",children:(0,a.jsx)("button",{className:"btn btn-primary",title:"Hi, I am a top tooltip!","data-toggle":"tooltip","data-placement":"top",children:"Tooltip on top"})})})}),(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"element-heading mt-3",children:(0,a.jsx)("h6",{children:"Tooltip on right"})})}),(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"card",children:(0,a.jsx)("div",{className:"card-body",children:(0,a.jsx)("a",{className:"btn btn-success ms-auto",href:"#","data-bs-toggle":"tooltip","data-bs-placement":"right",title:"Hi, I am a right tooltip!",children:"Tooltip on right"})})})}),(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"element-heading mt-3",children:(0,a.jsx)("h6",{children:"Tooltip on bottom"})})}),(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"card",children:(0,a.jsx)("div",{className:"card-body text-center",children:(0,a.jsx)("a",{className:"btn btn-info",href:"#","data-bs-toggle":"tooltip","data-bs-placement":"bottom",title:"Hi, I am a bottom tooltip!",children:"Tooltip on bottom"})})})}),(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"element-heading mt-3",children:(0,a.jsx)("h6",{children:"Tooltip on left"})})}),(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"card",children:(0,a.jsx)("div",{className:"card-body",children:(0,a.jsx)("div",{className:"text-end",children:(0,a.jsx)("a",{className:"btn btn-danger",href:"#","data-bs-toggle":"tooltip","data-bs-placement":"left",title:"Hi, I am a left tooltip!",children:"Tooltip on left"})})})})}),(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"element-heading mt-3",children:(0,a.jsx)("h6",{children:"Tooltip on HTML"})})}),(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"card",children:(0,a.jsx)("div",{className:"card-body",children:(0,a.jsx)("button",{className:"btn btn-warning",type:"button","data-bs-toggle":"tooltip","data-bs-html":"true",title:"Tooltip with Next js",children:"Tooltip with Next js"})})})})]})}))},9e4:(e,t,s)=>{"use strict";s.d(t,{D:()=>l});var a=s(12115);let l=()=>{let[e,t]=(0,a.useState)("light"),[s,l]=(0,a.useState)(!1);(0,a.useEffect)(()=>{t(localStorage.getItem("theme")||"light"),l(!0)},[]),(0,a.useEffect)(()=>{s&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,s]);let i=(0,a.useCallback)(()=>{t(e=>"dark"===e?"light":"dark")},[]),c=(0,a.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}i()},[i]);return{theme:e,toggleTheme:i,handleDarkModeToggle:c}}}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,1955,2703,6164,8441,1684,7358],()=>t(59042)),_N_E=e.O()}]);