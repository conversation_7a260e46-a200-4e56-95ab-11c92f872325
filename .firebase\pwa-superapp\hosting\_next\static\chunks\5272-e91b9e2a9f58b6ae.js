"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5272],{95272:(e,s,l)=>{l.d(s,{A:()=>o});var d=l(95155),a=l(12115),c=l(82940),r=l.n(c),t=l(38336),i=l(26957),n=l(43864);let o=e=>{var s,l,c,o,m;let{reportData:h,type:x}=e,[j,N]=(0,a.useState)([]);console.log(h);let f=[h.locationOne,h.locationTwo,h.locationThree,h.locationFour,h.locationFive,h.locationSix].filter(e=>!!(null==e?void 0:e.name)).map(e=>e.name).join(" > ")||"N/A";(0,a.useEffect)(()=>{(async()=>{try{let e=await t.A.get(i.Jo);N(e.data)}catch(e){console.error("Fetching users failed",e)}})()},[]);let u=e=>{if(!e)return"";let s=j.find(s=>s.id===e);return(null==s?void 0:s.firstName)||""};return(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsxs)("div",{className:"p-4 rounded border",style:{backgroundColor:"#e3f2fd"},children:[(0,d.jsxs)("div",{className:"mb-3",children:[(0,d.jsx)("h4",{className:"fw-bold mb-1",style:{color:"#1976d2"},children:"Inspection Details"}),(0,d.jsx)("small",{className:"text-muted",children:"Comprehensive inspection report"})]}),(0,d.jsxs)("div",{className:"row g-4",children:[(0,d.jsx)("div",{className:"col-md-4",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h6",{className:"fw-bold mb-1",style:{color:"#1976d2"},children:"Category"}),(0,d.jsx)("p",{className:"text-muted mb-0",children:h.inspectionCategory||"N/A"})]})}),(0,d.jsx)("div",{className:"col-md-4",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h6",{className:"fw-bold mb-1",style:{color:"#1976d2"},children:"Scheduled Date"}),(0,d.jsx)("p",{className:"text-muted mb-0",children:h.scheduledDate?r()(h.scheduledDate).format("DD MMM YYYY"):"N/A"})]})}),(0,d.jsx)("div",{className:"col-md-4",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h6",{className:"fw-bold mb-1",style:{color:"#1976d2"},children:"Due Date"}),(0,d.jsx)("p",{className:"text-muted mb-0",children:h.dueDate?r()(h.dueDate).format("DD MMM YYYY"):"N/A"})]})})]}),(0,d.jsx)("hr",{className:"my-4",style:{borderColor:"#bbdefb"}}),(0,d.jsxs)("div",{className:"row g-4",children:[(0,d.jsx)("div",{className:"col-md-4",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h6",{className:"fw-bold mb-1",style:{color:"#1976d2"},children:"Inspector"}),(0,d.jsx)("p",{className:"text-muted mb-0",children:(null==(s=h.inspector)?void 0:s.firstName)||"N/A"})]})}),(0,d.jsx)("div",{className:"col-md-4",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h6",{className:"fw-bold mb-1",style:{color:"#1976d2"},children:"Checklist"}),(0,d.jsx)("p",{className:"text-muted mb-0",children:(null==(l=h.checklist)?void 0:l.name)||"N/A"})]})}),(0,d.jsx)("div",{className:"col-md-4",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h6",{className:"fw-bold mb-1",style:{color:"#1976d2"},children:"Version"}),(0,d.jsx)("p",{className:"text-muted mb-0",children:h.checklistVersion||"N/A"})]})})]}),(0,d.jsx)("hr",{className:"my-4",style:{borderColor:"#bbdefb"}}),(0,d.jsx)("div",{className:"row g-4",children:(0,d.jsx)("div",{className:"col-12",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h6",{className:"fw-bold mb-1",style:{color:"#1976d2"},children:"Location"}),(0,d.jsx)("p",{className:"text-muted mb-0",children:f})]})})}),(0,d.jsx)("hr",{className:"my-4",style:{borderColor:"#bbdefb"}}),(0,d.jsxs)("div",{className:"row g-4",children:[(0,d.jsx)("div",{className:"col-md-4",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h6",{className:"fw-bold mb-1",style:{color:"#1976d2"},children:"Status"}),(0,d.jsx)("span",{className:"px-3 py-1 rounded text-white",style:{backgroundColor:"Completed"===h.status?"#4caf50":"In Progress"===h.status?"#ff9800":"#9e9e9e",fontSize:"0.875rem"},children:h.status||"N/A"})]})}),(0,d.jsx)("div",{className:"col-md-4",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h6",{className:"fw-bold mb-1",style:{color:"#1976d2"},children:"Completion Date"}),(0,d.jsx)("p",{className:"text-muted mb-0",children:h.actualCompletionDate?r()(h.actualCompletionDate).format("DD MMM YYYY"):"N/A"})]})}),(0,d.jsx)("div",{className:"col-md-4",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h6",{className:"fw-bold mb-1",style:{color:"#1976d2"},children:"Assigned By"}),(0,d.jsx)("p",{className:"text-muted mb-0",children:(null==(c=h.assignedBy)?void 0:c.firstName)||"N/A"})]})})]}),(0,d.jsx)("hr",{className:"my-4",style:{borderColor:"#bbdefb"}}),(0,d.jsxs)("div",{className:"row g-4",children:[(0,d.jsx)("div",{className:"col-md-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h6",{className:"fw-bold mb-1",style:{color:"#1976d2"},children:"Checklist ID"}),(0,d.jsx)("p",{className:"text-muted mb-0",children:(null==(o=h.checklist)?void 0:o.customId)||"N/A"})]})}),(0,d.jsx)("div",{className:"col-md-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h6",{className:"fw-bold mb-1",style:{color:"#1976d2"},children:"Category"}),(0,d.jsx)("p",{className:"text-muted mb-0",children:(null==(m=h.checklist)?void 0:m.category)||"N/A"})]})})]})]})}),"view"===x&&(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)("h4",{className:"fw-bold mb-0",style:{color:"#1976d2"},children:"Checklist Components"})}),(()=>{let e=[];if(h.value)if(Array.isArray(h.value))e=h.value;else{let s=h.value;e=Object.keys(s).filter(e=>!["hasActions","actionsCount"].includes(e)).map(e=>s[e]).filter(e=>e&&"object"==typeof e&&e.type)}return e.length?[...e].sort((e,s)=>e.position-s.position).map((e,s)=>{let l="component-".concat(s);if("header"===e.type){let s=e.data;return(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)("h4",{className:"text-dark fw-bold mb-2",children:s.text}),(0,d.jsx)("hr",{style:{borderColor:"#e3f2fd",borderWidth:"2px"}})]},l)}if("section-header"===e.type){let s=e.data;return(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)("div",{className:"p-3 rounded",style:{backgroundColor:"#f3f9ff",borderLeft:"4px solid #2196f3"},children:(0,d.jsx)("h5",{className:"mb-0 fw-bold",style:{color:"#1976d2"},children:s.text})})},l)}if("text-body"===e.type){let s=e.data;return(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)("div",{className:"p-3 rounded",style:{backgroundColor:"#f8f9fa",border:"1px solid #e9ecef"},children:(0,d.jsx)("p",{className:"text-muted mb-0",style:{whiteSpace:"pre-wrap",lineHeight:"1.6"},children:s.content})})},l)}if("date"===e.type){let s=e.data,a=s.selectedDate||e.selectedDate;return(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)("div",{className:"p-3 rounded border",style:{backgroundColor:"#f3f9ff"},children:(0,d.jsxs)("div",{className:"d-flex justify-content-between align-items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h6",{className:"fw-bold mb-1",style:{color:"#1976d2"},children:s.label}),(0,d.jsx)("small",{className:"text-muted",children:"Selected Date"})]}),(0,d.jsx)("div",{children:(0,d.jsx)("span",{className:"px-3 py-1 rounded text-white",style:{backgroundColor:"#2196f3",fontSize:"0.875rem"},children:a?r()(a).format("DD MMM YYYY"):"Not Selected"})})]})})},l)}if("sign"===e.type){let s=e.data,a=s.signature||e.signature;return(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsxs)("div",{className:"p-3 rounded border",style:{backgroundColor:"#f3f9ff"},children:[(0,d.jsx)("h6",{className:"fw-bold mb-3",style:{color:"#1976d2"},children:s.label}),a?(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"mb-3",children:(0,d.jsx)("span",{className:"px-2 py-1 rounded text-white",style:{backgroundColor:"#4caf50",fontSize:"0.875rem"},children:"✓ Signature Captured"})}),(0,d.jsx)("div",{className:"border rounded p-3 bg-white d-inline-block",children:(0,d.jsx)(n.A,{fileName:a,size:200,name:!1})})]}):(0,d.jsx)("div",{children:(0,d.jsx)("span",{className:"px-2 py-1 rounded",style:{backgroundColor:"#f5f5f5",color:"#757575",fontSize:"0.875rem"},children:"No signature provided"})})]})},l)}if("checkpoint"===e.type){let s=e.data,a=s.selected||e.selected,c=s.remarks||e.remarks,t=s.actionToBeTaken||e.actionToBeTaken,i=s.dueDate||e.dueDate,o=s.assignee||e.assignee,m=s.uploads||e.uploads;return(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsxs)("div",{className:"p-3 rounded border",style:{backgroundColor:"#f3f9ff"},children:[(0,d.jsxs)("div",{className:"d-flex justify-content-between align-items-start mb-3",children:[(0,d.jsx)("div",{className:"flex-grow-1",children:(0,d.jsx)("h6",{className:"fw-bold mb-2",style:{color:"#1976d2"},children:s.text})}),(0,d.jsx)("span",{className:"px-3 py-1 rounded text-white",style:{backgroundColor:(e=>{switch(e){case"Yes":return"#4caf50";case"No":return"#f44336";case"N/A":return"#ff9800";default:return"#9e9e9e"}})(a),fontSize:"0.875rem"},children:a||"Not Selected"})]}),(c||t||i||o||m&&m.length>0)&&(0,d.jsx)("div",{className:"border-top pt-3 mt-3",style:{borderColor:"#e3f2fd"},children:(0,d.jsxs)("div",{className:"row g-3",children:[c&&(0,d.jsx)("div",{className:"col-12",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("small",{className:"fw-medium",style:{color:"#1976d2"},children:"Remarks"}),(0,d.jsx)("p",{className:"mb-0 text-dark mt-1",children:c})]})}),t&&(0,d.jsx)("div",{className:"col-md-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("small",{className:"fw-medium",style:{color:"#1976d2"},children:"Action Required"}),(0,d.jsx)("p",{className:"mb-0 text-dark mt-1",children:t})]})}),i&&(0,d.jsx)("div",{className:"col-md-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("small",{className:"fw-medium",style:{color:"#1976d2"},children:"Due Date"}),(0,d.jsx)("p",{className:"mb-0 text-dark mt-1",children:r()(i).format("DD MMM YYYY")})]})}),o&&(0,d.jsx)("div",{className:"col-md-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("small",{className:"fw-medium",style:{color:"#1976d2"},children:"Assignee"}),(0,d.jsx)("p",{className:"mb-0 text-dark mt-1",children:u(o)})]})}),m&&m.length>0&&(0,d.jsx)("div",{className:"col-12",children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("small",{className:"fw-medium",style:{color:"#1976d2"},children:["Attachments (",m.length,")"]}),(0,d.jsx)("div",{className:"mt-2 d-flex flex-wrap gap-2",children:m.map((e,s)=>(0,d.jsx)("div",{className:"border rounded p-2 bg-white",children:(0,d.jsx)(n.A,{fileName:e,size:80,name:!0})},s))})]})})]})})]})},l)}if("checkpoint-group"===e.type){let s=e.data,a=s.groupAnswer||e.groupAnswer,c=s.checkpoints||e.checkpoints||[],t=s.reason||e.remarks;return(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsxs)("div",{className:"border rounded",style:{backgroundColor:"#f3f9ff"},children:[(0,d.jsx)("div",{className:"p-3 border-bottom",style:{backgroundColor:"#e3f2fd"},children:(0,d.jsxs)("div",{className:"d-flex justify-content-between align-items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h5",{className:"fw-bold mb-1",style:{color:"#1976d2"},children:s.title}),(0,d.jsx)("small",{className:"text-muted",children:"Checkpoint Group"})]}),(0,d.jsx)("span",{className:"px-3 py-1 rounded text-white",style:{backgroundColor:(e=>{switch(e){case"Yes":return"#4caf50";case"No":return"#f44336";default:return"#9e9e9e"}})(a||""),fontSize:"0.875rem"},children:a||"Not Answered"})]})}),(0,d.jsx)("div",{className:"p-3",children:"Yes"===a&&c.length>0?(0,d.jsx)("div",{children:c.map((e,s)=>(0,d.jsxs)("div",{className:"border rounded p-3 mb-3 ".concat(s===c.length-1?"mb-0":""),style:{backgroundColor:"#ffffff"},children:[(0,d.jsxs)("div",{className:"d-flex justify-content-between align-items-start mb-2",children:[(0,d.jsx)("div",{className:"flex-grow-1",children:(0,d.jsx)("h6",{className:"fw-medium mb-1",style:{color:"#1976d2"},children:e.text})}),(0,d.jsx)("span",{className:"px-2 py-1 rounded text-white",style:{backgroundColor:(e=>{switch(e){case"Yes":return"#4caf50";case"No":return"#f44336";case"N/A":return"#ff9800";default:return"#9e9e9e"}})(e.selected||""),fontSize:"0.75rem"},children:e.selected||"Not Selected"})]}),(e.remarks||e.actionToBeTaken||e.dueDate||e.assignee||e.uploads&&e.uploads.length>0)&&(0,d.jsx)("div",{className:"border-top pt-2 mt-2",style:{borderColor:"#e3f2fd"},children:(0,d.jsxs)("div",{className:"row g-2",children:[e.remarks&&(0,d.jsx)("div",{className:"col-12",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("small",{className:"fw-medium",style:{color:"#1976d2"},children:"Remarks"}),(0,d.jsx)("p",{className:"mb-0 small text-dark mt-1",children:e.remarks})]})}),e.actionToBeTaken&&(0,d.jsx)("div",{className:"col-md-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("small",{className:"fw-medium",style:{color:"#1976d2"},children:"Action"}),(0,d.jsx)("p",{className:"mb-0 small text-dark mt-1",children:e.actionToBeTaken})]})}),e.dueDate&&(0,d.jsx)("div",{className:"col-md-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("small",{className:"fw-medium",style:{color:"#1976d2"},children:"Due"}),(0,d.jsx)("p",{className:"mb-0 small text-dark mt-1",children:r()(e.dueDate).format("DD MMM YYYY")})]})}),e.assignee&&(0,d.jsx)("div",{className:"col-md-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("small",{className:"fw-medium",style:{color:"#1976d2"},children:"Assignee"}),(0,d.jsx)("p",{className:"mb-0 small text-dark mt-1",children:u(e.assignee)})]})}),e.uploads&&e.uploads.length>0&&(0,d.jsx)("div",{className:"col-12",children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("small",{className:"fw-medium",style:{color:"#1976d2"},children:["Attachments (",e.uploads.length,")"]}),(0,d.jsx)("div",{className:"mt-1 d-flex flex-wrap gap-1",children:e.uploads.map((e,s)=>(0,d.jsx)("div",{className:"border rounded p-1 bg-light",children:(0,d.jsx)(n.A,{fileName:e,size:60,name:!0})},s))})]})})]})})]},"cp-".concat(s)))}):(0,d.jsx)("div",{className:"text-center py-4",children:"No"===a?(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"mb-3",children:(0,d.jsx)("span",{className:"text-muted",children:'This section was marked as "No"'})}),t&&(0,d.jsx)("div",{className:"p-3 rounded",style:{backgroundColor:"#ffebee",border:"1px solid #ffcdd2"},children:(0,d.jsxs)("div",{className:"text-start",children:[(0,d.jsx)("small",{className:"fw-medium",style:{color:"#d32f2f"},children:"Reason"}),(0,d.jsx)("p",{className:"mb-0 text-dark mt-1",children:t})]})})]}):(0,d.jsx)("div",{children:(0,d.jsx)("span",{className:"text-muted",children:"This section was not completed"})})})})]})},l)}if("text-input"===e.type){let s=e.data,a=s.textValue||e.textValue;return(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsxs)("div",{className:"p-3 rounded border",style:{backgroundColor:"#f3f9ff"},children:[(0,d.jsx)("h6",{className:"fw-bold mb-3",style:{color:"#1976d2"},children:s.label}),a?(0,d.jsx)("div",{className:"p-3 rounded border bg-white",children:(0,d.jsx)("p",{className:"mb-0 text-dark",style:{whiteSpace:"pre-wrap",wordBreak:"break-word"},children:a})}):(0,d.jsx)("div",{className:"text-muted",children:(0,d.jsx)("span",{children:"No text entered"})})]})},l)}if("image-input"===e.type){let s=e.data,a=s.uploads||e.imageFiles||[];return(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsxs)("div",{className:"p-3 rounded border",style:{backgroundColor:"#f3f9ff"},children:[(0,d.jsx)("h6",{className:"fw-bold mb-3",style:{color:"#1976d2"},children:s.label}),a&&a.length>0?(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"mb-2",children:(0,d.jsxs)("small",{className:"fw-medium",style:{color:"#1976d2"},children:["Uploaded Images (",a.length,")"]})}),(0,d.jsx)("div",{className:"d-flex flex-wrap gap-2",children:a.map((e,s)=>(0,d.jsx)("div",{className:"border rounded p-2 bg-white",children:(0,d.jsx)(n.A,{fileName:e,size:100,name:!0})},s))})]}):(0,d.jsx)("div",{className:"text-muted",children:(0,d.jsx)("span",{children:"No images uploaded"})})]})},l)}if("attachment-input"===e.type){let s=e.data,a=s.uploads||e.uploads||[];return(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsxs)("div",{className:"p-3 rounded border",style:{backgroundColor:"#f3f9ff"},children:[(0,d.jsx)("h6",{className:"fw-bold mb-3",style:{color:"#1976d2"},children:s.label}),a&&a.length>0?(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"mb-2",children:(0,d.jsxs)("small",{className:"fw-medium",style:{color:"#1976d2"},children:["Uploaded Attachments (",a.length,")"]})}),(0,d.jsx)("div",{className:"d-flex flex-wrap gap-2",children:a.map((e,s)=>(0,d.jsx)("div",{className:"border rounded p-2 bg-white",children:(0,d.jsx)(n.A,{fileName:e,size:100,name:!0})},s))})]}):(0,d.jsx)("div",{className:"text-muted",children:(0,d.jsx)("span",{children:"No attachments uploaded"})})]})},l)}return null}):(0,d.jsx)("p",{className:"text-muted",children:"No checklist data available"})})()]})]})}}}]);