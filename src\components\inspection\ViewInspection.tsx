"use client";

import React, { useEffect, useState } from "react";
// Bootstrap components no longer needed with simplified design
import moment from "moment";
import API from "@/services/API";
import { USERS_URL } from "@/constant";
import ImageComponent from "@/services/FileDownlodS3";

/* ──────────────── Types ───────────────── */

interface Location {
  name?: string;
}

interface User {
  id: string;
  firstName?: string;
}

interface ChecklistMeta {
  name?: string;
  customId?: string;
  category?: string;
  status?: string;
  value?: {
    metadata: {
      name: string;
      version: string;
      createdAt: string;
      totalComponents: number;
      modes: {
        communicate: number;
        feedback: number;
      };
    };
    components: ChecklistComponent[];
  };
}

interface Checkpoint {
  id: string;
  text: string;
  selected?: "Yes" | "No" | "N/A" | "";
  remarks?: string;
  actionToBeTaken?: string;
  dueDate?: Date | string | null;
  assignee?: string;
  uploads?: string[];
}

interface CheckpointGroupData {
  id: string;
  type: "checkpoint-group";
  position: number;
  required: boolean;
  allowFromLibrary: boolean;
  title: string;
  checkpoints: Checkpoint[]; // Use full Checkpoint interface
  groupAnswer?: "Yes" | "No" | "";
  reason?: string;
}

interface CheckpointData {
  id: string;
  type: "checkpoint";
  position: number;
  required: boolean;
  allowFromLibrary: boolean;
  text: string;
}

interface DateData {
  id: string;
  type: "date";
  position: number;
  required: boolean;
  allowFromLibrary: boolean;
  label: string;
}

interface SignData {
  id: string;
  type: "sign";
  position: number;
  required: boolean;
  allowFromLibrary: boolean;
  label: string;
}

interface HeaderData {
  id: string;
  type: "header";
  position: number;
  required: boolean;
  allowFromLibrary: boolean;
  text: string;
}

interface TextBodyData {
  type: "text-body";
  position: number;
  content: string;
}

interface SectionHeaderData {
  id: string;
  type: "section-header";
  position: number;
  required: boolean;
  allowFromLibrary: boolean;
  text: string;
}

interface TextInputData {
  id: string;
  type: "text-input";
  position: number;
  required: boolean;
  label: string;
  placeholder?: string;
  textValue?: string; // The actual text value entered by user
}

interface ImageInputData {
  id: string;
  type: "image-input";
  position: number;
  required: boolean;
  label: string;
  uploads?: string[];
}

interface AttachmentInputData {
  id: string;
  type: "attachment-input";
  position: number;
  required: boolean;
  label: string;
  attachmentConfig: {
    image: {
      enabled: boolean;
      galleryUploads: boolean;
    };
    video: {
      enabled: boolean;
      galleryUploads: boolean;
    };
    documents: {
      enabled: boolean;
    };
  };
  uploads?: string[];
}

interface ChecklistComponent {
  id: string;
  type: "checkpoint-group" | "checkpoint" | "date" | "sign" | "header" | "text-body" | "section-header" | "text-input" | "image-input" | "attachment-input";
  position: number;
  data: CheckpointGroupData | CheckpointData | DateData | SignData | HeaderData | TextBodyData | SectionHeaderData | TextInputData | ImageInputData | AttachmentInputData;
  validation: {
    isValid: boolean;
    lastValidated: string;
  };
  // Response data for user interactions
  groupAnswer?: "Yes" | "No" | "";
  checkpoints?: Checkpoint[];
  selected?: "Yes" | "No" | "N/A" | "";
  remarks?: string;
  actionToBeTaken?: string;
  dueDate?: Date | string | null;
  assignee?: string;
  uploads?: string[];
  selectedDate?: Date | string | null;
  signature?: string;
  textValue?: string; // For text-input components
  imageFiles?: string[]; // For image-input components
}

interface ReportData {
  inspectionCategory?: string;
  scheduledDate?: string;
  dueDate?: string;
  actualCompletionDate?: string;
  status?: string;
  checklistVersion?: string;
  checklist?: ChecklistMeta;
  inspector?: User;
  assignedBy?: User;
  locationOne?: Location;
  locationTwo?: Location;
  locationThree?: Location;
  locationFour?: Location;
  locationFive?: Location;
  locationSix?: Location;
  value?: ChecklistComponent[] | { [key: string]: ChecklistComponent | boolean | number }; // Support both array and object structure
}

interface ViewInspectionProps {
  reportData: ReportData;
  type: string;
}

/* ─────────────── Component ─────────────── */

const ViewInspection: React.FC<ViewInspectionProps> = ({ reportData, type }) => {
  const [users, setUsers] = useState<User[]>([]);
  console.log(reportData)
  const locationDisplay: string = (
    [
      reportData.locationOne,
      reportData.locationTwo,
      reportData.locationThree,
      reportData.locationFour,
      reportData.locationFive,
      reportData.locationSix,
    ]
      .filter((loc): loc is Location => Boolean(loc?.name))
      .map((loc) => loc.name)
      .join(" > ") || "N/A"
  );

  /* ─────────────── effects ─────────────── */
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await API.get<User[]>(USERS_URL);
        setUsers(response.data);
      } catch (e) {
        console.error("Fetching users failed", e);
      }
    };

    fetchUsers();
  }, []);

  /* ─────────────── helpers ─────────────── */
  const getName = (id?: string): string => {
    if (!id) return "";
    const user = users.find((u) => u.id === id);
    return user?.firstName || "";
  };

  const renderChecklistComponents = () => {
    // Get checklist data from either the new structure or fallback
    let checklistData: ChecklistComponent[] = [];

    if (reportData.value) {
      // Handle object-based structure like your example
      if (Array.isArray(reportData.value)) {
        checklistData = reportData.value;
      } else {
        // Convert object structure to array
        const valueObj = reportData.value as { [key: string]: ChecklistComponent | boolean | number };
        checklistData = Object.keys(valueObj)
          .filter(key => !['hasActions', 'actionsCount'].includes(key))
          .map(key => valueObj[key] as ChecklistComponent)
          .filter(item => item && typeof item === 'object' && item.type);
      }
    }

    if (!checklistData.length) {
      return <p className="text-muted">No checklist data available</p>;
    }

    // Sort components by position
    const sortedComponents = [...checklistData].sort((a, b) => a.position - b.position);

    return sortedComponents.map((component, index) => {
      const key = `component-${index}`;

      // Render different component types
      if (component.type === "header") {
        const headerData = component.data as HeaderData;
        return (
          <div key={key} className="mb-4">
            <h4 className="text-dark fw-bold mb-2">{headerData.text}</h4>
            <hr style={{ borderColor: '#e3f2fd', borderWidth: '2px' }} />
          </div>
        );
      }

      if (component.type === "section-header") {
        const sectionHeaderData = component.data as SectionHeaderData;
        return (
          <div key={key} className="mb-4">
            <div className="p-3 rounded" style={{ backgroundColor: '#f3f9ff', borderLeft: '4px solid #2196f3' }}>
              <h5 className="mb-0 fw-bold" style={{ color: '#1976d2' }}>
                {sectionHeaderData.text}
              </h5>
            </div>
          </div>
        );
      }

      if (component.type === "text-body") {
        const textBodyData = component.data as TextBodyData;
        return (
          <div key={key} className="mb-4">
            <div className="p-3 rounded" style={{ backgroundColor: '#f8f9fa', border: '1px solid #e9ecef' }}>
              <p className="text-muted mb-0" style={{ whiteSpace: 'pre-wrap', lineHeight: '1.6' }}>
                {textBodyData.content}
              </p>
            </div>
          </div>
        );
      }

      if (component.type === "date") {
        const dateData = component.data as DateData;
        // Handle both old structure (component.selectedDate) and new structure (dateData.selectedDate)
        const selectedDate = (dateData as any).selectedDate || component.selectedDate;

        return (
          <div key={key} className="mb-4">
            <div className="p-3 rounded border" style={{ backgroundColor: '#f3f9ff' }}>
              <div className="d-flex justify-content-between align-items-center">
                <div>
                  <h6 className="fw-bold mb-1" style={{ color: '#1976d2' }}>{dateData.label}</h6>
                  <small className="text-muted">Selected Date</small>
                </div>
                <div>
                  <span className="px-3 py-1 rounded text-white" style={{ backgroundColor: '#2196f3', fontSize: '0.875rem' }}>
                    {selectedDate
                      ? moment(selectedDate).format("DD MMM YYYY")
                      : "Not Selected"
                    }
                  </span>
                </div>
              </div>
            </div>
          </div>
        );
      }

      if (component.type === "sign") {
        const signData = component.data as SignData;
        // Handle both old structure (component.signature) and new structure (signData.signature)
        const signature = (signData as any).signature || component.signature;

        return (
          <div key={key} className="mb-4">
            <div className="p-3 rounded border" style={{ backgroundColor: '#f3f9ff' }}>
              <h6 className="fw-bold mb-3" style={{ color: '#1976d2' }}>{signData.label}</h6>
              {signature ? (
                <div>
                  <div className="mb-3">
                    <span className="px-2 py-1 rounded text-white" style={{ backgroundColor: '#4caf50', fontSize: '0.875rem' }}>
                      ✓ Signature Captured
                    </span>
                  </div>
                  <div className="border rounded p-3 bg-white d-inline-block">
                    <ImageComponent fileName={signature} size={200} name={false} />
                  </div>
                </div>
              ) : (
                <div>
                  <span className="px-2 py-1 rounded" style={{ backgroundColor: '#f5f5f5', color: '#757575', fontSize: '0.875rem' }}>
                    No signature provided
                  </span>
                </div>
              )}
            </div>
          </div>
        );
      }

      if (component.type === "checkpoint") {
        const checkpointData = component.data as CheckpointData;
        // Handle both old structure (component.selected) and new structure (checkpointData.selected)
        const selected = (checkpointData as any).selected || component.selected;
        const remarks = (checkpointData as any).remarks || component.remarks;
        const actionToBeTaken = (checkpointData as any).actionToBeTaken || component.actionToBeTaken;
        const dueDate = (checkpointData as any).dueDate || component.dueDate;
        const assignee = (checkpointData as any).assignee || component.assignee;
        const uploads = (checkpointData as any).uploads || component.uploads;

        const getStatusColor = (status: string) => {
          switch (status) {
            case "Yes": return "#4caf50";
            case "No": return "#f44336";
            case "N/A": return "#ff9800";
            default: return "#9e9e9e";
          }
        };

        return (
          <div key={key} className="mb-4">
            <div className="p-3 rounded border" style={{ backgroundColor: '#f3f9ff' }}>
              <div className="d-flex justify-content-between align-items-start mb-3">
                <div className="flex-grow-1">
                  <h6 className="fw-bold mb-2" style={{ color: '#1976d2' }}>{checkpointData.text}</h6>
                </div>
                <span className="px-3 py-1 rounded text-white" style={{ backgroundColor: getStatusColor(selected), fontSize: '0.875rem' }}>
                  {selected || "Not Selected"}
                </span>
              </div>

              {/* Additional Details */}
              {(remarks || actionToBeTaken || dueDate || assignee || (uploads && uploads.length > 0)) && (
                <div className="border-top pt-3 mt-3" style={{ borderColor: '#e3f2fd' }}>
                  <div className="row g-3">
                    {remarks && (
                      <div className="col-12">
                        <div>
                          <small className="fw-medium" style={{ color: '#1976d2' }}>Remarks</small>
                          <p className="mb-0 text-dark mt-1">{remarks}</p>
                        </div>
                      </div>
                    )}

                    {actionToBeTaken && (
                      <div className="col-md-6">
                        <div>
                          <small className="fw-medium" style={{ color: '#1976d2' }}>Action Required</small>
                          <p className="mb-0 text-dark mt-1">{actionToBeTaken}</p>
                        </div>
                      </div>
                    )}

                    {dueDate && (
                      <div className="col-md-6">
                        <div>
                          <small className="fw-medium" style={{ color: '#1976d2' }}>Due Date</small>
                          <p className="mb-0 text-dark mt-1">{moment(dueDate).format("DD MMM YYYY")}</p>
                        </div>
                      </div>
                    )}

                    {assignee && (
                      <div className="col-md-6">
                        <div>
                          <small className="fw-medium" style={{ color: '#1976d2' }}>Assignee</small>
                          <p className="mb-0 text-dark mt-1">{getName(assignee)}</p>
                        </div>
                      </div>
                    )}

                    {uploads && uploads.length > 0 && (
                      <div className="col-12">
                        <div>
                          <small className="fw-medium" style={{ color: '#1976d2' }}>Attachments ({uploads.length})</small>
                          <div className="mt-2 d-flex flex-wrap gap-2">
                            {uploads.map((fileName: string, idx: number) => (
                              <div key={idx} className="border rounded p-2 bg-white">
                                <ImageComponent fileName={fileName} size={80} name={true} />
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        );
      }

      if (component.type === "checkpoint-group") {
        const groupData = component.data as CheckpointGroupData;
        const groupAnswer = groupData.groupAnswer || component.groupAnswer;
        const checkpoints = groupData.checkpoints || component.checkpoints || [];
        const reason = groupData.reason || component.remarks;

        const getGroupStatusColor = (status: string) => {
          switch (status) {
            case "Yes": return "#4caf50";
            case "No": return "#f44336";
            default: return "#9e9e9e";
          }
        };

        return (
          <div key={key} className="mb-4">
            <div className="border rounded" style={{ backgroundColor: '#f3f9ff' }}>
              <div className="p-3 border-bottom" style={{ backgroundColor: '#e3f2fd' }}>
                <div className="d-flex justify-content-between align-items-center">
                  <div>
                    <h5 className="fw-bold mb-1" style={{ color: '#1976d2' }}>{groupData.title}</h5>
                    <small className="text-muted">Checkpoint Group</small>
                  </div>
                  <span className="px-3 py-1 rounded text-white" style={{ backgroundColor: getGroupStatusColor(groupAnswer || ""), fontSize: '0.875rem' }}>
                    {groupAnswer || "Not Answered"}
                  </span>
                </div>
              </div>
              <div className="p-3">
                {groupAnswer === "Yes" && checkpoints.length > 0 ? (
                  <div>
                    {checkpoints.map((checkpoint, cpIndex) => {
                      const getCheckpointStatusColor = (status: string) => {
                        switch (status) {
                          case "Yes": return "#4caf50";
                          case "No": return "#f44336";
                          case "N/A": return "#ff9800";
                          default: return "#9e9e9e";
                        }
                      };

                      return (
                        <div key={`cp-${cpIndex}`} className={`border rounded p-3 mb-3 ${cpIndex === checkpoints.length - 1 ? 'mb-0' : ''}`}
                             style={{ backgroundColor: '#ffffff' }}>
                          <div className="d-flex justify-content-between align-items-start mb-2">
                            <div className="flex-grow-1">
                              <h6 className="fw-medium mb-1" style={{ color: '#1976d2' }}>{checkpoint.text}</h6>
                            </div>
                            <span className="px-2 py-1 rounded text-white" style={{ backgroundColor: getCheckpointStatusColor(checkpoint.selected || ""), fontSize: '0.75rem' }}>
                              {checkpoint.selected || "Not Selected"}
                            </span>
                          </div>

                          {/* Checkpoint Details */}
                          {(checkpoint.remarks || checkpoint.actionToBeTaken || checkpoint.dueDate || checkpoint.assignee || (checkpoint.uploads && checkpoint.uploads.length > 0)) && (
                            <div className="border-top pt-2 mt-2" style={{ borderColor: '#e3f2fd' }}>
                              <div className="row g-2">
                                {checkpoint.remarks && (
                                  <div className="col-12">
                                    <div>
                                      <small className="fw-medium" style={{ color: '#1976d2' }}>Remarks</small>
                                      <p className="mb-0 small text-dark mt-1">{checkpoint.remarks}</p>
                                    </div>
                                  </div>
                                )}

                                {checkpoint.actionToBeTaken && (
                                  <div className="col-md-6">
                                    <div>
                                      <small className="fw-medium" style={{ color: '#1976d2' }}>Action</small>
                                      <p className="mb-0 small text-dark mt-1">{checkpoint.actionToBeTaken}</p>
                                    </div>
                                  </div>
                                )}

                                {checkpoint.dueDate && (
                                  <div className="col-md-6">
                                    <div>
                                      <small className="fw-medium" style={{ color: '#1976d2' }}>Due</small>
                                      <p className="mb-0 small text-dark mt-1">{moment(checkpoint.dueDate).format("DD MMM YYYY")}</p>
                                    </div>
                                  </div>
                                )}

                                {checkpoint.assignee && (
                                  <div className="col-md-6">
                                    <div>
                                      <small className="fw-medium" style={{ color: '#1976d2' }}>Assignee</small>
                                      <p className="mb-0 small text-dark mt-1">{getName(checkpoint.assignee)}</p>
                                    </div>
                                  </div>
                                )}

                                {checkpoint.uploads && checkpoint.uploads.length > 0 && (
                                  <div className="col-12">
                                    <div>
                                      <small className="fw-medium" style={{ color: '#1976d2' }}>Attachments ({checkpoint.uploads.length})</small>
                                      <div className="mt-1 d-flex flex-wrap gap-1">
                                        {checkpoint.uploads.map((fileName, idx) => (
                                          <div key={idx} className="border rounded p-1 bg-light">
                                            <ImageComponent fileName={fileName} size={60} name={true} />
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-4">
                    {groupAnswer === "No" ? (
                      <div>
                        <div className="mb-3">
                          <span className="text-muted">This section was marked as &quot;No&quot;</span>
                        </div>
                        {reason && (
                          <div className="p-3 rounded" style={{ backgroundColor: '#ffebee', border: '1px solid #ffcdd2' }}>
                            <div className="text-start">
                              <small className="fw-medium" style={{ color: '#d32f2f' }}>Reason</small>
                              <p className="mb-0 text-dark mt-1">{reason}</p>
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div>
                        <span className="text-muted">This section was not completed</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      }

      if (component.type === "text-input") {
        const textInputData = component.data as TextInputData;
        const textValue = textInputData.textValue || component.textValue;

        return (
          <div key={key} className="mb-4">
            <div className="p-3 rounded border" style={{ backgroundColor: '#f3f9ff' }}>
              <h6 className="fw-bold mb-3" style={{ color: '#1976d2' }}>{textInputData.label}</h6>
              {textValue ? (
                <div className="p-3 rounded border bg-white">
                  <p className="mb-0 text-dark" style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                    {textValue}
                  </p>
                </div>
              ) : (
                <div className="text-muted">
                  <span>No text entered</span>
                </div>
              )}
            </div>
          </div>
        );
      }

      if (component.type === "image-input") {
        const imageInputData = component.data as ImageInputData;
        // Handle both data.uploads and component.imageFiles for compatibility
        const imageFiles = imageInputData.uploads || component.imageFiles || [];

        return (
          <div key={key} className="mb-4">
            <div className="p-3 rounded border" style={{ backgroundColor: '#f3f9ff' }}>
              <h6 className="fw-bold mb-3" style={{ color: '#1976d2' }}>{imageInputData.label}</h6>
              {imageFiles && imageFiles.length > 0 ? (
                <div>
                  <div className="mb-2">
                    <small className="fw-medium" style={{ color: '#1976d2' }}>
                      Uploaded Images ({imageFiles.length})
                    </small>
                  </div>
                  <div className="d-flex flex-wrap gap-2">
                    {imageFiles.map((fileName: string, idx: number) => (
                      <div key={idx} className="border rounded p-2 bg-white">
                        <ImageComponent fileName={fileName} size={100} name={true} />
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-muted">
                  <span>No images uploaded</span>
                </div>
              )}
            </div>
          </div>
        );
      }

      if (component.type === "attachment-input") {
        const attachmentInputData = component.data as AttachmentInputData;
        // Handle both data.uploads and component.uploads for compatibility
        const attachmentFiles = attachmentInputData.uploads || component.uploads || [];

        return (
          <div key={key} className="mb-4">
            <div className="p-3 rounded border" style={{ backgroundColor: '#f3f9ff' }}>
              <h6 className="fw-bold mb-3" style={{ color: '#1976d2' }}>{attachmentInputData.label}</h6>
              {attachmentFiles && attachmentFiles.length > 0 ? (
                <div>
                  <div className="mb-2">
                    <small className="fw-medium" style={{ color: '#1976d2' }}>
                      Uploaded Attachments ({attachmentFiles.length})
                    </small>
                  </div>
                  <div className="d-flex flex-wrap gap-2">
                    {attachmentFiles.map((fileName: string, idx: number) => (
                      <div key={idx} className="border rounded p-2 bg-white">
                        <ImageComponent fileName={fileName} size={100} name={true} />
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-muted">
                  <span>No attachments uploaded</span>
                </div>
              )}
            </div>
          </div>
        );
      }

      return null;
    });
  };

  /* ─────────────── render ─────────────── */
  return (
    <div>
      {/* Inspection Overview Card */}
      <div className="mb-4">
        <div className="p-4 rounded border" style={{ backgroundColor: '#e3f2fd' }}>
          <div className="mb-3">
            <h4 className="fw-bold mb-1" style={{ color: '#1976d2' }}>Inspection Details</h4>
            <small className="text-muted">Comprehensive inspection report</small>
          </div>

          {/* Meta Information Grid */}
          <div className="row g-4">
            <div className="col-md-4">
              <div>
                <h6 className="fw-bold mb-1" style={{ color: '#1976d2' }}>Category</h6>
                <p className="text-muted mb-0">{reportData.inspectionCategory || "N/A"}</p>
              </div>
            </div>
            <div className="col-md-4">
              <div>
                <h6 className="fw-bold mb-1" style={{ color: '#1976d2' }}>Scheduled Date</h6>
                <p className="text-muted mb-0">
                  {reportData.scheduledDate
                    ? moment(reportData.scheduledDate).format("DD MMM YYYY")
                    : "N/A"}
                </p>
              </div>
            </div>
            <div className="col-md-4">
              <div>
                <h6 className="fw-bold mb-1" style={{ color: '#1976d2' }}>Due Date</h6>
                <p className="text-muted mb-0">
                  {reportData.dueDate
                    ? moment(reportData.dueDate).format("DD MMM YYYY")
                    : "N/A"}
                </p>
              </div>
            </div>
          </div>


          <hr className="my-4" style={{ borderColor: '#bbdefb' }} />

          <div className="row g-4">
            <div className="col-md-4">
              <div>
                <h6 className="fw-bold mb-1" style={{ color: '#1976d2' }}>Inspector</h6>
                <p className="text-muted mb-0">{reportData.inspector?.firstName || "N/A"}</p>
              </div>
            </div>
            <div className="col-md-4">
              <div>
                <h6 className="fw-bold mb-1" style={{ color: '#1976d2' }}>Checklist</h6>
                <p className="text-muted mb-0">{reportData.checklist?.name || "N/A"}</p>
              </div>
            </div>
            <div className="col-md-4">
              <div>
                <h6 className="fw-bold mb-1" style={{ color: '#1976d2' }}>Version</h6>
                <p className="text-muted mb-0">{reportData.checklistVersion || "N/A"}</p>
              </div>
            </div>
          </div>

          <hr className="my-4" style={{ borderColor: '#bbdefb' }} />

          <div className="row g-4">
            <div className="col-12">
              <div>
                <h6 className="fw-bold mb-1" style={{ color: '#1976d2' }}>Location</h6>
                <p className="text-muted mb-0">{locationDisplay}</p>
              </div>
            </div>
          </div>

          <hr className="my-4" style={{ borderColor: '#bbdefb' }} />

          <div className="row g-4">
            <div className="col-md-4">
              <div>
                <h6 className="fw-bold mb-1" style={{ color: '#1976d2' }}>Status</h6>
                <span className="px-3 py-1 rounded text-white" style={{
                  backgroundColor: reportData.status === 'Completed' ? '#4caf50' : reportData.status === 'In Progress' ? '#ff9800' : '#9e9e9e',
                  fontSize: '0.875rem'
                }}>
                  {reportData.status || "N/A"}
                </span>
              </div>
            </div>
            <div className="col-md-4">
              <div>
                <h6 className="fw-bold mb-1" style={{ color: '#1976d2' }}>Completion Date</h6>
                <p className="text-muted mb-0">
                  {reportData.actualCompletionDate
                    ? moment(reportData.actualCompletionDate).format("DD MMM YYYY")
                    : "N/A"}
                </p>
              </div>
            </div>
            <div className="col-md-4">
              <div>
                <h6 className="fw-bold mb-1" style={{ color: '#1976d2' }}>Assigned By</h6>
                <p className="text-muted mb-0">{reportData.assignedBy?.firstName || "N/A"}</p>
              </div>
            </div>
          </div>

          <hr className="my-4" style={{ borderColor: '#bbdefb' }} />

          <div className="row g-4">
            <div className="col-md-6">
              <div>
                <h6 className="fw-bold mb-1" style={{ color: '#1976d2' }}>Checklist ID</h6>
                <p className="text-muted mb-0">{reportData.checklist?.customId || "N/A"}</p>
              </div>
            </div>
            <div className="col-md-6">
              <div>
                <h6 className="fw-bold mb-1" style={{ color: '#1976d2' }}>Category</h6>
                <p className="text-muted mb-0">{reportData.checklist?.category || "N/A"}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Checklist Components */}
      {type === "view" && (
        <div>
          <div className="mb-4">
            <h4 className="fw-bold mb-0" style={{ color: '#1976d2' }}>Checklist Components</h4>
          </div>
          {renderChecklistComponents()}
        </div>
      )}
    </div>
  );
};

export default ViewInspection;
