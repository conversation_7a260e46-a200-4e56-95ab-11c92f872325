(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[205,740,943,1165,1367,1375,1635,1637,1721,2036,2455,3132,3401,3424,3427,3523,4426,4576,5414,5598,5691,5904,6038,6204,6385,6658,7157,7165,7642,7779,7984,8470,8711,9437,9554,9739],{21217:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var l=s(95155),i=s(9e4),a=s(38808),c=s(12115);let n=e=>{let{handleShowSetting:t,showSetting:s}=e,{theme:c,handleDarkModeToggle:n}=(0,i.D)(),{viewMode:r,handleRTLToggling:d}=(0,a.L)();return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{id:"setting-popup-overlay",className:s?"active":"",onClick:t}),(0,l.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(s?"active":""),id:"settingCard",children:(0,l.jsx)("div",{className:"card-body",children:(0,l.jsxs)("div",{className:"container",children:[(0,l.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,l.jsx)("p",{className:"mb-0",children:"Settings"}),(0,l.jsx)("div",{onClick:t,className:"btn-close",id:"settingCardClose"})]}),(0,l.jsx)("div",{className:"single-setting-panel",children:(0,l.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,l.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,l.jsx)("div",{className:"single-setting-panel",children:(0,l.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,l.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,l.jsx)("div",{className:"single-setting-panel",children:(0,l.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,l.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===c,onChange:n}),(0,l.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===c?"Light":"Dark"," mode"]})]})}),(0,l.jsx)("div",{className:"single-setting-panel",children:(0,l.jsxs)("div",{className:"form-check form-switch",children:[(0,l.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===r,onChange:d}),(0,l.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===r?"LTR":"RTL"," mode"]})]})})]})})})]})};var r=s(6874),d=s.n(r);let o=e=>{let{links:t,title:s}=e,[i,a]=(0,c.useState)(!1),r=()=>a(!i);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"header-area",id:"headerArea",children:(0,l.jsx)("div",{className:"container",children:(0,l.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,l.jsx)("div",{className:"back-button",children:(0,l.jsx)(d(),{href:"/".concat(t),children:(0,l.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,l.jsx)("div",{className:"page-heading",children:(0,l.jsx)("h6",{className:"mb-0",children:s})}),(0,l.jsx)("div",{className:"setting-wrapper",onClick:r,children:(0,l.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,l.jsx)("i",{className:"bi bi-gear"}),(0,l.jsx)("span",{})]})})]})})}),(0,l.jsx)(n,{showSetting:i,handleShowSetting:r})]})}},38808:(e,t,s)=>{"use strict";s.d(t,{L:()=>i});var l=s(12115);let i=()=>{let[e,t]=(0,l.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,l.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let s=()=>{t(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:s,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}s()}}}},38983:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var l=s(95155),i=s(6874),a=s.n(i);s(12115);let c=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],n=()=>(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,l.jsx)("div",{className:"container px-0",children:(0,l.jsx)("div",{className:"footer-nav position-relative",children:(0,l.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:c.map((e,t)=>(0,l.jsx)("li",{children:(0,l.jsxs)(a(),{href:"/".concat(e.link),children:[(0,l.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,l.jsx)("span",{children:e.title})]})},t))})})})})})},61187:(e,t,s)=>{Promise.resolve().then(s.bind(s,38983)),Promise.resolve().then(s.bind(s,21217))},9e4:(e,t,s)=>{"use strict";s.d(t,{D:()=>i});var l=s(12115);let i=()=>{let[e,t]=(0,l.useState)("light"),[s,i]=(0,l.useState)(!1);(0,l.useEffect)(()=>{t(localStorage.getItem("theme")||"light"),i(!0)},[]),(0,l.useEffect)(()=>{s&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,s]);let a=(0,l.useCallback)(()=>{t(e=>"dark"===e?"light":"dark")},[]),c=(0,l.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}a()},[a]);return{theme:e,toggleTheme:a,handleDarkModeToggle:c}}}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,8441,1684,7358],()=>t(61187)),_N_E=e.O()}]);