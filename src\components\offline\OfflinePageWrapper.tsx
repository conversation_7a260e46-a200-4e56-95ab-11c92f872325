"use client";

import React, { useState, useEffect, ReactNode } from 'react';
import { offlineStorage } from '@/services/offlineStorage';
import OfflineIndicator from './OfflineIndicator';

interface OfflinePageWrapperProps {
  children: ReactNode;
  fallbackContent?: ReactNode;
  showOfflineIndicator?: boolean;
  className?: string;
}

const OfflinePageWrapper: React.FC<OfflinePageWrapperProps> = ({
  children,
  fallbackContent,
  showOfflineIndicator = true,
  className = ''
}) => {
  const [isOnline, setIsOnline] = useState(true);
  const [hasOfflineData, setHasOfflineData] = useState(false);

  useEffect(() => {
    // Initialize online status
    setIsOnline(navigator.onLine);

    // Check if we have any offline data
    checkOfflineData();

    // Listen for online/offline events
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const checkOfflineData = async () => {
    try {
      const info = await offlineStorage.getStorageInfo();
      setHasOfflineData(info.services > 0 || info.actions > 0);
    } catch (error) {
      console.error('Error checking offline data:', error);
    }
  };

  // If offline and no cached data, show fallback
  if (!isOnline && !hasOfflineData && fallbackContent) {
    return (
      <div className={className}>
        {showOfflineIndicator && (
          <div className="d-flex justify-content-end p-3">
            <OfflineIndicator showDetails={true} />
          </div>
        )}
        {fallbackContent}
      </div>
    );
  }

  // If offline but have cached data, show offline banner
  if (!isOnline && hasOfflineData) {
    return (
      <div className={className}>
        {/* Offline Banner */}
        <div 
          className="alert alert-warning d-flex align-items-center mb-0"
          style={{
            borderRadius: '0',
            borderLeft: 'none',
            borderRight: 'none',
            borderTop: 'none',
            backgroundColor: '#fff3cd',
            borderColor: '#ffeaa7',
            color: '#856404'
          }}
        >
          <i className="bi bi-wifi-off me-2"></i>
          <div className="flex-grow-1">
            <small style={{ fontSize: '12px', fontWeight: '500' }}>
              You&apos;re offline. Showing cached data.
            </small>
          </div>
          {showOfflineIndicator && (
            <OfflineIndicator showDetails={false} />
          )}
        </div>
        {children}
      </div>
    );
  }

  // Online or offline with data - show normal content
  return (
    <div className={className}>
      {showOfflineIndicator && !isOnline && (
        <div className="d-flex justify-content-end p-3">
          <OfflineIndicator showDetails={true} />
        </div>
      )}
      {children}
    </div>
  );
};

export default OfflinePageWrapper;
