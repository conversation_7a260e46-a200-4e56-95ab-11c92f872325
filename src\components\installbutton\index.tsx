"use client";

import { useState, useEffect } from "react";

interface NavigatorStandalone extends Navigator {
  standalone?: boolean;
}

const InstallButton = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<Event | null>(null);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isIos, setIsIos] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  // Controls whether we show the custom iOS popup
  const [showIosPopup, setShowIosPopup] = useState(false);

  useEffect(() => {
    const userAgent = window.navigator.userAgent.toLowerCase();

    if (/iphone|ipad|ipod/.test(userAgent)) {
      setIsIos(true);

      // Check if the app is already installed (launched from home screen)
      if ((window.navigator as NavigatorStandalone).standalone) {
        setIsInstalled(true);
      }
    }

    // For Android and other platforms that support beforeinstallprompt
    const handleBeforeInstallPrompt = (event: Event) => {
      event.preventDefault();
      setDeferredPrompt(event);
      setIsInstallable(true);
    };

    window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt);

    // Listen for the "appinstalled" event (non-iOS platforms)
    window.addEventListener("appinstalled", () => {
      setDeferredPrompt(null);
      setIsInstallable(false);
    });

    return () => {
      window.removeEventListener("beforeinstallprompt", handleBeforeInstallPrompt);
    };
  }, []);

  useEffect(() => {
    // If on iOS and not installed, show the custom popup
    if (isIos && !isInstalled) {
      setShowIosPopup(true);
    }
  }, [isIos, isInstalled]);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    (deferredPrompt as any).prompt();
    const choiceResult = await (deferredPrompt as any).userChoice;

    if (choiceResult.outcome === "accepted") {
      console.log("User accepted the PWA installation");
    }
    setDeferredPrompt(null);
    setIsInstallable(false);
  };

  const closeIosPopup = () => {
    setShowIosPopup(false);
  };

  return (
    <>
      {/* Android/Chromium (beforeinstallprompt) button */}
      {isInstallable && !isIos && (
        <button className="install-btn" onClick={handleInstallClick}>
          Install App
        </button>
      )}

      {/* Custom popup for iOS users */}
      {showIosPopup && (
        <div className="ios-popup-overlay">
          <div className="ios-popup-content">
            <p>
              <strong>Acuizen</strong>
              <br />
              To install the app, tap the{" "}
              <strong>Share</strong>{" "}
              icon{" "}
              <svg
                viewBox="0 0 24 24"
                width="16"
                height="16"
                style={{ verticalAlign: "middle", margin: "0 4px" }}
                aria-hidden="true"
              >
                <path
                  fill="currentColor"
                  d="M12 2.5l-3.5 3.5h2.5v6h2v-6h2.5L12 2.5zM6 14v6h12v-6h2v6c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2v-6h2z"
                />
              </svg>
              in Safari and select <strong>Add to Home Screen</strong>.
            </p>
            <button onClick={closeIosPopup} className="ios-popup-close">
              Close
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default InstallButton;
