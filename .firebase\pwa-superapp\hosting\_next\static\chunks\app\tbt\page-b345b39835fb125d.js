(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6483],{2383:(e,s,a)=>{"use strict";a.d(s,{default:()=>p});var l=a(95155),t=a(11518),r=a.n(t),i=a(12115),c=a(35695),n=a(38336),d=a(26957),o=a(56160),m=a(60902),x=a(52702),f=a(82940),b=a.n(f),h=a(43864);let j=e=>{var s,a,t,r,i,c,n,d,o,m;let{formData:x}=e;return(0,l.jsxs)("div",{style:{background:"transparent"},children:[(0,l.jsxs)("div",{className:"row g-3 mb-4",children:[(0,l.jsx)("div",{className:"col-md-6",children:(0,l.jsx)("div",{className:"card border-0",style:{backgroundColor:"#f0f9ff"},children:(0,l.jsxs)("div",{className:"card-body p-3",children:[(0,l.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-2",children:[(0,l.jsx)("i",{className:"bi bi-calendar-event text-info"}),(0,l.jsx)("h6",{className:"mb-0 fw-semibold",children:"Commence Date"})]}),(0,l.jsx)("p",{className:"mb-0 text-muted",children:x.commenceDate?b()(x.commenceDate).format("DD-MM-YYYY HH:mm"):"Not started"})]})})}),(0,l.jsx)("div",{className:"col-md-6",children:(0,l.jsx)("div",{className:"card border-0",style:{backgroundColor:"#f0f9ff"},children:(0,l.jsxs)("div",{className:"card-body p-3",children:[(0,l.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-2",children:[(0,l.jsx)("i",{className:"bi bi-person-badge text-info"}),(0,l.jsx)("h6",{className:"mb-0 fw-semibold",children:"Conducted By"})]}),(0,l.jsx)("p",{className:"mb-0 text-muted",children:(null==(s=x.conductedBy)?void 0:s.firstName)||"-"})]})})})]}),(0,l.jsx)("div",{className:"card border-0 mb-3",style:{backgroundColor:"#f8fafc"},children:(0,l.jsxs)("div",{className:"card-body p-3",children:[(0,l.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-2",children:[(0,l.jsx)("i",{className:"bi bi-briefcase text-primary"}),(0,l.jsx)("h6",{className:"mb-0 fw-semibold",children:"Work Activity / Situational Hazard"})]}),(0,l.jsx)("p",{className:"mb-0 text-muted",children:(null==(t=x.riskAssessment)||null==(a=t.workActivity)?void 0:a.name)||"-"})]})}),(0,l.jsx)("div",{className:"card border-0 mb-3",style:{backgroundColor:"#f8fafc"},children:(0,l.jsxs)("div",{className:"card-body p-3",children:[(0,l.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-2",children:[(0,l.jsx)("i",{className:"bi bi-geo-alt text-primary"}),(0,l.jsx)("h6",{className:"mb-0 fw-semibold",children:"Location"})]}),(0,l.jsx)("p",{className:"mb-0 text-muted",children:[null==x||null==(r=x.locationOne)?void 0:r.name,null==x||null==(i=x.locationTwo)?void 0:i.name,null==x||null==(c=x.locationThree)?void 0:c.name,null==x||null==(n=x.locationFour)?void 0:n.name].filter(Boolean).join(" > ")||"-"})]})}),(0,l.jsx)("div",{className:"card border-0 mb-4",style:{backgroundColor:"#fef2f2"},children:(0,l.jsxs)("div",{className:"card-body p-3",children:[(0,l.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-2",children:[(0,l.jsx)("i",{className:"bi bi-people text-danger"}),(0,l.jsx)("h6",{className:"mb-0 fw-semibold",children:"Participants"})]}),(0,l.jsx)("div",{className:"d-flex align-items-center gap-2",children:(0,l.jsxs)("span",{className:"badge bg-danger text-white px-3 py-2",children:[null!=(m=x.noOfPersonsParticipated)?m:0," people participated"]})})]})}),(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-3",children:[(0,l.jsx)("i",{className:"bi bi-list-check text-primary"}),(0,l.jsx)("h5",{className:"mb-0 fw-semibold",children:"Safety Assessment Tasks"})]}),null==x||null==(d=x.tasks)?void 0:d.map((e,s)=>{var a,t,r,i,c;return(0,l.jsxs)("div",{className:"card border-0 shadow-sm mb-4",style:{borderRadius:"12px",overflow:"hidden"},children:[(0,l.jsxs)("div",{className:"card-header bg-light border-0 py-3",children:[(0,l.jsxs)("div",{className:"d-flex align-items-center gap-2",children:[(0,l.jsx)("span",{className:"badge bg-danger text-white",style:{fontSize:"10px"},children:s+1}),(0,l.jsxs)("h6",{className:"mb-0 fw-semibold",children:["Sub-Activity ",s+1]})]}),(null==(a=e.activity)?void 0:a.type)==="activity"&&(0,l.jsxs)("p",{className:"mb-0 mt-2 text-muted small",children:[(0,l.jsx)("strong",{children:"Activity:"})," ",e.activity.name]})]}),(0,l.jsxs)("div",{className:"card-body p-4",style:{backgroundColor:"#fafbfc"},children:[(null==(r=e.hazards)||null==(t=r.selected)?void 0:t.length)>0&&(0,l.jsx)("div",{className:"mb-4",children:(0,l.jsx)("div",{className:"card border-0 mb-3",style:{backgroundColor:"#fff7ed"},children:(0,l.jsxs)("div",{className:"card-body p-3",children:[(0,l.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-3",children:[(0,l.jsx)("i",{className:"bi bi-exclamation-triangle text-warning"}),(0,l.jsx)("h6",{className:"mb-0 fw-semibold",children:"Identified Hazards"})]}),e.hazards.selected.map((e,s)=>(0,l.jsx)("div",{className:"card border-0 mb-3 shadow-sm",children:(0,l.jsx)("div",{className:"card-body p-3",children:(0,l.jsxs)("div",{className:"d-flex align-items-start gap-3 mb-3",children:[(0,l.jsx)("div",{className:"p-2 rounded",style:{backgroundColor:"#f3f4f6"},children:(0,l.jsx)("img",{src:"https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/".concat(e.image),alt:e.name,style:{width:40,height:40,objectFit:"contain"}})}),(0,l.jsxs)("div",{className:"flex-grow-1",children:[(0,l.jsx)("h6",{className:"mb-2 fw-medium",children:e.name}),(0,l.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-2",children:[(0,l.jsx)("span",{className:"small fw-medium",children:"Team Briefed:"}),(0,l.jsx)("span",{className:"badge ".concat("Yes"===e.toolbox_value?"bg-success":"No"===e.toolbox_value?"bg-danger":"bg-secondary"," text-white"),style:{fontSize:"10px"},children:e.toolbox_value})]}),("No"===e.toolbox_value||"Not Applicable"===e.toolbox_value)&&(0,l.jsx)("div",{className:"mt-2 p-2 rounded",style:{backgroundColor:"#fef2f2"},children:(0,l.jsxs)("small",{className:"text-muted",children:[(0,l.jsx)("strong",{children:"Remarks:"})," ",e.toolbox_remarks]})})]})]})})},e.id))]})})}),(null==(c=e.currentControl)||null==(i=c.option)?void 0:i.length)>0&&(0,l.jsx)("div",{className:"card border-0",style:{backgroundColor:"#f0fdf4"},children:(0,l.jsxs)("div",{className:"card-body p-3",children:[(0,l.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-3",children:[(0,l.jsx)("i",{className:"bi bi-shield-check text-success"}),(0,l.jsx)("h6",{className:"mb-0 fw-semibold",children:"Safety Controls"})]}),e.currentControl.option.map((e,s)=>(0,l.jsx)("div",{className:"card border-0 mb-2 shadow-sm",children:(0,l.jsxs)("div",{className:"card-body p-3",children:[(0,l.jsx)("p",{className:"mb-2 fw-medium",children:e.value||"No control description provided"}),(0,l.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-2",children:[(0,l.jsx)("span",{className:"small fw-medium",children:"Implemented:"}),(0,l.jsx)("span",{className:"badge ".concat("Yes"===e.toolbox_value?"bg-success":"No"===e.toolbox_value?"bg-danger":"bg-secondary"," text-white"),style:{fontSize:"10px"},children:e.toolbox_value})]}),("No"===e.toolbox_value||"Not Applicable"===e.toolbox_value)&&(0,l.jsx)("div",{className:"mt-2 p-2 rounded",style:{backgroundColor:"#fef2f2"},children:(0,l.jsxs)("small",{className:"text-muted",children:[(0,l.jsx)("strong",{children:"Remarks:"})," ",e.toolbox_remarks]})})]})},s))]})})]})]},s)})]}),(0,l.jsxs)("div",{className:"card border-0 shadow-sm mb-4",style:{borderRadius:"12px"},children:[(0,l.jsx)("div",{className:"card-header bg-light border-0 py-3",children:(0,l.jsxs)("div",{className:"d-flex align-items-center gap-2",children:[(0,l.jsx)("i",{className:"bi bi-plus-circle text-info"}),(0,l.jsx)("h6",{className:"mb-0 fw-semibold",children:"Additional Controls"})]})}),(0,l.jsxs)("div",{className:"card-body p-4",children:[(0,l.jsxs)("div",{className:"row g-3",children:[(0,l.jsx)("div",{className:"col-md-6",children:(0,l.jsx)("div",{className:"card border-0",style:{backgroundColor:"#f0f9ff"},children:(0,l.jsxs)("div",{className:"card-body p-3",children:[(0,l.jsx)("h6",{className:"mb-2 fw-medium",children:"Additional Controls Identified?"}),(0,l.jsx)("span",{className:"badge ".concat(x.controls.isAdditionalControlsIdentified?"bg-success":"bg-secondary"," text-white px-3 py-2"),children:x.controls.isAdditionalControlsIdentified?"Yes":"No"})]})})}),(0,l.jsx)("div",{className:"col-md-6",children:(0,l.jsx)("div",{className:"card border-0",style:{backgroundColor:"#f0f9ff"},children:(0,l.jsxs)("div",{className:"card-body p-3",children:[(0,l.jsx)("h6",{className:"mb-2 fw-medium",children:"Team Briefed"}),(0,l.jsx)("div",{className:"d-flex align-items-center gap-2",children:(0,l.jsx)("span",{className:"badge ".concat("Yes"===x.controls.teamBrief?"bg-success":"No"===x.controls.teamBrief?"bg-danger":"bg-secondary"," text-white"),style:{fontSize:"10px"},children:x.controls.teamBrief})})]})})})]}),x.controls.isAdditionalControlsIdentified&&(0,l.jsx)("div",{className:"mt-3",children:(0,l.jsx)("div",{className:"card border-0",style:{backgroundColor:"#f8fafc"},children:(0,l.jsxs)("div",{className:"card-body p-3",children:[(0,l.jsx)("h6",{className:"mb-2 fw-medium",children:"Description"}),(0,l.jsx)("p",{className:"mb-0 text-muted",children:x.controls.describeAdditionalControls})]})})}),("No"===x.controls.teamBrief||"Not Applicable"===x.controls.teamBrief)&&(0,l.jsx)("div",{className:"mt-3",children:(0,l.jsx)("div",{className:"card border-0",style:{backgroundColor:"#fef2f2"},children:(0,l.jsxs)("div",{className:"card-body p-3",children:[(0,l.jsx)("h6",{className:"mb-2 fw-medium",children:"Team Brief Remarks"}),(0,l.jsx)("p",{className:"mb-0 text-muted",children:x.controls.teamBriefRemarks})]})})})]})]}),(0,l.jsxs)("div",{className:"card border-0 shadow-sm mb-4",style:{borderRadius:"12px"},children:[(0,l.jsx)("div",{className:"card-header bg-light border-0 py-3",children:(0,l.jsxs)("div",{className:"d-flex align-items-center gap-2",children:[(0,l.jsx)("i",{className:"bi bi-pen text-success"}),(0,l.jsx)("h6",{className:"mb-0 fw-semibold",children:"Team Member Acknowledgement"})]})}),(0,l.jsx)("div",{className:"card-body p-4",children:(0,l.jsx)("div",{className:"row g-3",children:x.toolboxSignStatuses.map((e,s)=>{var a;return(0,l.jsx)("div",{className:"col-md-6",children:(0,l.jsx)("div",{className:"card border-0",style:{backgroundColor:"#f0fdf4"},children:(0,l.jsxs)("div",{className:"card-body p-3",children:[(0,l.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-3",children:[(0,l.jsx)("i",{className:"bi bi-person-check text-success"}),(0,l.jsx)("h6",{className:"mb-0 fw-medium",children:(null==(a=e.signedBy)?void 0:a.firstName)||"-"})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"p-3 rounded border",style:{backgroundColor:"#ffffff",display:"inline-block"},children:(0,l.jsx)(h.A,{size:120,fileName:e.sign})}),(0,l.jsx)("p",{className:"small text-muted mt-2 mb-0",children:"Digital Signature"})]})]})})},s)})})})]}),(0,l.jsxs)("div",{className:"card border-0 shadow-sm",style:{borderRadius:"12px"},children:[(0,l.jsx)("div",{className:"card-header bg-light border-0 py-3",children:(0,l.jsxs)("div",{className:"d-flex align-items-center gap-2",children:[(0,l.jsx)("i",{className:"bi bi-check-circle text-success"}),(0,l.jsx)("h6",{className:"mb-0 fw-semibold",children:"Session Close Out"})]})}),(0,l.jsxs)("div",{className:"card-body p-4",children:[(0,l.jsx)("div",{className:"card border-0 mb-3",style:{backgroundColor:"#f0f9ff"},children:(0,l.jsxs)("div",{className:"card-body p-3",children:[(0,l.jsxs)("div",{className:"d-flex align-items-center gap-2 mb-2",children:[(0,l.jsx)("i",{className:"bi bi-question-circle text-info"}),(0,l.jsx)("h6",{className:"mb-0 fw-medium",children:"Unexpected Challenges Encountered?"})]}),(0,l.jsx)("span",{className:"badge ".concat(x.isCloseOutChallenges?"bg-warning text-dark":"bg-success"," text-white px-3 py-2"),children:x.isCloseOutChallenges?"Yes":"No"})]})}),x.isCloseOutChallenges&&(null==(o=x.closeOutChallenges)?void 0:o.map((e,s)=>(0,l.jsx)("div",{className:"card border-0 mb-3",style:{backgroundColor:"#fff7ed"},children:(0,l.jsxs)("div",{className:"card-body p-3",children:[(0,l.jsxs)("div",{className:"mb-3",children:[(0,l.jsxs)("h6",{className:"fw-medium mb-2",children:["Challenge ",s+1]}),(0,l.jsx)("p",{className:"mb-0 text-muted",children:e.unexpectedChallenges})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h6",{className:"fw-medium mb-2",children:"Resolution/Remarks"}),(0,l.jsx)("p",{className:"mb-0 text-muted",children:e.remarks})]})]})},s)))]})]})]})},p=()=>{let e=(0,c.useRouter)(),[s,a]=(0,i.useState)("submitted"),[t,f]=(0,i.useState)([]),[b,h]=(0,i.useState)(!1),[p,u]=(0,i.useState)(null),[g,N]=(0,i.useState)(null),[v,y]=(0,i.useState)(!1),[w,k]=(0,i.useState)([]),[C,S]=(0,i.useState)(""),z=async()=>{h(!0),u(null);try{let e={include:[{relation:"locationOne"},{relation:"locationTwo"},{relation:"locationThree"},{relation:"locationFour"},{relation:"locationFive"},{relation:"locationSix"},{relation:"locationSix"},{relation:"toolboxSignStatuses",scope:{include:[{relation:"signedBy"}]}},{relation:"conductedBy"},{relation:"riskAssessment",scope:{include:[{relation:"workActivity"}]}}]},s="".concat(d.pZ,"?filter=").concat(encodeURIComponent(JSON.stringify(e))),a=await n.A.get(s);200===a.status&&f(a.data.reverse())}catch(e){u("Failed to fetch archived data."),console.error("Error fetching archived data:",e)}finally{h(!1)}};(0,i.useEffect)(()=>{"submitted"===s&&(z(),A())},[s]);let A=async()=>{k((await n.A.get(d.Jo)).data)};(0,i.useEffect)(()=>{D()},[]);let D=async()=>{try{let e=(await n.A.get((0,d._i)(localStorage.getItem("logo")),{headers:{"Content-Type":"application/json"}})).data;S(e)}catch(e){console.error("Error fetching logo:",e)}},T=e=>{N(e),y(!0)};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{style:{backgroundColor:"#f8fafc",minHeight:"100vh"},className:"jsx-72e4c4022f0a3433 page-content-wrapper",children:(0,l.jsxs)("div",{className:"jsx-72e4c4022f0a3433 container-fluid px-3 py-4",children:[(0,l.jsxs)("button",{style:{borderRadius:"12px",fontSize:"16px",fontWeight:"600",border:"none",boxShadow:"0 2px 8px rgba(220, 38, 38, 0.2)"},onClick:()=>e.push("/tbt/new"),className:"jsx-72e4c4022f0a3433 btn btn-danger w-100 py-3 mb-4 d-flex align-items-center justify-content-between",children:[(0,l.jsxs)("div",{className:"jsx-72e4c4022f0a3433 d-flex align-items-center",children:[(0,l.jsx)("div",{style:{width:"40px",height:"40px"},className:"jsx-72e4c4022f0a3433 bg-white bg-opacity-20 rounded-circle p-2 me-3 d-flex align-items-center justify-content-center",children:(0,l.jsx)("span",{style:{fontSize:"18px"},className:"jsx-72e4c4022f0a3433 text-white fw-bold",children:"\uD83D\uDCE2"})}),(0,l.jsx)("span",{className:"jsx-72e4c4022f0a3433",children:"Conduct Toolbox Talk"})]}),(0,l.jsx)("span",{style:{fontSize:"18px"},className:"jsx-72e4c4022f0a3433 text-white fw-bold",children:"→"})]}),(0,l.jsx)("div",{className:"jsx-72e4c4022f0a3433 card border-0 shadow-sm mb-4",children:(0,l.jsx)("div",{className:"jsx-72e4c4022f0a3433 card-body p-3",children:(0,l.jsx)("div",{className:"jsx-72e4c4022f0a3433 d-flex gap-2",children:[{id:"submitted",label:"Submitted Talks",icon:"✅",count:t.length}].map(e=>(0,l.jsxs)("button",{style:{border:"none",fontWeight:"600",fontSize:"14px",transition:"all 0.3s ease"},onClick:()=>a(e.id),className:"jsx-72e4c4022f0a3433 "+"btn flex-fill py-3 rounded-3 d-flex align-items-center justify-content-center gap-2 ".concat(s===e.id?"btn-danger text-white shadow-sm":"btn-light text-muted"),children:[(0,l.jsx)("span",{style:{fontSize:"16px"},className:"jsx-72e4c4022f0a3433",children:e.icon}),(0,l.jsx)("span",{className:"jsx-72e4c4022f0a3433",children:e.label}),e.count>0&&(0,l.jsx)("span",{style:{fontSize:"10px"},className:"jsx-72e4c4022f0a3433 "+"badge ".concat(s===e.id?"bg-white text-danger":"bg-danger text-white"," ms-1"),children:e.count})]},e.id))})})}),(0,l.jsx)("div",{className:"jsx-72e4c4022f0a3433",children:"submitted"===s&&(0,l.jsxs)("div",{className:"jsx-72e4c4022f0a3433",children:[(0,l.jsx)("div",{className:"jsx-72e4c4022f0a3433 card border-0 shadow-sm mb-4",children:(0,l.jsx)("div",{className:"jsx-72e4c4022f0a3433 card-body py-3",children:(0,l.jsxs)("div",{className:"jsx-72e4c4022f0a3433 d-flex justify-content-between align-items-center",children:[(0,l.jsxs)("div",{className:"jsx-72e4c4022f0a3433 d-flex align-items-center gap-3",children:[(0,l.jsx)("div",{style:{backgroundColor:"#fef2f2",width:"40px",height:"40px"},className:"jsx-72e4c4022f0a3433 p-2 rounded-circle d-flex align-items-center justify-content-center",children:(0,l.jsx)("span",{style:{fontSize:"20px"},className:"jsx-72e4c4022f0a3433",children:"\uD83D\uDCE2"})}),(0,l.jsxs)("div",{className:"jsx-72e4c4022f0a3433",children:[(0,l.jsx)("h6",{style:{color:"#1f2937",fontWeight:"600"},className:"jsx-72e4c4022f0a3433 mb-0",children:"Submitted Toolbox Talks"}),(0,l.jsx)("small",{className:"jsx-72e4c4022f0a3433 text-muted",children:"Safety briefings and team communications"})]})]}),(0,l.jsx)("div",{className:"jsx-72e4c4022f0a3433 text-end",children:(0,l.jsxs)("span",{style:{fontSize:"12px"},className:"jsx-72e4c4022f0a3433 badge bg-danger px-3 py-2",children:[t.length," talks"]})})]})})}),(0,l.jsxs)("div",{style:{maxHeight:"calc(100vh - 400px)",overflowY:"auto"},className:"jsx-72e4c4022f0a3433 custom-scrollbar",children:[b&&(0,l.jsx)("div",{className:"jsx-72e4c4022f0a3433 text-center py-5",children:(0,l.jsxs)("div",{className:"jsx-72e4c4022f0a3433 d-flex flex-column align-items-center",children:[(0,l.jsx)("div",{role:"status",className:"jsx-72e4c4022f0a3433 spinner-border text-danger mb-3",children:(0,l.jsx)("span",{className:"jsx-72e4c4022f0a3433 visually-hidden",children:"Loading..."})}),(0,l.jsx)("p",{className:"jsx-72e4c4022f0a3433 text-muted mb-0",children:"Loading toolbox talks..."})]})}),p&&(0,l.jsx)("div",{role:"alert",className:"jsx-72e4c4022f0a3433 alert alert-danger border-0 shadow-sm",children:(0,l.jsxs)("div",{className:"jsx-72e4c4022f0a3433 d-flex align-items-center",children:[(0,l.jsx)("i",{className:"jsx-72e4c4022f0a3433 bi bi-exclamation-triangle-fill me-2"}),p]})}),t.length>0?(0,l.jsx)("div",{className:"jsx-72e4c4022f0a3433 row g-3",children:t.map((e,s)=>{var a,t;return(0,l.jsx)("div",{className:"jsx-72e4c4022f0a3433 col-12",children:(0,l.jsx)("div",{style:{cursor:"pointer",transition:"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",boxShadow:"0 4px 12px rgba(0,0,0,0.08)",borderRadius:"16px",backgroundColor:"#FFFFFF",borderLeft:"4px solid #dc2626"},onClick:()=>T(e),onMouseEnter:e=>{e.currentTarget.style.transform="translateY(-4px)",e.currentTarget.style.boxShadow="0 12px 32px rgba(220, 38, 38, 0.15)"},onMouseLeave:e=>{e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0,0,0,0.08)"},className:"jsx-72e4c4022f0a3433 card border-0 h-100",children:(0,l.jsxs)("div",{className:"jsx-72e4c4022f0a3433 card-body p-4",children:[(0,l.jsxs)("div",{className:"jsx-72e4c4022f0a3433 d-flex justify-content-between align-items-start mb-3",children:[(0,l.jsxs)("div",{className:"jsx-72e4c4022f0a3433 d-flex align-items-center gap-2",children:[(0,l.jsx)("div",{style:{backgroundColor:"#fef2f2",width:"32px",height:"32px"},className:"jsx-72e4c4022f0a3433 p-2 rounded-circle d-flex align-items-center justify-content-center",children:(0,l.jsx)("span",{style:{fontSize:"14px"},className:"jsx-72e4c4022f0a3433",children:"\uD83D\uDCE2"})}),(0,l.jsxs)("span",{style:{fontSize:"13px"},className:"jsx-72e4c4022f0a3433 text-muted fw-medium",children:["#",e.maskId]})]}),(0,l.jsx)("span",{style:{fontSize:"11px",fontWeight:"600"},className:"jsx-72e4c4022f0a3433 "+"badge px-3 py-1 ".concat("Published"===e.status?"bg-success":"Pending"===e.status?"bg-warning text-dark":"Submitted"===e.status?"bg-info":"bg-secondary"),children:e.status})]}),(0,l.jsx)("h6",{style:{color:"#1f2937",lineHeight:"1.4",fontSize:"15px"},className:"jsx-72e4c4022f0a3433 mb-3 fw-semibold",children:(null==(t=e.riskAssessment)||null==(a=t.workActivity)?void 0:a.name)||"Toolbox Talk Session"}),(0,l.jsxs)("div",{className:"jsx-72e4c4022f0a3433 d-flex justify-content-between align-items-center",children:[(0,l.jsxs)("div",{className:"jsx-72e4c4022f0a3433 d-flex align-items-center gap-1 text-muted",children:[(0,l.jsx)("span",{style:{fontSize:"12px"},className:"jsx-72e4c4022f0a3433",children:"\uD83D\uDCC5"}),(0,l.jsx)("small",{style:{fontSize:"12px"},className:"jsx-72e4c4022f0a3433",children:new Date(e.created).toLocaleDateString()})]}),(0,l.jsx)("div",{className:"jsx-72e4c4022f0a3433 d-flex align-items-center gap-2",children:(0,l.jsxs)("span",{style:{fontSize:"10px",fontWeight:"600"},className:"jsx-72e4c4022f0a3433 badge bg-danger text-white px-2 py-1",children:["\uD83D\uDC65 ",e.noOfPersonsParticipated||0," participants"]})})]})]})})},s)})}):!b&&(0,l.jsx)("div",{className:"jsx-72e4c4022f0a3433 text-center py-5",children:(0,l.jsxs)("div",{className:"jsx-72e4c4022f0a3433 d-flex flex-column align-items-center",children:[(0,l.jsx)("div",{style:{backgroundColor:"#f3f4f6",width:"80px",height:"80px"},className:"jsx-72e4c4022f0a3433 p-4 rounded-circle mb-3 d-flex align-items-center justify-content-center",children:(0,l.jsx)("span",{style:{fontSize:"40px"},className:"jsx-72e4c4022f0a3433",children:"\uD83D\uDCE2"})}),(0,l.jsx)("h6",{className:"jsx-72e4c4022f0a3433 text-muted mb-2",children:"No Toolbox Talks Found"}),(0,l.jsx)("p",{className:"jsx-72e4c4022f0a3433 text-muted small mb-0",children:"No submitted toolbox talks available at the moment."})]})})]})]})})]})}),(0,l.jsxs)(o.A,{show:v,onHide:()=>y(!1),centered:!0,size:"lg",children:[(0,l.jsx)(o.A.Header,{closeButton:!0,className:"border-0 pb-0",children:g&&(0,l.jsx)("div",{className:"jsx-72e4c4022f0a3433 w-100",children:(0,l.jsx)("div",{className:"jsx-72e4c4022f0a3433 d-flex align-items-center justify-content-between",children:(0,l.jsxs)("div",{className:"jsx-72e4c4022f0a3433 d-flex align-items-center gap-3",children:[(0,l.jsx)(x.A,{logo:C}),(0,l.jsxs)("div",{className:"jsx-72e4c4022f0a3433",children:[(0,l.jsx)("h5",{className:"jsx-72e4c4022f0a3433 mb-1 fw-semibold",children:"Toolbox Talk Details"}),(0,l.jsxs)("div",{className:"jsx-72e4c4022f0a3433 d-flex align-items-center gap-2",children:[(0,l.jsxs)("span",{className:"jsx-72e4c4022f0a3433 text-muted small",children:["#",g.maskId||""]}),(0,l.jsx)("span",{style:{fontSize:"11px"},className:"jsx-72e4c4022f0a3433 "+"badge px-2 py-1 ".concat("Published"===g.status?"bg-success":"Pending"===g.status?"bg-warning text-dark":"Submitted"===g.status?"bg-info":"bg-secondary"),children:g.status})]})]})]})})})}),(0,l.jsx)(o.A.Body,{className:"pt-2",children:(0,l.jsx)(j,{formData:g})}),(0,l.jsx)(o.A.Footer,{className:"border-0 pt-0",children:(0,l.jsx)(m.A,{variant:"outline-secondary",onClick:()=>y(!1),className:"px-4",children:"✕ Close"})})]}),(0,l.jsx)(r(),{id:"72e4c4022f0a3433",children:".observation-report.jsx-72e4c4022f0a3433{background:#fff;-webkit-border-radius:10px;-moz-border-radius:10px;border-radius:10px;padding:20px}.section-title.jsx-72e4c4022f0a3433{font-size:1.1rem;font-weight:bold;color:#333}.obs-title.jsx-72e4c4022f0a3433{font-size:.9rem;font-weight:bold;color:#555;margin-bottom:5px}.obs-content.jsx-72e4c4022f0a3433{font-size:.9rem;color:#777}.image-box.jsx-72e4c4022f0a3433{border:1px solid#ddd;background:#f8f9fa;height:100px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.custom-scrollbar.jsx-72e4c4022f0a3433{scrollbar-width:thin;scrollbar-color:#cbd5e1#f1f5f9}.custom-scrollbar.jsx-72e4c4022f0a3433::-webkit-scrollbar{width:6px}.custom-scrollbar.jsx-72e4c4022f0a3433::-webkit-scrollbar-track{background:#f1f5f9;-webkit-border-radius:3px;-moz-border-radius:3px;border-radius:3px}.custom-scrollbar.jsx-72e4c4022f0a3433::-webkit-scrollbar-thumb{background:#cbd5e1;-webkit-border-radius:3px;-moz-border-radius:3px;border-radius:3px}.custom-scrollbar.jsx-72e4c4022f0a3433::-webkit-scrollbar-thumb:hover{background:#94a3b8}@media(max-width:768px){.card-body.jsx-72e4c4022f0a3433{padding:1rem!important}}@-webkit-keyframes pulse{0%{-webkit-transform:scale(1);transform:scale(1);opacity:.3}50%{-webkit-transform:scale(1.1);transform:scale(1.1);opacity:.1}100%{-webkit-transform:scale(1.2);transform:scale(1.2);opacity:0}}@-moz-keyframes pulse{0%{-moz-transform:scale(1);transform:scale(1);opacity:.3}50%{-moz-transform:scale(1.1);transform:scale(1.1);opacity:.1}100%{-moz-transform:scale(1.2);transform:scale(1.2);opacity:0}}@-o-keyframes pulse{0%{-o-transform:scale(1);transform:scale(1);opacity:.3}50%{-o-transform:scale(1.1);transform:scale(1.1);opacity:.1}100%{-o-transform:scale(1.2);transform:scale(1.2);opacity:0}}@keyframes pulse{0%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);opacity:.3}50%{-webkit-transform:scale(1.1);-moz-transform:scale(1.1);-o-transform:scale(1.1);transform:scale(1.1);opacity:.1}100%{-webkit-transform:scale(1.2);-moz-transform:scale(1.2);-o-transform:scale(1.2);transform:scale(1.2);opacity:0}}"})]})}},43864:(e,s,a)=>{"use strict";a.d(s,{A:()=>b});var l=a(95155),t=a(12115),r=a(26957),i=a(38336);let c=async e=>{try{return(await i.A.get((0,r._i)(e),{headers:{"Content-Type":"application/json"}})).data}catch(e){return console.error("Failed to fetch image URL:",e),null}},n=async e=>{try{let s=(await i.A.post(r.J9,{presignedUrl:e},{responseType:"blob"})).data;return new Promise((e,a)=>{let l=new FileReader;l.onloadend=()=>e(l.result),l.onerror=a,l.readAsDataURL(s)})}catch(e){throw console.error("Error fetching Data URL:",e),e}};var d=a(11518),o=a.n(d),m=a(36209);a(58561);var x=a(4178);let f=e=>{let{imageSrc:s}=e,[a,r]=(0,t.useState)(!1);return(0,l.jsxs)("div",{className:"jsx-15b99a83659358da container",children:[(0,l.jsx)("div",{className:"jsx-15b99a83659358da card",children:(0,l.jsxs)("div",{className:"jsx-15b99a83659358da body-blue text-center",children:[(0,l.jsx)("img",{src:s,alt:"Displayed",onClick:e=>{e.preventDefault(),e.stopPropagation(),r(!0)},style:{cursor:"pointer"},className:"jsx-15b99a83659358da display-image"}),a&&(0,l.jsx)(m.Ay,{open:a,close:()=>r(!1),slides:[{src:s}],plugins:[x.A],carousel:{finite:!0}})]})}),(0,l.jsx)(o(),{id:"15b99a83659358da",children:".display-image.jsx-15b99a83659358da{max-width:80px;max-height:80px;width:auto;height:auto;-o-object-fit:cover;object-fit:cover;cursor:pointer!important;-webkit-transition:-webkit-transform.3s ease-in-out;-moz-transition:-moz-transform.3s ease-in-out;-o-transition:-o-transform.3s ease-in-out;transition:-webkit-transform.3s ease-in-out;transition:-moz-transform.3s ease-in-out;transition:-o-transform.3s ease-in-out;transition:transform.3s ease-in-out;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;border:1px solid#dee2e6;pointer-events:auto;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.display-image.jsx-15b99a83659358da:hover{-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-ms-transform:scale(1.05);-o-transform:scale(1.05);transform:scale(1.05);-webkit-box-shadow:0 2px 8px rgba(0,0,0,.15);-moz-box-shadow:0 2px 8px rgba(0,0,0,.15);box-shadow:0 2px 8px rgba(0,0,0,.15)}.container.jsx-15b99a83659358da{padding:0;margin:0;max-width:none;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.card.jsx-15b99a83659358da{border:none;background:none;margin:0;padding:0;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.body-blue.jsx-15b99a83659358da{padding:0;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}"})]})},b=e=>{let{fileName:s,size:a=100,name:r=!1}=e,[i,d]=(0,t.useState)(null),[o,m]=(0,t.useState)(null);if((0,t.useEffect)(()=>{(async()=>{try{var e;let a=await c(s);d(a);let l=null==(e=s.split(".").pop())?void 0:e.toLowerCase();if(l&&["jpg","jpeg","png","gif","bmp","webp"].includes(l)){let e=await n(a);m(e)}}catch(e){console.error("Error fetching file or data URL:",e)}})()},[s]),!i)return(0,l.jsx)("p",{children:"Loading..."});let x=(e=>{var s;let a=null==(s=e.split(".").pop())?void 0:s.toLowerCase();return a?["jpg","jpeg","png","gif","bmp","webp"].includes(a)?"image":["pdf"].includes(a)?"pdf":["xls","xlsx"].includes(a)?"xls":"other":"other"})(s),b=s.replace(/^\d+[\s-_]*/,"");switch(x){case"image":return(0,l.jsxs)("div",{className:"d-flex flex-column align-items-center",style:{padding:"4px"},children:[o?(0,l.jsx)(f,{imageSrc:o}):(0,l.jsx)("div",{className:"d-flex align-items-center justify-content-center bg-light border rounded",style:{width:a,height:a},children:(0,l.jsx)("div",{className:"spinner-border spinner-border-sm text-primary",role:"status",children:(0,l.jsx)("span",{className:"visually-hidden",children:"Loading..."})})}),r&&(0,l.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center",fontSize:"10px",margin:"2px 0 0 0"},children:b})]});case"pdf":return(0,l.jsxs)("div",{className:"d-flex flex-column align-items-center p-2",children:[(0,l.jsx)("a",{href:i,target:"_blank",rel:"noopener noreferrer",children:(0,l.jsx)("i",{className:"bi bi-file-pdf-fill fs-1 text-danger"})}),r&&(0,l.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center"},children:b})]});case"xls":return(0,l.jsxs)("div",{className:"d-flex flex-column align-items-center p-2",children:[(0,l.jsx)("a",{href:i,target:"_blank",rel:"noopener noreferrer",children:(0,l.jsx)("i",{className:"bi bi-file-excel-fill fs-1 text-success"})}),r&&(0,l.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center"},children:b})]});default:return(0,l.jsxs)("div",{className:"d-flex flex-column align-items-center p-2",children:[(0,l.jsx)("a",{href:i,target:"_blank",rel:"noopener noreferrer",children:(0,l.jsx)("i",{className:"bi bi-file-earmark-fill fs-1 text-secondary"})}),r&&(0,l.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center"},children:b})]})}}},46554:(e,s,a)=>{"use strict";a.d(s,{default:()=>o});var l=a(95155),t=a(12115),r=a(35695),i=a(38336),c=a(26957),n=a(34540),d=a(81359);let o=e=>{let{heading:s}=e,a=(0,r.useRouter)(),[o,m]=(0,t.useState)(""),x=(0,n.wA)();t.useEffect(()=>{f()},[]);let f=async()=>{try{let e=await i.A.get(c.AM);200===e.status?(m(e.data.firstName),x(d.l.setUser(e.data))):a.push("/")}catch(e){console.log(e)}};return(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{className:"header-area",id:"headerArea",children:(0,l.jsx)("div",{className:"container",children:(0,l.jsxs)("div",{className:"header-content header-style-four position-relative d-flex align-items-center justify-content-between",children:[(0,l.jsx)("div",{className:"back-button",children:(0,l.jsx)("button",{onClick:()=>a.back(),className:"border-0 bg-transparent p-0",style:{cursor:"pointer"},children:(0,l.jsx)("i",{className:"bi bi-arrow-left-short fs-3"})})}),(0,l.jsx)("div",{className:"page-heading",children:(0,l.jsx)("h6",{className:"mb-0",children:s})}),(0,l.jsx)("div",{className:"user-profile-wrapper"})]})})})})}},52702:(e,s,a)=>{"use strict";a.d(s,{A:()=>c});var l=a(95155),t=a(12115),r=a(38336),i=a(26957);let c=e=>{let{logo:s}=e,[a,c]=(0,t.useState)("");return(0,t.useEffect)(()=>{let e=async()=>{try{let e=await r.A.post(i.J9,{presignedUrl:s},{responseType:"blob"}),a=new FileReader;a.onloadend=()=>{"string"==typeof a.result&&c(a.result)},a.readAsDataURL(e.data)}catch(e){console.error("Error fetching logo blob:",e)}};s&&e()},[s]),(0,l.jsx)("img",{src:a||"/default-logo.png",alt:"Logo",style:{maxWidth:"125px",height:"auto"}})}},76144:(e,s,a)=>{Promise.resolve().then(a.bind(a,90371)),Promise.resolve().then(a.bind(a,2383)),Promise.resolve().then(a.bind(a,46554))},90371:(e,s,a)=>{"use strict";a.d(s,{default:()=>c});var l=a(95155),t=a(12115),r=a(79459),i=a(36651);let c=e=>{let{children:s,pageTitle:a="Page",requiresOnline:c=!1,customFallback:n,className:d=""}=e,[o,m]=(0,t.useState)(!0);if((0,t.useEffect)(()=>{m(navigator.onLine);let e=()=>m(!0),s=()=>m(!1);return window.addEventListener("online",e),window.addEventListener("offline",s),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}},[]),c&&!o){let e=n||(0,l.jsx)(i.A,{title:"".concat(a," Unavailable Offline"),message:"".concat(a," requires an internet connection. Please check your connection and try again."),showHomeLink:!0});return(0,l.jsx)("div",{className:d,children:e})}return(0,l.jsx)(r.A,{className:d,showOfflineIndicator:!0,children:s})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3496,586,6874,6078,635,4816,1205,5898,381,1434,8441,1684,7358],()=>s(76144)),_N_E=e.O()}]);