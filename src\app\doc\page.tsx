"use client";

import HeaderSeven from "@/layouts/headers/HeaderSeven";
import React, { useState, useEffect } from "react";
import API from "@/services/API";
import { ADMINDROPDOWNS, API_URL } from "@/constant";
import { useRouter } from "next/navigation";

const MY_DOCUMENTS_API = `${API_URL}/my-documents`;

interface CategoryItem {
    id: string;
    name: string;
    level: number;
    created: string;
    updated: string;
    dropdownId: string;
}

interface CategoryOption {
    label: string;
    value: string;
}

interface Document {
    id: string;
    name: string;
    type: string;
    size: string;
    uploadedBy: string;
    uploadedDate: string;
    category: string;
    tags: string[];
    description: string;
    maskId?: string;
    scopeApplicability?: string;
    purpose?: string;
    keywords?: string;
    docId?: string;
    created: string;
    updated: string;
    creatorTargetDate?: string;
    reviewerTargetDate?: string;
    approverTargetDate?: string;
    initiatorId?: string;
    creatorId?: string;
    reviewerId?: string;
    approverId?: string;
    documentCategoryId?: string;
    initiator?: any;
    creator?: any;
    reviewer?: any;
    approver?: any;
    documentCategory?: any;
    files?: any[];
    status: string;
    value?: any;
}

const DocumentManagement: React.FC = () => {
    const router = useRouter();
    const [categoryOptions, setCategoryOptions] = useState<CategoryOption[]>([]);
    const [categorizedDocuments, setCategorizedDocuments] = useState<{[key: string]: Document[]}>({});
    const [loading, setLoading] = useState<boolean>(true);
    const [documentsLoading, setDocumentsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string>("");
    
    useEffect(() => {
        fetchDocumentCategories();
        fetchMyDocuments();
    }, []);

    const handleCategoryClick = (categoryId: string, categoryName?: string) => {
        localStorage.setItem('selectedCategoryId', categoryId);
        localStorage.setItem('selectedCategoryName', categoryName || 'Category');
        router.push(`/doc/category?categoryId=${categoryId}`);
    };

    const fetchDocumentCategories = async () => {
        try {
            setLoading(true);
            setError("");

            const maskId = 'doc_category';
            const uriString = {
                where: { maskId },
                include: [{ relation: "dropdownItems" }],
            };
            const url = `${ADMINDROPDOWNS}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
            const response = await API.get(url);

            if (response.status === 200 && response.data && response.data.length > 0) {
                const data = response.data[0]?.dropdownItems.map((item: CategoryItem) => ({
                    label: item.name,
                    value: item.id,
                })) || [];
                setCategoryOptions(data);
            } else {
                setError("No categories found");
            }
        } catch (err) {
            console.error('Error fetching document categories:', err);
            setError("Failed to load categories");
        } finally {
            setLoading(false);
        }
    };

    const fetchMyDocuments = async () => {
        try {
            setDocumentsLoading(true);

            const uriString = {
                include: [
                    { relation: "creator" },
                    { relation: "documentCategory" },
                    { relation: "reviewer" },
                    { relation: "approver" },
                    { relation: "initiator" }
                ]
            };

            const url = `${MY_DOCUMENTS_API}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
            const response = await API.get(url);

            console.log('My documents response:', response);

            // Transform API response to match our Document interface
            const transformedDocuments = response.data.map((doc: any) => ({
                id: doc.id,
                name: doc.name,
                type: doc.type,
                size: doc.size || 'Unknown',
                uploadedBy: doc.creator?.firstName || doc.uploadedBy || doc.createdBy || 'Not assigned',
                uploadedDate: doc.uploadedDate || doc.created || new Date().toISOString(),
                category: doc.documentCategory?.name || doc.category || 'Uncategorized',
                tags: doc.keywords ? doc.keywords.split(',').map((tag: string) => tag.trim()) : [],
                description: doc.purpose || doc.description || '',
                maskId: doc.maskId,
                scopeApplicability: doc.scopeApplicability,
                purpose: doc.purpose,
                keywords: doc.keywords,
                docId: doc.docId,
                created: doc.created,
                updated: doc.updated,
                creatorTargetDate: doc.creatorTargetDate,
                reviewerTargetDate: doc.reviewerTargetDate,
                approverTargetDate: doc.approverTargetDate,
                initiatorId: doc.initiatorId,
                creatorId: doc.creatorId,
                reviewerId: doc.reviewerId,
                approverId: doc.approverId,
                documentCategoryId: doc.documentCategoryId,
                initiator: doc.initiator,
                creator: doc.creator,
                reviewer: doc.reviewer,
                approver: doc.approver,
                documentCategory: doc.documentCategory,
                files: doc.files,
                status: doc.status || 'Draft',
                value: doc.value
            }));

            // Categorize documents by documentCategoryId
            const categorized = transformedDocuments.reduce((acc: {[key: string]: Document[]}, doc: Document) => {
                const categoryId = doc.documentCategoryId || 'uncategorized';

                if (!acc[categoryId]) {
                    acc[categoryId] = [];
                }
                acc[categoryId].push(doc);
                return acc;
            }, {});

            console.log('Categorized documents:', categorized);
            setCategorizedDocuments(categorized);

        } catch (err) {
            console.error('Error fetching my documents:', err);
        } finally {
            setDocumentsLoading(false);
        }
    };

    return (
        <>
            <HeaderSeven heading={'Document Management'} />

            <div className="page-content-wrapper py-3">
                <div className="container">
                    {/* Document Categories Section */}
                    <div className="card shadow-sm">
                        <div className="card-body">
                            <h5 className="card-title mb-3" style={{ color: '#0d6efd' }}>
                                <i className="bi bi-folder2-open me-2"></i>
                                Document Categories
                            </h5>

                            {(loading || documentsLoading) ? (
                                <div className="d-flex justify-content-center align-items-center py-5">
                                    <div className="spinner-border text-primary" role="status">
                                        <span className="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            ) : error ? (
                                <div className="alert alert-warning d-flex align-items-center" role="alert">
                                    <i className="bi bi-exclamation-triangle me-2"></i>
                                    {error}
                                </div>
                            ) : Object.keys(categorizedDocuments).length > 0 ? (
                                <div className="row g-4">
                                    {categoryOptions.map((category) => {
                                        const categoryDocs = categorizedDocuments[category.value] || [];

                                        if (categoryDocs.length === 0) return null;

                                        return (
                                            <div key={category.value} className="col-12">
                                                <div className="category-section mb-4">
                                                    {/* Category Header - Clickable */}
                                                    <div
                                                        className="d-flex align-items-center mb-3 p-3"
                                                        style={{
                                                            backgroundColor: '#f8f9fa',
                                                            borderRadius: '12px',
                                                            border: '1px solid #e9ecef',
                                                            cursor: 'pointer',
                                                            transition: 'all 0.2s ease'
                                                        }}
                                                        onClick={() => handleCategoryClick(category.value, category.label)}
                                                        onMouseEnter={(e) => {
                                                            e.currentTarget.style.backgroundColor = '#e9ecef';
                                                        }}
                                                        onMouseLeave={(e) => {
                                                            e.currentTarget.style.backgroundColor = '#f8f9fa';
                                                        }}
                                                    >
                                                        <div
                                                            className="category-icon me-3"
                                                            style={{
                                                                width: '48px',
                                                                height: '48px',
                                                                backgroundColor: '#e3f2fd',
                                                                borderRadius: '12px',
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                justifyContent: 'center'
                                                            }}
                                                        >
                                                            <i
                                                                className="bi bi-folder2"
                                                                style={{
                                                                    fontSize: '20px',
                                                                    color: '#0d6efd'
                                                                }}
                                                            ></i>
                                                        </div>
                                                        <div className="flex-grow-1">
                                                            <h5 className="mb-1" style={{ color: '#2c3e50', fontWeight: '600' }}>
                                                                {category.label}
                                                            </h5>
                                                            <p className="mb-0 text-muted" style={{ fontSize: '14px' }}>
                                                                {categoryDocs.length} document{categoryDocs.length !== 1 ? 's' : ''}
                                                            </p>
                                                        </div>
                                                        <div className="ms-2">
                                                            <i
                                                                className="bi bi-chevron-right"
                                                                style={{
                                                                    fontSize: '16px',
                                                                    color: '#6c757d'
                                                                }}
                                                            ></i>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        );
                                    })}

                                    {/* Uncategorized documents */}
                                    {categorizedDocuments['uncategorized'] && categorizedDocuments['uncategorized'].length > 0 && (
                                        <div className="col-12">
                                            <div className="category-section mb-4">
                                                {/* Category Header - Clickable */}
                                                <div
                                                    className="d-flex align-items-center mb-3 p-3"
                                                    style={{
                                                        backgroundColor: '#f8f9fa',
                                                        borderRadius: '12px',
                                                        border: '1px solid #e9ecef',
                                                        cursor: 'pointer',
                                                        transition: 'all 0.2s ease'
                                                    }}
                                                    onClick={() => handleCategoryClick('uncategorized', 'Uncategorized')}
                                                    onMouseEnter={(e) => {
                                                        e.currentTarget.style.backgroundColor = '#e9ecef';
                                                    }}
                                                    onMouseLeave={(e) => {
                                                        e.currentTarget.style.backgroundColor = '#f8f9fa';
                                                    }}
                                                >
                                                    <div
                                                        className="category-icon me-3"
                                                        style={{
                                                            width: '48px',
                                                            height: '48px',
                                                            backgroundColor: '#fff3cd',
                                                            borderRadius: '12px',
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center'
                                                        }}
                                                    >
                                                        <i
                                                            className="bi bi-question-circle"
                                                            style={{
                                                                fontSize: '20px',
                                                                color: '#856404'
                                                            }}
                                                        ></i>
                                                    </div>
                                                    <div className="flex-grow-1">
                                                        <h5 className="mb-1" style={{ color: '#2c3e50', fontWeight: '600' }}>
                                                            Uncategorized
                                                        </h5>
                                                        <p className="mb-0 text-muted" style={{ fontSize: '14px' }}>
                                                            {categorizedDocuments['uncategorized'].length} document{categorizedDocuments['uncategorized'].length !== 1 ? 's' : ''} without category
                                                        </p>
                                                    </div>
                                                    <div className="ms-2">
                                                        <i
                                                            className="bi bi-chevron-right"
                                                            style={{
                                                                fontSize: '16px',
                                                                color: '#6c757d'
                                                            }}
                                                        ></i>
                                                    </div>
                                                </div>


                                            </div>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div className="text-center py-5">
                                    <i className="bi bi-files" style={{ fontSize: '48px', color: '#6c757d' }}></i>
                                    <p className="mt-3 text-muted">No documents found</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default DocumentManagement;
