(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6367],{21217:(e,c,s)=>{"use strict";s.d(c,{default:()=>n});var l=s(95155),a=s(9e4),i=s(38808),t=s(12115);let h=e=>{let{handleShowSetting:c,showSetting:s}=e,{theme:t,handleDarkModeToggle:h}=(0,a.D)(),{viewMode:r,handleRTLToggling:d}=(0,i.L)();return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{id:"setting-popup-overlay",className:s?"active":"",onClick:c}),(0,l.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(s?"active":""),id:"settingCard",children:(0,l.jsx)("div",{className:"card-body",children:(0,l.jsxs)("div",{className:"container",children:[(0,l.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,l.jsx)("p",{className:"mb-0",children:"Settings"}),(0,l.jsx)("div",{onClick:c,className:"btn-close",id:"settingCardClose"})]}),(0,l.jsx)("div",{className:"single-setting-panel",children:(0,l.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,l.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,l.jsx)("div",{className:"single-setting-panel",children:(0,l.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,l.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,l.jsx)("div",{className:"single-setting-panel",children:(0,l.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,l.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===t,onChange:h}),(0,l.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===t?"Light":"Dark"," mode"]})]})}),(0,l.jsx)("div",{className:"single-setting-panel",children:(0,l.jsxs)("div",{className:"form-check form-switch",children:[(0,l.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===r,onChange:d}),(0,l.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===r?"LTR":"RTL"," mode"]})]})})]})})})]})};var r=s(6874),d=s.n(r);let n=e=>{let{links:c,title:s}=e,[a,i]=(0,t.useState)(!1),r=()=>i(!a);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"header-area",id:"headerArea",children:(0,l.jsx)("div",{className:"container",children:(0,l.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,l.jsx)("div",{className:"back-button",children:(0,l.jsx)(d(),{href:"/".concat(c),children:(0,l.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,l.jsx)("div",{className:"page-heading",children:(0,l.jsx)("h6",{className:"mb-0",children:s})}),(0,l.jsx)("div",{className:"setting-wrapper",onClick:r,children:(0,l.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,l.jsx)("i",{className:"bi bi-gear"}),(0,l.jsx)("span",{})]})})]})})}),(0,l.jsx)(h,{showSetting:a,handleShowSetting:r})]})}},38808:(e,c,s)=>{"use strict";s.d(c,{L:()=>a});var l=s(12115);let a=()=>{let[e,c]=(0,l.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,l.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let s=()=>{c(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:s,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let c=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(c),e.style.display="none")},20)},1e3))}s()}}}},38983:(e,c,s)=>{"use strict";s.d(c,{default:()=>h});var l=s(95155),a=s(6874),i=s.n(a);s(12115);let t=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],h=()=>(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,l.jsx)("div",{className:"container px-0",children:(0,l.jsx)("div",{className:"footer-nav position-relative",children:(0,l.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:t.map((e,c)=>(0,l.jsx)("li",{children:(0,l.jsxs)(i(),{href:"/".concat(e.link),children:[(0,l.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,l.jsx)("span",{children:e.title})]})},c))})})})})})},45577:(e,c,s)=>{"use strict";s.d(c,{default:()=>a});var l=s(95155);s(12115);let a=()=>(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("div",{className:"page-content-wrapper py-3",children:[(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"element-heading",children:(0,l.jsx)("h6",{children:"Checkbox"})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"card",children:(0,l.jsx)("div",{className:"card-body",children:(0,l.jsxs)("form",{onSubmit:e=>e.preventDefault(),children:[(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input",id:"defaultCheckbox",type:"checkbox",value:""}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"defaultCheckbox",children:"Default Checkbox"})]}),(0,l.jsx)("div",{className:"mb-2"}),(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input",id:"checkedCheckbox",type:"checkbox",value:"",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"checkedCheckbox",children:"Checked Checkbox"})]}),(0,l.jsx)("div",{className:"mb-2"}),(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input indeterminate",id:"indeterminateCheckbox",type:"checkbox",value:""}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"indeterminateCheckbox",children:"Indeterminate Checkbox"})]}),(0,l.jsx)("div",{className:"mb-2"}),(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input",id:"disabledCheckbox",type:"checkbox",value:"",disabled:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"disabledCheckbox",children:"Disabled Checkbox"})]}),(0,l.jsx)("div",{className:"mb-2"}),(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input",id:"disabledCheckbox2",type:"checkbox",value:"",defaultChecked:!0,disabled:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"disabledCheckbox2",children:"Disabled Checked Checkbox"})]})]})})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"element-heading mt-3",children:(0,l.jsx)("h6",{children:"Colorful Checkbox"})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"card",children:(0,l.jsx)("div",{className:"card-body",children:(0,l.jsxs)("form",{action:"#",method:"GET",children:[(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input form-check-success",id:"successCheckbox",type:"checkbox",value:"",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"successCheckbox",children:"Success Checkbox"})]}),(0,l.jsx)("div",{className:"mb-2"}),(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input form-check-danger",id:"dangerCheckbox",type:"checkbox",value:"",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"dangerCheckbox",children:"Danger Checkbox"})]}),(0,l.jsx)("div",{className:"mb-2"}),(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input form-check-warning",id:"warningCheckbox",type:"checkbox",value:"",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"warningCheckbox",children:"Warning Checkbox"})]}),(0,l.jsx)("div",{className:"mb-2"}),(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input form-check-info",id:"infoCheckbox",type:"checkbox",value:"",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"infoCheckbox",children:"Info Checkbox"})]}),(0,l.jsx)("div",{className:"mb-2"}),(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input form-check-secondary",id:"secondaryCheckbox",type:"checkbox",value:"",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"secondaryCheckbox",children:"Secondary Checkbox"})]}),(0,l.jsx)("div",{className:"mb-2"}),(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input form-check-light",id:"lightCheckbox",type:"checkbox",value:"",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"lightCheckbox",children:"Light Checkbox"})]}),(0,l.jsx)("div",{className:"mb-2"}),(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input form-check-dark",id:"darkCheckbox",type:"checkbox",value:"",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"darkCheckbox",children:"Dark Checkbox"})]})]})})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"element-heading mt-3",children:(0,l.jsx)("h6",{children:"Checkbox Large"})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"card",children:(0,l.jsxs)("div",{className:"card-body",children:[(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input form-check-input-lg form-check-success",id:"successCheckbox2",type:"checkbox",value:"",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"successCheckbox2",children:"Success Checkbox"})]}),(0,l.jsx)("div",{className:"mb-2"}),(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input form-check-input-lg form-check-danger",id:"dangerCheckbox2",type:"checkbox",value:"",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"dangerCheckbox2",children:"Danger Checkbox"})]}),(0,l.jsx)("div",{className:"mb-2"}),(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input form-check-input-lg form-check-warning",id:"warningCheckbox2",type:"checkbox",value:"",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"warningCheckbox2",children:"Warning Checkbox"})]}),(0,l.jsx)("div",{className:"mb-2"}),(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input form-check-input-lg form-check-info",id:"infoCheckbox2",type:"checkbox",value:"",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"infoCheckbox2",children:"Info Checkbox"})]}),(0,l.jsx)("div",{className:"mb-2"}),(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input form-check-input-lg form-check-secondary",id:"secondaryCheckbox2",type:"checkbox",value:"",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"secondaryCheckbox2",children:"Secondary Checkbox"})]}),(0,l.jsx)("div",{className:"mb-2"}),(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input form-check-input-lg form-check-light",id:"lightCheckbox2",type:"checkbox",value:"",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"lightCheckbox2",children:"Light Checkbox"})]}),(0,l.jsx)("div",{className:"mb-2"}),(0,l.jsxs)("div",{className:"form-check",children:[(0,l.jsx)("input",{className:"form-check-input form-check-input-lg form-check-dark",id:"darkCheckbox2",type:"checkbox",value:"",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"darkCheckbox2",children:"Dark Checkbox"})]})]})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"element-heading mt-3",children:(0,l.jsx)("h6",{children:"Checkbox Card"})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"card",children:(0,l.jsx)("div",{className:"card-body",children:(0,l.jsxs)("div",{className:"list-group",children:[(0,l.jsxs)("label",{className:"list-group-item",htmlFor:"listCheckbox1",children:[(0,l.jsx)("input",{className:"form-check-input me-2",id:"listCheckbox1",type:"checkbox",value:""}),"List Checkbox 1"]}),(0,l.jsxs)("label",{className:"list-group-item",htmlFor:"listCheckbox2",children:[(0,l.jsx)("input",{className:"form-check-input me-2",id:"listCheckbox2",type:"checkbox",value:"",defaultChecked:!0}),"List Checkbox 2"]}),(0,l.jsxs)("label",{className:"list-group-item",htmlFor:"listCheckbox3",children:[(0,l.jsx)("input",{className:"form-check-input me-2",id:"listCheckbox3",type:"checkbox",value:""}),"List Checkbox 3"]}),(0,l.jsxs)("label",{className:"list-group-item",htmlFor:"listCheckbox4",children:[(0,l.jsx)("input",{className:"form-check-input me-2",id:"listCheckbox4",type:"checkbox",value:""}),"List Checkbox 4"]})]})})})})]})})},53667:(e,c,s)=>{Promise.resolve().then(s.bind(s,45577)),Promise.resolve().then(s.bind(s,38983)),Promise.resolve().then(s.bind(s,21217))},9e4:(e,c,s)=>{"use strict";s.d(c,{D:()=>a});var l=s(12115);let a=()=>{let[e,c]=(0,l.useState)("light"),[s,a]=(0,l.useState)(!1);(0,l.useEffect)(()=>{c(localStorage.getItem("theme")||"light"),a(!0)},[]),(0,l.useEffect)(()=>{s&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,s]);let i=(0,l.useCallback)(()=>{c(e=>"dark"===e?"light":"dark")},[]),t=(0,l.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let c=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(c),e.style.display="none")},20)},1e3))}i()},[i]);return{theme:e,toggleTheme:i,handleDarkModeToggle:t}}}},e=>{var c=c=>e(e.s=c);e.O(0,[6874,8441,1684,7358],()=>c(53667)),_N_E=e.O()}]);