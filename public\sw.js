// Main PWA Service Worker
// Note: Firebase messaging uses its own service worker (firebase-messaging-sw.js)
// This service worker handles general PWA functionality

self.addEventListener("install", () => {
    console.log("✅ PWA Service Worker installing.");
    self.skipWaiting();
});

self.addEventListener("activate", (event) => {
    console.log("✅ PWA Service Worker activated.");
    event.waitUntil(self.clients.claim());
});

self.addEventListener("fetch", (event) => {
    // Only log important requests to avoid spam
    if (event.request.url.includes('/api/') || event.request.url.includes('.json')) {
        console.log("🌐 Service Worker fetching:", event.request.url);
    }
});
