// Firebase Messaging Service Worker
// This file is required by Firebase Cloud Messaging for background message handling

// Import Firebase scripts for service worker
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

// Firebase configuration (must match your main app configuration)
const firebaseConfig = {
  apiKey: "AIzaSyCvHAIHZPnzl9xMJu9NpPQ8J-iL8zKDh9k",
  authDomain: "pwa-superapp.firebaseapp.com",
  projectId: "pwa-superapp",
  storageBucket: "pwa-superapp.firebasestorage.app",
  messagingSenderId: "596753047002",
  appId: "1:596753047002:web:e809373bd1190e6a8bf4e3"
};

// Initialize Firebase in service worker
firebase.initializeApp(firebaseConfig);

// Initialize Firebase Messaging
const messaging = firebase.messaging();

// Handle background messages when the app is not in focus
messaging.onBackgroundMessage((payload) => {
  console.log('📱 Background message received in firebase-messaging-sw.js:', payload);
  
  const { notification, data } = payload;
  
  // Customize notification
  const notificationTitle = notification?.title || 'New Notification';
  const notificationOptions = {
    body: notification?.body || '',
    icon: notification?.icon || '/assets/icons/Icon-192.png',
    badge: '/assets/icons/Icon-72.png',
    tag: data?.tag || 'default',
    data: data || {},
    requireInteraction: true,
    actions: [
      {
        action: 'view',
        title: 'View',
        icon: '/assets/icons/Icon-72.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss'
      }
    ]
  };

  // Show notification
  self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  console.log('🔔 Notification clicked in firebase-messaging-sw.js:', event);
  
  event.notification.close();
  
  const action = event.action;
  const data = event.notification.data;
  
  if (action === 'dismiss') {
    return;
  }
  
  // Handle notification click - open the app
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      // Check if app is already open
      for (const client of clientList) {
        if (client.url.includes(self.location.origin) && 'focus' in client) {
          client.focus();
          
          // Send message to client about notification click
          client.postMessage({
            type: 'NOTIFICATION_CLICKED',
            data: data
          });
          
          return;
        }
      }
      
      // If app is not open, open it
      if (clients.openWindow) {
        const urlToOpen = data?.url || '/notifications';
        clients.openWindow(self.location.origin + urlToOpen);
      }
    })
  );
});

// Handle service worker installation
self.addEventListener('install', (event) => {
  console.log('✅ Firebase messaging service worker installing');
  self.skipWaiting();
});

// Handle service worker activation
self.addEventListener('activate', (event) => {
  console.log('✅ Firebase messaging service worker activated');
  event.waitUntil(self.clients.claim());
});

console.log('✅ Firebase messaging service worker loaded successfully');
