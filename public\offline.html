<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - AcuiZen</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 20px;
        }
        
        .offline-container {
            max-width: 400px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .offline-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.8;
        }
        
        h1 {
            font-size: 28px;
            margin-bottom: 16px;
            font-weight: 600;
        }
        
        p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .features {
            margin-top: 30px;
            text-align: left;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;
            opacity: 0.8;
        }
        
        .feature-icon {
            margin-right: 10px;
            font-size: 16px;
        }
        
        @media (max-width: 480px) {
            .offline-container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            .offline-icon {
                font-size: 48px;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📱</div>
        <h1>You're Offline</h1>
        <p>Don't worry! You can still access some features while offline. Your data will sync when you're back online.</p>
        
        <button class="retry-btn" onclick="window.location.reload()">
            Try Again
        </button>
        
        <div class="features">
            <div class="feature">
                <span class="feature-icon">✅</span>
                <span>View cached services</span>
            </div>
            <div class="feature">
                <span class="feature-icon">✅</span>
                <span>Access saved actions</span>
            </div>
            <div class="feature">
                <span class="feature-icon">✅</span>
                <span>Browse offline content</span>
            </div>
            <div class="feature">
                <span class="feature-icon">🔄</span>
                <span>Auto-sync when online</span>
            </div>
        </div>
    </div>

    <script>
        // Check for online status and auto-reload
        window.addEventListener('online', () => {
            console.log('🌐 Back online! Reloading...');
            window.location.reload();
        });
        
        // Show connection status
        if (navigator.onLine) {
            console.log('🌐 Online - redirecting to app');
            window.location.href = '/';
        }
    </script>
</body>
</html>
