(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2173],{21217:(e,a,s)=>{"use strict";s.d(a,{default:()=>r});var l=s(95155),t=s(9e4),i=s(38808),d=s(12115);let c=e=>{let{handleShowSetting:a,showSetting:s}=e,{theme:d,handleDarkModeToggle:c}=(0,t.D)(),{viewMode:n,handleRTLToggling:o}=(0,i.L)();return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{id:"setting-popup-overlay",className:s?"active":"",onClick:a}),(0,l.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(s?"active":""),id:"settingCard",children:(0,l.jsx)("div",{className:"card-body",children:(0,l.jsxs)("div",{className:"container",children:[(0,l.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,l.jsx)("p",{className:"mb-0",children:"Settings"}),(0,l.jsx)("div",{onClick:a,className:"btn-close",id:"settingCardClose"})]}),(0,l.jsx)("div",{className:"single-setting-panel",children:(0,l.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,l.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,l.jsx)("div",{className:"single-setting-panel",children:(0,l.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,l.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,l.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,l.jsx)("div",{className:"single-setting-panel",children:(0,l.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,l.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===d,onChange:c}),(0,l.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===d?"Light":"Dark"," mode"]})]})}),(0,l.jsx)("div",{className:"single-setting-panel",children:(0,l.jsxs)("div",{className:"form-check form-switch",children:[(0,l.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===n,onChange:o}),(0,l.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===n?"LTR":"RTL"," mode"]})]})})]})})})]})};var n=s(6874),o=s.n(n);let r=e=>{let{links:a,title:s}=e,[t,i]=(0,d.useState)(!1),n=()=>i(!t);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"header-area",id:"headerArea",children:(0,l.jsx)("div",{className:"container",children:(0,l.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,l.jsx)("div",{className:"back-button",children:(0,l.jsx)(o(),{href:"/".concat(a),children:(0,l.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,l.jsx)("div",{className:"page-heading",children:(0,l.jsx)("h6",{className:"mb-0",children:s})}),(0,l.jsx)("div",{className:"setting-wrapper",onClick:n,children:(0,l.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,l.jsx)("i",{className:"bi bi-gear"}),(0,l.jsx)("span",{})]})})]})})}),(0,l.jsx)(c,{showSetting:t,handleShowSetting:n})]})}},32516:(e,a,s)=>{Promise.resolve().then(s.bind(s,90662)),Promise.resolve().then(s.bind(s,38983)),Promise.resolve().then(s.bind(s,21217))},38808:(e,a,s)=>{"use strict";s.d(a,{L:()=>t});var l=s(12115);let t=()=>{let[e,a]=(0,l.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,l.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let s=()=>{a(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:s,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let a=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(a),e.style.display="none")},20)},1e3))}s()}}}},38983:(e,a,s)=>{"use strict";s.d(a,{default:()=>c});var l=s(95155),t=s(6874),i=s.n(t);s(12115);let d=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],c=()=>(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,l.jsx)("div",{className:"container px-0",children:(0,l.jsx)("div",{className:"footer-nav position-relative",children:(0,l.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:d.map((e,a)=>(0,l.jsx)("li",{children:(0,l.jsxs)(i(),{href:"/".concat(e.link),children:[(0,l.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,l.jsx)("span",{children:e.title})]})},a))})})})})})},9e4:(e,a,s)=>{"use strict";s.d(a,{D:()=>t});var l=s(12115);let t=()=>{let[e,a]=(0,l.useState)("light"),[s,t]=(0,l.useState)(!1);(0,l.useEffect)(()=>{a(localStorage.getItem("theme")||"light"),t(!0)},[]),(0,l.useEffect)(()=>{s&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,s]);let i=(0,l.useCallback)(()=>{a(e=>"dark"===e?"light":"dark")},[]),d=(0,l.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let a=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(a),e.style.display="none")},20)},1e3))}i()},[i]);return{theme:e,toggleTheme:i,handleDarkModeToggle:d}}},90662:(e,a,s)=>{"use strict";s.d(a,{default:()=>t});var l=s(95155);s(12115);let t=()=>(s(81531),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"page-content-wrapper py-3",children:[(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"element-heading",children:(0,l.jsx)("h6",{children:"Bootstrap Basic Modal"})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"card direction-rtl",children:(0,l.jsx)("div",{className:"card-body",children:(0,l.jsx)("button",{className:"btn btn-primary",type:"button","data-bs-toggle":"modal","data-bs-target":"#bootstrapBasicModal",children:"Bootstrap Basic Modal"})})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"element-heading mt-3",children:(0,l.jsx)("h6",{children:"Fullscreen Modal"})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"card direction-rtl",children:(0,l.jsx)("div",{className:"card-body",children:(0,l.jsx)("button",{className:"btn btn-primary",type:"button","data-bs-toggle":"modal","data-bs-target":"#fullscreenModal",children:"Fullscreen Modal"})})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"element-heading mt-3",children:(0,l.jsx)("h6",{children:"Static Backdrop"})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"card direction-rtl",children:(0,l.jsx)("div",{className:"card-body",children:(0,l.jsx)("button",{className:"btn btn-primary",type:"button","data-bs-toggle":"modal","data-bs-target":"#staticBackdrop",children:"Static Backdrop Modal"})})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"element-heading mt-3",children:(0,l.jsx)("h6",{children:"Bottom Align Modal"})})}),(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"card direction-rtl",children:(0,l.jsx)("div",{className:"card-body",children:(0,l.jsx)("button",{className:"btn btn-primary",type:"button","data-bs-toggle":"modal","data-bs-target":"#bottomAlignModal",children:"Bottom Align Modal"})})})})]}),(0,l.jsx)("div",{className:"modal fade",id:"bootstrapBasicModal",tabIndex:-1,"aria-labelledby":"exampleModalLabel","aria-hidden":"true",children:(0,l.jsx)("div",{className:"modal-dialog",children:(0,l.jsxs)("div",{className:"modal-content",children:[(0,l.jsxs)("div",{className:"modal-header",children:[(0,l.jsx)("h5",{className:"modal-title",id:"exampleModalLabel",children:"Bootstrap Basic Modal"}),(0,l.jsx)("button",{type:"button",className:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})]}),(0,l.jsx)("div",{className:"modal-body",children:"This is a Bootstrap basic modal."}),(0,l.jsxs)("div",{className:"modal-footer",children:[(0,l.jsx)("button",{type:"button",className:"btn btn-secondary","data-bs-dismiss":"modal",children:"Close"}),(0,l.jsx)("button",{type:"button",className:"btn btn-primary",children:"Save changes"})]})]})})}),(0,l.jsx)("div",{className:"modal fade",id:"fullscreenModal",tabIndex:-1,"aria-labelledby":"exampleModalLabel","aria-hidden":"true",children:(0,l.jsx)("div",{className:"modal-dialog modal-fullscreen",children:(0,l.jsxs)("div",{className:"modal-content",children:[(0,l.jsxs)("div",{className:"modal-header",children:[(0,l.jsx)("h5",{className:"modal-title",id:"exampleModalLabel",children:"Fullscreen Modal"}),(0,l.jsx)("button",{type:"button",className:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})]}),(0,l.jsx)("div",{className:"modal-body",children:"This is a fullscreen modal."}),(0,l.jsxs)("div",{className:"modal-footer",children:[(0,l.jsx)("button",{type:"button",className:"btn btn-secondary","data-bs-dismiss":"modal",children:"Close"}),(0,l.jsx)("button",{type:"button",className:"btn btn-primary",children:"Save changes"})]})]})})}),(0,l.jsx)("div",{className:"modal fade",id:"staticBackdrop","data-bs-backdrop":"static","data-bs-keyboard":"false",tabIndex:-1,"aria-labelledby":"staticBackdropLabel","aria-hidden":"true",children:(0,l.jsx)("div",{className:"modal-dialog",children:(0,l.jsxs)("div",{className:"modal-content",children:[(0,l.jsxs)("div",{className:"modal-header",children:[(0,l.jsx)("h5",{className:"modal-title",id:"staticBackdropLabel",children:"Static Backdrop Modal"}),(0,l.jsx)("button",{type:"button",className:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})]}),(0,l.jsx)("div",{className:"modal-body",children:"This is a static backdrop modal. Click outside will not close this modal."}),(0,l.jsxs)("div",{className:"modal-footer",children:[(0,l.jsx)("button",{type:"button",className:"btn btn-secondary","data-bs-dismiss":"modal",children:"Close"}),(0,l.jsx)("button",{type:"button",className:"btn btn-primary",children:"Save changes"})]})]})})}),(0,l.jsx)("div",{className:"modal fade",id:"bottomAlignModal",tabIndex:1,"aria-labelledby":"bottomAlignModalLabel","aria-hidden":"true",children:(0,l.jsx)("div",{className:"modal-dialog modal-dialog-centered modal-dialog-scrollable modal-bottom",children:(0,l.jsxs)("div",{className:"modal-content",children:[(0,l.jsxs)("div",{className:"modal-header",children:[(0,l.jsx)("h5",{className:"modal-title",id:"bottomAlignModalLabel",children:"Bottom Align Modal"}),(0,l.jsx)("button",{type:"button",className:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})]}),(0,l.jsx)("div",{className:"modal-body",children:"This is a bottom align modal."}),(0,l.jsxs)("div",{className:"modal-footer",children:[(0,l.jsx)("button",{type:"button",className:"btn btn-secondary","data-bs-dismiss":"modal",children:"Close"}),(0,l.jsx)("button",{type:"button",className:"btn btn-primary",children:"Save changes"})]})]})})})]}))}},e=>{var a=a=>e(e.s=a);e.O(0,[6874,1955,1531,8441,1684,7358],()=>a(32516)),_N_E=e.O()}]);