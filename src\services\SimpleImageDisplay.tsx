import React, { useState } from "react";

interface SimpleImageDisplayProps {
  imageSrc: string;
  size?: number;
}

const SimpleImageDisplay: React.FC<SimpleImageDisplayProps> = ({ imageSrc, size = 80 }) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  if (imageError) {
    return (
      <div 
        className="d-flex align-items-center justify-content-center bg-light border rounded"
        style={{ 
          width: size, 
          height: size,
          fontSize: '12px',
          color: '#666'
        }}
      >
        <i className="bi bi-image" style={{ fontSize: '24px' }}></i>
      </div>
    );
  }

  return (
    <div style={{ position: 'relative', width: size, height: size }}>
      {!imageLoaded && (
        <div 
          className="d-flex align-items-center justify-content-center bg-light border rounded"
          style={{ 
            width: size, 
            height: size,
            position: 'absolute',
            top: 0,
            left: 0,
            zIndex: 1
          }}
        >
          <div className="spinner-border spinner-border-sm text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      )}
      <img
        src={imageSrc}
        alt="Uploaded"
        onError={handleImageError}
        onLoad={handleImageLoad}
        style={{
          width: size,
          height: size,
          objectFit: 'cover',
          borderRadius: '4px',
          border: '1px solid #dee2e6',
          display: imageLoaded ? 'block' : 'none'
        }}
      />
    </div>
  );
};

export default SimpleImageDisplay;
