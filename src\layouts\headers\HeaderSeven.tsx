"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import API from "@/services/API";
import { CURRENT_USER_URL } from "@/constant";
import { useDispatch } from "react-redux";
import { loginActions } from "@/store/login-slice";
// Define Props Interface
interface HeaderSevenProps {
  heading: string;
}

const HeaderSeven: React.FC<HeaderSevenProps> = ({ heading }) => {
  const router = useRouter();

  const [userName, setUsername] = useState('')
  const dispatch = useDispatch()
  React.useEffect(() => {
    getUsersInfo()
  }, [])

  const getUsersInfo = async () => {
    try {
      const response = await API.get(CURRENT_USER_URL);

      if (response.status === 200) {
        setUsername(response.data.firstName)
        dispatch(loginActions.setUser(response.data))

      } else {
        router.push('/')
      }
    } catch (e) {
      console.log(e)
    }
  }
  return (
    <>
      <div className="header-area" id="headerArea">
        <div className="container">
          {/* <!-- Header Content--> */}
          <div className="header-content header-style-four position-relative d-flex align-items-center justify-content-between">
            {/* <!-- Back Button--> */}
            <div className="back-button">
              <button
                onClick={() => router.back()} // Use Next.js router to navigate back
                className="border-0 bg-transparent p-0"
                style={{ cursor: "pointer" }}
              >
                <i className="bi bi-arrow-left-short fs-3"></i>
              </button>
            </div>

            {/* <!-- Page Title--> */}
            <div className="page-heading">
              <h6 className="mb-0">{heading}</h6>
            </div>

            {/* <!-- User Profile--> */}
            <div className="user-profile-wrapper">
              {/* <a className="user-profile-trigger-btn" href="#">
                <img src="/assets/img/bg-img/20.jpg" alt="" />
              </a> */}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default HeaderSeven;
