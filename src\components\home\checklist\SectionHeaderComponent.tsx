"use client";

import React from "react";
import { ChecklistComponent, SectionHeaderData } from "../types/ChecklistTypes";

interface SectionHeaderComponentProps {
    component: ChecklistComponent;
    componentIndex: number;
}

const SectionHeaderComponent: React.FC<SectionHeaderComponentProps> = ({ component }) => {
    const sectionHeaderData = component.data as SectionHeaderData;
    
    return (
        <div className="mb-3 p-4 rounded  text-white text-center">
            <h4 className="mb-0 fw-bold">
                {sectionHeaderData.text}
            </h4>
        </div>
    );
};

export default SectionHeaderComponent;
