export interface ShowItem {
    id: string;
    // add any other properties you actually need here
}

export interface LocationEntity {
    id?: string;
    name?: string;
}

export interface InspectorEntity {
    firstName?: string;
}

export interface ChecklistMeta {
    name?: string;
    value?: {
        metadata: {
            name: string;
            version: string;
            createdAt: string;
            totalComponents: number;
            modes: {
                communicate: number;
                feedback: number;
            };
        };
        components: ChecklistComponent[];
    };
}

export interface ApplicationDetails {
    maskId?: string;
    status?: string;
    inspectionCategory?: string;
    scheduledDate?: string;
    dueDate?: string;
    inspector?: InspectorEntity;
    checklist?: ChecklistMeta;
    checklistVersion?: string;
    locationOne?: LocationEntity;
    locationTwo?: LocationEntity;
    locationThree?: LocationEntity;
    locationFour?: LocationEntity;
    locationFive?: LocationEntity;
    locationSix?: LocationEntity;
}

/** Option type for the react-select */
export interface OptionType {
    label: string;
    value: string;
}

/* New Checklist Structures */
export interface Checkpoint {
    id: string;
    text: string;
    selected?: "Yes" | "No" | "N/A" | "";
    remarks?: string;
    actionToBeTaken?: string;
    dueDate?: Date | string | null;
    assignee?: string;
    uploads?: string[];
}

export interface CheckpointGroupData {
    id: string;
    type: "checkpoint-group";
    position: number;
    required: boolean;
    allowFromLibrary: boolean;
    title: string;
    checkpoints: Array<{
        id: string;
        text: string;
    }>;
}

export interface CheckpointData {
    id: string;
    type: "checkpoint";
    position: number;
    required: boolean;
    allowFromLibrary: boolean;
    text: string;
}

export interface DateData {
    id: string;
    type: "date";
    position: number;
    required: boolean;
    allowFromLibrary: boolean;
    label: string;
}

export interface SignData {
    id: string;
    type: "sign";
    position: number;
    required: boolean;
    allowFromLibrary: boolean;
    label: string;
}

export interface HeaderData {
    id: string;
    type: "header";
    position: number;
    required: boolean;
    allowFromLibrary: boolean;
    text: string;
}

export interface TextBodyData {
    type: "text-body";
    position: number;
    content: string;
}

export interface SectionHeaderData {
    id: string;
    type: "section-header";
    position: number;
    required: boolean;
    allowFromLibrary: boolean;
    text: string;
}

export interface TextInputData {
    id: string;
    type: "text-input";
    position: number;
    required: boolean;
    label: string;
    placeholder?: string;
}

export interface ImageInputData {
    id: string;
    type: "image-input";
    position: number;
    required: boolean;
    label: string;
    uploads?: string[];
}

export interface AttachmentInputData {
    id: string;
    type: "attachment-input";
    position: number;
    required: boolean;
    label: string;
    attachmentConfig: {
        image: {
            enabled: boolean;
            galleryUploads: boolean;
        };
        video: {
            enabled: boolean;
            galleryUploads: boolean;
        };
        documents: {
            enabled: boolean;
        };
    };
    uploads?: string[];
}

export interface ChecklistComponent {
    id: string;
    type: "checkpoint-group" | "checkpoint" | "date" | "sign" | "header" | "text-body" | "section-header" | "text-input" | "image-input" | "attachment-input";
    position: number;
    data: CheckpointGroupData | CheckpointData | DateData | SignData | HeaderData | TextBodyData | SectionHeaderData | TextInputData | ImageInputData | AttachmentInputData;
    validation: {
        isValid: boolean;
        lastValidated: string;
    };
    // Response data for user interactions
    groupAnswer?: "Yes" | "No" | "";
    checkpoints?: Checkpoint[];
    selected?: "Yes" | "No" | "N/A" | "";
    remarks?: string;
    actionToBeTaken?: string;
    dueDate?: Date | string | null;
    assignee?: string;
    uploads?: string[];
    selectedDate?: Date | string | null;
    signature?: string;
    textValue?: string; // For text-input components
    imageFiles?: string[]; // For image-input components
    // Additional fields for checkpoint groups
    isChecked?: boolean;
    reason?: string;
}

export interface NewChecklistMeta {
    name?: string;
    value?: {
        metadata: {
            name: string;
            version: string;
            createdAt: string;
            totalComponents: number;
            modes: {
                communicate: number;
                feedback: number;
            };
        };
        components: ChecklistComponent[];
    };
}

export interface PostAction {
    actionToBeTaken: string;
    dueDate: Date | null;
    uploads: string[];
    assignee: string;
}

export interface ErrorBuckets {
    group: Record<string, string>;
    checklist: Record<string, string>;
    post: Record<string, string>;
}

export interface ConductActionProps {
    show: boolean;
    applicationDetails: ApplicationDetails;
    showItem: ShowItem;
    closeModal: () => void;
}
