"use client";

import HeaderSeven from "@/layouts/headers/HeaderSeven";
import React, { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import API from "@/services/API"; // Import API service
import { FILE_DOWNLOAD, OBSERVATION_ARCHIVED_URL, USERS_URL } from "@/constant"; // Your API endpoint
import { Modal, Button } from "react-bootstrap"; // Import Bootstrap Modal
import moment from 'moment'
import ImageComponent from "@/services/FileDownlodS3";
import MyLogoComponent from "@/services/MyLogoComponet";
import axios from "axios";
import ViewOBS from "./ViewOBS";


interface User {
    id: string | number;
    firstName: string;
}
interface ObservationAction {
    actionType: string;
    // Add more fields if necessary
}
const New: React.FC = () => {
    const router = useRouter();
    const user = useSelector((state: RootState) => state.login.user);
    const [activeTab, setActiveTab] = useState("reported");
    let k = 0;

    // Check if user has obsreporter role
    const isObsReporter = useMemo(() => {
        return user && (user as any)?.roles?.some((role: any) => ['obsreporter'].includes(role.maskId));
    }, [user]);
    // State for Archived Data
    const [archivedData, setArchivedData] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [reportData, setReportData] = useState<any | null>(null);
    const [showModal, setShowModal] = useState(false);
    const [users, setUsers] = useState([])
    const [logo, setLogo] = useState('')
    // Function to Fetch Archived Data
    const fetchArchivedData = async () => {
        setLoading(true);
        setError(null);
        try {
            const uriString = {
                include: [
                    { relation: "locationOne" },
                    { relation: "locationTwo" },
                    { relation: "locationThree" },
                    { relation: "locationFour" },
                    { relation: "locationFive" },
                    { relation: "locationSix" },
                    { relation: "reporter" },
                    { relation: "actionOwner" },
                    { relation: 'multiActionOwners' },
                    { relation: "reviewer" },
                    { relation: "observationActions" },
                ]

            };
            const url = `${OBSERVATION_ARCHIVED_URL}?filter=${encodeURIComponent(
                JSON.stringify(uriString)
            )}`;
            const response = await API.get(url);
            if (response.status === 200) {
                setArchivedData(response.data.reverse()); // Assuming response.data is an array
            }
        } catch (err) {
            setError("Failed to fetch archived data.");
            console.error("Error fetching archived data:", err);
        } finally {
            setLoading(false);
        }
    };

    // Fetch data when Archived tab is selected
    useEffect(() => {
        if (activeTab === "reported") {
            fetchArchivedData();
            getAllUsers()
        }
    }, [activeTab]);

    const getAllUsers = async () => {
        const response = await API.get(USERS_URL);
        setUsers(response.data)
    }
    useEffect(() => {
        getFetchLogo()
    }, [])
    const getFetchLogo = async () => {
        try {
            const response = await API.get(FILE_DOWNLOAD(localStorage.getItem('logo')), {
                headers: {
                    'Content-Type': 'application/json'
                },
            });
            const data = response.data;
            const logoUrl = data // Assuming the API returns an object with a `url` field
            setLogo(logoUrl);

        } catch (error) {
            console.error('Error fetching logo:', error);
        }
    }
    const handleObservationClick = (obs: any) => {
        setReportData(obs);
        setShowModal(true);
    };

    return (
        <>
            <div className="page-content-wrapper" style={{ backgroundColor: "#ffffff" }}>
                <div className="container-fluid px-3 py-3">

                    {/* Record a new observation - Only show if user has obsreporter role */}
                    {isObsReporter && (
                        <div
                            className="card border-0 p-4 mb-4 d-flex align-items-center flex-row"
                            style={{
                                cursor: "pointer",
                                transition: "transform 0.2s ease, box-shadow 0.2s ease",
                                boxShadow: "0 4px 12px rgba(0,0,0,0.15)"
                            }}
                            onClick={() => router.push("/observation/new")}
                            onMouseEnter={(e) => {
                                e.currentTarget.style.transform = "translateY(-3px)";
                                e.currentTarget.style.boxShadow = "0 8px 25px rgba(220, 53, 69, 0.2)";
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.transform = "translateY(0)";
                                e.currentTarget.style.boxShadow = "0 4px 12px rgba(0,0,0,0.15)";
                            }}
                        >
                            <div
                                className="rounded-circle d-flex align-items-center justify-content-center me-3"
                                style={{
                                    width: "56px",
                                    height: "56px",
                                    backgroundColor: "#dc3545",
                                    color: "white"
                                }}
                            >
                                <i className="bi bi-file-earmark-plus" style={{ fontSize: "24px" }}></i>
                            </div>
                            <div className="flex-grow-1">
                                <h6 className="mb-1" style={{ fontWeight: "600", color: "#111827" }}>
                                    Record a new observation
                                </h6>
                                <p className="mb-0 text-muted" style={{ fontSize: "14px" }}>
                                    Create and submit a new safety observation
                                </p>
                            </div>
                            <i className="bi bi-chevron-right text-muted" style={{ fontSize: "20px" }}></i>
                        </div>
                    )}

                    {/* Simple Tab Navigation */}
                    <div className="mb-4">
                        <div className="d-flex gap-2 p-1 rounded-3" style={{ backgroundColor: "#f8fafc" }}>
                            {[
                                { id: "reported", label: "Reported Observations" }
                            ].map((tab) => (
                                <button
                                    key={tab.id}
                                    className={`btn flex-fill py-2 px-3 rounded-2 ${
                                        activeTab === tab.id
                                            ? "btn-danger text-white"
                                            : "btn-light text-muted"
                                    }`}
                                    style={{
                                        border: "none",
                                        fontWeight: "500",
                                        fontSize: "14px",
                                        transition: "all 0.2s ease"
                                    }}
                                    onClick={() => setActiveTab(tab.id)}
                                >
                                    {tab.label}
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Content Area */}
                    <div>
                        {activeTab === "reported" && (
                            <div>
                                {/* Simple Header */}
                                <div className="d-flex justify-content-between align-items-center mb-3">
                                    <h6 className="mb-0" style={{ color: "#374151", fontWeight: "600" }}>
                                        Reported Observations
                                    </h6>
                                    <span className="badge bg-light text-muted px-3 py-2" style={{ fontSize: "12px" }}>
                                        {archivedData.length} items
                                    </span>
                                </div>

                                {/* Content */}
                                <div style={{ maxHeight: "70vh", overflowY: "auto" }}>
                                    {loading && (
                                        <div className="text-center py-5">
                                            <div className="spinner-border text-danger" role="status">
                                                <span className="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    )}

                                    {error && (
                                        <div className="alert alert-danger" role="alert">
                                            {error}
                                        </div>
                                    )}

                                    {archivedData.length > 0 ? (
                                        <div className="row g-3">
                                            {archivedData.map((obs, index) => (
                                                <div key={index} className="col-12">
                                                    <div
                                                        className="card border-0"
                                                        style={{
                                                            cursor: "pointer",
                                                            transition: "transform 0.2s ease, box-shadow 0.2s ease",
                                                            boxShadow: "0 4px 12px rgba(0,0,0,0.15)"
                                                        }}
                                                        onClick={() => handleObservationClick(obs)}
                                                        onMouseEnter={(e) => {
                                                            e.currentTarget.style.transform = "translateY(-3px)";
                                                            e.currentTarget.style.boxShadow = "0 8px 25px rgba(0,0,0,0.2)";
                                                        }}
                                                        onMouseLeave={(e) => {
                                                            e.currentTarget.style.transform = "translateY(0)";
                                                            e.currentTarget.style.boxShadow = "0 4px 12px rgba(0,0,0,0.15)";
                                                        }}
                                                    >
                                                        <div className="card-body p-3">
                                                            <div className="d-flex justify-content-between align-items-center mb-2">
                                                                <span className="text-muted small">#{obs.maskId}</span>
                                                                <span className="badge bg-light text-muted" style={{ fontSize: "11px" }}>
                                                                    {obs.observationCategory} Observation
                                                                </span>
                                                            </div>

                                                            <h6 className="mb-2" style={{ color: "#111827", lineHeight: "1.4" }}>
                                                                {obs.description}
                                                            </h6>

                                                            <p className="text-muted small mb-2">{obs.status}</p>

                                                            <div className="d-flex justify-content-between align-items-center">
                                                                <small className="text-muted">
                                                                    {new Date(obs.created).toLocaleDateString()}
                                                                </small>
                                                                <span className={`badge ${
                                                                    obs.observationType === "Safe" ? "bg-success" : "bg-danger"
                                                                } text-white`}>
                                                                    {obs.observationType}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        !loading && (
                                            <div className="text-center py-5">
                                                <i className="bi bi-eye-slash text-muted mb-3" style={{ fontSize: "3rem" }}></i>
                                                <p className="text-muted">No observations found</p>
                                            </div>
                                        )
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>

                {/* Observation Details Modal */}
                <Modal show={showModal} onHide={() => setShowModal(false)} centered>
                    <Modal.Header closeButton>
                        {reportData && (
                            <div className="row" style={{ width: '100%' }}>
                                <div className="col-12">
                                    <div className="row">
                                        <div  >
                                            {/* <img src={logo} className="me-3" alt="logo" style={{ maxWidth: '125px' }} /> */}
                                            <MyLogoComponent logo={logo} />
                                        </div>
                                        <div className="col">
                                            <h6>Observation</h6>
                                            <div className="d-flex align-items-center">
                                                <p className="me-2">#{reportData.maskId || ''} </p>
                                                <p className=" badge bg-primary text-white">{reportData.status} </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                            </div>
                        )}
                    </Modal.Header>
                    <Modal.Body>
                        <ViewOBS reportData={reportData} />
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={() => setShowModal(false)}>Close</Button>
                    </Modal.Footer>
                </Modal>

                <style jsx>{`
        .observation-report {
          background: #fff;
          border-radius: 10px;
          padding: 20px;
        }
        .section-title {
          font-size: 1.1rem;
          font-weight: bold;
          color: #333;
        }
        .obs-title {
          font-size: 0.9rem;
          font-weight: bold;
          color: #555;
          margin-bottom: 5px;
        }
        .obs-content {
          font-size: 0.9rem;
          color: #777;
        }
        .image-box {
          border: 1px solid #ddd;
          background: #f8f9fa;
          height: 100px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      `}</style>
        </>
    );
};

export default New;
