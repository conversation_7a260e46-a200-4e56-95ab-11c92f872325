"use client";

/**
 * iOS-specific notification service to handle push notifications
 * when FCM doesn't work properly on iOS devices
 */

interface IOSNotificationConfig {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  tag?: string;
  data?: any;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
}

class IOSNotificationService {
  private isIOS(): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  }

  private isIOSPWA(): boolean {
    return this.isIOS() && (window.navigator as any).standalone === true;
  }

  private isIOSSafari(): boolean {
    return this.isIOS() && !this.isIOSPWA();
  }

  private getIOSVersion(): number | null {
    const match = navigator.userAgent.match(/OS (\d+)_(\d+)_?(\d+)?/);
    if (match) {
      return parseInt(match[1], 10);
    }
    return null;
  }

  /**
   * Check if iOS push notifications are supported
   */
  isSupported(): boolean {
    if (!this.isIOS()) {
      return false; // Not iOS, use FCM instead
    }

    // Check iOS version (requires 16.4+)
    const iosVersion = this.getIOSVersion();
    if (iosVersion && iosVersion < 16) {
      console.log('⚠️ iOS version too old for web push notifications (requires 16.4+)');
      return false;
    }

    // Check if notifications are supported
    if (!('Notification' in window)) {
      console.log('⚠️ Notification API not supported');
      return false;
    }

    // Check if service workers are supported
    if (!('serviceWorker' in navigator)) {
      console.log('⚠️ Service Worker not supported');
      return false;
    }

    return true;
  }

  /**
   * Get current notification permission status
   */
  getPermissionStatus(): NotificationPermission {
    if (typeof window === 'undefined' || !('Notification' in window)) {
      return 'denied';
    }
    return Notification.permission;
  }

  /**
   * Request notification permission with iOS-specific handling
   */
  async requestPermission(): Promise<boolean> {
    if (!this.isSupported()) {
      console.log('❌ iOS notifications not supported on this device/version');
      return false;
    }

    try {
      console.log('🍎 Requesting iOS notification permission...');
      
      // For iOS PWA, we might need special handling
      if (this.isIOSPWA()) {
        console.log('📱 Requesting permission in iOS PWA context');
      }

      const permission = await Notification.requestPermission();
      
      if (permission === 'granted') {
        console.log('✅ iOS notification permission granted');
        return true;
      } else {
        console.log('❌ iOS notification permission denied');
        this.showPermissionGuidance();
        return false;
      }
    } catch (error) {
      console.error('❌ Error requesting iOS notification permission:', error);
      return false;
    }
  }

  /**
   * Show guidance for enabling notifications on iOS
   */
  private showPermissionGuidance(): void {
    console.log('📱 iOS Notification Setup Guide:');
    console.log('1. Open Safari Settings');
    console.log('2. Scroll down to "Websites"');
    console.log('3. Tap "Notifications"');
    console.log('4. Find your website and set to "Allow"');
    
    if (this.isIOSPWA()) {
      console.log('📱 PWA Additional Steps:');
      console.log('5. You may need to re-add the app to home screen');
      console.log('6. Ensure the app is opened from home screen icon');
    }
  }

  /**
   * Show a local notification (for testing or fallback)
   */
  async showLocalNotification(config: IOSNotificationConfig): Promise<boolean> {
    if (this.getPermissionStatus() !== 'granted') {
      console.log('❌ Cannot show notification: permission not granted');
      return false;
    }

    try {
      const notification = new Notification(config.title, {
        body: config.body,
        icon: config.icon || '/assets/icons/Icon-192.png',
        badge: config.badge || '/assets/icons/Icon-72.png',
        tag: config.tag || 'ios-notification',
        data: config.data || {},
        requireInteraction: true
      });

      // Handle notification click
      notification.onclick = (event) => {
        event.preventDefault();
        console.log('📱 iOS notification clicked');
        
        // Focus the app window if it's open
        if (window.focus) {
          window.focus();
        }
        
        // Handle custom data
        if (config.data?.url) {
          window.location.href = config.data.url;
        }
        
        notification.close();
      };

      // Auto-close after 10 seconds
      setTimeout(() => {
        notification.close();
      }, 10000);

      return true;
    } catch (error) {
      console.error('❌ Error showing iOS notification:', error);
      return false;
    }
  }

  /**
   * Register for push notifications using Web Push API
   * This is an alternative to FCM for iOS
   */
  async registerForPushNotifications(): Promise<string | null> {
    if (!this.isSupported()) {
      return null;
    }

    try {
      // Register service worker
      const registration = await navigator.serviceWorker.register('/sw.js');
      console.log('✅ Service worker registered for iOS push');

      // Subscribe to push notifications
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(
          process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY || 
          'BDukpatijXC4YB_g4W7FvW-UjBfYtMAM7epw7MJFkcb4ICU8udWq7UYAQlk-hFJ9WmZ-EutlgybC5-HIAkaMSnU'
        )
      });

      console.log('✅ iOS push subscription created');
      
      // Convert subscription to a format your server can use
      const subscriptionData = {
        endpoint: subscription.endpoint,
        keys: {
          p256dh: this.arrayBufferToBase64(subscription.getKey('p256dh')!),
          auth: this.arrayBufferToBase64(subscription.getKey('auth')!)
        }
      };

      // Send to your server
      await this.sendSubscriptionToServer(subscriptionData);
      
      return JSON.stringify(subscriptionData);
    } catch (error) {
      console.error('❌ Error registering for iOS push notifications:', error);
      return null;
    }
  }

  /**
   * Send push subscription to server
   */
  private async sendSubscriptionToServer(subscription: any): Promise<void> {
    try {
      const authToken = localStorage.getItem('access_token') || 
                       localStorage.getItem('token') || 
                       localStorage.getItem('authToken');

      if (!authToken) {
        console.log('⚠️ No auth token found for iOS push subscription');
        return;
      }

      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://client-api.acuizen.com';
      const apiUrl = `${baseUrl}/users/me/ios-push-subscription`;

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
        },
        body: JSON.stringify({
          subscription,
          deviceType: 'ios-web',
          userAgent: navigator.userAgent
        }),
      });

      if (response.ok) {
        console.log('✅ iOS push subscription sent to server');
      } else {
        console.log('⚠️ Failed to send iOS push subscription to server:', response.status);
      }
    } catch (error) {
      console.error('❌ Error sending iOS push subscription to server:', error);
    }
  }

  /**
   * Utility functions
   */
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return window.btoa(binary);
  }

  /**
   * Test notification functionality
   */
  async testNotification(): Promise<void> {
    console.log('🧪 Testing iOS notification...');
    
    const success = await this.showLocalNotification({
      title: 'Test Notification',
      body: 'iOS notifications are working!',
      tag: 'test',
      data: { test: true }
    });

    if (success) {
      console.log('✅ iOS test notification sent successfully');
    } else {
      console.log('❌ iOS test notification failed');
    }
  }
}

// Export singleton instance
export const iosNotificationService = new IOSNotificationService();
export default iosNotificationService;
