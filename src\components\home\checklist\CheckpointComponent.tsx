"use client";

import React from "react";
import { Card, Form } from "react-bootstrap";
import DatePicker from "react-datepicker";
import { ChecklistComponent, CheckpointData, ErrorBuckets, OptionType } from "../types/ChecklistTypes";
import FileUploader from "@/services/FileUploader";
import ModalSelect from "@/services/ModalSelect";

interface CheckpointComponentProps {
    component: ChecklistComponent;
    componentIndex: number;
    checklistData: ChecklistComponent[];
    setChecklistData: React.Dispatch<React.SetStateAction<ChecklistComponent[]>>;
    errorMap: ErrorBuckets;
    assessor: OptionType[];
    handleFileUpload: (files: string[], componentIndex: number, checkpointIndex?: number) => void;
}

const CheckpointComponent: React.FC<CheckpointComponentProps> = ({
    component,
    componentIndex,
    checklistData,
    setChecklistData,
    errorMap,
    assessor,
    handleFileUpload,
}) => {
    const checkpointData = component.data as CheckpointData;
    const sel = component.selected || "";

    return (
        <Card className="mb-3 shadow-sm" style={{
            border: "1px solid #e5e7eb",
            borderRadius: "8px",
            backgroundColor: sel === "Yes" ? "#f0fdf4" : sel === "No" ? "#fef2f2" : "#ffffff"
        }}>
            <Card.Body className="p-3">
                <div className="mb-3 fw-semibold d-flex align-items-start">
                    <i className="bi bi-check-square me-2 mt-1" style={{
                        color: sel === "Yes" ? "#10b981" : sel === "No" ? "#ef4444" : "#6b7280",
                        fontSize: "1rem"
                    }}></i>
                    <span style={{ color: "#374151", fontSize: "0.95rem" }}>{checkpointData.text}</span>
                </div>

                {/* Yes/No/N/A buttons - mobile friendly */}
                <div className="d-flex flex-column flex-sm-row w-100 mb-2 gap-2">
                    {["Yes", "No", "N/A"].map((opt) => {
                        const id = `checkpoint-${componentIndex}-${opt}`;
                        const isSelected = sel === opt;

                        return (
                            <div key={opt} className="flex-grow-1">
                                <input
                                    type="radio"
                                    className="btn-check"
                                    id={id}
                                    name={`checkpoint-${componentIndex}`}
                                    autoComplete="off"
                                    checked={sel === opt}
                                    onChange={() => {
                                        const updated = [...checklistData];
                                        updated[componentIndex].selected = opt as any;
                                        if (opt !== "No") {
                                            updated[componentIndex].actionToBeTaken = "";
                                            updated[componentIndex].dueDate = null;
                                            updated[componentIndex].assignee = "";
                                            updated[componentIndex].uploads = [];
                                        }
                                        updated[componentIndex].remarks = "";
                                        setChecklistData(updated);
                                    }}
                                />
                                <label
                                    className={`btn w-100 ${
                                        opt === "Yes"
                                            ? isSelected ? "btn-success" : "btn-outline-success"
                                            : opt === "No"
                                            ? isSelected ? "btn-danger" : "btn-outline-danger"
                                            : isSelected ? "btn-warning" : "btn-outline-warning"
                                    }`}
                                    htmlFor={id}
                                    style={{ fontSize: "0.9rem", padding: "0.5rem" }}
                                >
                                    {opt}
                                </label>
                            </div>
                        );
                    })}
                </div>
                {errorMap.checklist[`${componentIndex}-sel`] && (
                    <div className="text-danger small">
                        {errorMap.checklist[`${componentIndex}-sel`]}
                    </div>
                )}

                {/* Remarks for Yes/N/A */}
                {["Yes", "N/A"].includes(sel) && (
                    <Form.Group className="mb-2">
                        <Form.Control
                            placeholder="Remarks"
                            value={component.remarks || ""}
                            onChange={(e) => {
                                const updated = [...checklistData];
                                updated[componentIndex].remarks = e.target.value;
                                setChecklistData(updated);
                            }}
                            isInvalid={!!errorMap.checklist[`${componentIndex}-remarks`]}
                        />
                        <Form.Control.Feedback type="invalid">
                            {errorMap.checklist[`${componentIndex}-remarks`]}
                        </Form.Control.Feedback>
                    </Form.Group>
                )}

                {/* Extra fields when answer is NO */}
                {sel === "No" && (
                    <div className="p-3 rounded border border-danger bg-light">
                        <h6 className="mb-3 d-flex align-items-center fw-semibold text-danger">
                            <i className="bi bi-exclamation-triangle me-2"></i>
                            Action Required
                        </h6>

                        {/* Remarks */}
                        <Form.Group className="mb-2">
                            <Form.Label>Remarks</Form.Label>
                            <Form.Control
                                placeholder="Remarks "
                                value={component.remarks || ""}
                                onChange={(e) => {
                                    const updated = [...checklistData];
                                    updated[componentIndex].remarks = e.target.value;
                                    setChecklistData(updated);
                                }}
                                isInvalid={!!errorMap.checklist[`${componentIndex}-remarks`]}
                            />
                            <Form.Control.Feedback type="invalid">
                                {errorMap.checklist[`${componentIndex}-remarks`]}
                            </Form.Control.Feedback>
                        </Form.Group>

                        {/* Action */}
                        <Form.Group className="mb-2">
                            <Form.Label>Actions to be taken</Form.Label>
                            <Form.Control
                                placeholder="Actions to be taken *"
                                value={component.actionToBeTaken || ""}
                                onChange={(e) => {
                                    const updated = [...checklistData];
                                    updated[componentIndex].actionToBeTaken = e.target.value;
                                    setChecklistData(updated);
                                }}
                                isInvalid={!!errorMap.checklist[`${componentIndex}-action`]}
                            />
                            <Form.Control.Feedback type="invalid">
                                {errorMap.checklist[`${componentIndex}-action`]}
                            </Form.Control.Feedback>
                        </Form.Group>

                        {/* Upload */}
                        {/* <Form.Group className="mb-2">
                            <Form.Label>Upload media (optional)</Form.Label>
                            <FileUploader
                                onFilesSelected={(updatedList: string[]) => {
                                    handleFileUpload(updatedList, componentIndex);
                                }}
                                disabled={false}
                                files={component.uploads || []}
                            />
                        </Form.Group> */}

                        {/* Due Date */}
                        <Form.Group className="mb-2">
                            <Form.Label>Due Date</Form.Label>
                            <div
                                className={
                                    errorMap.checklist[`${componentIndex}-due`]
                                        ? "border border-danger rounded"
                                        : ""
                                }
                            >
                                <DatePicker
                                    selected={component.dueDate ? new Date(component.dueDate) : null}
                                    onChange={(d: Date | null | undefined) => {
                                        const updated = [...checklistData];
                                        updated[componentIndex].dueDate = d ? d.toISOString() : null;
                                        setChecklistData(updated);
                                    }}
                                    minDate={new Date()}
                                    placeholderText="Select due date"
                                    dateFormat="dd-MM-yyyy"
                                    className="form-control"
                                    popperClassName="datepicker-high-zindex"
                                />
                            </div>
                            {errorMap.checklist[`${componentIndex}-due`] && (
                                <div className="text-danger small mt-1">
                                    {errorMap.checklist[`${componentIndex}-due`]}
                                </div>
                            )}
                        </Form.Group>

                        {/* Assignee */}
                        <Form.Group className="mb-2">
                            <Form.Label>Assign Action to *</Form.Label>
                            <ModalSelect
                                title={""}
                                options={assessor}
                                selectedValue={component.assignee}
                                onChange={(newVal: any) => {
                                    const updated = [...checklistData];
                                    updated[componentIndex].assignee = newVal;
                                    setChecklistData(updated);
                                }}
                                placeholder="Select Action Owner"
                                clearable
                                disabled={false}
                            />
                            {errorMap.checklist[`${componentIndex}-own`] && (
                                <div className="text-danger small mt-1">
                                    {errorMap.checklist[`${componentIndex}-own`]}
                                </div>
                            )}
                        </Form.Group>
                    </div>
                )}
            </Card.Body>
        </Card>
    );
};

export default CheckpointComponent;
