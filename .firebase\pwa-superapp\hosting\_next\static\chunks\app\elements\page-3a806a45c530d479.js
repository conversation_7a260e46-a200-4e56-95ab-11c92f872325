(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6818],{17451:(e,a,s)=>{"use strict";s.d(a,{default:()=>f});var t=s(95155),r=s(26957),i=s(9e4),n=s(6874),o=s.n(n),l=s(12115),c=s(38336),m=s(34540),d=s(81359),h=s(35695);let f=()=>{s(81531);let[e,a]=(0,l.useState)(""),[n,f]=(0,l.useState)(""),u=(0,m.wA)(),p=(0,h.useRouter)(),{theme:g,handleDarkModeToggle:b}=(0,i.D)();(0,l.useEffect)(()=>{v(),x()},[]);let v=async()=>{try{if(window.localStorage){let e=localStorage.getItem("logo");if(!e)return void console.warn("No logo key found in localStorage");let s=(await c.A.get((0,r._i)(e),{headers:{"Content-Type":"application/json"}})).data;a(s)}else console.warn("localStorage is not available")}catch(e){console.error("Error fetching logo:",e)}},x=async()=>{try{let e=await c.A.get(r.AM);200===e.status?(f(e.data.firstName),u(d.l.setUser(e.data))):p.push("/")}catch(e){console.log(e)}};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"header-area",id:"headerArea",children:(0,t.jsx)("div",{className:"container",children:(0,t.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,t.jsxs)("div",{className:"navbar--toggler",id:"affanNavbarToggler","data-bs-toggle":"offcanvas","data-bs-target":"#affanOffcanvas","aria-controls":"affanOffcanvas",children:[(0,t.jsx)("span",{className:"d-block"}),(0,t.jsx)("span",{className:"d-block"}),(0,t.jsx)("span",{className:"d-block"})]}),(0,t.jsxs)("div",{className:"logo-wrapper text-center",children:[(0,t.jsx)(o(),{href:"/home",children:(0,t.jsx)("img",{src:e,alt:""})}),(0,t.jsx)("div",{className:"element-heading",children:(0,t.jsx)("h6",{children:"AcuiZen WorkHub"})})]}),(0,t.jsx)("div",{className:"setting-wrapper",children:(0,t.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn2",children:[(0,t.jsx)("i",{className:"bi bi-bell"}),(0,t.jsx)("span",{})]})})]})})}),(0,t.jsxs)("div",{className:"offcanvas offcanvas-start",id:"affanOffcanvas","data-bs-scroll":"true",tabIndex:-1,"aria-labelledby":"affanOffcanvsLabel",children:[(0,t.jsx)("button",{className:"btn-close btn-close-white text-reset",type:"button","data-bs-dismiss":"offcanvas","aria-label":"Close"}),(0,t.jsx)("div",{className:"offcanvas-body p-0",children:(0,t.jsxs)("div",{className:"sidenav-wrapper",children:[(0,t.jsxs)("div",{className:"sidenav-profile bg-gradient",children:[(0,t.jsx)("div",{className:"sidenav-style1"}),(0,t.jsx)("div",{className:"user-profile"}),(0,t.jsx)("div",{className:"user-info",children:(0,t.jsx)("h6",{className:"user-name mb-0",children:n})})]}),(0,t.jsxs)("ul",{className:"sidenav-nav ps-0",children:[(0,t.jsx)("li",{children:(0,t.jsxs)(o(),{href:"/home",children:[(0,t.jsx)("i",{className:"bi bi-house-door"})," Home"]})}),(0,t.jsx)("li",{children:(0,t.jsxs)("div",{className:"night-mode-nav",children:[(0,t.jsx)("i",{className:"bi bi-moon"}),"dark"===g?"Light":"Dark"," Mode",(0,t.jsx)("div",{className:"form-check form-switch",children:(0,t.jsx)("input",{className:"form-check-input form-check-success",id:"darkSwitch",type:"checkbox",checked:"dark"===g,onChange:b})})]})}),(0,t.jsx)("li",{children:(0,t.jsxs)("button",{className:"btn w-100 text-start",onClick:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("COGNITO_USER_DOMAIN"),localStorage.removeItem("enterprise_id"),p.push("/")},style:{border:"none",background:"none",padding:"10px 22px "},children:[(0,t.jsx)("i",{className:"bi bi-box-arrow-right",style:{fontSize:"20px",paddingRight:10}})," Logout"]})})]}),(0,t.jsx)("div",{className:"copyright-info",children:(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{id:"copyrightYear"}),new Date().getFullYear()," \xa9 Made by ",(0,t.jsx)("a",{target:"_blank",href:"https://www.acuizen.com",children:"AcuiZen"})]})})]})})]})]})}},26957:(e,a,s)=>{"use strict";s.d(a,{AM:()=>o,Dp:()=>m,F4:()=>L,FI:()=>T,H$:()=>t,J9:()=>u,Jo:()=>g,K9:()=>l,M6:()=>h,MO:()=>d,OT:()=>O,P4:()=>y,UR:()=>C,WD:()=>S,WH:()=>x,WU:()=>w,_i:()=>n,bW:()=>N,dG:()=>r,dm:()=>A,iJ:()=>I,mh:()=>P,oo:()=>f,pZ:()=>v,u3:()=>c,x2:()=>j,xE:()=>i,xo:()=>k,yo:()=>b,zP:()=>p});let t="https://client-api.acuizen.com",r=t+"/login-configs",i=t+"/services",n=e=>t+"/files/"+e+"/presigned-url",o=t+"/users/me",l=t+"/dynamic-titles",c=t+"/users/get_users",m=t+"/files",d=t+"/observation-reports",h=t+"/my-observation-reports",f=t+"/dropdowns",u=t+"/get-blob",p=t+"/permit-reports",g=t+"/users",b=t+"/toolbox-talks",v=t+"/my-toolbox-talks",x=e=>t+"/my-assigned-actions/"+e,j=e=>t+"/inspection-checklist-submit/"+e,y=e=>t+"/observation-reports/"+e,N=e=>t+"/inspection-task-submit/"+e,k=e=>t+"/inspections/"+e,w=e=>t+"/permit-report-submit/"+e,S=e=>t+"/permit-reports-acknowledge/"+e,C=e=>t+"/permit-reports-update-status/"+e,T=e=>t+"/observation-action-submit/"+e,A=t+"/risk-assessments",I=e=>t+"/risk-assessments/"+e,L=e=>t+"/ra-team-member-submit-signature/"+e,O=t+"/permit-reports",P=e=>t+"/permit-reports/"+e},35695:(e,a,s)=>{"use strict";var t=s(18999);s.o(t,"useRouter")&&s.d(a,{useRouter:function(){return t.useRouter}}),s.o(t,"useSearchParams")&&s.d(a,{useSearchParams:function(){return t.useSearchParams}})},38336:(e,a,s)=>{"use strict";s.d(a,{A:()=>n});var t=s(96078),r=s(26957);let i=t.A.create({baseURL:r.H$,headers:{Accept:"application/json","Content-Type":"application/json"},mode:"no-cors"});i.interceptors.request.use(async e=>(e.metadata={requestTime:Date.now()},e),e=>Promise.reject(e)),i.interceptors.response.use(e=>{var a;return e.headers["x-request-time"]=null==(a=e.config.metadata)?void 0:a.requestTime,e},async e=>{let{offlineQueue:a}=await s.e(8836).then(s.bind(s,48836)),{offlineStorage:t}=await s.e(58).then(s.bind(s,60058));if(a.shouldQueue(e)){var r,i,n,o;let s=e.config;if(await a.addRequest(s.url,(null==(r=s.method)?void 0:r.toUpperCase())||"GET",s.data,s.headers),(null==(i=s.method)?void 0:i.toLowerCase())==="get")try{if(null==(n=s.url)?void 0:n.includes("/services")){let e=await t.getServices();if(e.length>0)return console.log("\uD83D\uDCF1 Serving services from offline cache"),{data:e,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:s}}if(null==(o=s.url)?void 0:o.includes("assigned-actions")){let e=new URLSearchParams(s.url.split("?")[1]).get("filter"),a="All";if(e)try{JSON.parse(decodeURIComponent(e))}catch(r){let e=s.url.split("/"),t=e.findIndex(e=>"assigned-actions"===e);-1!==t&&e[t+1]&&(a=e[t+1])}let r=await t.getActions(a);if(r.length>0)return console.log("\uD83D\uDCF1 Serving actions from offline cache"),{data:r,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:s}}}catch(e){console.error("❌ Error serving from cache:",e)}}return Promise.reject(e)});let n=i},38983:(e,a,s)=>{"use strict";s.d(a,{default:()=>o});var t=s(95155),r=s(6874),i=s.n(r);s(12115);let n=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],o=()=>(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,t.jsx)("div",{className:"container px-0",children:(0,t.jsx)("div",{className:"footer-nav position-relative",children:(0,t.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:n.map((e,a)=>(0,t.jsx)("li",{children:(0,t.jsxs)(i(),{href:"/".concat(e.link),children:[(0,t.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,t.jsx)("span",{children:e.title})]})},a))})})})})})},81359:(e,a,s)=>{"use strict";s.d(a,{A:()=>i,l:()=>r});let t=(0,s(51990).Z0)({name:"login",initialState:{isLogin:!1,user:null},reducers:{setLogin(e){e.isLogin=!0},setLogout(e){e.isLogin=!1,e.user=null},setUser(e,a){e.user=a.payload}}}),r=t.actions,i=t},81878:(e,a,s)=>{"use strict";s.d(a,{default:()=>l});var t=s(95155),r=s(6874),i=s.n(r),n=s(12115);let o=[{category:"Navigation",icon:"bi bi-list",description:"Modern header, footer, and sidebar nav.",items:[{name:"Header Variations",href:"/header-menu"},{name:"Footer Variations",href:"/footer-menu"},{name:"Left Sidebar Nav",href:"/sidebar-left-menu"},{name:"Right Sidebar Nav",href:"/sidebar-right-menu"}]},{category:"Notifications",icon:"bi bi-bell-fill",description:"Display alerts & notifications creatively.",items:[{name:"Alerts",href:"/alerts"},{name:"Toasts",href:"/toasts"},{name:"Online / Offline Detection",href:"/online-offline-detection"}]},{category:"Form Layouts",icon:"bi bi-ui-checks",description:"All form input components.",items:[{name:"Input",href:"/form-input"},{name:"Textarea",href:"/form-textarea"},{name:"Select",href:"/form-select"},{name:"Input Group",href:"/form-input-group"},{name:"Checkbox",href:"/form-check"},{name:"Radio",href:"/form-radio"},{name:"File Upload",href:"/form-file-upload"},{name:"Range",href:"/form-range"},{name:"Auto Complete",href:"/form-auto-complete"},{name:"Switches",href:"/form-switches"},{name:"Form Validation",href:"/form-validation"}]},{category:"UI Elements",icon:"bi bi-columns-gap",description:"Beautifully designed lots of UI elements.",items:[{name:"Accordion",href:"/accordion"},{name:"Badge",href:"/badge"},{name:"Button",href:"/button"},{name:"Breadcrumb",href:"/breadcrumb"},{name:"Timeline",href:"/timeline"},{name:"Card",href:"/card"},{name:"Image Gallery",href:"/image-gallery"},{name:"Hero Blocks",href:"/hero-blocks"},{name:"Tab",href:"/tab"},{name:"Offcanvas",href:"/offcanvas"},{name:"User Ratings",href:"/user-ratings"},{name:"Testimonials",href:"/testimonial"},{name:"Call to Action",href:"/call-to-action"},{name:"Partner Logo",href:"/partner-logo"}]},{category:"Helpers",icon:"bi bi-tools",description:"Quickly create any blocks with helpers.",items:[{name:"Borders",href:"/borders"},{name:"Colors",href:"/colors"},{name:"Dividers",href:"/dividers"},{name:"Embeds Video",href:"/embed-video"},{name:"Images",href:"/images"},{name:"List Group",href:"/list-group"},{name:"Modal",href:"/modal"},{name:"Pagination",href:"/pagination"},{name:"Progress Bar",href:"/progress-bar"},{name:"Scrollspy",href:"/scrollspy"},{name:"Spinners",href:"/spinners"},{name:"Stretched link",href:"/stretched-link"},{name:"Shadows",href:"/shadows"},{name:"Sizing",href:"/sizing"},{name:"Tooltips",href:"/tooltips"},{name:"Text truncation",href:"/text-truncation"},{name:"Typography",href:"/typography"},{name:"Text",href:"/text"}]},{category:"Carousels",icon:"bi bi-sliders",description:"Create a variety of carousels.",items:[{name:"Bootstrap Carousel",href:"/bootstrap-carousel"}]},{category:"Tables",icon:"bi bi-table",description:"Make responsive table layouts.",items:[{name:"Basic Table",href:"/basic-table"},{name:"Data Table",href:"/data-table"},{name:"Price Table",href:"/price-table"},{name:"Comparison Table",href:"/comparison-table"}]},{category:"Timer",icon:"bi bi-clock-history",description:"Countdown or countup your milestones.",items:[{name:"Count Down",href:"/countdown"},{name:"Counter Up",href:"/counterup"}]},{category:"Charts",icon:"bi bi-bar-chart",description:"Lots of charts for showing data.",items:[{name:"Area Chart",href:"/area-charts"},{name:"Column Chart",href:"/column-charts"},{name:"Line Chart",href:"/line-charts"},{name:"Pie Chart",href:"/pie-charts"}]}],l=()=>{let[e,a]=(0,n.useState)(""),s=o.map(a=>({...a,items:a.items.filter(a=>a.name.toLowerCase().includes(e.toLowerCase()))})).filter(e=>e.items.length>0);return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("div",{className:"page-content-wrapper py-3",id:"elementsSearchList",children:(0,t.jsxs)("div",{className:"container",children:[(0,t.jsx)("div",{className:"card",children:(0,t.jsx)("div",{className:"card-body p-3",children:(0,t.jsx)("div",{className:"form-group mb-0",children:(0,t.jsx)("input",{type:"text",id:"elementsSearchInput",placeholder:"Search element...",className:"form-control",value:e,onChange:e=>{a(e.target.value)}})})})}),s.map((e,a)=>(0,t.jsxs)(n.Fragment,{children:[(0,t.jsx)("div",{className:"affan-element-item",children:(0,t.jsxs)("div",{className:"element-heading-wrapper",children:[(0,t.jsx)("i",{className:e.icon}),(0,t.jsxs)("div",{className:"heading-text",children:[(0,t.jsx)("h6",{className:"mb-1",children:e.category}),(0,t.jsx)("span",{children:e.description})]})]})}),e.items.map((e,a)=>(0,t.jsxs)(i(),{className:"affan-element-item",href:e.href,children:[e.name,(0,t.jsx)("i",{className:"bi bi-caret-right-fill fz-12"})]},a))]},a))]})})})}},9e4:(e,a,s)=>{"use strict";s.d(a,{D:()=>r});var t=s(12115);let r=()=>{let[e,a]=(0,t.useState)("light"),[s,r]=(0,t.useState)(!1);(0,t.useEffect)(()=>{a(localStorage.getItem("theme")||"light"),r(!0)},[]),(0,t.useEffect)(()=>{s&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,s]);let i=(0,t.useCallback)(()=>{a(e=>"dark"===e?"light":"dark")},[]),n=(0,t.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let a=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(a),e.style.display="none")},20)},1e3))}i()},[i]);return{theme:e,toggleTheme:i,handleDarkModeToggle:n}}},92704:(e,a,s)=>{Promise.resolve().then(s.bind(s,81878)),Promise.resolve().then(s.bind(s,38983)),Promise.resolve().then(s.bind(s,17451))}},e=>{var a=a=>e(e.s=a);e.O(0,[6874,6078,1955,635,1531,8441,1684,7358],()=>a(92704)),_N_E=e.O()}]);