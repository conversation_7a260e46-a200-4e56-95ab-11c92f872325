(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5110],{8289:(e,s,a)=>{Promise.resolve().then(a.bind(a,53122))},53122:(e,s,a)=>{"use strict";a.d(s,{default:()=>d});var t=a(95155);a(12115);var r=a(38983),i=a(6874),l=a.n(i);let n=e=>{let{links:s,title:a}=e;return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("div",{className:"header-area",id:"headerArea",children:(0,t.jsx)("div",{className:"container",children:(0,t.jsxs)("div",{className:"header-content header-style-three position-relative d-flex align-items-center justify-content-between",children:[(0,t.jsx)("div",{className:"back-button",children:(0,t.jsx)(l(),{href:"/".concat(s),children:(0,t.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,t.jsx)("div",{className:"page-heading",children:(0,t.jsx)("h6",{className:"mb-0",children:a})}),(0,t.jsx)("div",{className:"user-profile-wrapper",children:(0,t.jsx)("a",{className:"user-profile-trigger-btn",href:"#",children:(0,t.jsx)("img",{src:"/assets/img/bg-img/2.jpg",alt:""})})})]})})})})};var c=a(22070);let d=()=>(a(81531),(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n,{links:"elements",title:"Left Sidebar"}),(0,t.jsx)(c.default,{home:"Home",elements:"Elements",title:"Left Sidebar",button_text:"Click the button for left sidebar"}),(0,t.jsx)(r.default,{})]}))}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,1955,1531,386,8441,1684,7358],()=>s(8289)),_N_E=e.O()}]);