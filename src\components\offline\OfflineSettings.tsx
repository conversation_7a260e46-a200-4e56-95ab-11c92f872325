"use client";

import React, { useState, useEffect } from 'react';
import { offlineAPI } from '@/services/offlineAPI';
import { offlineQueue, QueueStats } from '@/services/offlineQueue';
import { offlineStorage } from '@/services/offlineStorage';
import SyncStatus from './SyncStatus';

const OfflineSettings: React.FC = () => {
  const [isOnline, setIsOnline] = useState(true);
  const [storageInfo, setStorageInfo] = useState({ services: 0, actions: 0, queue: 0 });
  const [queueStats, setQueueStats] = useState<QueueStats>({ pending: 0, failed: 0, processing: false });
  const [lastSync, setLastSync] = useState<{ services: string; actions: string }>({
    services: 'Never',
    actions: 'Never'
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    updateStatus();
    
    // Subscribe to connection changes
    const unsubscribeConnection = offlineAPI.onConnectionChange((online) => {
      setIsOnline(online);
      updateStatus();
    });

    // Subscribe to queue updates
    const unsubscribeQueue = offlineQueue.subscribe(setQueueStats);

    return () => {
      unsubscribeConnection();
      unsubscribeQueue();
    };
  }, []);

  const updateStatus = async () => {
    try {
      const info = await offlineStorage.getStorageInfo();
      const servicesAge = await offlineAPI.getDataAge('services');
      const actionsAge = await offlineAPI.getDataAge('actions');
      
      setStorageInfo(info);
      setLastSync({ services: servicesAge, actions: actionsAge });
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const handleSyncAll = async () => {
    if (!isOnline) return;
    
    setLoading(true);
    try {
      await offlineAPI.syncAll();
      updateStatus();
    } catch (error) {
      console.error('Sync failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClearCache = async () => {
    if (confirm('Are you sure you want to clear all offline data? This cannot be undone.')) {
      setLoading(true);
      try {
        await offlineAPI.clearOfflineData();
        updateStatus();
      } catch (error) {
        console.error('Clear cache failed:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleRetryFailed = async () => {
    try {
      await offlineQueue.retryFailedRequests();
    } catch (error) {
      console.error('Retry failed:', error);
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="container-fluid px-3 py-4">
      {/* Header */}
      <div className="d-flex align-items-center mb-4">
        <div
          className="rounded-circle d-flex align-items-center justify-content-center me-3"
          style={{
            width: '40px',
            height: '40px',
            backgroundColor: '#f0f9ff',
            color: '#0ea5e9'
          }}
        >
          <i className="bi bi-cloud-arrow-down" style={{ fontSize: '18px' }}></i>
        </div>
        <div>
          <h5 className="mb-0" style={{ fontWeight: '600' }}>Offline Settings</h5>
          <small className="text-muted">Manage your offline data and sync preferences</small>
        </div>
      </div>

      {/* Connection Status */}
      <div className="bg-white rounded-3 p-3 mb-3 shadow-sm">
        <div className="d-flex align-items-center justify-content-between">
          <div className="d-flex align-items-center">
            <div
              className="rounded-circle d-flex align-items-center justify-content-center me-3"
              style={{
                width: '32px',
                height: '32px',
                backgroundColor: isOnline ? '#e8f5e8' : '#ffebee',
                color: isOnline ? '#2e7d32' : '#d32f2f'
              }}
            >
              <i className={`bi ${isOnline ? 'bi-wifi' : 'bi-wifi-off'}`} style={{ fontSize: '14px' }}></i>
            </div>
            <div>
              <div style={{ fontWeight: '500', fontSize: '14px' }}>
                {isOnline ? 'Online' : 'Offline'}
              </div>
              <small className="text-muted">
                {isOnline ? 'Connected to internet' : 'Using cached data'}
              </small>
            </div>
          </div>
          {isOnline && (
            <button
              className="btn btn-primary btn-sm"
              onClick={handleSyncAll}
              disabled={loading || queueStats.processing}
            >
              {loading || queueStats.processing ? (
                <>
                  <i className="bi bi-arrow-repeat spin me-1"></i>
                  Syncing...
                </>
              ) : (
                <>
                  <i className="bi bi-arrow-clockwise me-1"></i>
                  Sync Now
                </>
              )}
            </button>
          )}
        </div>
      </div>

      {/* Sync Status */}
      <SyncStatus className="mb-3" />

      {/* Storage Information */}
      <div className="bg-white rounded-3 p-3 mb-3 shadow-sm">
        <h6 className="mb-3" style={{ fontWeight: '600' }}>Offline Storage</h6>
        
        <div className="row g-3 mb-3">
          <div className="col-4">
            <div className="text-center">
              <div style={{ fontSize: '20px', fontWeight: '600', color: '#2196f3' }}>
                {storageInfo.services}
              </div>
              <small className="text-muted">Services</small>
              <div style={{ fontSize: '10px', color: '#666' }}>
                {lastSync.services}
              </div>
            </div>
          </div>
          <div className="col-4">
            <div className="text-center">
              <div style={{ fontSize: '20px', fontWeight: '600', color: '#4caf50' }}>
                {storageInfo.actions}
              </div>
              <small className="text-muted">Actions</small>
              <div style={{ fontSize: '10px', color: '#666' }}>
                {lastSync.actions}
              </div>
            </div>
          </div>
          <div className="col-4">
            <div className="text-center">
              <div style={{ fontSize: '20px', fontWeight: '600', color: '#ff9800' }}>
                {storageInfo.queue}
              </div>
              <small className="text-muted">Queued</small>
              <div style={{ fontSize: '10px', color: '#666' }}>
                Pending sync
              </div>
            </div>
          </div>
        </div>

        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-danger btn-sm flex-fill"
            onClick={handleClearCache}
            disabled={loading}
          >
            <i className="bi bi-trash me-1"></i>
            Clear Cache
          </button>
          {queueStats.failed > 0 && (
            <button
              className="btn btn-outline-warning btn-sm"
              onClick={handleRetryFailed}
            >
              <i className="bi bi-arrow-repeat me-1"></i>
              Retry Failed ({queueStats.failed})
            </button>
          )}
        </div>
      </div>

      {/* Offline Features */}
      <div className="bg-white rounded-3 p-3 shadow-sm">
        <h6 className="mb-3" style={{ fontWeight: '600' }}>Offline Features</h6>
        
        <div className="list-group list-group-flush">
          <div className="list-group-item d-flex align-items-center px-0">
            <i className="bi bi-check-circle text-success me-3"></i>
            <div>
              <div style={{ fontSize: '14px', fontWeight: '500' }}>View Services</div>
              <small className="text-muted">Access your services while offline</small>
            </div>
          </div>
          <div className="list-group-item d-flex align-items-center px-0">
            <i className="bi bi-check-circle text-success me-3"></i>
            <div>
              <div style={{ fontSize: '14px', fontWeight: '500' }}>Browse Actions</div>
              <small className="text-muted">View and filter your actions offline</small>
            </div>
          </div>
          <div className="list-group-item d-flex align-items-center px-0">
            <i className="bi bi-check-circle text-success me-3"></i>
            <div>
              <div style={{ fontSize: '14px', fontWeight: '500' }}>Auto Sync</div>
              <small className="text-muted">Automatic sync when connection returns</small>
            </div>
          </div>
          <div className="list-group-item d-flex align-items-center px-0">
            <i className="bi bi-check-circle text-success me-3"></i>
            <div>
              <div style={{ fontSize: '14px', fontWeight: '500' }}>Queue Requests</div>
              <small className="text-muted">Failed requests are queued for retry</small>
            </div>
          </div>
        </div>
      </div>

      {/* Spinning animation */}
      <style jsx>{`
        .spin {
          animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default OfflineSettings;
