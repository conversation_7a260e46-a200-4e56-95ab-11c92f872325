import React, { useState, useEffect } from 'react';
import { fetchFullImageUrl, fetchDataUrlForImage } from './imageUtils';
import ImageDisplay from './Gallery';

interface ImageComponentProps {
    fileName: string;
    size?: number;
    name?: boolean;
}

const ImageComponent: React.FC<ImageComponentProps> = ({ fileName, size = 100, name = false }) => {
    const [fileUrl, setFileUrl] = useState<string | null>(null);
    const [dataUrl, setDataUrl] = useState<string | null>(null);

    useEffect(() => {
        const getFileUrl = async () => {
            try {
                const url = await fetchFullImageUrl(fileName);
                setFileUrl(url);

                // Check if the file is an image before fetching data URL
                const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
                const extension = fileName.split('.').pop()?.toLowerCase();

                if (extension && imageExtensions.includes(extension)) {
                    const dataUrl = await fetchDataUrlForImage(url);
                    setDataUrl(dataUrl);
                }
            } catch (error) {
                console.error('Error fetching file or data URL:', error);
            }
        };

        getFileUrl();
    }, [fileName]);

    const cleanFileName = (fileName: string): string => {
        return fileName.replace(/^\d+[\s-_]*/, '');
    };

    const checkFileType = (fileName: string): 'image' | 'pdf' | 'xls' | 'other' => {
        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        const pdfExtensions = ['pdf'];
        const xlsExtensions = ['xls', 'xlsx'];
        const extension = fileName.split('.').pop()?.toLowerCase();

        if (!extension) return 'other';

        if (imageExtensions.includes(extension)) return 'image';
        if (pdfExtensions.includes(extension)) return 'pdf';
        if (xlsExtensions.includes(extension)) return 'xls';

        return 'other';
    };

    if (!fileUrl) {
        return <p>Loading...</p>;
    }

    const fileType = checkFileType(fileName);
    const cleanedFileName = cleanFileName(fileName);

    switch (fileType) {
        case 'image':
            return (
                <div className="d-flex flex-column align-items-center" style={{ padding: '4px' }}>
                    {dataUrl ? (
                        <ImageDisplay imageSrc={dataUrl} />
                    ) : (
                        <div
                            className="d-flex align-items-center justify-content-center bg-light border rounded"
                            style={{
                                width: size,
                                height: size
                            }}
                        >
                            <div className="spinner-border spinner-border-sm text-primary" role="status">
                                <span className="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    )}

                    {name && (
                        <p style={{ wordBreak: 'break-word', textAlign: 'center', fontSize: '10px', margin: '2px 0 0 0' }}>{cleanedFileName}</p>
                    )}
                </div>
            );
        case 'pdf':
            return (
                <div className="d-flex flex-column align-items-center p-2">
                    <a href={fileUrl} target="_blank" rel="noopener noreferrer">
                        <i className="bi bi-file-pdf-fill fs-1 text-danger"></i>
                    </a>
                    {name && (
                        <p style={{ wordBreak: 'break-word', textAlign: 'center' }}>{cleanedFileName}</p>
                    )}
                </div>
            );
        case 'xls':
            return (
                <div className="d-flex flex-column align-items-center p-2">
                    <a href={fileUrl} target="_blank" rel="noopener noreferrer">
                        <i className="bi bi-file-excel-fill fs-1 text-success"></i>
                    </a>
                    {name && (
                        <p style={{ wordBreak: 'break-word', textAlign: 'center' }}>{cleanedFileName}</p>
                    )}
                </div>
            );
        case 'other':
        default:
            return (
                <div className="d-flex flex-column align-items-center p-2">
                    <a href={fileUrl} target="_blank" rel="noopener noreferrer">
                        <i className="bi bi-file-earmark-fill fs-1 text-secondary"></i>
                    </a>
                    {name && (
                        <p style={{ wordBreak: 'break-word', textAlign: 'center' }}>{cleanedFileName}</p>
                    )}
                </div>
            );
    }
};

export default ImageComponent;
