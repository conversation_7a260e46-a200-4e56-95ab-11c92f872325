// IndexedDB wrapper for offline data storage
export interface ServiceType {
  id: string;
  name: string;
  description: string;
  maskName: string;
  applicability: string;
  status: boolean;
  url: string;
  created: string;
  updated: string;
  mobileShortName: string;
  color: string;
  icon: string;
  lastUpdated?: number;
}

export interface ActionType {
  id: string;
  actionType: string;
  application: string;
  applicationId: string;
  counter: number;
  created: string;
  description: string;
  dueDate: string;
  maskId: string;
  observationCategory: string;
  observationType: string;
  status: string;
  actionToBeTaken: string;
  submittedBy?: { firstName?: string };
  applicationDetails?: any;
  lastUpdated?: number;
}

export interface OfflineQueueItem {
  id: string;
  url: string;
  method: string;
  data?: any;
  headers?: Record<string, string>;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
}

class OfflineStorage {
  private dbName = 'AcuiZenOfflineDB';
  private version = 1;
  private db: IDBDatabase | null = null;

  // Store names
  private stores = {
    services: 'services',
    actions: 'actions',
    queue: 'offlineQueue',
    metadata: 'metadata'
  };

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => {
        console.error('❌ Failed to open IndexedDB:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('✅ IndexedDB initialized successfully');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        console.log('🔄 Upgrading IndexedDB schema');

        // Services store
        if (!db.objectStoreNames.contains(this.stores.services)) {
          const servicesStore = db.createObjectStore(this.stores.services, { keyPath: 'id' });
          servicesStore.createIndex('maskName', 'maskName', { unique: false });
          servicesStore.createIndex('lastUpdated', 'lastUpdated', { unique: false });
        }

        // Actions store
        if (!db.objectStoreNames.contains(this.stores.actions)) {
          const actionsStore = db.createObjectStore(this.stores.actions, { keyPath: 'id' });
          actionsStore.createIndex('application', 'application', { unique: false });
          actionsStore.createIndex('status', 'status', { unique: false });
          actionsStore.createIndex('dueDate', 'dueDate', { unique: false });
          actionsStore.createIndex('lastUpdated', 'lastUpdated', { unique: false });
        }

        // Offline queue store
        if (!db.objectStoreNames.contains(this.stores.queue)) {
          const queueStore = db.createObjectStore(this.stores.queue, { keyPath: 'id' });
          queueStore.createIndex('timestamp', 'timestamp', { unique: false });
          queueStore.createIndex('retryCount', 'retryCount', { unique: false });
        }

        // Metadata store for sync timestamps
        if (!db.objectStoreNames.contains(this.stores.metadata)) {
          db.createObjectStore(this.stores.metadata, { keyPath: 'key' });
        }
      };
    });
  }

  // Services methods
  async saveServices(services: ServiceType[]): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const transaction = this.db.transaction([this.stores.services], 'readwrite');
    const store = transaction.objectStore(this.stores.services);
    const timestamp = Date.now();

    for (const service of services) {
      await store.put({ ...service, lastUpdated: timestamp });
    }

    await this.setMetadata('servicesLastSync', timestamp);
    console.log(`💾 Saved ${services.length} services to offline storage`);
  }

  async getServices(): Promise<ServiceType[]> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.stores.services], 'readonly');
      const store = transaction.objectStore(this.stores.services);
      const request = store.getAll();

      request.onsuccess = () => {
        console.log(`📱 Retrieved ${request.result.length} services from offline storage`);
        resolve(request.result);
      };

      request.onerror = () => reject(request.error);
    });
  }

  // Actions methods
  async saveActions(actions: ActionType[]): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const transaction = this.db.transaction([this.stores.actions], 'readwrite');
    const store = transaction.objectStore(this.stores.actions);
    const timestamp = Date.now();

    for (const action of actions) {
      await store.put({ ...action, lastUpdated: timestamp });
    }

    await this.setMetadata('actionsLastSync', timestamp);
    console.log(`💾 Saved ${actions.length} actions to offline storage`);
  }

  async getActions(filter?: string): Promise<ActionType[]> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.stores.actions], 'readonly');
      const store = transaction.objectStore(this.stores.actions);
      const request = store.getAll();

      request.onsuccess = () => {
        let results = request.result;
        
        if (filter && filter !== 'All') {
          results = results.filter(action => action.application === filter);
        }

        console.log(`📱 Retrieved ${results.length} actions from offline storage`);
        resolve(results);
      };

      request.onerror = () => reject(request.error);
    });
  }

  // Offline queue methods
  async addToQueue(item: Omit<OfflineQueueItem, 'id' | 'timestamp' | 'retryCount'>): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const queueItem: OfflineQueueItem = {
      ...item,
      id: `queue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      retryCount: 0
    };

    const transaction = this.db.transaction([this.stores.queue], 'readwrite');
    const store = transaction.objectStore(this.stores.queue);
    await store.add(queueItem);

    console.log('📤 Added item to offline queue:', queueItem.url);
  }

  async getQueueItems(): Promise<OfflineQueueItem[]> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.stores.queue], 'readonly');
      const store = transaction.objectStore(this.stores.queue);
      const request = store.getAll();

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async removeFromQueue(id: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const transaction = this.db.transaction([this.stores.queue], 'readwrite');
    const store = transaction.objectStore(this.stores.queue);
    await store.delete(id);

    console.log('✅ Removed item from offline queue:', id);
  }

  async updateQueueItemRetryCount(id: string, retryCount: number): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const transaction = this.db.transaction([this.stores.queue], 'readwrite');
    const store = transaction.objectStore(this.stores.queue);
    const getRequest = store.get(id);

    getRequest.onsuccess = () => {
      const item = getRequest.result;
      if (item) {
        item.retryCount = retryCount;
        store.put(item);
      }
    };
  }

  // Metadata methods
  async setMetadata(key: string, value: any): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const transaction = this.db.transaction([this.stores.metadata], 'readwrite');
    const store = transaction.objectStore(this.stores.metadata);
    await store.put({ key, value, timestamp: Date.now() });
  }

  async getMetadata(key: string): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.stores.metadata], 'readonly');
      const store = transaction.objectStore(this.stores.metadata);
      const request = store.get(key);

      request.onsuccess = () => {
        resolve(request.result?.value || null);
      };

      request.onerror = () => reject(request.error);
    });
  }

  // Utility methods
  async clearAllData(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const storeNames = Object.values(this.stores);
    const transaction = this.db.transaction(storeNames, 'readwrite');

    for (const storeName of storeNames) {
      const store = transaction.objectStore(storeName);
      await store.clear();
    }

    console.log('🗑️ Cleared all offline data');
  }

  async getStorageInfo(): Promise<{ services: number; actions: number; queue: number }> {
    if (!this.db) throw new Error('Database not initialized');

    const services = await this.getServices();
    const actions = await this.getActions();
    const queue = await this.getQueueItems();

    return {
      services: services.length,
      actions: actions.length,
      queue: queue.length
    };
  }

  isOnline(): boolean {
    return navigator.onLine;
  }
}

// Export singleton instance
export const offlineStorage = new OfflineStorage();

// Initialize on import
if (typeof window !== 'undefined') {
  offlineStorage.init().catch(console.error);
}
