(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1531],{81531:function(e,t,i){e.exports=function(e){"use strict";let t=function(e){let t=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e){for(let i in e)if("default"!==i){let s=Object.getOwnPropertyDescriptor(e,i);Object.defineProperty(t,i,s.get?s:{enumerable:!0,get:()=>e[i]})}}return t.default=e,Object.freeze(t)}(e),i=new Map,s={set(e,t,s){i.has(e)||i.set(e,new Map);let n=i.get(e);if(!n.has(t)&&0!==n.size)return void console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(n.keys())[0]}.`);n.set(t,s)},get:(e,t)=>i.has(e)&&i.get(e).get(t)||null,remove(e,t){if(!i.has(e))return;let s=i.get(e);s.delete(t),0===s.size&&i.delete(e)}},n="transitionend",r=e=>(e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,(e,t)=>`#${CSS.escape(t)}`)),e),o=e=>null==e?`${e}`:Object.prototype.toString.call(e).match(/\s([a-z]+)/i)[1].toLowerCase(),l=e=>{do e+=Math.floor(1e6*Math.random());while(document.getElementById(e));return e},a=e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:i}=window.getComputedStyle(e),s=Number.parseFloat(t),n=Number.parseFloat(i);return s||n?(t=t.split(",")[0],i=i.split(",")[0],(Number.parseFloat(t)+Number.parseFloat(i))*1e3):0},h=e=>{e.dispatchEvent(new Event(n))},c=e=>!!e&&"object"==typeof e&&(void 0!==e.jquery&&(e=e[0]),void 0!==e.nodeType),d=e=>c(e)?e.jquery?e[0]:e:"string"==typeof e&&e.length>0?document.querySelector(r(e)):null,u=e=>{if(!c(e)||0===e.getClientRects().length)return!1;let t="visible"===getComputedStyle(e).getPropertyValue("visibility"),i=e.closest("details:not([open])");if(!i)return t;if(i!==e){let t=e.closest("summary");if(t&&t.parentNode!==i||null===t)return!1}return t},_=e=>!!(!e||e.nodeType!==Node.ELEMENT_NODE||e.classList.contains("disabled"))||(void 0!==e.disabled?e.disabled:e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled")),g=e=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){let t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?g(e.parentNode):null},f=()=>{},m=e=>{e.offsetHeight},p=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,b=[],v=e=>{"loading"===document.readyState?(b.length||document.addEventListener("DOMContentLoaded",()=>{for(let e of b)e()}),b.push(e)):e()},y=()=>"rtl"===document.documentElement.dir,w=e=>{v(()=>{let t=p();if(t){let i=e.NAME,s=t.fn[i];t.fn[i]=e.jQueryInterface,t.fn[i].Constructor=e,t.fn[i].noConflict=()=>(t.fn[i]=s,e.jQueryInterface)}})},A=(e,t=[],i=e)=>"function"==typeof e?e(...t):i,E=(e,t,i=!0)=>{if(!i)return void A(e);let s=a(t)+5,r=!1,o=({target:i})=>{i===t&&(r=!0,t.removeEventListener(n,o),A(e))};t.addEventListener(n,o),setTimeout(()=>{r||h(t)},s)},C=(e,t,i,s)=>{let n=e.length,r=e.indexOf(t);return -1===r?!i&&s?e[n-1]:e[0]:(r+=i?1:-1,s&&(r=(r+n)%n),e[Math.max(0,Math.min(r,n-1))])},T=/[^.]*(?=\..*)\.|.*/,k=/\..*/,$=/::\d+$/,S={},L=1,O={mouseenter:"mouseover",mouseleave:"mouseout"},I=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function N(e,t){return t&&`${t}::${L++}`||e.uidEvent||L++}function D(e){let t=N(e);return e.uidEvent=t,S[t]=S[t]||{},S[t]}function P(e,t,i=null){return Object.values(e).find(e=>e.callable===t&&e.delegationSelector===i)}function x(e,t,i){let s="string"==typeof t,n=F(e);return I.has(n)||(n=e),[s,s?i:t||i,n]}function M(e,t,i,s,n){var r,o;if("string"!=typeof t||!e)return;let[l,a,h]=x(t,i,s);if(t in O){let e;e=a,a=function(t){if(!t.relatedTarget||t.relatedTarget!==t.delegateTarget&&!t.delegateTarget.contains(t.relatedTarget))return e.call(this,t)}}let c=D(e),d=c[h]||(c[h]={}),u=P(d,a,l?i:null);if(u){u.oneOff=u.oneOff&&n;return}let _=N(a,t.replace(T,"")),g=l?(r=a,function t(s){let n=e.querySelectorAll(i);for(let{target:o}=s;o&&o!==this;o=o.parentNode)for(let l of n)if(l===o)return H(s,{delegateTarget:o}),t.oneOff&&z.off(e,s.type,i,r),r.apply(o,[s])}):(o=a,function t(i){return H(i,{delegateTarget:e}),t.oneOff&&z.off(e,i.type,o),o.apply(e,[i])});g.delegationSelector=l?i:null,g.callable=a,g.oneOff=n,g.uidEvent=_,d[_]=g,e.addEventListener(h,g,l)}function j(e,t,i,s,n){let r=P(t[i],s,n);r&&(e.removeEventListener(i,r,!!n),delete t[i][r.uidEvent])}function F(e){return O[e=e.replace(k,"")]||e}let z={on(e,t,i,s){M(e,t,i,s,!1)},one(e,t,i,s){M(e,t,i,s,!0)},off(e,t,i,s){if("string"!=typeof t||!e)return;let[n,r,o]=x(t,i,s),l=o!==t,a=D(e),h=a[o]||{},c=t.startsWith(".");if(void 0!==r){if(!Object.keys(h).length)return;j(e,a,o,r,n?i:null);return}if(c)for(let i of Object.keys(a)){var d=t.slice(1);for(let[t,s]of Object.entries(a[i]||{}))t.includes(d)&&j(e,a,i,s.callable,s.delegationSelector)}for(let[i,s]of Object.entries(h)){let n=i.replace($,"");(!l||t.includes(n))&&j(e,a,o,s.callable,s.delegationSelector)}},trigger(e,t,i){if("string"!=typeof t||!e)return null;let s=p(),n=F(t),r=null,o=!0,l=!0,a=!1;t!==n&&s&&(r=s.Event(t,i),s(e).trigger(r),o=!r.isPropagationStopped(),l=!r.isImmediatePropagationStopped(),a=r.isDefaultPrevented());let h=H(new Event(t,{bubbles:o,cancelable:!0}),i);return a&&h.preventDefault(),l&&e.dispatchEvent(h),h.defaultPrevented&&r&&r.preventDefault(),h}};function H(e,t={}){for(let[i,s]of Object.entries(t))try{e[i]=s}catch(t){Object.defineProperty(e,i,{configurable:!0,get:()=>s})}return e}function q(e){if("true"===e)return!0;if("false"===e)return!1;if(e===Number(e).toString())return Number(e);if(""===e||"null"===e)return null;if("string"!=typeof e)return e;try{return JSON.parse(decodeURIComponent(e))}catch(t){return e}}function W(e){return e.replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`)}let R={setDataAttribute(e,t,i){e.setAttribute(`data-bs-${W(t)}`,i)},removeDataAttribute(e,t){e.removeAttribute(`data-bs-${W(t)}`)},getDataAttributes(e){if(!e)return{};let t={};for(let i of Object.keys(e.dataset).filter(e=>e.startsWith("bs")&&!e.startsWith("bsConfig"))){let s=i.replace(/^bs/,"");t[s=s.charAt(0).toLowerCase()+s.slice(1,s.length)]=q(e.dataset[i])}return t},getDataAttribute:(e,t)=>q(e.getAttribute(`data-bs-${W(t)}`))};class B{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(e,t){let i=c(t)?R.getDataAttribute(t,"config"):{};return{...this.constructor.Default,..."object"==typeof i?i:{},...c(t)?R.getDataAttributes(t):{},..."object"==typeof e?e:{}}}_typeCheckConfig(e,t=this.constructor.DefaultType){for(let[i,s]of Object.entries(t)){let t=e[i],n=c(t)?"element":o(t);if(!new RegExp(s).test(n))throw TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${i}" provided type "${n}" but expected type "${s}".`)}}}class K extends B{constructor(e,t){if(super(),!(e=d(e)))return;this._element=e,this._config=this._getConfig(t),s.set(this._element,this.constructor.DATA_KEY,this)}dispose(){for(let e of(s.remove(this._element,this.constructor.DATA_KEY),z.off(this._element,this.constructor.EVENT_KEY),Object.getOwnPropertyNames(this)))this[e]=null}_queueCallback(e,t,i=!0){E(e,t,i)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(e){return s.get(d(e),this.DATA_KEY)}static getOrCreateInstance(e,t={}){return this.getInstance(e)||new this(e,"object"==typeof t?t:null)}static get VERSION(){return"5.3.3"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}let V=e=>{let t=e.getAttribute("data-bs-target");if(!t||"#"===t){let i=e.getAttribute("href");if(!i||!i.includes("#")&&!i.startsWith("."))return null;i.includes("#")&&!i.startsWith("#")&&(i=`#${i.split("#")[1]}`),t=i&&"#"!==i?i.trim():null}return t?t.split(",").map(e=>r(e)).join(","):null},Q={find:(e,t=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(t,e)),findOne:(e,t=document.documentElement)=>Element.prototype.querySelector.call(t,e),children:(e,t)=>[].concat(...e.children).filter(e=>e.matches(t)),parents(e,t){let i=[],s=e.parentNode.closest(t);for(;s;)i.push(s),s=s.parentNode.closest(t);return i},prev(e,t){let i=e.previousElementSibling;for(;i;){if(i.matches(t))return[i];i=i.previousElementSibling}return[]},next(e,t){let i=e.nextElementSibling;for(;i;){if(i.matches(t))return[i];i=i.nextElementSibling}return[]},focusableChildren(e){let t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(e=>`${e}:not([tabindex^="-"])`).join(",");return this.find(t,e).filter(e=>!_(e)&&u(e))},getSelectorFromElement(e){let t=V(e);return t&&Q.findOne(t)?t:null},getElementFromSelector(e){let t=V(e);return t?Q.findOne(t):null},getMultipleElementsFromSelector(e){let t=V(e);return t?Q.find(t):[]}},X=(e,t="hide")=>{let i=`click.dismiss${e.EVENT_KEY}`,s=e.NAME;z.on(document,i,`[data-bs-dismiss="${s}"]`,function(i){if(["A","AREA"].includes(this.tagName)&&i.preventDefault(),_(this))return;let n=Q.getElementFromSelector(this)||this.closest(`.${s}`);e.getOrCreateInstance(n)[t]()})},Y=".bs.alert",U=`close${Y}`,G=`closed${Y}`;class J extends K{static get NAME(){return"alert"}close(){if(z.trigger(this._element,U).defaultPrevented)return;this._element.classList.remove("show");let e=this._element.classList.contains("fade");this._queueCallback(()=>this._destroyElement(),this._element,e)}_destroyElement(){this._element.remove(),z.trigger(this._element,G),this.dispose()}static jQueryInterface(e){return this.each(function(){let t=J.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e](this)}})}}X(J,"close"),w(J);let Z='[data-bs-toggle="button"]',ee="click.bs.button.data-api";class et extends K{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(e){return this.each(function(){let t=et.getOrCreateInstance(this);"toggle"===e&&t[e]()})}}z.on(document,ee,Z,e=>{e.preventDefault();let t=e.target.closest(Z);et.getOrCreateInstance(t).toggle()}),w(et);let ei=".bs.swipe",es=`touchstart${ei}`,en=`touchmove${ei}`,er=`touchend${ei}`,eo=`pointerdown${ei}`,el=`pointerup${ei}`,ea={endCallback:null,leftCallback:null,rightCallback:null},eh={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class ec extends B{constructor(e,t){if(super(),this._element=e,!e||!ec.isSupported())return;this._config=this._getConfig(t),this._deltaX=0,this._supportPointerEvents=!!window.PointerEvent,this._initEvents()}static get Default(){return ea}static get DefaultType(){return eh}static get NAME(){return"swipe"}dispose(){z.off(this._element,ei)}_start(e){if(!this._supportPointerEvents){this._deltaX=e.touches[0].clientX;return}this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX)}_end(e){this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX-this._deltaX),this._handleSwipe(),A(this._config.endCallback)}_move(e){this._deltaX=e.touches&&e.touches.length>1?0:e.touches[0].clientX-this._deltaX}_handleSwipe(){let e=Math.abs(this._deltaX);if(e<=40)return;let t=e/this._deltaX;this._deltaX=0,t&&A(t>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(z.on(this._element,eo,e=>this._start(e)),z.on(this._element,el,e=>this._end(e)),this._element.classList.add("pointer-event")):(z.on(this._element,es,e=>this._start(e)),z.on(this._element,en,e=>this._move(e)),z.on(this._element,er,e=>this._end(e)))}_eventIsPointerPenTouch(e){return this._supportPointerEvents&&("pen"===e.pointerType||"touch"===e.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}let ed=".bs.carousel",eu=".data-api",e_="next",eg="prev",ef="left",em="right",ep=`slide${ed}`,eb=`slid${ed}`,ev=`keydown${ed}`,ey=`mouseenter${ed}`,ew=`mouseleave${ed}`,eA=`dragstart${ed}`,eE=`load${ed}${eu}`,eC=`click${ed}${eu}`,eT="carousel",ek="active",e$=".active",eS=".carousel-item",eL=e$+eS,eO={ArrowLeft:em,ArrowRight:ef},eI={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},eN={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class eD extends K{constructor(e,t){super(e,t),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=Q.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===eT&&this.cycle()}static get Default(){return eI}static get DefaultType(){return eN}static get NAME(){return"carousel"}next(){this._slide(e_)}nextWhenVisible(){!document.hidden&&u(this._element)&&this.next()}prev(){this._slide(eg)}pause(){this._isSliding&&h(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){if(this._config.ride){if(this._isSliding)return void z.one(this._element,eb,()=>this.cycle());this.cycle()}}to(e){let t=this._getItems();if(e>t.length-1||e<0)return;if(this._isSliding)return void z.one(this._element,eb,()=>this.to(e));let i=this._getItemIndex(this._getActive());i!==e&&this._slide(e>i?e_:eg,t[e])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(e){return e.defaultInterval=e.interval,e}_addEventListeners(){this._config.keyboard&&z.on(this._element,ev,e=>this._keydown(e)),"hover"===this._config.pause&&(z.on(this._element,ey,()=>this.pause()),z.on(this._element,ew,()=>this._maybeEnableCycle())),this._config.touch&&ec.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(let e of Q.find(".carousel-item img",this._element))z.on(e,eA,e=>e.preventDefault());this._swipeHelper=new ec(this._element,{leftCallback:()=>this._slide(this._directionToOrder(ef)),rightCallback:()=>this._slide(this._directionToOrder(em)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),500+this._config.interval))}})}_keydown(e){if(/input|textarea/i.test(e.target.tagName))return;let t=eO[e.key];t&&(e.preventDefault(),this._slide(this._directionToOrder(t)))}_getItemIndex(e){return this._getItems().indexOf(e)}_setActiveIndicatorElement(e){if(!this._indicatorsElement)return;let t=Q.findOne(e$,this._indicatorsElement);t.classList.remove(ek),t.removeAttribute("aria-current");let i=Q.findOne(`[data-bs-slide-to="${e}"]`,this._indicatorsElement);i&&(i.classList.add(ek),i.setAttribute("aria-current","true"))}_updateInterval(){let e=this._activeElement||this._getActive();if(!e)return;let t=Number.parseInt(e.getAttribute("data-bs-interval"),10);this._config.interval=t||this._config.defaultInterval}_slide(e,t=null){if(this._isSliding)return;let i=this._getActive(),s=e===e_,n=t||C(this._getItems(),i,s,this._config.wrap);if(n===i)return;let r=this._getItemIndex(n),o=t=>z.trigger(this._element,t,{relatedTarget:n,direction:this._orderToDirection(e),from:this._getItemIndex(i),to:r});if(o(ep).defaultPrevented||!i||!n)return;let l=!!this._interval;this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(r),this._activeElement=n;let a=s?"carousel-item-start":"carousel-item-end",h=s?"carousel-item-next":"carousel-item-prev";n.classList.add(h),m(n),i.classList.add(a),n.classList.add(a),this._queueCallback(()=>{n.classList.remove(a,h),n.classList.add(ek),i.classList.remove(ek,h,a),this._isSliding=!1,o(eb)},i,this._isAnimated()),l&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return Q.findOne(eL,this._element)}_getItems(){return Q.find(eS,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(e){return y()?e===ef?eg:e_:e===ef?e_:eg}_orderToDirection(e){return y()?e===eg?ef:em:e===eg?em:ef}static jQueryInterface(e){return this.each(function(){let t=eD.getOrCreateInstance(this,e);if("number"==typeof e)return void t.to(e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e]()}})}}z.on(document,eC,"[data-bs-slide], [data-bs-slide-to]",function(e){let t=Q.getElementFromSelector(this);if(!t||!t.classList.contains(eT))return;e.preventDefault();let i=eD.getOrCreateInstance(t),s=this.getAttribute("data-bs-slide-to");if(s){i.to(s),i._maybeEnableCycle();return}if("next"===R.getDataAttribute(this,"slide")){i.next(),i._maybeEnableCycle();return}i.prev(),i._maybeEnableCycle()}),z.on(window,eE,()=>{for(let e of Q.find('[data-bs-ride="carousel"]'))eD.getOrCreateInstance(e)}),w(eD);let eP=".bs.collapse",ex=`show${eP}`,eM=`shown${eP}`,ej=`hide${eP}`,eF=`hidden${eP}`,ez=`click${eP}.data-api`,eH="show",eq="collapse",eW="collapsing",eR=`:scope .${eq} .${eq}`,eB='[data-bs-toggle="collapse"]',eK={parent:null,toggle:!0},eV={parent:"(null|element)",toggle:"boolean"};class eQ extends K{constructor(e,t){for(let i of(super(e,t),this._isTransitioning=!1,this._triggerArray=[],Q.find(eB))){let e=Q.getSelectorFromElement(i),t=Q.find(e).filter(e=>e===this._element);null!==e&&t.length&&this._triggerArray.push(i)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return eK}static get DefaultType(){return eV}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let e=[];if(this._config.parent&&(e=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter(e=>e!==this._element).map(e=>eQ.getOrCreateInstance(e,{toggle:!1}))),e.length&&e[0]._isTransitioning||z.trigger(this._element,ex).defaultPrevented)return;for(let t of e)t.hide();let t=this._getDimension();this._element.classList.remove(eq),this._element.classList.add(eW),this._element.style[t]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;let i=t[0].toUpperCase()+t.slice(1),s=`scroll${i}`;this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(eW),this._element.classList.add(eq,eH),this._element.style[t]="",z.trigger(this._element,eM)},this._element,!0),this._element.style[t]=`${this._element[s]}px`}hide(){if(this._isTransitioning||!this._isShown()||z.trigger(this._element,ej).defaultPrevented)return;let e=this._getDimension();for(let t of(this._element.style[e]=`${this._element.getBoundingClientRect()[e]}px`,m(this._element),this._element.classList.add(eW),this._element.classList.remove(eq,eH),this._triggerArray)){let e=Q.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0,this._element.style[e]="",this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(eW),this._element.classList.add(eq),z.trigger(this._element,eF)},this._element,!0)}_isShown(e=this._element){return e.classList.contains(eH)}_configAfterMerge(e){return e.toggle=!!e.toggle,e.parent=d(e.parent),e}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(this._config.parent)for(let e of this._getFirstLevelChildren(eB)){let t=Q.getElementFromSelector(e);t&&this._addAriaAndCollapsedClass([e],this._isShown(t))}}_getFirstLevelChildren(e){let t=Q.find(eR,this._config.parent);return Q.find(e,this._config.parent).filter(e=>!t.includes(e))}_addAriaAndCollapsedClass(e,t){if(e.length)for(let i of e)i.classList.toggle("collapsed",!t),i.setAttribute("aria-expanded",t)}static jQueryInterface(e){let t={};return"string"==typeof e&&/show|hide/.test(e)&&(t.toggle=!1),this.each(function(){let i=eQ.getOrCreateInstance(this,t);if("string"==typeof e){if(void 0===i[e])throw TypeError(`No method named "${e}"`);i[e]()}})}}z.on(document,ez,eB,function(e){for(let t of(("A"===e.target.tagName||e.delegateTarget&&"A"===e.delegateTarget.tagName)&&e.preventDefault(),Q.getMultipleElementsFromSelector(this)))eQ.getOrCreateInstance(t,{toggle:!1}).toggle()}),w(eQ);let eX="dropdown",eY=".bs.dropdown",eU=".data-api",eG="ArrowDown",eJ=`hide${eY}`,eZ=`hidden${eY}`,e0=`show${eY}`,e1=`shown${eY}`,e3=`click${eY}${eU}`,e5=`keydown${eY}${eU}`,e2=`keyup${eY}${eU}`,e4="show",e6='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',e8=`${e6}.${e4}`,e9=".dropdown-menu",e7=y()?"top-end":"top-start",te=y()?"top-start":"top-end",tt=y()?"bottom-end":"bottom-start",ti=y()?"bottom-start":"bottom-end",ts=y()?"left-start":"right-start",tn=y()?"right-start":"left-start",tr={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},to={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class tl extends K{constructor(e,t){super(e,t),this._popper=null,this._parent=this._element.parentNode,this._menu=Q.next(this._element,e9)[0]||Q.prev(this._element,e9)[0]||Q.findOne(e9,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return tr}static get DefaultType(){return to}static get NAME(){return eX}toggle(){return this._isShown()?this.hide():this.show()}show(){if(_(this._element)||this._isShown())return;let e={relatedTarget:this._element};if(!z.trigger(this._element,e0,e).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(let e of[].concat(...document.body.children))z.on(e,"mouseover",f);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(e4),this._element.classList.add(e4),z.trigger(this._element,e1,e)}}hide(){if(_(this._element)||!this._isShown())return;let e={relatedTarget:this._element};this._completeHide(e)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(e){if(!z.trigger(this._element,eJ,e).defaultPrevented){if("ontouchstart"in document.documentElement)for(let e of[].concat(...document.body.children))z.off(e,"mouseover",f);this._popper&&this._popper.destroy(),this._menu.classList.remove(e4),this._element.classList.remove(e4),this._element.setAttribute("aria-expanded","false"),R.removeDataAttribute(this._menu,"popper"),z.trigger(this._element,eZ,e)}}_getConfig(e){if("object"==typeof(e=super._getConfig(e)).reference&&!c(e.reference)&&"function"!=typeof e.reference.getBoundingClientRect)throw TypeError(`${eX.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return e}_createPopper(){if(void 0===t)throw TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let e=this._element;"parent"===this._config.reference?e=this._parent:c(this._config.reference)?e=d(this._config.reference):"object"==typeof this._config.reference&&(e=this._config.reference);let i=this._getPopperConfig();this._popper=t.createPopper(e,this._menu,i)}_isShown(){return this._menu.classList.contains(e4)}_getPlacement(){let e=this._parent;if(e.classList.contains("dropend"))return ts;if(e.classList.contains("dropstart"))return tn;if(e.classList.contains("dropup-center"))return"top";if(e.classList.contains("dropdown-center"))return"bottom";let t="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return e.classList.contains("dropup")?t?te:e7:t?ti:tt}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){let{offset:e}=this._config;return"string"==typeof e?e.split(",").map(e=>Number.parseInt(e,10)):"function"==typeof e?t=>e(t,this._element):e}_getPopperConfig(){let e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(R.setDataAttribute(this._menu,"popper","static"),e.modifiers=[{name:"applyStyles",enabled:!1}]),{...e,...A(this._config.popperConfig,[e])}}_selectMenuItem({key:e,target:t}){let i=Q.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter(e=>u(e));i.length&&C(i,t,e===eG,!i.includes(t)).focus()}static jQueryInterface(e){return this.each(function(){let t=tl.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw TypeError(`No method named "${e}"`);t[e]()}})}static clearMenus(e){if(2!==e.button&&("keyup"!==e.type||"Tab"===e.key))for(let t of Q.find(e8)){let i=tl.getInstance(t);if(!i||!1===i._config.autoClose)continue;let s=e.composedPath(),n=s.includes(i._menu);if(s.includes(i._element)||"inside"===i._config.autoClose&&!n||"outside"===i._config.autoClose&&n||i._menu.contains(e.target)&&("keyup"===e.type&&"Tab"===e.key||/input|select|option|textarea|form/i.test(e.target.tagName)))continue;let r={relatedTarget:i._element};"click"===e.type&&(r.clickEvent=e),i._completeHide(r)}}static dataApiKeydownHandler(e){let t=/input|textarea/i.test(e.target.tagName),i="Escape"===e.key,s=["ArrowUp",eG].includes(e.key);if(!s&&!i||t&&!i)return;e.preventDefault();let n=this.matches(e6)?this:Q.prev(this,e6)[0]||Q.next(this,e6)[0]||Q.findOne(e6,e.delegateTarget.parentNode),r=tl.getOrCreateInstance(n);if(s){e.stopPropagation(),r.show(),r._selectMenuItem(e);return}r._isShown()&&(e.stopPropagation(),r.hide(),n.focus())}}z.on(document,e5,e6,tl.dataApiKeydownHandler),z.on(document,e5,e9,tl.dataApiKeydownHandler),z.on(document,e3,tl.clearMenus),z.on(document,e2,tl.clearMenus),z.on(document,e3,e6,function(e){e.preventDefault(),tl.getOrCreateInstance(this).toggle()}),w(tl);let ta="backdrop",th="show",tc=`mousedown.bs.${ta}`,td={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},tu={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class t_ extends B{constructor(e){super(),this._config=this._getConfig(e),this._isAppended=!1,this._element=null}static get Default(){return td}static get DefaultType(){return tu}static get NAME(){return ta}show(e){if(!this._config.isVisible)return void A(e);this._append();let t=this._getElement();this._config.isAnimated&&m(t),t.classList.add(th),this._emulateAnimation(()=>{A(e)})}hide(e){if(!this._config.isVisible)return void A(e);this._getElement().classList.remove(th),this._emulateAnimation(()=>{this.dispose(),A(e)})}dispose(){this._isAppended&&(z.off(this._element,tc),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){let e=document.createElement("div");e.className=this._config.className,this._config.isAnimated&&e.classList.add("fade"),this._element=e}return this._element}_configAfterMerge(e){return e.rootElement=d(e.rootElement),e}_append(){if(this._isAppended)return;let e=this._getElement();this._config.rootElement.append(e),z.on(e,tc,()=>{A(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(e){E(e,this._getElement(),this._config.isAnimated)}}let tg=".bs.focustrap",tf=`focusin${tg}`,tm=`keydown.tab${tg}`,tp="backward",tb={autofocus:!0,trapElement:null},tv={autofocus:"boolean",trapElement:"element"};class ty extends B{constructor(e){super(),this._config=this._getConfig(e),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return tb}static get DefaultType(){return tv}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),z.off(document,tg),z.on(document,tf,e=>this._handleFocusin(e)),z.on(document,tm,e=>this._handleKeydown(e)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,z.off(document,tg))}_handleFocusin(e){let{trapElement:t}=this._config;if(e.target===document||e.target===t||t.contains(e.target))return;let i=Q.focusableChildren(t);0===i.length?t.focus():this._lastTabNavDirection===tp?i[i.length-1].focus():i[0].focus()}_handleKeydown(e){"Tab"===e.key&&(this._lastTabNavDirection=e.shiftKey?tp:"forward")}}let tw=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",tA=".sticky-top",tE="padding-right",tC="margin-right";class tT{constructor(){this._element=document.body}getWidth(){let e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}hide(){let e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,tE,t=>t+e),this._setElementAttributes(tw,tE,t=>t+e),this._setElementAttributes(tA,tC,t=>t-e)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,tE),this._resetElementAttributes(tw,tE),this._resetElementAttributes(tA,tC)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(e,t,i){let s=this.getWidth();this._applyManipulationCallback(e,e=>{if(e!==this._element&&window.innerWidth>e.clientWidth+s)return;this._saveInitialAttribute(e,t);let n=window.getComputedStyle(e).getPropertyValue(t);e.style.setProperty(t,`${i(Number.parseFloat(n))}px`)})}_saveInitialAttribute(e,t){let i=e.style.getPropertyValue(t);i&&R.setDataAttribute(e,t,i)}_resetElementAttributes(e,t){this._applyManipulationCallback(e,e=>{let i=R.getDataAttribute(e,t);if(null===i)return void e.style.removeProperty(t);R.removeDataAttribute(e,t),e.style.setProperty(t,i)})}_applyManipulationCallback(e,t){if(c(e))return void t(e);for(let i of Q.find(e,this._element))t(i)}}let tk=".bs.modal",t$=`hide${tk}`,tS=`hidePrevented${tk}`,tL=`hidden${tk}`,tO=`show${tk}`,tI=`shown${tk}`,tN=`resize${tk}`,tD=`click.dismiss${tk}`,tP=`mousedown.dismiss${tk}`,tx=`keydown.dismiss${tk}`,tM=`click${tk}.data-api`,tj="modal-open",tF="show",tz="modal-static",tH={backdrop:!0,focus:!0,keyboard:!0},tq={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class tW extends K{constructor(e,t){super(e,t),this._dialog=Q.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new tT,this._addEventListeners()}static get Default(){return tH}static get DefaultType(){return tq}static get NAME(){return"modal"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||this._isTransitioning||z.trigger(this._element,tO,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(tj),this._adjustDialog(),this._backdrop.show(()=>this._showElement(e)))}hide(){this._isShown&&!this._isTransitioning&&(z.trigger(this._element,t$).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(tF),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated())))}dispose(){z.off(window,tk),z.off(this._dialog,tk),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new t_({isVisible:!!this._config.backdrop,isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new ty({trapElement:this._element})}_showElement(e){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;let t=Q.findOne(".modal-body",this._dialog);t&&(t.scrollTop=0),m(this._element),this._element.classList.add(tF),this._queueCallback(()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,z.trigger(this._element,tI,{relatedTarget:e})},this._dialog,this._isAnimated())}_addEventListeners(){z.on(this._element,tx,e=>{if("Escape"===e.key){if(this._config.keyboard)return void this.hide();this._triggerBackdropTransition()}}),z.on(window,tN,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),z.on(this._element,tP,e=>{z.one(this._element,tD,t=>{if(this._element===e.target&&this._element===t.target){if("static"===this._config.backdrop)return void this._triggerBackdropTransition();this._config.backdrop&&this.hide()}})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(tj),this._resetAdjustments(),this._scrollBar.reset(),z.trigger(this._element,tL)})}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(z.trigger(this._element,tS).defaultPrevented)return;let e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._element.style.overflowY;"hidden"===t||this._element.classList.contains(tz)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(tz),this._queueCallback(()=>{this._element.classList.remove(tz),this._queueCallback(()=>{this._element.style.overflowY=t},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){let e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._scrollBar.getWidth(),i=t>0;if(i&&!e){let e=y()?"paddingLeft":"paddingRight";this._element.style[e]=`${t}px`}if(!i&&e){let e=y()?"paddingRight":"paddingLeft";this._element.style[e]=`${t}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,t){return this.each(function(){let i=tW.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===i[e])throw TypeError(`No method named "${e}"`);i[e](t)}})}}z.on(document,tM,'[data-bs-toggle="modal"]',function(e){let t=Q.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),z.one(t,tO,e=>{e.defaultPrevented||z.one(t,tL,()=>{u(this)&&this.focus()})});let i=Q.findOne(".modal.show");i&&tW.getInstance(i).hide(),tW.getOrCreateInstance(t).toggle(this)}),X(tW),w(tW);let tR=".bs.offcanvas",tB=".data-api",tK=`load${tR}${tB}`,tV="show",tQ="showing",tX="hiding",tY=".offcanvas.show",tU=`show${tR}`,tG=`shown${tR}`,tJ=`hide${tR}`,tZ=`hidePrevented${tR}`,t0=`hidden${tR}`,t1=`resize${tR}`,t3=`click${tR}${tB}`,t5=`keydown.dismiss${tR}`,t2={backdrop:!0,keyboard:!0,scroll:!1},t4={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class t6 extends K{constructor(e,t){super(e,t),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return t2}static get DefaultType(){return t4}static get NAME(){return"offcanvas"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||z.trigger(this._element,tU,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||new tT().hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(tQ),this._queueCallback(()=>{(!this._config.scroll||this._config.backdrop)&&this._focustrap.activate(),this._element.classList.add(tV),this._element.classList.remove(tQ),z.trigger(this._element,tG,{relatedTarget:e})},this._element,!0))}hide(){this._isShown&&!z.trigger(this._element,tJ).defaultPrevented&&(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(tX),this._backdrop.hide(),this._queueCallback(()=>{this._element.classList.remove(tV,tX),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||new tT().reset(),z.trigger(this._element,t0)},this._element,!0))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){let e=!!this._config.backdrop;return new t_({className:"offcanvas-backdrop",isVisible:e,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:e?()=>{if("static"===this._config.backdrop)return void z.trigger(this._element,tZ);this.hide()}:null})}_initializeFocusTrap(){return new ty({trapElement:this._element})}_addEventListeners(){z.on(this._element,t5,e=>{if("Escape"===e.key){if(this._config.keyboard)return void this.hide();z.trigger(this._element,tZ)}})}static jQueryInterface(e){return this.each(function(){let t=t6.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e](this)}})}}z.on(document,t3,'[data-bs-toggle="offcanvas"]',function(e){let t=Q.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&e.preventDefault(),_(this))return;z.one(t,t0,()=>{u(this)&&this.focus()});let i=Q.findOne(tY);i&&i!==t&&t6.getInstance(i).hide(),t6.getOrCreateInstance(t).toggle(this)}),z.on(window,tK,()=>{for(let e of Q.find(tY))t6.getOrCreateInstance(e).show()}),z.on(window,t1,()=>{for(let e of Q.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(e).position&&t6.getOrCreateInstance(e).hide()}),X(t6),w(t6);let t8={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},t9=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),t7=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,ie=(e,t)=>{let i=e.nodeName.toLowerCase();return t.includes(i)?!t9.has(i)||!!t7.test(e.nodeValue):t.filter(e=>e instanceof RegExp).some(e=>e.test(i))},it={allowList:t8,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},ii={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},is={entry:"(string|element|function|null)",selector:"(string|element)"};class ir extends B{constructor(e){super(),this._config=this._getConfig(e)}static get Default(){return it}static get DefaultType(){return ii}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map(e=>this._resolvePossibleFunction(e)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(e){return this._checkContent(e),this._config.content={...this._config.content,...e},this}toHtml(){let e=document.createElement("div");for(let[t,i]of(e.innerHTML=this._maybeSanitize(this._config.template),Object.entries(this._config.content)))this._setContent(e,i,t);let t=e.children[0],i=this._resolvePossibleFunction(this._config.extraClass);return i&&t.classList.add(...i.split(" ")),t}_typeCheckConfig(e){super._typeCheckConfig(e),this._checkContent(e.content)}_checkContent(e){for(let[t,i]of Object.entries(e))super._typeCheckConfig({selector:t,entry:i},is)}_setContent(e,t,i){let s=Q.findOne(i,e);if(s){if(!(t=this._resolvePossibleFunction(t)))return void s.remove();if(c(t))return void this._putElementInTemplate(d(t),s);if(this._config.html){s.innerHTML=this._maybeSanitize(t);return}s.textContent=t}}_maybeSanitize(e){return this._config.sanitize?function(e,t,i){if(!e.length)return e;if(i&&"function"==typeof i)return i(e);let s=new window.DOMParser().parseFromString(e,"text/html");for(let e of[].concat(...s.body.querySelectorAll("*"))){let i=e.nodeName.toLowerCase();if(!Object.keys(t).includes(i)){e.remove();continue}let s=[].concat(...e.attributes),n=[].concat(t["*"]||[],t[i]||[]);for(let t of s)ie(t,n)||e.removeAttribute(t.nodeName)}return s.body.innerHTML}(e,this._config.allowList,this._config.sanitizeFn):e}_resolvePossibleFunction(e){return A(e,[this])}_putElementInTemplate(e,t){if(this._config.html){t.innerHTML="",t.append(e);return}t.textContent=e.textContent}}let io=new Set(["sanitize","allowList","sanitizeFn"]),il="fade",ia="show",ih=".modal",ic="hide.bs.modal",id="hover",iu="focus",i_={AUTO:"auto",TOP:"top",RIGHT:y()?"left":"right",BOTTOM:"bottom",LEFT:y()?"right":"left"},ig={allowList:t8,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},im={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class ip extends K{constructor(e,i){if(void 0===t)throw TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(e,i),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return ig}static get DefaultType(){return im}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){if(this._isEnabled){if(this._activeTrigger.click=!this._activeTrigger.click,this._isShown())return void this._leave();this._enter()}}dispose(){clearTimeout(this._timeout),z.off(this._element.closest(ih),ic,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw Error("Please use show on visible elements");if(!(this._isWithContent()&&this._isEnabled))return;let e=z.trigger(this._element,this.constructor.eventName("show")),t=(g(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(e.defaultPrevented||!t)return;this._disposePopper();let i=this._getTipElement();this._element.setAttribute("aria-describedby",i.getAttribute("id"));let{container:s}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(s.append(i),z.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(i),i.classList.add(ia),"ontouchstart"in document.documentElement)for(let e of[].concat(...document.body.children))z.on(e,"mouseover",f);this._queueCallback(()=>{z.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1},this.tip,this._isAnimated())}hide(){if(this._isShown()&&!z.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(ia),"ontouchstart"in document.documentElement)for(let e of[].concat(...document.body.children))z.off(e,"mouseover",f);this._activeTrigger.click=!1,this._activeTrigger[iu]=!1,this._activeTrigger[id]=!1,this._isHovered=null,this._queueCallback(()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),z.trigger(this._element,this.constructor.eventName("hidden")))},this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(e){let t=this._getTemplateFactory(e).toHtml();if(!t)return null;t.classList.remove(il,ia),t.classList.add(`bs-${this.constructor.NAME}-auto`);let i=l(this.constructor.NAME).toString();return t.setAttribute("id",i),this._isAnimated()&&t.classList.add(il),t}setContent(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new ir({...this._config,content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{".tooltip-inner":this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(il)}_isShown(){return this.tip&&this.tip.classList.contains(ia)}_createPopper(e){let i=i_[A(this._config.placement,[this,e,this._element]).toUpperCase()];return t.createPopper(this._element,e,this._getPopperConfig(i))}_getOffset(){let{offset:e}=this._config;return"string"==typeof e?e.split(",").map(e=>Number.parseInt(e,10)):"function"==typeof e?t=>e(t,this._element):e}_resolvePossibleFunction(e){return A(e,[this._element])}_getPopperConfig(e){let t={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:e=>{this._getTipElement().setAttribute("data-popper-placement",e.state.placement)}}]};return{...t,...A(this._config.popperConfig,[t])}}_setListeners(){for(let e of this._config.trigger.split(" "))if("click"===e)z.on(this._element,this.constructor.eventName("click"),this._config.selector,e=>{this._initializeOnDelegatedTarget(e).toggle()});else if("manual"!==e){let t=e===id?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),i=e===id?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");z.on(this._element,t,this._config.selector,e=>{let t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusin"===e.type?iu:id]=!0,t._enter()}),z.on(this._element,i,this._config.selector,e=>{let t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusout"===e.type?iu:id]=t._element.contains(e.relatedTarget),t._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},z.on(this._element.closest(ih),ic,this._hideModalHandler)}_fixTitle(){let e=this._element.getAttribute("title");e&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}_enter(){if(this._isShown()||this._isHovered){this._isHovered=!0;return}this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show)}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(e,t){clearTimeout(this._timeout),this._timeout=setTimeout(e,t)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(e){let t=R.getDataAttributes(this._element);for(let e of Object.keys(t))io.has(e)&&delete t[e];return e={...t,..."object"==typeof e&&e?e:{}},e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e.container=!1===e.container?document.body:d(e.container),"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),e}_getDelegateConfig(){let e={};for(let[t,i]of Object.entries(this._config))this.constructor.Default[t]!==i&&(e[t]=i);return e.selector=!1,e.trigger="manual",e}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(e){return this.each(function(){let t=ip.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw TypeError(`No method named "${e}"`);t[e]()}})}}w(ip);let ib={...ip.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},iv={...ip.DefaultType,content:"(null|string|element|function)"};class iy extends ip{static get Default(){return ib}static get DefaultType(){return iv}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{".popover-header":this._getTitle(),".popover-body":this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(e){return this.each(function(){let t=iy.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw TypeError(`No method named "${e}"`);t[e]()}})}}w(iy);let iw=".bs.scrollspy",iA=`activate${iw}`,iE=`click${iw}`,iC=`load${iw}.data-api`,iT="active",ik="[href]",i$=".nav-link",iS=`${i$}, .nav-item > ${i$}, .list-group-item`,iL={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},iO={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class iI extends K{constructor(e,t){super(e,t),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return iL}static get DefaultType(){return iO}static get NAME(){return"scrollspy"}refresh(){for(let e of(this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver(),this._observableSections.values()))this._observer.observe(e)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(e){return e.target=d(e.target)||document.body,e.rootMargin=e.offset?`${e.offset}px 0px -30%`:e.rootMargin,"string"==typeof e.threshold&&(e.threshold=e.threshold.split(",").map(e=>Number.parseFloat(e))),e}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(z.off(this._config.target,iE),z.on(this._config.target,iE,ik,e=>{let t=this._observableSections.get(e.target.hash);if(t){e.preventDefault();let i=this._rootElement||window,s=t.offsetTop-this._element.offsetTop;if(i.scrollTo)return void i.scrollTo({top:s,behavior:"smooth"});i.scrollTop=s}}))}_getNewObserver(){return new IntersectionObserver(e=>this._observerCallback(e),{root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin})}_observerCallback(e){let t=e=>this._targetLinks.get(`#${e.target.id}`),i=e=>{this._previousScrollData.visibleEntryTop=e.target.offsetTop,this._process(t(e))},s=(this._rootElement||document.documentElement).scrollTop,n=s>=this._previousScrollData.parentScrollTop;for(let r of(this._previousScrollData.parentScrollTop=s,e)){if(!r.isIntersecting){this._activeTarget=null,this._clearActiveClass(t(r));continue}let e=r.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(n&&e){if(i(r),!s)return;continue}n||e||i(r)}}_initializeTargetsAndObservables(){for(let e of(this._targetLinks=new Map,this._observableSections=new Map,Q.find(ik,this._config.target))){if(!e.hash||_(e))continue;let t=Q.findOne(decodeURI(e.hash),this._element);u(t)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,t))}}_process(e){this._activeTarget!==e&&(this._clearActiveClass(this._config.target),this._activeTarget=e,e.classList.add(iT),this._activateParents(e),z.trigger(this._element,iA,{relatedTarget:e}))}_activateParents(e){if(e.classList.contains("dropdown-item"))return void Q.findOne(".dropdown-toggle",e.closest(".dropdown")).classList.add(iT);for(let t of Q.parents(e,".nav, .list-group"))for(let e of Q.prev(t,iS))e.classList.add(iT)}_clearActiveClass(e){for(let t of(e.classList.remove(iT),Q.find(`${ik}.${iT}`,e)))t.classList.remove(iT)}static jQueryInterface(e){return this.each(function(){let t=iI.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e]()}})}}z.on(window,iC,()=>{for(let e of Q.find('[data-bs-spy="scroll"]'))iI.getOrCreateInstance(e)}),w(iI);let iN=".bs.tab",iD=`hide${iN}`,iP=`hidden${iN}`,ix=`show${iN}`,iM=`shown${iN}`,ij=`click${iN}`,iF=`keydown${iN}`,iz=`load${iN}`,iH="ArrowRight",iq="ArrowDown",iW="Home",iR="active",iB="fade",iK="show",iV=".dropdown-toggle",iQ=`:not(${iV})`,iX=`.nav-link${iQ}, .list-group-item${iQ}, [role="tab"]${iQ}`,iY='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',iU=`${iX}, ${iY}`,iG=`.${iR}[data-bs-toggle="tab"], .${iR}[data-bs-toggle="pill"], .${iR}[data-bs-toggle="list"]`;class iJ extends K{constructor(e){if(super(e),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),!this._parent)return;this._setInitialAttributes(this._parent,this._getChildren()),z.on(this._element,iF,e=>this._keydown(e))}static get NAME(){return"tab"}show(){let e=this._element;if(this._elemIsActive(e))return;let t=this._getActiveElem(),i=t?z.trigger(t,iD,{relatedTarget:e}):null;z.trigger(e,ix,{relatedTarget:t}).defaultPrevented||i&&i.defaultPrevented||(this._deactivate(t,e),this._activate(e,t))}_activate(e,t){e&&(e.classList.add(iR),this._activate(Q.getElementFromSelector(e)),this._queueCallback(()=>{if("tab"!==e.getAttribute("role"))return void e.classList.add(iK);e.removeAttribute("tabindex"),e.setAttribute("aria-selected",!0),this._toggleDropDown(e,!0),z.trigger(e,iM,{relatedTarget:t})},e,e.classList.contains(iB)))}_deactivate(e,t){e&&(e.classList.remove(iR),e.blur(),this._deactivate(Q.getElementFromSelector(e)),this._queueCallback(()=>{if("tab"!==e.getAttribute("role"))return void e.classList.remove(iK);e.setAttribute("aria-selected",!1),e.setAttribute("tabindex","-1"),this._toggleDropDown(e,!1),z.trigger(e,iP,{relatedTarget:t})},e,e.classList.contains(iB)))}_keydown(e){let t;if(!["ArrowLeft",iH,"ArrowUp",iq,iW,"End"].includes(e.key))return;e.stopPropagation(),e.preventDefault();let i=this._getChildren().filter(e=>!_(e));if([iW,"End"].includes(e.key))t=i[e.key===iW?0:i.length-1];else{let s=[iH,iq].includes(e.key);t=C(i,e.target,s,!0)}t&&(t.focus({preventScroll:!0}),iJ.getOrCreateInstance(t).show())}_getChildren(){return Q.find(iU,this._parent)}_getActiveElem(){return this._getChildren().find(e=>this._elemIsActive(e))||null}_setInitialAttributes(e,t){for(let i of(this._setAttributeIfNotExists(e,"role","tablist"),t))this._setInitialAttributesOnChild(i)}_setInitialAttributesOnChild(e){e=this._getInnerElement(e);let t=this._elemIsActive(e),i=this._getOuterElement(e);e.setAttribute("aria-selected",t),i!==e&&this._setAttributeIfNotExists(i,"role","presentation"),t||e.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(e,"role","tab"),this._setInitialAttributesOnTargetPanel(e)}_setInitialAttributesOnTargetPanel(e){let t=Q.getElementFromSelector(e);t&&(this._setAttributeIfNotExists(t,"role","tabpanel"),e.id&&this._setAttributeIfNotExists(t,"aria-labelledby",`${e.id}`))}_toggleDropDown(e,t){let i=this._getOuterElement(e);if(!i.classList.contains("dropdown"))return;let s=(e,s)=>{let n=Q.findOne(e,i);n&&n.classList.toggle(s,t)};s(iV,iR),s(".dropdown-menu",iK),i.setAttribute("aria-expanded",t)}_setAttributeIfNotExists(e,t,i){e.hasAttribute(t)||e.setAttribute(t,i)}_elemIsActive(e){return e.classList.contains(iR)}_getInnerElement(e){return e.matches(iU)?e:Q.findOne(iU,e)}_getOuterElement(e){return e.closest(".nav-item, .list-group-item")||e}static jQueryInterface(e){return this.each(function(){let t=iJ.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e]()}})}}z.on(document,ij,iY,function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),_(this)||iJ.getOrCreateInstance(this).show()}),z.on(window,iz,()=>{for(let e of Q.find(iG))iJ.getOrCreateInstance(e)}),w(iJ);let iZ=".bs.toast",i0=`mouseover${iZ}`,i1=`mouseout${iZ}`,i3=`focusin${iZ}`,i5=`focusout${iZ}`,i2=`hide${iZ}`,i4=`hidden${iZ}`,i6=`show${iZ}`,i8=`shown${iZ}`,i9="hide",i7="show",se="showing",st={animation:"boolean",autohide:"boolean",delay:"number"},si={animation:!0,autohide:!0,delay:5e3};class ss extends K{constructor(e,t){super(e,t),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return si}static get DefaultType(){return st}static get NAME(){return"toast"}show(){z.trigger(this._element,i6).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(i9),m(this._element),this._element.classList.add(i7,se),this._queueCallback(()=>{this._element.classList.remove(se),z.trigger(this._element,i8),this._maybeScheduleHide()},this._element,this._config.animation))}hide(){this.isShown()&&!z.trigger(this._element,i2).defaultPrevented&&(this._element.classList.add(se),this._queueCallback(()=>{this._element.classList.add(i9),this._element.classList.remove(se,i7),z.trigger(this._element,i4)},this._element,this._config.animation))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(i7),super.dispose()}isShown(){return this._element.classList.contains(i7)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(e,t){switch(e.type){case"mouseover":case"mouseout":this._hasMouseInteraction=t;break;case"focusin":case"focusout":this._hasKeyboardInteraction=t}if(t)return void this._clearTimeout();let i=e.relatedTarget;this._element===i||this._element.contains(i)||this._maybeScheduleHide()}_setListeners(){z.on(this._element,i0,e=>this._onInteraction(e,!0)),z.on(this._element,i1,e=>this._onInteraction(e,!1)),z.on(this._element,i3,e=>this._onInteraction(e,!0)),z.on(this._element,i5,e=>this._onInteraction(e,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each(function(){let t=ss.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw TypeError(`No method named "${e}"`);t[e](this)}})}}return X(ss),w(ss),{Alert:J,Button:et,Carousel:eD,Collapse:eQ,Dropdown:tl,Modal:tW,Offcanvas:t6,Popover:iy,ScrollSpy:iI,Tab:iJ,Toast:ss,Tooltip:ip}}(i(57984))}}]);