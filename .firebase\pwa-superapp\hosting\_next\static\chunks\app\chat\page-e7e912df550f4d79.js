(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8457],{26611:(s,e,a)=>{"use strict";a.d(e,{default:()=>t});var i=a(95155),l=a(12115);let c=s=>{let{isAudioOpen:e,setIsAudioOpen:a}=s;return(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("div",{className:"calling-popup-wrap ".concat(e?"screen-active":""),id:"callingPopup",children:(0,i.jsxs)("div",{className:"calling-popup-body bg-primary",children:[(0,i.jsx)("div",{className:"user-thumbnail mb-3",children:(0,i.jsx)("img",{src:"/assets/img/bg-img/2.jpg",alt:""})}),(0,i.jsx)("div",{className:"call-icon d-block mb-2",children:(0,i.jsx)("i",{className:"bi bi-telephone text-white"})}),(0,i.jsx)("h6",{className:"mb-5 text-white",children:"Affan is calling..."}),(0,i.jsxs)("div",{className:"button-group",children:[(0,i.jsx)("a",{className:"btn btn-lg btn-danger rounded-pill me-2",id:"callDecline",href:"#",onClick:()=>a(!1),children:"Decline"}),(0,i.jsx)("a",{className:"btn btn-lg btn-success rounded-pill ms-2",href:"#",children:"Accept"})]})]})})})};var r=a(6874),n=a.n(r);let d=s=>{let{setIsVideoOpen:e,isVideoOpen:a}=s;return(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("div",{className:"video-calling-popup-wrap ".concat(a?"screen-active":""),id:"videoCallingPopup",children:(0,i.jsxs)("div",{className:"video-calling-popup-body bg-overlay",style:{backgroundImage:"url(/assets/img/bg-img/2.jpg)"},children:[(0,i.jsx)("div",{className:"user-thumbnail mb-3",children:(0,i.jsx)("img",{src:"/assets/img/bg-img/2.jpg",alt:""})}),(0,i.jsx)("div",{className:"video-icon d-block mb-1",children:(0,i.jsx)("i",{className:"bi bi-camera-video text-white"})}),(0,i.jsx)("h6",{className:"mb-5 text-white",children:"Affan is video calling..."}),(0,i.jsxs)("div",{className:"button-group",children:[(0,i.jsx)("a",{className:"btn btn-lg btn-danger rounded-pill me-3",id:"videoCallDecline",href:"#",onClick:()=>e(!1),children:"Decline"}),(0,i.jsx)(n(),{className:"btn btn-lg btn-success rounded-pill ms-3",href:"/video-call",children:"Accept"})]})]})})})},t=()=>{let[s,e]=(0,l.useState)(!1),[a,r]=(0,l.useState)(!1);return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"header-area",id:"headerArea",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,i.jsxs)("div",{className:"chat-user--info d-flex align-items-center",children:[(0,i.jsx)("div",{className:"back-button",children:(0,i.jsx)(n(),{href:"/chat-users",children:(0,i.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,i.jsxs)("div",{className:"user-thumbnail-name",children:[(0,i.jsx)("img",{src:"/assets/img/bg-img/2.jpg",alt:""}),(0,i.jsxs)("div",{className:"info ms-1",children:[(0,i.jsx)("p",{children:"Affan"}),(0,i.jsx)("span",{className:"active-status",children:"Active Now"})]})]})]}),(0,i.jsxs)("div",{className:"call-video-wrapper d-flex align-items-center",children:[(0,i.jsx)("div",{className:"video-icon me-3",children:(0,i.jsx)("a",{className:"text-secondary",onClick:()=>e(!s),id:"videoCallingButton",href:"#",children:(0,i.jsx)("i",{className:"bi bi-camera-video"})})}),(0,i.jsx)("div",{className:"call-icon me-3",children:(0,i.jsx)("a",{className:"text-secondary",onClick:()=>r(!a),id:"callingButton",href:"#",children:(0,i.jsx)("i",{className:"bi bi-telephone"})})}),(0,i.jsx)("div",{className:"info-icon",children:(0,i.jsx)("a",{className:"text-secondary",href:"#",children:(0,i.jsx)("i",{className:"bi bi-info-circle"})})})]})]})})}),(0,i.jsx)(d,{isVideoOpen:s,setIsVideoOpen:e}),(0,i.jsx)(c,{isAudioOpen:a,setIsAudioOpen:r})]})}},40144:(s,e,a)=>{Promise.resolve().then(a.bind(a,58136)),Promise.resolve().then(a.bind(a,26611))},58136:(s,e,a)=>{"use strict";a.d(e,{default:()=>c});var i=a(95155),l=a(12115);let c=()=>{let[s,e]=(0,l.useState)(!1);return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"page-content-wrapper py-3",id:"chat-wrapper",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"chat-content-wrap",children:[(0,i.jsxs)("div",{className:"single-chat-item",children:[(0,i.jsxs)("div",{className:"user-avatar mt-1",children:[(0,i.jsx)("span",{className:"name-first-letter",children:"A"}),(0,i.jsx)("img",{src:"/assets/img/bg-img/2.jpg",alt:""})]}),(0,i.jsxs)("div",{className:"user-message",children:[(0,i.jsxs)("div",{className:"message-content",children:[(0,i.jsx)("div",{className:"single-message",children:(0,i.jsx)("p",{children:"Hello, Are you there?"})}),(0,i.jsxs)("div",{className:"dropstart",children:[(0,i.jsx)("button",{className:"btn btn-options dropdown-toggle",type:"button","data-bs-toggle":"dropdown","aria-expanded":"false",children:(0,i.jsx)("i",{className:"bi bi-three-dots-vertical"})}),(0,i.jsxs)("ul",{className:"dropdown-menu",children:[(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-reply"}),"Reply"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-forward"}),"Forward"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-trash"}),"Remove"]})})]})]})]}),(0,i.jsx)("div",{className:"message-time-status",children:(0,i.jsx)("div",{className:"sent-time",children:"11:39 AM"})})]})]}),(0,i.jsxs)("div",{className:"single-chat-item outgoing",children:[(0,i.jsxs)("div",{className:"user-avatar mt-1",children:[(0,i.jsx)("span",{className:"name-first-letter",children:"A"}),(0,i.jsx)("img",{src:"/assets/img/bg-img/user3.png",alt:""})]}),(0,i.jsxs)("div",{className:"user-message",children:[(0,i.jsxs)("div",{className:"message-content",children:[(0,i.jsx)("div",{className:"single-message",children:(0,i.jsx)("p",{children:"Yes, How can I help you?"})}),(0,i.jsxs)("div",{className:"dropstart",children:[(0,i.jsx)("button",{className:"btn btn-options dropdown-toggle",type:"button","data-bs-toggle":"dropdown","aria-expanded":"false",children:(0,i.jsx)("i",{className:"bi bi-three-dots-vertical"})}),(0,i.jsxs)("ul",{className:"dropdown-menu",children:[(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-reply"}),"Reply"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-forward"}),"Forward"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-trash"}),"Remove"]})})]})]})]}),(0,i.jsxs)("div",{className:"message-time-status",children:[(0,i.jsx)("div",{className:"sent-time",children:"11:46 AM"}),(0,i.jsx)("div",{className:"sent-status seen",children:(0,i.jsx)("i",{className:"bi bi-check"})})]})]})]}),(0,i.jsxs)("div",{className:"single-chat-item",children:[(0,i.jsxs)("div",{className:"user-avatar mt-1",children:[(0,i.jsx)("span",{className:"name-first-letter",children:"A"}),(0,i.jsx)("img",{src:"/assets/img/bg-img/2.jpg",alt:""})]}),(0,i.jsxs)("div",{className:"user-message",children:[(0,i.jsxs)("div",{className:"message-content",children:[(0,i.jsx)("div",{className:"single-message",children:(0,i.jsx)("p",{children:"I want to buy your Affan template."})}),(0,i.jsxs)("div",{className:"dropstart",children:[(0,i.jsx)("button",{className:"btn btn-options dropdown-toggle",type:"button","data-bs-toggle":"dropdown","aria-expanded":"false",children:(0,i.jsx)("i",{className:"bi bi-three-dots-vertical"})}),(0,i.jsxs)("ul",{className:"dropdown-menu",children:[(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-reply"}),"Reply"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-forward"}),"Forward"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-trash"}),"Remove"]})})]})]})]}),(0,i.jsxs)("div",{className:"message-content",children:[(0,i.jsx)("div",{className:"single-message",children:(0,i.jsx)("p",{children:"Affan - PWA Mobile HTML Template"})}),(0,i.jsxs)("div",{className:"dropstart",children:[(0,i.jsx)("button",{className:"btn btn-options dropdown-toggle",type:"button","data-bs-toggle":"dropdown","aria-expanded":"false",children:(0,i.jsx)("i",{className:"bi bi-three-dots-vertical"})}),(0,i.jsxs)("ul",{className:"dropdown-menu",children:[(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-reply"}),"Reply"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-forward"}),"Forward"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-trash"}),"Remove"]})})]})]})]}),(0,i.jsx)("div",{className:"message-time-status",children:(0,i.jsx)("div",{className:"sent-time",children:"11:46 AM"})})]})]}),(0,i.jsxs)("div",{className:"single-chat-item outgoing",children:[(0,i.jsxs)("div",{className:"user-avatar mt-1",children:[(0,i.jsx)("span",{className:"name-first-letter",children:"A"}),(0,i.jsx)("img",{src:"/assets/img/bg-img/user3.png",alt:""})]}),(0,i.jsxs)("div",{className:"user-message",children:[(0,i.jsxs)("div",{className:"message-content",children:[(0,i.jsx)("div",{className:"single-message",children:(0,i.jsx)("div",{className:"gallery-img",children:(0,i.jsx)("a",{href:"img/bg-img/30.jpg",children:(0,i.jsx)("img",{src:"/assets/img/bg-img/30.jpg",alt:""})})})}),(0,i.jsxs)("div",{className:"dropstart",children:[(0,i.jsx)("button",{className:"btn btn-options dropdown-toggle",type:"button","data-bs-toggle":"dropdown","aria-expanded":"false",children:(0,i.jsx)("i",{className:"bi bi-three-dots-vertical"})}),(0,i.jsxs)("ul",{className:"dropdown-menu",children:[(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-reply"}),"Reply"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-forward"}),"Forward"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-trash"}),"Remove"]})})]})]})]}),(0,i.jsxs)("div",{className:"message-time-status",children:[(0,i.jsx)("div",{className:"sent-time",children:"11:39 AM"}),(0,i.jsx)("div",{className:"sent-status seen",children:(0,i.jsx)("i",{className:"bi bi-check"})})]})]})]}),(0,i.jsxs)("div",{className:"single-chat-item",children:[(0,i.jsxs)("div",{className:"user-avatar mt-1",children:[(0,i.jsx)("span",{className:"name-first-letter",children:"A"}),(0,i.jsx)("img",{src:"/assets/img/bg-img/2.jpg",alt:""})]}),(0,i.jsxs)("div",{className:"user-message",children:[(0,i.jsxs)("div",{className:"message-content",children:[(0,i.jsx)("div",{className:"single-message",children:(0,i.jsx)("p",{children:"Would you please provide a purchase link?"})}),(0,i.jsxs)("div",{className:"dropstart",children:[(0,i.jsx)("button",{className:"btn btn-options dropdown-toggle",type:"button","data-bs-toggle":"dropdown","aria-expanded":"false",children:(0,i.jsx)("i",{className:"bi bi-three-dots-vertical"})}),(0,i.jsxs)("ul",{className:"dropdown-menu",children:[(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-reply"}),"Reply"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-forward"}),"Forward"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-trash"}),"Remove"]})})]})]})]}),(0,i.jsx)("div",{className:"message-time-status",children:(0,i.jsx)("div",{className:"sent-time",children:"11:39 AM"})})]})]}),(0,i.jsxs)("div",{className:"single-chat-item outgoing",children:[(0,i.jsxs)("div",{className:"user-avatar mt-1",children:[(0,i.jsx)("span",{className:"name-first-letter",children:"A"}),(0,i.jsx)("img",{src:"/assets/img/bg-img/user3.png",alt:""})]}),(0,i.jsxs)("div",{className:"user-message",children:[(0,i.jsxs)("div",{className:"message-content",children:[(0,i.jsx)("div",{className:"single-message",children:(0,i.jsx)("p",{children:"Sure, Here are the purchase link. Please click the purchase now button, then fill up your all payment info."})}),(0,i.jsxs)("div",{className:"dropstart",children:[(0,i.jsx)("button",{className:"btn btn-options dropdown-toggle",type:"button","data-bs-toggle":"dropdown","aria-expanded":"false",children:(0,i.jsx)("i",{className:"bi bi-three-dots-vertical"})}),(0,i.jsxs)("ul",{className:"dropdown-menu",children:[(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-reply"}),"Reply"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-forward"}),"Forward"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-trash"}),"Remove"]})})]})]})]}),(0,i.jsxs)("div",{className:"message-content",children:[(0,i.jsx)("div",{className:"single-message",children:(0,i.jsx)("a",{className:"btn btn-success rounded-pill",href:"https://themeforest.net/item/affan-pwa-mobile-html-template/29715548",target:"_blank",children:"Buy Now - $24"})}),(0,i.jsxs)("div",{className:"dropstart",children:[(0,i.jsx)("button",{className:"btn btn-options dropdown-toggle",type:"button","data-bs-toggle":"dropdown","aria-expanded":"false",children:(0,i.jsx)("i",{className:"bi bi-three-dots-vertical"})}),(0,i.jsxs)("ul",{className:"dropdown-menu",children:[(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-reply"}),"Reply"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-forward"}),"Forward"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-trash"}),"Remove"]})})]})]})]}),(0,i.jsxs)("div",{className:"message-time-status",children:[(0,i.jsx)("div",{className:"sent-time",children:"11:46 AM"}),(0,i.jsx)("div",{className:"sent-status seen",children:(0,i.jsx)("i",{className:"bi bi-check"})})]})]})]}),(0,i.jsxs)("div",{className:"single-chat-item",children:[(0,i.jsxs)("div",{className:"user-avatar mt-1",children:[(0,i.jsx)("span",{className:"name-first-letter",children:"A"}),(0,i.jsx)("img",{src:"/assets/img/bg-img/2.jpg",alt:""})]}),(0,i.jsxs)("div",{className:"user-message",children:[(0,i.jsxs)("div",{className:"message-content",children:[(0,i.jsx)("div",{className:"single-message",children:(0,i.jsx)("p",{children:"Thanks!"})}),(0,i.jsxs)("div",{className:"dropstart",children:[(0,i.jsx)("button",{className:"btn btn-options dropdown-toggle",type:"button","data-bs-toggle":"dropdown","aria-expanded":"false",children:(0,i.jsx)("i",{className:"bi bi-three-dots-vertical"})}),(0,i.jsxs)("ul",{className:"dropdown-menu",children:[(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-reply"}),"Reply"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-forward"}),"Forward"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-trash"}),"Remove"]})})]})]})]}),(0,i.jsx)("div",{className:"message-time-status",children:(0,i.jsx)("div",{className:"sent-time",children:"11:39 AM"})})]})]}),(0,i.jsxs)("div",{className:"single-chat-item",children:[(0,i.jsxs)("div",{className:"user-avatar mt-1",children:[(0,i.jsx)("span",{className:"name-first-letter",children:"A"}),(0,i.jsx)("img",{src:"/assets/img/bg-img/2.jpg",alt:""})]}),(0,i.jsxs)("div",{className:"user-message",children:[(0,i.jsxs)("div",{className:"message-content",children:[(0,i.jsx)("div",{className:"single-message",children:(0,i.jsxs)("div",{className:"download-file-wrap d-flex align-items-center",children:[(0,i.jsxs)("div",{className:"download-avatar bg-light",children:[(0,i.jsx)("div",{className:"dl-icon",children:(0,i.jsx)("i",{className:"bi bi-file-earmark-zip-fill"})}),(0,i.jsx)("a",{className:"download-btn",href:"#",children:(0,i.jsx)("i",{className:"bi bi-download"})})]}),(0,i.jsxs)("div",{className:"download-file-info",children:[(0,i.jsx)("div",{className:"file-name text-truncate",children:"affan-pwa-mobile-html-template.zip"}),(0,i.jsx)("div",{className:"file-size",children:"11.69 MB"})]})]})}),(0,i.jsxs)("div",{className:"dropstart",children:[(0,i.jsx)("button",{className:"btn btn-options dropdown-toggle",type:"button","data-bs-toggle":"dropdown","aria-expanded":"false",children:(0,i.jsx)("i",{className:"bi bi-three-dots-vertical"})}),(0,i.jsxs)("ul",{className:"dropdown-menu",children:[(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-reply"}),"Reply"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-forward"}),"Forward"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-trash"}),"Remove"]})})]})]})]}),(0,i.jsx)("div",{className:"message-time-status",children:(0,i.jsx)("div",{className:"sent-time",children:"11:39 AM"})})]})]}),(0,i.jsxs)("div",{className:"single-chat-item outgoing",children:[(0,i.jsxs)("div",{className:"user-avatar mt-1",children:[(0,i.jsx)("span",{className:"name-first-letter",children:"A"}),(0,i.jsx)("img",{src:"/assets/img/bg-img/user3.png",alt:""})]}),(0,i.jsxs)("div",{className:"user-message",children:[(0,i.jsxs)("div",{className:"message-content",children:[(0,i.jsx)("div",{className:"single-message",children:(0,i.jsx)("p",{children:"You are welcome &"})}),(0,i.jsxs)("div",{className:"dropstart",children:[(0,i.jsx)("button",{className:"btn btn-options dropdown-toggle",type:"button","data-bs-toggle":"dropdown","aria-expanded":"false",children:(0,i.jsx)("i",{className:"bi bi-three-dots-vertical"})}),(0,i.jsxs)("ul",{className:"dropdown-menu",children:[(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-reply"}),"Reply"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-forward"}),"Forward"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-trash"}),"Remove"]})})]})]})]}),(0,i.jsxs)("div",{className:"message-time-status",children:[(0,i.jsx)("div",{className:"sent-time",children:"11:46 AM"}),(0,i.jsx)("div",{className:"sent-status delivered",children:(0,i.jsx)("i",{className:"bi bi-check"})})]})]})]}),(0,i.jsxs)("div",{className:"single-chat-item",children:[(0,i.jsxs)("div",{className:"user-avatar mt-1",children:[(0,i.jsx)("span",{className:"name-first-letter",children:"A"}),(0,i.jsx)("img",{src:"/assets/img/bg-img/2.jpg",alt:""})]}),(0,i.jsx)("div",{className:"user-message",children:(0,i.jsx)("div",{className:"message-content",children:(0,i.jsx)("div",{className:"single-message",children:(0,i.jsxs)("div",{className:"typing",children:[(0,i.jsx)("span",{className:"dot"}),(0,i.jsx)("span",{className:"dot"}),(0,i.jsx)("span",{className:"dot"})]})})})})]})]})})}),(0,i.jsx)("div",{className:"chat-footer",children:(0,i.jsx)("div",{className:"container h-100",children:(0,i.jsx)("div",{className:"chat-footer-content h-100 d-flex align-items-center",children:(0,i.jsxs)("form",{onSubmit:s=>s.preventDefault(),children:[(0,i.jsx)("input",{className:"form-control",type:"text",placeholder:"Type here..."}),(0,i.jsx)("button",{className:"btn btn-emoji mx-2",type:"button",children:(0,i.jsx)("i",{className:"bi bi-emoji-smile"})}),(0,i.jsxs)("div",{className:"dropup me-2",children:[(0,i.jsx)("button",{onClick:()=>e(!s),className:"btn btn-add-file dropdown-toggle ".concat(s?"show":""),type:"button","data-bs-toggle":"dropdown","aria-expanded":"false",children:(0,i.jsx)("i",{className:"bi bi-plus-circle"})}),(0,i.jsxs)("ul",{className:"dropdown-menu ".concat(s?"show":""),style:{position:"absolute",inset:"auto auto 0px 0px",margin:"0px",transform:"translate(0px, -34px)"},children:[(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-files"}),"Files"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-mic"}),"Audio"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-file-earmark"}),"Document"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-file-bar-graph"}),"Pull"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("a",{href:"#",children:[(0,i.jsx)("i",{className:"bi bi-geo-alt"}),"Location"]})})]})]}),(0,i.jsx)("button",{className:"btn btn-submit",type:"submit",children:(0,i.jsx)("i",{className:"bi bi-cursor"})})]})})})})]})}}},s=>{var e=e=>s(s.s=e);s.O(0,[6874,8441,1684,7358],()=>e(40144)),_N_E=s.O()}]);