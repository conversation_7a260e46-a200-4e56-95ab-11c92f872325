<!DOCTYPE html><html id="previewPage" data-theme="light" lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/57cae671176964c8.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/dd0c9f11e78b6897.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/47e96c737914dbc6.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/f834c3f21e6553d1.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/14831314538c9bba.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/b0288f40f3624e4f.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/143eeed8da4915af.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/b60c7510b0bb27b3.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-05e9b7d736bedb12.js"/><script src="/_next/static/chunks/4bd1b696-760b913b742900c1.js" async=""></script><script src="/_next/static/chunks/1684-27cf822636589fb7.js" async=""></script><script src="/_next/static/chunks/main-app-aee89a841e90a376.js" async=""></script><script src="/_next/static/chunks/6078-824c0e1fb78de585.js" async=""></script><script src="/_next/static/chunks/635-fc754490e8f1c721.js" async=""></script><script src="/_next/static/chunks/7666-5aa1b0bf553cfd31.js" async=""></script><script src="/_next/static/chunks/6222-05ff697aae6edc8e.js" async=""></script><script src="/_next/static/chunks/app/layout-9719ca53aa52e84d.js" async=""></script><script src="/_next/static/chunks/6874-b40c7929c749ad65.js" async=""></script><script src="/_next/static/chunks/1955-be380e4885464387.js" async=""></script><script src="/_next/static/chunks/1531-6e49e700c3d39dad.js" async=""></script><script src="/_next/static/chunks/1727-e51dc7373f0f6346.js" async=""></script><script src="/_next/static/chunks/app/not-found-6f5d8f8dafe4f2c5.js" async=""></script><script src="/_next/static/chunks/1205-74df047484910daa.js" async=""></script><script src="/_next/static/chunks/6639-ebff1f958685ab6b.js" async=""></script><script src="/_next/static/chunks/9697-216bb4725e3c7ac6.js" async=""></script><script src="/_next/static/chunks/7726-e35cfd850b289bfd.js" async=""></script><script src="/_next/static/chunks/app/test-attachment/page-d14333cddeef0cf4.js" async=""></script><meta name="apple-mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"/><meta name="mobile-web-app-capable" content="yes"/><meta name="theme-color" content="#ffffff"/><meta name="theme-color" content="#000000"/><link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&amp;family=Raleway:wght@100;200;300;400;500;600;700;800;900&amp;display=swap" rel="stylesheet"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body><div hidden=""><!--$--><!--/$--></div><div class="container py-5"><h1 class="mb-4">Attachment Uploader Test</h1><div class="card mb-4"><div class="card-header bg-primary text-white"><h5 class="mb-0">Images: Camera + Gallery</h5></div><div class="card-body"><div class="attachment-uploader mt-3 position-relative"><label class="fw-medium mb-2 form-label">Take photos or upload from gallery</label><div class="upload-buttons-container p-3 mb-3 bg-light rounded-3 border" style="background-color:#f0f1fe"><div class="d-flex justify-content-center gap-2 flex-wrap"><label class="btn px-3 py-2 d-flex align-items-center" style="background:#5a67d8;border-radius:8px;border:none;color:white;font-size:14px;font-weight:500;box-shadow:0 2px 4px rgba(0,0,0,0.1);min-width:120px;justify-content:center"><i class="bi bi-cloud-arrow-up me-2"></i>Gallery<input hidden="" multiple="" type="file" accept=".jpg,.jpeg,.png,.gif,.bmp,.webp,.svg"/></label><button class="btn px-3 py-2 d-flex align-items-center" style="background:#38bdf8;border-radius:8px;border:none;color:white;font-size:14px;font-weight:500;box-shadow:0 2px 4px rgba(0,0,0,0.1);min-width:120px;justify-content:center"><i class="bi bi-camera me-2"></i>Take Photo</button></div><div class="text-center mt-2"><small class="text-muted">Accepted file types: <!-- -->Gallery: Images | Camera: Photos</small></div></div></div></div></div><div class="card mb-4"><div class="card-header bg-secondary text-white"><h5 class="mb-0">Images: Camera Only</h5></div><div class="card-body"><div class="attachment-uploader mt-3 position-relative"><label class="fw-medium mb-2 form-label">Take photos only (no gallery)</label><div class="upload-buttons-container p-3 mb-3 bg-light rounded-3 border" style="background-color:#f0f1fe"><div class="d-flex justify-content-center gap-2 flex-wrap"><button class="btn px-3 py-2 d-flex align-items-center" style="background:#38bdf8;border-radius:8px;border:none;color:white;font-size:14px;font-weight:500;box-shadow:0 2px 4px rgba(0,0,0,0.1);min-width:120px;justify-content:center"><i class="bi bi-camera me-2"></i>Take Photo</button></div><div class="text-center mt-2"><small class="text-muted">Accepted file types: <!-- -->Camera: Photos</small></div></div></div></div></div><div class="card mb-4"><div class="card-header bg-info text-white"><h5 class="mb-0">Images: Gallery Only</h5></div><div class="card-body"><div class="attachment-uploader mt-3 position-relative"><label class="fw-medium mb-2 form-label">Upload images from gallery only</label><div class="upload-buttons-container p-3 mb-3 bg-light rounded-3 border" style="background-color:#f0f1fe"><div class="d-flex justify-content-center gap-2 flex-wrap"><label class="btn px-3 py-2 d-flex align-items-center" style="background:#5a67d8;border-radius:8px;border:none;color:white;font-size:14px;font-weight:500;box-shadow:0 2px 4px rgba(0,0,0,0.1);min-width:120px;justify-content:center"><i class="bi bi-cloud-arrow-up me-2"></i>Gallery<input hidden="" multiple="" type="file" accept=".jpg,.jpeg,.png,.gif,.bmp,.webp,.svg"/></label></div><div class="text-center mt-2"><small class="text-muted">Accepted file types: <!-- -->Gallery: Images</small></div></div></div></div></div><div class="card mb-4"><div class="card-header bg-success text-white"><h5 class="mb-0">Documents Only</h5></div><div class="card-body"><div class="attachment-uploader mt-3 position-relative"><label class="fw-medium mb-2 form-label">Upload Documents Only</label><div class="upload-buttons-container p-3 mb-3 bg-light rounded-3 border" style="background-color:#f0f1fe"><div class="d-flex justify-content-center gap-2 flex-wrap"><label class="btn px-3 py-2 d-flex align-items-center" style="background:#5a67d8;border-radius:8px;border:none;color:white;font-size:14px;font-weight:500;box-shadow:0 2px 4px rgba(0,0,0,0.1);min-width:120px;justify-content:center"><i class="bi bi-cloud-arrow-up me-2"></i>Gallery<input hidden="" multiple="" type="file" accept=".pdf,.doc,.docx,.xls,.xlsx,.txt,.rtf"/></label></div><div class="text-center mt-2"><small class="text-muted">Accepted file types: <!-- -->Gallery: Documents</small></div></div></div></div></div><div class="card mb-4"><div class="card-header bg-warning text-dark"><h5 class="mb-0">Video: Camera + Gallery</h5></div><div class="card-body"><div class="attachment-uploader mt-3 position-relative"><label class="fw-medium mb-2 form-label">Record videos or upload from gallery</label><div class="upload-buttons-container p-3 mb-3 bg-light rounded-3 border" style="background-color:#f0f1fe"><div class="d-flex justify-content-center gap-2 flex-wrap"><label class="btn px-3 py-2 d-flex align-items-center" style="background:#5a67d8;border-radius:8px;border:none;color:white;font-size:14px;font-weight:500;box-shadow:0 2px 4px rgba(0,0,0,0.1);min-width:120px;justify-content:center"><i class="bi bi-cloud-arrow-up me-2"></i>Gallery<input hidden="" multiple="" type="file" accept=".mp4,.mov,.avi,.webm,.mkv"/></label><button class="btn px-3 py-2 d-flex align-items-center" style="background:#f59e0b;border-radius:8px;border:none;color:white;font-size:14px;font-weight:500;box-shadow:0 2px 4px rgba(0,0,0,0.1);min-width:120px;justify-content:center"><i class="bi bi-camera-video me-2"></i>Record Video</button></div><div class="text-center mt-2"><small class="text-muted">Accepted file types: <!-- -->Gallery: Videos | Camera: Videos</small></div></div></div></div></div><div class="card mb-4"><div class="card-header bg-dark text-white"><h5 class="mb-0">All Types Allowed</h5></div><div class="card-body"><div class="attachment-uploader mt-3 position-relative"><label class="fw-medium mb-2 form-label">All options available</label><div class="upload-buttons-container p-3 mb-3 bg-light rounded-3 border" style="background-color:#f0f1fe"><div class="d-flex justify-content-center gap-2 flex-wrap"><label class="btn px-3 py-2 d-flex align-items-center" style="background:#5a67d8;border-radius:8px;border:none;color:white;font-size:14px;font-weight:500;box-shadow:0 2px 4px rgba(0,0,0,0.1);min-width:120px;justify-content:center"><i class="bi bi-cloud-arrow-up me-2"></i>Gallery<input hidden="" multiple="" type="file" accept=".jpg,.jpeg,.png,.gif,.bmp,.webp,.svg,.mp4,.mov,.avi,.webm,.mkv,.pdf,.doc,.docx,.xls,.xlsx,.txt,.rtf"/></label><button class="btn px-3 py-2 d-flex align-items-center" style="background:#38bdf8;border-radius:8px;border:none;color:white;font-size:14px;font-weight:500;box-shadow:0 2px 4px rgba(0,0,0,0.1);min-width:120px;justify-content:center"><i class="bi bi-camera me-2"></i>Take Photo</button><button class="btn px-3 py-2 d-flex align-items-center" style="background:#f59e0b;border-radius:8px;border:none;color:white;font-size:14px;font-weight:500;box-shadow:0 2px 4px rgba(0,0,0,0.1);min-width:120px;justify-content:center"><i class="bi bi-camera-video me-2"></i>Record Video</button></div><div class="text-center mt-2"><small class="text-muted">Accepted file types: <!-- -->Gallery: Images, Videos, Documents | Camera: Photos, Videos</small></div></div></div></div></div><div class="card mb-4"><div class="card-header bg-warning text-dark"><h5 class="mb-0">No Types Allowed</h5></div><div class="card-body"><div class="attachment-uploader"><div class="mb-2"><label class="fw-bold">No Uploads Allowed</label></div><div class="alert alert-warning">No file uploads are enabled for this attachment.</div></div></div></div></div><!--$--><!--/$--> <script src="/_next/static/chunks/webpack-05e9b7d736bedb12.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[79705,[\"6078\",\"static/chunks/6078-824c0e1fb78de585.js\",\"635\",\"static/chunks/635-fc754490e8f1c721.js\",\"7666\",\"static/chunks/7666-5aa1b0bf553cfd31.js\",\"6222\",\"static/chunks/6222-05ff697aae6edc8e.js\",\"7177\",\"static/chunks/app/layout-9719ca53aa52e84d.js\"],\"default\"]\n3:I[87555,[],\"\"]\n4:I[31295,[],\"\"]\n5:I[91727,[\"6874\",\"static/chunks/6874-b40c7929c749ad65.js\",\"1955\",\"static/chunks/1955-be380e4885464387.js\",\"1531\",\"static/chunks/1531-6e49e700c3d39dad.js\",\"1727\",\"static/chunks/1727-e51dc7373f0f6346.js\",\"4345\",\"static/chunks/app/not-found-6f5d8f8dafe4f2c5.js\"],\"default\"]\n6:I[6874,[\"6874\",\"static/chunks/6874-b40c7929c749ad65.js\",\"1955\",\"static/chunks/1955-be380e4885464387.js\",\"1531\",\"static/chunks/1531-6e49e700c3d39dad.js\",\"1727\",\"static/chunks/1727-e51dc7373f0f6346.js\",\"4345\",\"static/chunks/app/not-found-6f5d8f8dafe4f2c5.js\"],\"\"]\n7:I[69243,[\"6078\",\"static/chunks/6078-824c0e1fb78de585.js\",\"635\",\"static/chunks/635-fc754490e8f1c721.js\",\"7666\",\"static/chunks/7666-5aa1b0bf553cfd31.js\",\"6222\",\"static/chunks/6222-05ff697aae6edc8e.js\",\"7177\",\"static/chunks/app/layout-9719ca53aa52e84d.js\"],\"\"]\n9:I[90894,[],\"ClientPageRoot\"]\na:I[34761,[\"6078\",\"static/chunks/6078-824c0e1fb78de585.js\",\"1205\",\"static/chunks/1205-74df047484910daa.js\",\"6639\",\"static/chunks/6639-ebff1f958685ab6b.js\",\"9697\",\"static/chunks/9697-216bb4725e3c7ac6.js\",\"7726\",\"static/chunks/7726-e35cfd850b289bfd.js\",\"3859\",\"static/chunks/app/test-attachment/page-d14333cddeef0cf4.js\"],\"default\"]\nd:I[59665,[],\"OutletBoundary\"]\n10:I[74911,[],\"AsyncMetadataOutlet\"]\n12:I[59665,[],\"ViewportBoundary\"]\n14:I[59665,[],\"MetadataBoundary\"]\n16:I[26614,[],\"\"]\n:HL[\"/_next/static/css/57cae671176964c8.css\",\"style\"]\n:HL[\"/_next/static/css/dd0c9f11e78b6897.css\",\"style\"]\n:HL[\"/_next/static/css/47e96c737914dbc6.css\",\"style\"]\n:HL[\"/_next/static/css/f834c3f21e6553d1.css\",\"style\"]\n:HL[\"/_next/static/css/14831314538c9bba.css\",\"style\"]\n:HL[\"/_next/static/css/b0288f40f3624e4f.css\",\"style\"]\n:HL[\"/_next/static/css/143eeed8da4915af.css\",\"style\"]\n:HL[\"/_next/static/css/b60c7510b0bb2"])</script><script>self.__next_f.push([1,"7b3.css\",\"style\"]\n8:T568,\n         (function() {\n    var hostParts = window.location.hostname.split('.'); \n    var internalName = hostParts.length \u003e 1 ? hostParts[1] : ''; // Extract 'internal'\n\n    console.log(\"Internal Name:\", internalName); // Debugging purpose\n\n     document.title = \"Welcome to \" + internalName.charAt(0).toUpperCase() + internalName.slice(1) + \" Portal\";\n\n    var manifestLink = document.createElement(\"link\");\n    manifestLink.rel = \"manifest\";\n    manifestLink.href = \"/manifest-\" + internalName + \".json\"; // Load client-specific manifest\n    document.head.appendChild(manifestLink);\n\n    var iosIconSizes = [16,20,32, 36, 40, 48, 57, 72, 96, 114, 144, 192, 512];\n\n    // Generate Apple Touch Icons dynamically\n    iosIconSizes.forEach(size =\u003e {\n        var appleIcon = document.createElement(\"link\");\n        appleIcon.rel = \"apple-touch-icon\";\n        appleIcon.sizes = size + \"x\" + size;\n        appleIcon.href = \"/assets/iosicons/\" + internalName + \"/\" + size + \".png\"; // Load client-specific iOS icon\n        document.head.appendChild(appleIcon);\n    });\n\n    // Add Favicon dynamically\n    var faviconLink = document.createElement(\"link\");\n    faviconLink.rel = \"icon\";\n    faviconLink.type = \"image/png\";\n    faviconLink.href = \"/assets/iosicons/\" + internalName + \"/\" + 16+ \".png\"; // Load client-specific favicon\n    document.head.appendChild(faviconLink);\n\n})();\n\n        "])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"umzR2rTgz1T-81H90Y7RI\",\"p\":\"\",\"c\":[\"\",\"test-attachment\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"test-attachment\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/57cae671176964c8.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/dd0c9f11e78b6897.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/47e96c737914dbc6.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"3\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/f834c3f21e6553d1.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"4\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/14831314538c9bba.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"5\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/b0288f40f3624e4f.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"6\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/143eeed8da4915af.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"id\":\"previewPage\",\"data-theme\":\"light\",\"lang\":\"en\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"meta\",null,{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover\"}],[\"$\",\"meta\",null,{\"name\":\"apple-mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",null,{\"name\":\"apple-mobile-web-app-status-bar-style\",\"content\":\"black-translucent\"}],[\"$\",\"meta\",null,{\"name\":\"mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",null,{\"name\":\"theme-color\",\"content\":\"#ffffff\"}],[\"$\",\"link\",null,{\"href\":\"https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900\u0026family=Raleway:wght@100;200;300;400;500;600;700;800;900\u0026display=swap\",\"rel\":\"stylesheet\"}],[\"$\",\"meta\",null,{\"name\":\"theme-color\",\"content\":\"#000000\"}]]}],[\"$\",\"body\",null,{\"children\":[[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"$L5\",null,{\"links\":\"pages\",\"title\":\"Page Not Found\"}],[\"$\",\"div\",null,{\"className\":\"page-content-wrapper py-3\",\"children\":[\"$\",\"div\",null,{\"className\":\"custom-container\",\"children\":[\"$\",\"div\",null,{\"className\":\"card\",\"children\":[\"$\",\"div\",null,{\"className\":\"card-body px-5 text-center\",\"children\":[[\"$\",\"img\",null,{\"className\":\"mb-4\",\"src\":\"/assets/img/bg-img/39.png\",\"alt\":\"\"}],[\"$\",\"h4\",null,{\"children\":[\"OOPS... \",[\"$\",\"br\",null,{}],\" Page not found!\"]}],[\"$\",\"p\",null,{\"className\":\"mb-4\",\"children\":\"We couldnt find any results for your search. Try again.\"}],[\"$\",\"$L6\",null,{\"className\":\"btn btn-creative btn-danger\",\"href\":\"/home\",\"children\":\"Go to Home\"}]]}]}]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],\" \"]}],[\"$\",\"$L7\",null,{\"id\":\"dynamic-assets\",\"strategy\":\"afterInteractive\",\"children\":\"$8\"}]]}]]}],{\"children\":[\"test-attachment\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L9\",null,{\"Component\":\"$a\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@b\",\"$@c\"]}],[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/b60c7510b0bb27b3.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"$Ld\",null,{\"children\":[\"$Le\",\"$Lf\",[\"$\",\"$L10\",null,{\"promise\":\"$@11\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"g8A_5ZvftpclKgcMM62UJv\",{\"children\":[[\"$\",\"$L12\",null,{\"children\":\"$L13\"}],null]}],[\"$\",\"$L14\",null,{\"children\":\"$L15\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$16\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"17:\"$Sreact.suspense\"\n18:I[74911,[],\"AsyncMetadata\"]\nb:{}\nc:{}\n15:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$17\",null,{\"fallback\":null,\"children\":[\"$\",\"$L18\",null,{\"promise\":\"$@19\"}]}]}]\n"])</script><script>self.__next_f.push([1,"f:null\n"])</script><script>self.__next_f.push([1,"13:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\ne:null\n"])</script><script>self.__next_f.push([1,"11:{\"metadata\":[[\"$\",\"link\",\"0\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\n19:{\"metadata\":\"$11:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>