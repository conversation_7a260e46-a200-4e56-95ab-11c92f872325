(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9088],{3910:(e,s,r)=>{"use strict";r.d(s,{default:()=>c});var t=r(95155),i=r(78093),n=r(6874),a=r.n(n);r(12115);let c=()=>(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.A,{links:"login"}),(0,t.jsx)("div",{className:"login-wrapper d-flex align-items-center justify-content-center",children:(0,t.jsxs)("div",{className:"custom-container",children:[(0,t.jsx)("div",{className:"text-center px-4",children:(0,t.jsx)("img",{className:"login-intro-img",src:"/assets/img/bg-img/37.png",alt:""})}),(0,t.jsx)("div",{className:"register-form mt-4",children:(0,t.jsx)(a(),{href:"/forget-password-success",children:(0,t.jsxs)("form",{action:"/forget-password-success",onSubmit:e=>e.preventDefault(),children:[(0,t.jsx)("div",{className:"form-group text-start mb-3",children:(0,t.jsx)("input",{className:"form-control",type:"text",placeholder:"Enter your email address",required:!0})}),(0,t.jsx)("button",{className:"btn btn-primary w-100",type:"submit",children:"Reset Password"})]})})})]})})]})},62154:(e,s,r)=>{Promise.resolve().then(r.bind(r,3910))},78093:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});var t=r(95155),i=r(6874),n=r.n(i);r(12115);let a=e=>{let{links:s}=e;return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("div",{className:"login-back-button",children:(0,t.jsx)(n(),{href:"/".concat(s),children:(0,t.jsx)("i",{className:"bi bi-arrow-left-short"})})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,8441,1684,7358],()=>s(62154)),_N_E=e.O()}]);