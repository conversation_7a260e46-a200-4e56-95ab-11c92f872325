

import New from "@/components/eptw-gen/New";
import HeaderSeven from "@/layouts/headers/HeaderSeven";
import React, { useState } from "react";
import OfflineAwareLayout from "@/components/offline/OfflineAwareLayout";


export const metadata = {
    title: "Affan Pages - PWA Mobile Next.js Template",
};

const Observation: React.FC = () => {

    return (
        <>
            <HeaderSeven heading={'EPTW'} />
            <OfflineAwareLayout
                pageTitle="EPTW"
                requiresOnline={true}
            >
                <div className="page-content-wrapper">
                    <New />
                </div>
            </OfflineAwareLayout>
        </>
    );
};

export default Observation;
