(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9436],{11846:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var a=t(29300),r=t.n(a),l=t(12115),n=t(97390),i=t(95155);let o=l.forwardRef((e,s)=>{let{bsPrefix:t,className:a,as:l="div",...o}=e,c=(0,n.oU)(t,"row"),d=(0,n.gy)(),m=(0,n.Jm)(),x="".concat(c,"-cols"),h=[];return d.forEach(e=>{let s,t=o[e];delete o[e],null!=t&&"object"==typeof t?{cols:s}=t:s=t,null!=s&&h.push("".concat(x).concat(e!==m?"-".concat(e):"","-").concat(s))}),(0,i.jsx)(l,{ref:s,...o,className:r()(a,c,...h)})});o.displayName="Row";let c=o},27347:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var a=t(95155),r=t(12115),l=t(16639),n=t(60902),i=t(56160);let o=e=>{var s;let{title:t,options:o,selectedValue:c,disabled:d,onChange:m,placeholder:x="Select Option",clearable:h=!1,multiSelect:u=!1,selectedValues:p=[],onMultiChange:g,maxSelections:f=2}=e,[j,v]=(0,r.useState)(!1),[b,N]=(0,r.useState)(""),y=o.filter(e=>e.label.toLowerCase().includes(b.toLowerCase())),w=u?p.length>0?p.map(e=>{var s;return null==(s=o.find(s=>s.value===e))?void 0:s.label}).join(", "):x:(null==(s=o.find(e=>e.value===c))?void 0:s.label)||x,A=()=>{v(!1)},k=e=>{if(u&&g){let s=p||[];s.includes(e)?g(s.filter(s=>s!==e)):s.length<f&&g([...s,e])}else null==m||m(e),v(!1)};return(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)(l.A.Label,{children:t}),(0,a.jsxs)("div",{className:"d-flex gap-2",children:[(0,a.jsx)(n.A,{disabled:d,variant:"outline-secondary",className:"flex-grow-1 text-start",onClick:()=>{v(!0),N("")},children:w}),h&&(u?p.length>0:c)&&(0,a.jsx)(n.A,{variant:"outline-danger",onClick:()=>{u&&g?g([]):null==m||m("")},children:"Clear"})]}),(0,a.jsxs)(i.A,{show:j,onHide:A,centered:!0,children:[(0,a.jsx)(i.A.Header,{closeButton:!0,children:(0,a.jsx)(i.A.Title,{children:t})}),(0,a.jsxs)(i.A.Body,{children:[(0,a.jsx)(l.A.Control,{type:"text",placeholder:"Search...",value:b,onChange:e=>N(e.target.value),className:"mb-3"}),y.length>0?(0,a.jsx)("div",{style:{maxHeight:"300px",overflowY:"auto"},children:y.map(e=>{let s=u?p.includes(e.value):e.value===c,t=u&&!s&&p.length>=f;return(0,a.jsxs)("button",{className:"list-group-item list-group-item-action mb-1 ".concat(s?"active":""," ").concat(t?"disabled":""),onClick:()=>!t&&k(e.value),disabled:t,children:[u&&(0,a.jsx)("input",{type:"checkbox",checked:s,onChange:()=>{},className:"me-2",disabled:t}),e.label]},e.value)})}):(0,a.jsx)("p",{className:"text-muted",children:"No options found."}),u&&(0,a.jsxs)("div",{className:"mt-2 text-muted small",children:["Selected: ",p.length,"/",f]})]}),(0,a.jsxs)(i.A.Footer,{children:[u&&(0,a.jsx)(n.A,{variant:"primary",onClick:A,className:"me-auto",children:"Done"}),(0,a.jsx)(n.A,{variant:"secondary",onClick:A,children:"Close"})]})]})]})}},43864:(e,s,t)=>{"use strict";t.d(s,{A:()=>u});var a=t(95155),r=t(12115),l=t(26957),n=t(38336);let i=async e=>{try{return(await n.A.get((0,l._i)(e),{headers:{"Content-Type":"application/json"}})).data}catch(e){return console.error("Failed to fetch image URL:",e),null}},o=async e=>{try{let s=(await n.A.post(l.J9,{presignedUrl:e},{responseType:"blob"})).data;return new Promise((e,t)=>{let a=new FileReader;a.onloadend=()=>e(a.result),a.onerror=t,a.readAsDataURL(s)})}catch(e){throw console.error("Error fetching Data URL:",e),e}};var c=t(11518),d=t.n(c),m=t(36209);t(58561);var x=t(4178);let h=e=>{let{imageSrc:s}=e,[t,l]=(0,r.useState)(!1);return(0,a.jsxs)("div",{className:"jsx-15b99a83659358da container",children:[(0,a.jsx)("div",{className:"jsx-15b99a83659358da card",children:(0,a.jsxs)("div",{className:"jsx-15b99a83659358da body-blue text-center",children:[(0,a.jsx)("img",{src:s,alt:"Displayed",onClick:e=>{e.preventDefault(),e.stopPropagation(),l(!0)},style:{cursor:"pointer"},className:"jsx-15b99a83659358da display-image"}),t&&(0,a.jsx)(m.Ay,{open:t,close:()=>l(!1),slides:[{src:s}],plugins:[x.A],carousel:{finite:!0}})]})}),(0,a.jsx)(d(),{id:"15b99a83659358da",children:".display-image.jsx-15b99a83659358da{max-width:80px;max-height:80px;width:auto;height:auto;-o-object-fit:cover;object-fit:cover;cursor:pointer!important;-webkit-transition:-webkit-transform.3s ease-in-out;-moz-transition:-moz-transform.3s ease-in-out;-o-transition:-o-transform.3s ease-in-out;transition:-webkit-transform.3s ease-in-out;transition:-moz-transform.3s ease-in-out;transition:-o-transform.3s ease-in-out;transition:transform.3s ease-in-out;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;border:1px solid#dee2e6;pointer-events:auto;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.display-image.jsx-15b99a83659358da:hover{-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-ms-transform:scale(1.05);-o-transform:scale(1.05);transform:scale(1.05);-webkit-box-shadow:0 2px 8px rgba(0,0,0,.15);-moz-box-shadow:0 2px 8px rgba(0,0,0,.15);box-shadow:0 2px 8px rgba(0,0,0,.15)}.container.jsx-15b99a83659358da{padding:0;margin:0;max-width:none;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.card.jsx-15b99a83659358da{border:none;background:none;margin:0;padding:0;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.body-blue.jsx-15b99a83659358da{padding:0;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}"})]})},u=e=>{let{fileName:s,size:t=100,name:l=!1}=e,[n,c]=(0,r.useState)(null),[d,m]=(0,r.useState)(null);if((0,r.useEffect)(()=>{(async()=>{try{var e;let t=await i(s);c(t);let a=null==(e=s.split(".").pop())?void 0:e.toLowerCase();if(a&&["jpg","jpeg","png","gif","bmp","webp"].includes(a)){let e=await o(t);m(e)}}catch(e){console.error("Error fetching file or data URL:",e)}})()},[s]),!n)return(0,a.jsx)("p",{children:"Loading..."});let x=(e=>{var s;let t=null==(s=e.split(".").pop())?void 0:s.toLowerCase();return t?["jpg","jpeg","png","gif","bmp","webp"].includes(t)?"image":["pdf"].includes(t)?"pdf":["xls","xlsx"].includes(t)?"xls":"other":"other"})(s),u=s.replace(/^\d+[\s-_]*/,"");switch(x){case"image":return(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center",style:{padding:"4px"},children:[d?(0,a.jsx)(h,{imageSrc:d}):(0,a.jsx)("div",{className:"d-flex align-items-center justify-content-center bg-light border rounded",style:{width:t,height:t},children:(0,a.jsx)("div",{className:"spinner-border spinner-border-sm text-primary",role:"status",children:(0,a.jsx)("span",{className:"visually-hidden",children:"Loading..."})})}),l&&(0,a.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center",fontSize:"10px",margin:"2px 0 0 0"},children:u})]});case"pdf":return(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center p-2",children:[(0,a.jsx)("a",{href:n,target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)("i",{className:"bi bi-file-pdf-fill fs-1 text-danger"})}),l&&(0,a.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center"},children:u})]});case"xls":return(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center p-2",children:[(0,a.jsx)("a",{href:n,target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)("i",{className:"bi bi-file-excel-fill fs-1 text-success"})}),l&&(0,a.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center"},children:u})]});default:return(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center p-2",children:[(0,a.jsx)("a",{href:n,target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)("i",{className:"bi bi-file-earmark-fill fs-1 text-secondary"})}),l&&(0,a.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center"},children:u})]})}}},46554:(e,s,t)=>{"use strict";t.d(s,{default:()=>d});var a=t(95155),r=t(12115),l=t(35695),n=t(38336),i=t(26957),o=t(34540),c=t(81359);let d=e=>{let{heading:s}=e,t=(0,l.useRouter)(),[d,m]=(0,r.useState)(""),x=(0,o.wA)();r.useEffect(()=>{h()},[]);let h=async()=>{try{let e=await n.A.get(i.AM);200===e.status?(m(e.data.firstName),x(c.l.setUser(e.data))):t.push("/")}catch(e){console.log(e)}};return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"header-area",id:"headerArea",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"header-content header-style-four position-relative d-flex align-items-center justify-content-between",children:[(0,a.jsx)("div",{className:"back-button",children:(0,a.jsx)("button",{onClick:()=>t.back(),className:"border-0 bg-transparent p-0",style:{cursor:"pointer"},children:(0,a.jsx)("i",{className:"bi bi-arrow-left-short fs-3"})})}),(0,a.jsx)("div",{className:"page-heading",children:(0,a.jsx)("h6",{className:"mb-0",children:s})}),(0,a.jsx)("div",{className:"user-profile-wrapper"})]})})})})}},52702:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(95155),r=t(12115),l=t(38336),n=t(26957);let i=e=>{let{logo:s}=e,[t,i]=(0,r.useState)("");return(0,r.useEffect)(()=>{let e=async()=>{try{let e=await l.A.post(n.J9,{presignedUrl:s},{responseType:"blob"}),t=new FileReader;t.onloadend=()=>{"string"==typeof t.result&&i(t.result)},t.readAsDataURL(e.data)}catch(e){console.error("Error fetching logo blob:",e)}};s&&e()},[s]),(0,a.jsx)("img",{src:t||"/default-logo.png",alt:"Logo",style:{maxWidth:"125px",height:"auto"}})}},55794:(e,s,t)=>{"use strict";t.d(s,{default:()=>h});var a=t(95155),r=t(12115),l=t(35695),n=t(38336),i=t(26957),o=t(60902),c=t(56160),d=t(52702),m=t(79555),x=t(34540);let h=()=>{let e=(0,l.useRouter)(),[s,t]=(0,r.useState)("permit"),h=(0,x.d4)(e=>e.login.user),[u,p]=(0,r.useState)([]),[g,f]=(0,r.useState)([]),[j,v]=(0,r.useState)(0),[b,N]=(0,r.useState)(1),[y,w]=(0,r.useState)(!1),[A,k]=(0,r.useState)(!1),[S,C]=(0,r.useState)(null),[D,T]=(0,r.useState)(null),[R,E]=(0,r.useState)(!1),[z,I]=(0,r.useState)([]),[L,P]=(0,r.useState)(""),U=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;k(!0);let t="/archived-permits?page=".concat(e,"&limit=").concat(s,"&filter=").concat(encodeURIComponent(JSON.stringify({include:[{relation:"locationOne"},{relation:"locationTwo"},{relation:"locationThree"},{relation:"locationFour"},{relation:"locationFive"},{relation:"locationSix"},{relation:"applicant"},{relation:"assessor"},{relation:"approver"},{relation:"reviewer"}]})));try{let{data:e,total:s}=(await n.A.get(t)).data;f(e||[]),v(s||0)}catch(e){console.error("Error fetching archived permits:",e)}finally{k(!1)}},F=async()=>{w(!0),C(null);try{let e="".concat(i.zP,"?filter=").concat(encodeURIComponent(JSON.stringify({include:[{relation:"locationOne"},{relation:"locationTwo"},{relation:"locationThree"},{relation:"locationFour"},{relation:"locationFive"},{relation:"locationSix"},{relation:"applicant"},{relation:"assessor"},{relation:"approver"},{relation:"reviewer"}]}))),s=await n.A.get(e);200===s.status&&p(s.data.reverse())}catch(e){C("Failed to fetch permit data."),console.error("Error fetching permit data:",e)}finally{w(!1)}},Y=async()=>{I((await n.A.get(i.Jo)).data)},B=async()=>{try{let e=await n.A.get((0,i._i)(localStorage.getItem("logo")||""),{headers:{"Content-Type":"application/json"}});P(e.data)}catch(e){console.error("Error fetching logo:",e)}};(0,r.useEffect)(()=>{B()},[]),(0,r.useEffect)(()=>{"archived"===s&&(U(b,10),Y()),"permit"===s&&(F(),Y())},[s,b]);let H=(0,r.useMemo)(()=>{var e;return null==h||null==(e=h.roles)?void 0:e.some(e=>"eptwApplicant"===e.maskId)},[h]),O=e=>{T(e),E(!0)},M=u.filter(e=>"Active"===e.status),W=u.filter(e=>"Active"!==e.status);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"page-content-wrapper",style:{backgroundColor:"#ffffff"},children:(0,a.jsxs)("div",{className:"container-fluid px-3 py-3",children:[H&&(0,a.jsxs)("div",{className:"card border-0 p-4 mb-4 d-flex align-items-center flex-row",style:{cursor:"pointer",transition:"transform 0.2s ease, box-shadow 0.2s ease",boxShadow:"0 4px 12px rgba(0,0,0,0.15)"},onClick:()=>e.push("/eptw-gen/new"),onMouseEnter:e=>{e.currentTarget.style.transform="translateY(-3px)",e.currentTarget.style.boxShadow="0 8px 25px rgba(220, 53, 69, 0.2)"},onMouseLeave:e=>{e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0,0,0,0.15)"},children:[(0,a.jsx)("div",{className:"rounded-circle d-flex align-items-center justify-content-center me-3",style:{width:"56px",height:"56px",backgroundColor:"#dc3545",color:"white"},children:(0,a.jsx)("i",{className:"bi bi-file-earmark-plus",style:{fontSize:"24px"}})}),(0,a.jsxs)("div",{className:"flex-grow-1",children:[(0,a.jsx)("h6",{className:"mb-1",style:{fontWeight:"600",color:"#111827"},children:"Apply Permit"}),(0,a.jsx)("p",{className:"mb-0 text-muted",style:{fontSize:"14px"},children:"Create and submit a new permit application"})]}),(0,a.jsx)("i",{className:"bi bi-chevron-right text-muted",style:{fontSize:"20px"}})]}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)("div",{className:"d-flex gap-2 p-1 rounded-3",style:{backgroundColor:"#f8fafc"},children:[{id:"permit",label:"Under Review"},{id:"active",label:"Active"},{id:"archived",label:"Archived"}].map(e=>(0,a.jsx)("button",{className:"btn flex-fill py-2 px-3 rounded-2 ".concat(s===e.id?"btn-danger text-white":"btn-light text-muted"),style:{border:"none",fontWeight:"500",fontSize:"14px",transition:"all 0.2s ease"},onClick:()=>{t(e.id),N(1)},children:e.label},e.id))})}),(0,a.jsxs)("div",{children:["permit"===s&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-3",children:[(0,a.jsx)("h6",{className:"mb-0",style:{color:"#374151",fontWeight:"600"},children:"Under Review Permits"}),(0,a.jsxs)("span",{className:"badge bg-light text-muted px-3 py-2",style:{fontSize:"12px"},children:[W.length," items"]})]}),(0,a.jsxs)("div",{style:{maxHeight:"70vh",overflowY:"auto"},children:[y&&(0,a.jsx)("div",{className:"text-center py-5",children:(0,a.jsx)("div",{className:"spinner-border text-danger",role:"status",children:(0,a.jsx)("span",{className:"visually-hidden",children:"Loading..."})})}),S&&(0,a.jsx)("div",{className:"alert alert-danger",role:"alert",children:S}),W.length>0?(0,a.jsx)("div",{className:"row g-3",children:W.map((e,s)=>{var t,r,l;return(0,a.jsx)("div",{className:"col-12",children:(0,a.jsx)("div",{className:"card border-0",style:{cursor:"pointer",transition:"transform 0.2s ease, box-shadow 0.2s ease",boxShadow:"0 4px 12px rgba(0,0,0,0.15)"},onClick:()=>O(e),onMouseEnter:e=>{e.currentTarget.style.transform="translateY(-3px)",e.currentTarget.style.boxShadow="0 8px 25px rgba(0,0,0,0.2)"},onMouseLeave:e=>{e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0,0,0,0.15)"},children:(0,a.jsxs)("div",{className:"card-body p-3",children:[(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-2",children:[(0,a.jsxs)("span",{className:"text-muted small",children:["#",e.maskId]}),(0,a.jsx)("span",{className:"badge bg-warning text-dark",style:{fontSize:"11px"},children:e.status})]}),(0,a.jsx)("h6",{className:"mb-2",style:{color:"#111827",lineHeight:"1.4"},children:e.workDescription}),(0,a.jsxs)("div",{className:"row g-2 mb-2",children:[(0,a.jsxs)("div",{className:"col-6",children:[(0,a.jsx)("small",{className:"text-muted d-block",children:"Applicant"}),(0,a.jsx)("span",{style:{fontSize:"13px",fontWeight:"500"},children:(null==(t=e.applicant)?void 0:t.firstName)||"N/A"})]}),(0,a.jsxs)("div",{className:"col-6",children:[(0,a.jsx)("small",{className:"text-muted d-block",children:"Assessor"}),(0,a.jsx)("span",{style:{fontSize:"13px",fontWeight:"500"},children:(null==(r=e.assessor)?void 0:r.firstName)||"N/A"})]})]}),(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-center",children:[(0,a.jsx)("small",{className:"text-muted",children:new Date(e.created).toLocaleDateString()}),(0,a.jsxs)("small",{className:"text-muted",children:["Reviewer: ",(null==(l=e.reviewer)?void 0:l.firstName)||"N/A"]})]})]})})},s)})}):!y&&(0,a.jsxs)("div",{className:"text-center py-5",children:[(0,a.jsx)("i",{className:"bi bi-file-earmark-text text-muted mb-3",style:{fontSize:"3rem"}}),(0,a.jsx)("p",{className:"text-muted",children:"No permits under review"})]})]})]}),"active"===s&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-3",children:[(0,a.jsx)("h6",{className:"mb-0",style:{color:"#374151",fontWeight:"600"},children:"Active Permits"}),(0,a.jsxs)("span",{className:"badge bg-light text-muted px-3 py-2",style:{fontSize:"12px"},children:[M.length," items"]})]}),(0,a.jsxs)("div",{style:{maxHeight:"70vh",overflowY:"auto"},children:[y&&(0,a.jsx)("div",{className:"text-center py-5",children:(0,a.jsx)("div",{className:"spinner-border text-success",role:"status",children:(0,a.jsx)("span",{className:"visually-hidden",children:"Loading..."})})}),S&&(0,a.jsx)("div",{className:"alert alert-danger",role:"alert",children:S}),M.length>0?(0,a.jsx)("div",{className:"row g-3",children:M.map((e,s)=>{var t,r;return(0,a.jsx)("div",{className:"col-12",children:(0,a.jsx)("div",{className:"card border-0",style:{cursor:"pointer",transition:"transform 0.2s ease, box-shadow 0.2s ease",boxShadow:"0 4px 12px rgba(0,0,0,0.15)",borderLeft:"3px solid #198754"},onClick:()=>O(e),onMouseEnter:e=>{e.currentTarget.style.transform="translateY(-3px)",e.currentTarget.style.boxShadow="0 8px 25px rgba(25, 135, 84, 0.2)"},onMouseLeave:e=>{e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0,0,0,0.15)"},children:(0,a.jsxs)("div",{className:"card-body p-3",children:[(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-2",children:[(0,a.jsxs)("span",{className:"text-muted small",children:["#",e.maskId]}),(0,a.jsx)("span",{className:"badge bg-success text-white",style:{fontSize:"11px"},children:e.status})]}),(0,a.jsx)("h6",{className:"mb-2",style:{color:"#111827",lineHeight:"1.4"},children:e.workDescription}),(0,a.jsxs)("div",{className:"row g-2 mb-2",children:[(0,a.jsxs)("div",{className:"col-6",children:[(0,a.jsx)("small",{className:"text-muted d-block",children:"Applicant"}),(0,a.jsx)("span",{style:{fontSize:"13px",fontWeight:"500"},children:(null==(t=e.applicant)?void 0:t.firstName)||"N/A"})]}),(0,a.jsxs)("div",{className:"col-6",children:[(0,a.jsx)("small",{className:"text-muted d-block",children:"Approver"}),(0,a.jsx)("span",{style:{fontSize:"13px",fontWeight:"500"},children:(null==(r=e.approver)?void 0:r.firstName)||"N/A"})]})]}),(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-center",children:[(0,a.jsx)("small",{className:"text-muted",children:new Date(e.created).toLocaleDateString()}),(0,a.jsx)("small",{className:"text-success fw-bold",children:"Active Permit"})]})]})})},s)})}):!y&&(0,a.jsxs)("div",{className:"text-center py-5",children:[(0,a.jsx)("i",{className:"bi bi-check-circle text-success mb-3",style:{fontSize:"3rem"}}),(0,a.jsx)("p",{className:"text-muted",children:"No active permits found"})]})]})]}),"archived"===s&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-3",children:[(0,a.jsx)("h6",{className:"mb-0",style:{color:"#374151",fontWeight:"600"},children:"Archived Permits"}),(0,a.jsxs)("span",{className:"badge bg-light text-muted px-3 py-2",style:{fontSize:"12px"},children:[g.length," items"]})]}),(0,a.jsxs)("div",{style:{maxHeight:"70vh",overflowY:"auto"},children:[A&&(0,a.jsx)("div",{className:"text-center py-5",children:(0,a.jsx)("div",{className:"spinner-border text-secondary",role:"status",children:(0,a.jsx)("span",{className:"visually-hidden",children:"Loading..."})})}),S&&(0,a.jsx)("div",{className:"alert alert-danger",role:"alert",children:S}),g.length>0?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"row g-3",children:g.map((e,s)=>{var t,r;return(0,a.jsx)("div",{className:"col-12",children:(0,a.jsx)("div",{className:"card border-0",style:{cursor:"pointer",transition:"transform 0.2s ease, box-shadow 0.2s ease",boxShadow:"0 4px 12px rgba(0,0,0,0.15)",opacity:"0.8"},onClick:()=>O(e),onMouseEnter:e=>{e.currentTarget.style.transform="translateY(-3px)",e.currentTarget.style.boxShadow="0 8px 25px rgba(0,0,0,0.2)",e.currentTarget.style.opacity="1"},onMouseLeave:e=>{e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0,0,0,0.15)",e.currentTarget.style.opacity="0.8"},children:(0,a.jsxs)("div",{className:"card-body p-3",children:[(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-2",children:[(0,a.jsxs)("span",{className:"text-muted small",children:["#",e.maskId]}),(0,a.jsx)("span",{className:"badge bg-secondary text-white",style:{fontSize:"11px"},children:e.Status||"Archived"})]}),(0,a.jsx)("h6",{className:"mb-2",style:{color:"#111827",lineHeight:"1.4"},children:e.workDescription}),(0,a.jsxs)("div",{className:"row g-2 mb-2",children:[(0,a.jsxs)("div",{className:"col-6",children:[(0,a.jsx)("small",{className:"text-muted d-block",children:"Applicant"}),(0,a.jsx)("span",{style:{fontSize:"13px",fontWeight:"500"},children:(null==(t=e.applicant)?void 0:t.firstName)||"N/A"})]}),(0,a.jsxs)("div",{className:"col-6",children:[(0,a.jsx)("small",{className:"text-muted d-block",children:"Approver"}),(0,a.jsx)("span",{style:{fontSize:"13px",fontWeight:"500"},children:(null==(r=e.approver)?void 0:r.firstName)||"N/A"})]})]}),(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-center",children:[(0,a.jsx)("small",{className:"text-muted",children:new Date(e.created).toLocaleDateString()}),(0,a.jsx)("small",{className:"text-secondary",children:"Archived"})]})]})})},s)})}),(0,a.jsxs)("div",{className:"d-flex justify-content-center align-items-center gap-3 mt-4 pt-3 border-top",children:[(0,a.jsxs)(o.A,{variant:"outline-secondary",size:"sm",disabled:1===b,onClick:()=>N(e=>Math.max(e-1,1)),style:{borderRadius:"8px"},children:[(0,a.jsx)("i",{className:"bi bi-chevron-left me-1"}),"Previous"]}),(0,a.jsxs)("span",{className:"badge bg-light text-dark px-3 py-2",children:["Page ",b]}),(0,a.jsxs)(o.A,{variant:"outline-secondary",size:"sm",disabled:b>=Math.ceil(j/10),onClick:()=>N(e=>e+1),style:{borderRadius:"8px"},children:["Next",(0,a.jsx)("i",{className:"bi bi-chevron-right ms-1"})]})]})]}):!A&&(0,a.jsxs)("div",{className:"text-center py-5",children:[(0,a.jsx)("i",{className:"bi bi-archive text-muted mb-3",style:{fontSize:"3rem"}}),(0,a.jsx)("p",{className:"text-muted",children:"No archived permits found"})]})]})]})]})]})}),(0,a.jsxs)(c.A,{show:R,onHide:()=>E(!1),centered:!0,children:[(0,a.jsx)(c.A.Header,{closeButton:!0,children:D&&(0,a.jsx)("div",{className:"row w-100",children:(0,a.jsxs)("div",{className:"col-12 d-flex align-items-start gap-3",children:[(0,a.jsx)(d.A,{logo:L}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h6",{children:"Permit"}),(0,a.jsxs)("div",{className:"d-flex align-items-center",children:[(0,a.jsxs)("p",{className:"me-2",children:["#",D.maskId]}),(0,a.jsx)("p",{className:"badge bg-primary text-white",children:D.status})]})]})]})})}),(0,a.jsx)(c.A.Body,{children:(0,a.jsx)(m.A,{applicationDetails:D})}),(0,a.jsx)(c.A.Footer,{children:(0,a.jsx)(o.A,{variant:"secondary",onClick:()=>E(!1),children:"Close"})})]})]})}},69513:(e,s,t)=>{Promise.resolve().then(t.bind(t,55794)),Promise.resolve().then(t.bind(t,90371)),Promise.resolve().then(t.bind(t,46554))},79555:(e,s,t)=>{"use strict";t.d(s,{A:()=>w});var a=t(95155),r=t(12115),l=t(92809),n=t(94016),i=t(11846),o=t(68136),c=t(16639),d=t(60902),m=t(56160),x=t(82940),h=t.n(x),u=t(43864),p=t(34540),g=t(27347),f=t(26957),j=t(38336),v=t(24952),b=t(24752),N=t.n(b),y=t(35695);let w=e=>{var s,t,x,b,w,A,k,S,C,D,T,R,E,z,I,L,P,U,F,Y,B,H,O,M,W,_,J,V,q,G,K,Q,X,Z;let{applicationDetails:$}=e,ee=(0,r.useRef)(null),es=(0,p.d4)(e=>e.login.user),[et,ea]=(0,r.useState)(""),[er,el]=(0,r.useState)(!1),[en,ei]=(0,r.useState)([]),[eo,ec]=(0,r.useState)(""),[ed,em]=r.useState(!1),ex=(0,y.useRouter)(),eh=(null==(s=$.permitRiskControl)?void 0:s.reduce((e,s,t)=>((e[s.permitType]=e[s.permitType]||[]).push({...s,controlIndex:t}),e),{}))||{},eu=[{role:"Applicant",name:(null==(t=$.applicant)?void 0:t.firstName)||"N/A",roleName:"Applicant - ".concat((null==(x=$.applicant)?void 0:x.firstName)||"N/A"),signature:(null==(b=$.applicantStatus)?void 0:b.signature)||"N/A",status:(null==(w=$.applicantStatus)?void 0:w.status)?"Approved":"Pending",signedDate:(null==(A=$.applicantStatus)?void 0:A.signedDate)||"N/A",comments:(null==(k=$.applicantStatus)?void 0:k.comments)||"N/A",declaration:"I confirm that all required fields are accurately completed, and I acknowledge responsibility for adhering to the specified safety controls for this work activity."},$.reviewerId&&{role:"Reviewer",name:(null==(S=$.reviewer)?void 0:S.firstName)||"N/A",roleName:"Reviewer - ".concat((null==(C=$.reviewer)?void 0:C.firstName)||"N/A"),signature:(null==(D=$.reviewerStatus)?void 0:D.signature)||"N/A",status:(null==(T=$.reviewerStatus)?void 0:T.status)?"Approved":"Pending",signedDate:(null==(R=$.reviewerStatus)?void 0:R.signedDate)||"N/A",comments:(null==(E=$.reviewerStatus)?void 0:E.comments)||"N/A",declaration:"I have reviewed the application details and verify that the listed controls and prerequisites are suitable and sufficient for safe task execution."},$.assessorId&&{role:"Assessor",name:(null==(z=$.assessor)?void 0:z.firstName)||"N/A",roleName:"Assessor - ".concat((null==(I=$.assessor)?void 0:I.firstName)||"N/A"),signature:(null==(L=$.assessorStatus)?void 0:L.signature)||"N/A",status:(null==(P=$.assessorStatus)?void 0:P.status)?"Approved":"Pending",signedDate:(null==(U=$.assessorStatus)?void 0:U.signedDate)||"N/A",comments:(null==(F=$.assessorStatus)?void 0:F.comments)||"N/A",declaration:"I affirm that I have carefully assessed the risk levels, controls, and work conditions and that all necessary precautions are documented."},$.approverId&&{role:"Approver",name:(null==(Y=$.approver)?void 0:Y.firstName)||"N/A",roleName:"Approver - ".concat((null==(B=$.approver)?void 0:B.firstName)||"N/A"),signature:(null==(H=$.approverStatus)?void 0:H.signature)||"N/A",status:(null==(O=$.approverStatus)?void 0:O.status)?"Approved":"Pending",signedDate:(null==(M=$.approverStatus)?void 0:M.signedDate)||"N/A",comments:(null==(W=$.approverStatus)?void 0:W.comments)||"N/A",declaration:"I approve this permit with the assurance that all safety measures and controls have been verified and are in place to safely conduct this work."},$.closeoutStatus&&{role:"Closeout",name:$.closeoutStatus.by||"N/A",roleName:"Closeout - ".concat($.closeoutStatus.by||"N/A"),signature:(null==(_=$.closeoutStatus)?void 0:_.signature)||"N/A",status:null==(J=$.closeoutStatus)?void 0:J.status,signedDate:(null==(V=$.closeoutStatus)?void 0:V.signedDate)||"N/A",comments:(null==(q=$.closeoutStatus)?void 0:q.comments)||"N/A",declaration:"The task(s) have been completed. The work area(s) have been left in a tidy and safe condition."},$.acknowledgementStatus&&{role:"Acknowledger",name:$.acknowledgementStatus.by||"N/A",roleName:"Acknowledger - ".concat($.acknowledgementStatus.by||"N/A"),signature:(null==(G=$.acknowledgementStatus)?void 0:G.signature)||"N/A",status:null==(K=$.acknowledgementStatus)?void 0:K.status,signedDate:(null==(Q=$.acknowledgementStatus)?void 0:Q.signedDate)||"N/A",comments:(null==(X=$.acknowledgementStatus)?void 0:X.comments)||"N/A",declaration:"I acknowledge that, to the best of my knowledge, the work area(s) have been left in a tidy and safe condition by the applicant."}].filter(Boolean),ep="Internal"===es.type?eu.filter(e=>"Reviewer"!==e.role):eu;(0,r.useEffect)(()=>{eg("eptwAcknowledger")},[]);let eg=(0,r.useCallback)(async e=>{try{let s=await j.A.post(f.u3,{locationOneId:"",locationTwoId:"",locationThreeId:"",locationFourId:"",mode:e});if(200===s.status){let e=s.data.map(e=>({label:e.firstName,value:e.id}));ei(e)}}catch(e){console.error("Error fetching crew list:",e)}},[]),ef=(e,s)=>{for(var t=atob(e.split(",")[1]),a=e.split(",")[0].split(":")[1].split(";")[0],r=new ArrayBuffer(t.length),l=new DataView(r),n=0;n<t.length;n++)l.setUint8(n,t.charCodeAt(n));return new File([r],s,{type:a})},ej=async()=>{let e="".concat(new Date().getTime(),"_captin_sign.png"),s=new FormData,t=ef(et,e);s.append("file",t);try{let e=await j.A.post(f.Dp,s,{headers:{"Content-Type":"multipart/form-data"}});if(e&&200===e.status)return e.data.files[0].originalname;throw Error("File upload failed.")}catch(e){throw console.error("File upload error:",e),e}},ev=async()=>{if(!ed)return void N().fire("Validation Error","Please confirm the closeout checkbox before proceeding.","warning");if(!et)return void N().fire("Validation Error","Please provide your signature to close out the permit.","warning");if(!eo)return void N().fire("Validation Error","Please select an Acknowledger before submitting.","warning");try{let e=await ej(),s=await j.A.patch((0,f.UR)($.id),{status:"Closed",closeoutStatus:{signature:e,comments:""},acknowledgerId:eo});204===s.status?(N().fire("Success","Permit Closed Successfully","success"),ex.back()):N().fire("Error","Unexpected response from server","error")}catch(e){console.error("Closeout failed:",e),N().fire("Error","Something went wrong while closing the permit","error")}};return(0,a.jsxs)(l.A,{fluid:!0,className:"p-2",children:[(0,a.jsx)(n.A,{className:"mb-4",children:(0,a.jsxs)(n.A.Body,{className:"p-0",children:[(0,a.jsx)(i.A,{children:(0,a.jsx)(o.A,{children:(0,a.jsx)("h5",{children:"Permit Details"})})}),(0,a.jsxs)(i.A,{className:"mb-2",children:[(0,a.jsxs)(o.A,{md:4,children:[(0,a.jsx)("strong",{children:"Work Type:"}),(0,a.jsx)("br",{}),(null==(Z=$.permitType)?void 0:Z[0])||"N/A"]}),(0,a.jsxs)(o.A,{md:4,children:[(0,a.jsx)("strong",{children:"Start Date:"}),(0,a.jsx)("br",{}),$.permitStartDate?h()($.permitStartDate).format("DD-MM-YYYY hh:mm A"):"N/A"]}),(0,a.jsxs)(o.A,{md:4,children:[(0,a.jsx)("strong",{children:"End Date:"}),(0,a.jsx)("br",{}),$.permitEndDate?h()($.permitEndDate).format("DD-MM-YYYY hh:mm A"):"N/A"]})]}),(0,a.jsx)(i.A,{className:"mb-2",children:(0,a.jsxs)(o.A,{children:[(0,a.jsx)("strong",{children:"Work Description:"}),(0,a.jsx)("br",{}),$.workDescription||"N/A"]})}),(0,a.jsxs)(i.A,{className:"mb-2",children:[(0,a.jsxs)(o.A,{md:4,children:[(0,a.jsx)("strong",{children:"Site Supervisor:"}),(0,a.jsx)("br",{}),$.nameOfSiteSupervisor||"N/A"]}),(0,a.jsxs)(o.A,{md:4,children:[(0,a.jsx)("strong",{children:"No. of Workers:"}),(0,a.jsx)("br",{}),$.noOfWorkers||"N/A"]})]}),(0,a.jsxs)(i.A,{className:"mb-2",children:[(0,a.jsxs)(o.A,{md:4,children:[(0,a.jsx)("strong",{children:"Supervisor Contact:"}),(0,a.jsx)("br",{}),$.supervisorContactNo||"N/A"]}),(0,a.jsxs)(o.A,{md:4,children:[(0,a.jsx)("strong",{children:"Applicant Contact:"}),(0,a.jsx)("br",{}),$.applicantContactNo||"N/A"]}),(0,a.jsxs)(o.A,{md:4,children:[(0,a.jsx)("strong",{children:"Work Order No:"}),(0,a.jsx)("br",{}),$.workOrderNo||"N/A"]})]}),(0,a.jsx)(i.A,{className:"mb-2",children:(0,a.jsxs)(o.A,{children:[(0,a.jsx)("strong",{children:"Location:"}),(0,a.jsx)("br",{}),[$.locationOne,$.locationTwo,$.locationThree,$.locationFour,$.locationFive,$.locationSix].filter(e=>null==e?void 0:e.name).map(e=>null==e?void 0:e.name).join(" > ")||"N/A"]})}),(0,a.jsx)(i.A,{className:"mb-3",children:$.supportingDocuments&&$.supportingDocuments.length>0&&(0,a.jsxs)(o.A,{children:[(0,a.jsx)("strong",{children:"Supporting Documents"}),(0,a.jsx)(i.A,{className:"mt-2",children:$.supportingDocuments.map((e,s)=>(0,a.jsx)(o.A,{md:3,children:(0,a.jsx)(u.A,{fileName:e,name:!0})},s))})]})})]})}),Object.entries(eh).map(e=>{let[s,t]=e;return(0,a.jsxs)(n.A,{className:"mb-4",children:[(0,a.jsx)(n.A.Header,{as:"h5",children:s}),(0,a.jsx)(n.A.Body,{children:t.map(e=>{var s;return(0,a.jsxs)("div",{className:"border p-3 mb-3 rounded",children:[(0,a.jsxs)("p",{children:[(0,a.jsxs)("strong",{children:[e.controlIndex+1,"."]})," ",e.description]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Type:"})," ",e.currentType]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Value:"})," ",e.value]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Remarks:"})," ",e.remarks]}),(null==(s=e.evidence)?void 0:s.length)>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("strong",{children:"Evidence:"}),(0,a.jsx)(i.A,{className:"mt-2",children:e.evidence.map((e,s)=>(0,a.jsx)(o.A,{md:3,children:(0,a.jsx)(u.A,{fileName:e,name:!1})},s))})]})]},e.controlIndex)})})]},s)}),(0,a.jsxs)(n.A,{children:[(0,a.jsx)(n.A.Header,{as:"h4",children:"Status Details"}),(0,a.jsx)(n.A.Body,{children:ep.map((e,s)=>(0,a.jsxs)("div",{className:"border p-3 mb-3 rounded",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Role/Name:"})," ",e.roleName]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Declaration:"})," ",e.declaration]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Signature:"})," ","N/A"!==e.signature?(0,a.jsx)(u.A,{fileName:e.signature,name:!1}):"N/A"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Signed Date:"})," ","N/A"!==e.signedDate?h()(e.signedDate).format("DD-MM-YYYY hh:mm A"):"N/A"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Comments:"})," ",e.comments]})]},s))})]}),"Active"===$.status&&(null==es?void 0:es.id)===$.applicantId&&(0,a.jsx)(n.A,{className:"mt-4",children:(0,a.jsxs)(n.A.Body,{children:[(0,a.jsx)(c.A.Check,{type:"checkbox",id:"closeout-confirm",label:"The task(s) has been completed. The work area(s) have been left in a tidy and safe condition.",checked:ed,onChange:e=>em(e.target.checked)}),ed&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{className:"m-auto mb-3",xs:12,sm:12,md:12,children:(0,a.jsx)(n.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsx)(n.A.Body,{children:(0,a.jsxs)(i.A,{children:[(0,a.jsx)(o.A,{xs:12,sm:12,md:12,className:"d-flex justify-content-center p-2",onClick:()=>el(!0),children:(0,a.jsx)("span",{className:"bi bi-pencil-square",style:{fontSize:60}})}),(0,a.jsx)("div",{className:"d-flex justify-content-center",children:et?(0,a.jsx)("img",{src:et,height:100,style:{minWidth:150}}):(0,a.jsx)(a.Fragment,{})})]})})})}),(0,a.jsx)(o.A,{className:"m-auto mb-3",xs:12,sm:12,md:12,children:(0,a.jsx)(n.A,{style:{boxShadow:"rgba(0, 0, 0, 0.24) 0px 3px 8px"},children:(0,a.jsx)(n.A.Body,{children:(0,a.jsx)(c.A.Group,{className:"mb-3",children:(0,a.jsx)(g.A,{title:"Identify Acknowledger to closeOut",options:en,selectedValue:eo,onChange:e=>ec(e),placeholder:"Select Acknowledger",clearable:!0,disabled:!1})})})})}),(0,a.jsx)("div",{className:"mt-3 text-end",children:(0,a.jsx)(d.A,{variant:"success",onClick:()=>{ev()},children:"Closeout Permit"})})]})]})}),(0,a.jsxs)(m.A,{show:er,onHide:()=>{el(!1)},"aria-labelledby":"contained-modal-title-vcenter",centered:!0,backdrop:"static",children:[(0,a.jsx)(m.A.Header,{closeButton:!1,children:(0,a.jsx)(m.A.Title,{id:"contained-modal-title-vcenter",children:"Sign"})}),(0,a.jsx)(m.A.Body,{style:{background:"#f5f5f5",width:"100%"},children:(0,a.jsx)(v.A,{ref:ee,penColor:"#1F3BB3",backgroundColor:"white",canvasProps:{className:"sigCanvas",style:{width:"100%",background:"#fff",boxShadow:"0px 0px 10px 3px rgb(189 189 189)",height:"100px"}}})}),(0,a.jsxs)(m.A.Footer,{children:[(0,a.jsx)(d.A,{onClick:()=>{var e;let s=null==(e=ee.current)?void 0:e.toDataURL("image/png");s&&ea(s),el(!1)},children:"confirm"}),(0,a.jsx)(d.A,{onClick:()=>{var e;return null==(e=ee.current)?void 0:e.clear()},children:"Clear"}),(0,a.jsx)(d.A,{onClick:()=>{el(!1)},children:"Close"})]})]})]})}},90371:(e,s,t)=>{"use strict";t.d(s,{default:()=>i});var a=t(95155),r=t(12115),l=t(79459),n=t(36651);let i=e=>{let{children:s,pageTitle:t="Page",requiresOnline:i=!1,customFallback:o,className:c=""}=e,[d,m]=(0,r.useState)(!0);if((0,r.useEffect)(()=>{m(navigator.onLine);let e=()=>m(!0),s=()=>m(!1);return window.addEventListener("online",e),window.addEventListener("offline",s),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}},[]),i&&!d){let e=o||(0,a.jsx)(n.A,{title:"".concat(t," Unavailable Offline"),message:"".concat(t," requires an internet connection. Please check your connection and try again."),showHomeLink:!0});return(0,a.jsx)("div",{className:c,children:e})}return(0,a.jsx)(l.A,{className:c,showOfflineIndicator:!0,children:s})}},92809:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var a=t(29300),r=t.n(a),l=t(12115),n=t(97390),i=t(95155);let o=l.forwardRef((e,s)=>{let{bsPrefix:t,fluid:a=!1,as:l="div",className:o,...c}=e,d=(0,n.oU)(t,"container");return(0,i.jsx)(l,{ref:s,...c,className:r()(o,a?"".concat(d).concat("string"==typeof a?"-".concat(a):"-fluid"):d)})});o.displayName="Container";let c=o},94016:(e,s,t)=>{"use strict";t.d(s,{A:()=>y});var a=t(29300),r=t.n(a),l=t(12115),n=t(97390),i=t(95155);let o=l.forwardRef((e,s)=>{let{className:t,bsPrefix:a,as:l="div",...o}=e;return a=(0,n.oU)(a,"card-body"),(0,i.jsx)(l,{ref:s,className:r()(t,a),...o})});o.displayName="CardBody";let c=l.forwardRef((e,s)=>{let{className:t,bsPrefix:a,as:l="div",...o}=e;return a=(0,n.oU)(a,"card-footer"),(0,i.jsx)(l,{ref:s,className:r()(t,a),...o})});c.displayName="CardFooter";let d=l.createContext(null);d.displayName="CardHeaderContext";let m=l.forwardRef((e,s)=>{let{bsPrefix:t,className:a,as:o="div",...c}=e,m=(0,n.oU)(t,"card-header"),x=(0,l.useMemo)(()=>({cardHeaderBsPrefix:m}),[m]);return(0,i.jsx)(d.Provider,{value:x,children:(0,i.jsx)(o,{ref:s,...c,className:r()(a,m)})})});m.displayName="CardHeader";let x=l.forwardRef((e,s)=>{let{bsPrefix:t,className:a,variant:l,as:o="img",...c}=e,d=(0,n.oU)(t,"card-img");return(0,i.jsx)(o,{ref:s,className:r()(l?"".concat(d,"-").concat(l):d,a),...c})});x.displayName="CardImg";let h=l.forwardRef((e,s)=>{let{className:t,bsPrefix:a,as:l="div",...o}=e;return a=(0,n.oU)(a,"card-img-overlay"),(0,i.jsx)(l,{ref:s,className:r()(t,a),...o})});h.displayName="CardImgOverlay";let u=l.forwardRef((e,s)=>{let{className:t,bsPrefix:a,as:l="a",...o}=e;return a=(0,n.oU)(a,"card-link"),(0,i.jsx)(l,{ref:s,className:r()(t,a),...o})});u.displayName="CardLink";var p=t(58724);let g=(0,p.A)("h6"),f=l.forwardRef((e,s)=>{let{className:t,bsPrefix:a,as:l=g,...o}=e;return a=(0,n.oU)(a,"card-subtitle"),(0,i.jsx)(l,{ref:s,className:r()(t,a),...o})});f.displayName="CardSubtitle";let j=l.forwardRef((e,s)=>{let{className:t,bsPrefix:a,as:l="p",...o}=e;return a=(0,n.oU)(a,"card-text"),(0,i.jsx)(l,{ref:s,className:r()(t,a),...o})});j.displayName="CardText";let v=(0,p.A)("h5"),b=l.forwardRef((e,s)=>{let{className:t,bsPrefix:a,as:l=v,...o}=e;return a=(0,n.oU)(a,"card-title"),(0,i.jsx)(l,{ref:s,className:r()(t,a),...o})});b.displayName="CardTitle";let N=l.forwardRef((e,s)=>{let{bsPrefix:t,className:a,bg:l,text:c,border:d,body:m=!1,children:x,as:h="div",...u}=e,p=(0,n.oU)(t,"card");return(0,i.jsx)(h,{ref:s,...u,className:r()(a,p,l&&"bg-".concat(l),c&&"text-".concat(c),d&&"border-".concat(d)),children:m?(0,i.jsx)(o,{children:x}):x})});N.displayName="Card";let y=Object.assign(N,{Img:x,Title:b,Subtitle:f,Body:o,Link:u,Text:j,Header:m,Footer:c,ImgOverlay:h})}},e=>{var s=s=>e(e.s=s);e.O(0,[3496,586,8320,6874,6078,635,4816,1205,5898,6639,4952,381,1434,8441,1684,7358],()=>s(69513)),_N_E=e.O()}]);