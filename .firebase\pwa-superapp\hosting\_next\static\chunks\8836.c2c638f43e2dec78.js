"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[58,8836],{48836:(e,t,s)=>{s.d(t,{offlineQueue:()=>a});var o=s(60058),r=s(38336);class i{async addRequest(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"GET",s=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0;try{await o.offlineStorage.addToQueue({url:e,method:t,data:s,headers:r,maxRetries:this.maxRetries}),this.notifyListeners(),console.log("\uD83D\uDCE4 Added ".concat(t," request to queue: ").concat(e))}catch(e){console.error("❌ Failed to add request to queue:",e)}}async processQueue(){if(!this.isProcessing&&navigator.onLine){this.isProcessing=!0,this.notifyListeners();try{let e=await o.offlineStorage.getQueueItems();for(let t of(console.log("\uD83D\uDD04 Processing ".concat(e.length," queued requests")),e))await this.processQueueItem(t);console.log("✅ Queue processing completed")}catch(e){console.error("❌ Error processing queue:",e)}finally{this.isProcessing=!1,this.notifyListeners()}}}async processQueueItem(e){try{console.log("\uD83D\uDD04 Processing queued request: ".concat(e.method," ").concat(e.url));let t={method:e.method,url:e.url,headers:e.headers||{}};e.data&&("POST"===e.method||"PUT"===e.method||"PATCH"===e.method)&&(t.data=e.data);let s=await r.A.request(t);if(s.status>=200&&s.status<300)await o.offlineStorage.removeFromQueue(e.id),console.log("✅ Successfully processed: ".concat(e.url));else throw Error("HTTP ".concat(s.status,": ").concat(s.statusText))}catch(t){console.error("❌ Failed to process queued request: ".concat(e.url),t),await this.handleFailedRequest(e)}}async handleFailedRequest(e){let t=e.retryCount+1;t>=e.maxRetries?(await o.offlineStorage.removeFromQueue(e.id),console.log("❌ Max retries reached for: ".concat(e.url,". Removing from queue.")),this.notifyFailedRequest(e)):(await o.offlineStorage.updateQueueItemRetryCount(e.id,t),console.log("\uD83D\uDD04 Retry ".concat(t,"/").concat(e.maxRetries," for: ").concat(e.url)),setTimeout(()=>{navigator.onLine&&this.processQueueItem(e)},this.retryDelay*Math.pow(2,t-1)))}notifyFailedRequest(e){console.warn("\uD83D\uDEA8 Failed to sync request after ".concat(e.maxRetries," attempts: ").concat(e.url)),"Notification"in window&&"granted"===Notification.permission&&new Notification("Sync Failed",{body:"Failed to sync data. Please try again later.",icon:"/assets/icons/Icon-192.png"})}async getStats(){try{let e=await o.offlineStorage.getQueueItems(),t=e.filter(e=>e.retryCount>=e.maxRetries).length;return{pending:e.length-t,failed:t,processing:this.isProcessing}}catch(e){return console.error("❌ Error getting queue stats:",e),{pending:0,failed:0,processing:!1}}}async clearQueue(){try{for(let e of(await o.offlineStorage.getQueueItems()))await o.offlineStorage.removeFromQueue(e.id);console.log("\uD83D\uDDD1️ Cleared offline queue"),this.notifyListeners()}catch(e){console.error("❌ Error clearing queue:",e)}}subscribe(e){return this.listeners.push(e),this.getStats().then(e),()=>{let t=this.listeners.indexOf(e);t>-1&&this.listeners.splice(t,1)}}async notifyListeners(){if(this.listeners.length>0){let e=await this.getStats();this.listeners.forEach(t=>t(e))}}async retryFailedRequests(){try{let e=(await o.offlineStorage.getQueueItems()).filter(e=>e.retryCount>=e.maxRetries);for(let t of e)await o.offlineStorage.updateQueueItemRetryCount(t.id,0);console.log("\uD83D\uDD04 Reset ".concat(e.length," failed requests for retry")),navigator.onLine&&this.processQueue()}catch(e){console.error("❌ Error retrying failed requests:",e)}}shouldQueue(e){var t,s,o,r;return!!(!navigator.onLine||"NETWORK_ERROR"===e.code||(null==(t=e.message)?void 0:t.includes("Network Error")))||(null==(s=e.response)?void 0:s.status)>=500||(null==(o=e.response)?void 0:o.status)>=400&&(null==(r=e.response)?void 0:r.status)<500&&408===e.response.status}constructor(){this.isProcessing=!1,this.maxRetries=3,this.retryDelay=1e3,this.listeners=[],window.addEventListener("online",()=>{console.log("\uD83C\uDF10 Back online! Processing queue..."),this.processQueue()}),window.addEventListener("offline",()=>{console.log("\uD83D\uDCF1 Gone offline. Requests will be queued.")}),navigator.onLine&&setTimeout(()=>this.processQueue(),1e3)}}let a=new i;"serviceWorker"in navigator&&navigator.serviceWorker.ready.then(e=>{"sync"in e&&e.sync.register("background-sync").catch(console.error)})},60058:(e,t,s)=>{s.d(t,{offlineStorage:()=>r});class o{async init(){return new Promise((e,t)=>{let s=indexedDB.open(this.dbName,this.version);s.onerror=()=>{console.error("❌ Failed to open IndexedDB:",s.error),t(s.error)},s.onsuccess=()=>{this.db=s.result,console.log("✅ IndexedDB initialized successfully"),e()},s.onupgradeneeded=e=>{let t=e.target.result;if(console.log("\uD83D\uDD04 Upgrading IndexedDB schema"),!t.objectStoreNames.contains(this.stores.services)){let e=t.createObjectStore(this.stores.services,{keyPath:"id"});e.createIndex("maskName","maskName",{unique:!1}),e.createIndex("lastUpdated","lastUpdated",{unique:!1})}if(!t.objectStoreNames.contains(this.stores.actions)){let e=t.createObjectStore(this.stores.actions,{keyPath:"id"});e.createIndex("application","application",{unique:!1}),e.createIndex("status","status",{unique:!1}),e.createIndex("dueDate","dueDate",{unique:!1}),e.createIndex("lastUpdated","lastUpdated",{unique:!1})}if(!t.objectStoreNames.contains(this.stores.queue)){let e=t.createObjectStore(this.stores.queue,{keyPath:"id"});e.createIndex("timestamp","timestamp",{unique:!1}),e.createIndex("retryCount","retryCount",{unique:!1})}t.objectStoreNames.contains(this.stores.metadata)||t.createObjectStore(this.stores.metadata,{keyPath:"key"})}})}async saveServices(e){if(!this.db)throw Error("Database not initialized");let t=this.db.transaction([this.stores.services],"readwrite").objectStore(this.stores.services),s=Date.now();for(let o of e)await t.put({...o,lastUpdated:s});await this.setMetadata("servicesLastSync",s),console.log("\uD83D\uDCBE Saved ".concat(e.length," services to offline storage"))}async getServices(){if(!this.db)throw Error("Database not initialized");return new Promise((e,t)=>{let s=this.db.transaction([this.stores.services],"readonly").objectStore(this.stores.services).getAll();s.onsuccess=()=>{console.log("\uD83D\uDCF1 Retrieved ".concat(s.result.length," services from offline storage")),e(s.result)},s.onerror=()=>t(s.error)})}async saveActions(e){if(!this.db)throw Error("Database not initialized");let t=this.db.transaction([this.stores.actions],"readwrite").objectStore(this.stores.actions),s=Date.now();for(let o of e)await t.put({...o,lastUpdated:s});await this.setMetadata("actionsLastSync",s),console.log("\uD83D\uDCBE Saved ".concat(e.length," actions to offline storage"))}async getActions(e){if(!this.db)throw Error("Database not initialized");return new Promise((t,s)=>{let o=this.db.transaction([this.stores.actions],"readonly").objectStore(this.stores.actions).getAll();o.onsuccess=()=>{let s=o.result;e&&"All"!==e&&(s=s.filter(t=>t.application===e)),console.log("\uD83D\uDCF1 Retrieved ".concat(s.length," actions from offline storage")),t(s)},o.onerror=()=>s(o.error)})}async addToQueue(e){if(!this.db)throw Error("Database not initialized");let t={...e,id:"queue_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),timestamp:Date.now(),retryCount:0},s=this.db.transaction([this.stores.queue],"readwrite").objectStore(this.stores.queue);await s.add(t),console.log("\uD83D\uDCE4 Added item to offline queue:",t.url)}async getQueueItems(){if(!this.db)throw Error("Database not initialized");return new Promise((e,t)=>{let s=this.db.transaction([this.stores.queue],"readonly").objectStore(this.stores.queue).getAll();s.onsuccess=()=>e(s.result),s.onerror=()=>t(s.error)})}async removeFromQueue(e){if(!this.db)throw Error("Database not initialized");let t=this.db.transaction([this.stores.queue],"readwrite").objectStore(this.stores.queue);await t.delete(e),console.log("✅ Removed item from offline queue:",e)}async updateQueueItemRetryCount(e,t){if(!this.db)throw Error("Database not initialized");let s=this.db.transaction([this.stores.queue],"readwrite").objectStore(this.stores.queue),o=s.get(e);o.onsuccess=()=>{let e=o.result;e&&(e.retryCount=t,s.put(e))}}async setMetadata(e,t){if(!this.db)throw Error("Database not initialized");let s=this.db.transaction([this.stores.metadata],"readwrite").objectStore(this.stores.metadata);await s.put({key:e,value:t,timestamp:Date.now()})}async getMetadata(e){if(!this.db)throw Error("Database not initialized");return new Promise((t,s)=>{let o=this.db.transaction([this.stores.metadata],"readonly").objectStore(this.stores.metadata).get(e);o.onsuccess=()=>{var e;t((null==(e=o.result)?void 0:e.value)||null)},o.onerror=()=>s(o.error)})}async clearAllData(){if(!this.db)throw Error("Database not initialized");let e=Object.values(this.stores),t=this.db.transaction(e,"readwrite");for(let s of e){let e=t.objectStore(s);await e.clear()}console.log("\uD83D\uDDD1️ Cleared all offline data")}async getStorageInfo(){if(!this.db)throw Error("Database not initialized");let e=await this.getServices(),t=await this.getActions(),s=await this.getQueueItems();return{services:e.length,actions:t.length,queue:s.length}}isOnline(){return navigator.onLine}constructor(){this.dbName="AcuiZenOfflineDB",this.version=1,this.db=null,this.stores={services:"services",actions:"actions",queue:"offlineQueue",metadata:"metadata"}}}let r=new o;r.init().catch(console.error)}}]);