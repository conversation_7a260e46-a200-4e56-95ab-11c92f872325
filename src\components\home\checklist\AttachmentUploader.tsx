"use client";

import React, { useState } from "react";
import { Form, Spinner } from "react-bootstrap";
import API from "@/services/API";
import { FILE_URL } from "@/constant";
import piexif from "piexifjs";
import * as exifr from "exifr";
import ImageComponent from "@/services/FileDownlodS3";
import { Camera, CameraResultType, CameraSource } from "@capacitor/camera";

// Location-related utility functions
const ensureGeoAllowed = async (): Promise<boolean> => {
  if (!navigator.permissions || !navigator.geolocation) return false;

  const status = await navigator.permissions.query({ name: "geolocation" });
  switch (status.state) {
    case "granted":
      return true;                     // already allowed
    case "prompt":
      // will trigger the native prompt once
      return await new Promise((ok) =>
        navigator.geolocation.getCurrentPosition(
          () => ok(true),
          () => ok(false),
          { maximumAge: 0, timeout: 8000 }
        )
      );
    case "denied":
    default:
      return false;
  }
};

// Function to trigger location permission prompt
const requestLocationPermission = async (): Promise<boolean> => {
  if (!navigator.geolocation) return false;

  return new Promise((resolve) => {
    // Always attempt to get current position, which will trigger permission prompt if needed
    navigator.geolocation.getCurrentPosition(
      (position) => {
        console.log("Location permission granted, coordinates:", position.coords.latitude, position.coords.longitude);
        resolve(true);  // Permission granted
      },
      (error) => {
        console.log("Location permission denied or error:", error.message);
        resolve(false); // Permission denied or error
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0  // Always get fresh location to trigger permission prompt
      }
    );
  });
};

/* safe geolocation with 8-second timeout */
const getCoords = (): Promise<GeolocationCoordinates | null> =>
  new Promise((resolve) => {
    if (!navigator.geolocation) return resolve(null);

    let settled = false;
    const timer = setTimeout(() => {
      if (!settled) {
        settled = true;
        resolve(null); // timeout
      }
    }, 8000);

    navigator.geolocation.getCurrentPosition(
      (pos) => {
        if (!settled) {
          settled = true;
          clearTimeout(timer);
          resolve(pos.coords);
        }
      },
      () => {
        if (!settled) {
          settled = true;
          clearTimeout(timer);
          resolve(null); // denied / error
        }
      },
      { enableHighAccuracy: true, maximumAge: 10_000 }
    );
  });

/* polyfill for createImageBitmap (iOS PWA) */
const safeCreateBitmap = async (
  file: File
): Promise<ImageBitmap | HTMLImageElement> => {
  if ("createImageBitmap" in window) {
    // @ts-ignore
    return await createImageBitmap(file);
  }
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
};

/* Google reverse-geocode with 4-second abort */
const fetchAddress = async (
  lat: number,
  lng: number
): Promise<string | null> => {
  const key = 'AIzaSyDNdPSBifICIPY7YumDCko5JX5tTRkYYSk'
  if (!key) return null;

  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), 4000);

  try {
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${key}`;
    const res = await fetch(url, { signal: controller.signal });
    if (!res.ok) return null;
    const json = await res.json();

    // If we have results, try to get a more concise address
    if (json.results && json.results.length > 0) {
      // Try to find a more concise result from the address components
      // Look for the most appropriate result type for a readable address
      for (const result of json.results) {
        if (
          result.types.includes('street_address') ||
          result.types.includes('premise') ||
          result.types.includes('route')
        ) {
          return result.formatted_address;
        }
      }

      // If we couldn't find a more specific result, use the first one
      // but try to make it more concise by removing the country and postal code
      const fullAddress = json.results[0].formatted_address;

      // Extract just the first part of the address (usually the most relevant)
      const parts = fullAddress.split(',');
      if (parts.length > 2) {
        // Return first two parts of the address (usually street and city/area)
        return parts.slice(0, 2).join(',');
      }

      return fullAddress;
    }

    return null;
  } catch (error) {
    console.error("Error fetching address:", error);
    return null;
  } finally {
    clearTimeout(timeout);
  }
};

/* embed EXIF GPS (returns original file if coords null or not a JPEG) */
const injectGPS = async (file: File): Promise<File> => {
  // Only JPEG/JPG files can have EXIF data with GPS
  if (!/\.jpe?g$/i.test(file.name)) {
    console.log("Not a JPEG file, skipping GPS injection (but will still add footer):", file.name);
    return file;
  }

  // Get current coordinates with timeout
  console.log("Getting current coordinates...");
  const coords = await getCoords();
  if (!coords) {
    console.log("Could not get coordinates, returning original file");
    return file;
  }

  console.log("Injecting GPS coordinates:", coords.latitude, coords.longitude);

  try {
    const reader = new FileReader();
    const dataUrl = await new Promise<string>((resolve, reject) => {
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });

    // Parse existing EXIF or create new
    let exifObj: any;
    try {
      exifObj = piexif.load(dataUrl);
    } catch {
      exifObj = { "0th": {}, "Exif": {}, "GPS": {}, "1st": {}, "thumbnail": null };
    }

    // Inject GPS data
    const lat = coords.latitude;
    const lng = coords.longitude;

    exifObj.GPS[piexif.GPSIFD.GPSLatitudeRef] = lat >= 0 ? "N" : "S";
    exifObj.GPS[piexif.GPSIFD.GPSLatitude] = piexif.GPSHelper.degToDmsRational(Math.abs(lat));
    exifObj.GPS[piexif.GPSIFD.GPSLongitudeRef] = lng >= 0 ? "E" : "W";
    exifObj.GPS[piexif.GPSIFD.GPSLongitude] = piexif.GPSHelper.degToDmsRational(Math.abs(lng));

    const exifBytes = piexif.dump(exifObj);
    const newDataUrl = piexif.insert(exifBytes, dataUrl);

    // Convert back to File
    const response = await fetch(newDataUrl);
    const blob = await response.blob();
    return new File([blob], file.name, { type: file.type });
  } catch (err) {
    console.error("GPS injection failed:", err);
    return file;
  }
};

/* extend image and append footer strip with text wrapping */
const appendFooter = async (file: File, label: string, includeTimestamp: boolean = false) => {
  const img = await safeCreateBitmap(file);
  // @ts-ignore width/height presence on HTMLImageElement
  const w = img.width,
    h = img.height;
  const pad = 16;
  const fontSize = Math.max(16, h * 0.025); // Slightly smaller font for more text
  const lineHeight = fontSize * 1.2;

  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d")!;

  // Set font for measuring
  ctx.font = `${fontSize}px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif`;

  // Function to wrap text
  const wrapText = (text: string, maxWidth: number): string[] => {
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';

    for (const word of words) {
      const testLine = currentLine ? `${currentLine} ${word}` : word;
      const metrics = ctx.measureText(testLine);

      if (metrics.width <= maxWidth) {
        currentLine = testLine;
      } else {
        if (currentLine) {
          lines.push(currentLine);
          currentLine = word;
        } else {
          // Single word is too long, just add it anyway
          lines.push(word);
        }
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines;
  };

  // Add timestamp if requested
  let fullLabel = label;
  if (includeTimestamp) {
    const now = new Date();
    const timestamp = now.toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    });
    fullLabel = `${label} | Taken: ${timestamp}`;
  }

  // Calculate available width for text (canvas width minus padding)
  const maxTextWidth = w - (pad * 2);
  const lines = wrapText(fullLabel, maxTextWidth);
  const footerHeight = (lines.length * lineHeight) + (pad * 2);

  // Set canvas size
  canvas.width = w;
  canvas.height = h + footerHeight;

  // Draw original image
  ctx.drawImage(img, 0, 0);

  // Draw footer background
  ctx.fillStyle = "rgba(0, 0, 0, 0.8)";
  ctx.fillRect(0, h, w, footerHeight);

  // Draw text
  ctx.fillStyle = "white";
  ctx.font = `${fontSize}px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif`;
  ctx.textAlign = "left";
  ctx.textBaseline = "top";

  lines.forEach((line, index) => {
    const y = h + pad + (index * lineHeight);
    ctx.fillText(line, pad, y);
  });

  // Convert to blob and return as File
  return new Promise<File>((resolve) => {
    canvas.toBlob((blob) => {
      if (blob) {
        resolve(new File([blob], file.name, { type: file.type }));
      } else {
        resolve(file); // fallback
      }
    }, file.type, 0.9);
  });
};

// Define the attachment configuration type
interface AttachmentConfig {
  image?: {
    enabled: boolean;
    galleryUploads?: boolean;
  };
  video?: {
    enabled: boolean;
    galleryUploads?: boolean;
  };
  documents?: {
    enabled: boolean;
  };
}

interface AttachmentUploaderProps {
  attachmentConfig: AttachmentConfig;
  onFilesSelected: (files: string[]) => void;
  disabled?: boolean;
  files: string[];
  label?: string;
}

const AttachmentUploader: React.FC<AttachmentUploaderProps> = ({
  attachmentConfig,
  onFilesSelected,
  disabled = false,
  files,
  label,
}) => {
  const [pending, setPending] = useState<string[]>([]);
  const [busy, setBusy] = useState(false);

  // Define file extension mappings for each type
  const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"];
  const videoExtensions = [".mp4", ".mov", ".avi", ".webm", ".mkv"];
  const documentExtensions = [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".txt", ".rtf"];

  // Determine allowed file types based on attachment configuration (for validation)
  const getAllowedFileTypes = (): string[] => {
    const allowedTypes: string[] = [];

    // If no config provided, default to allowing images (for camera capture)
    if (!attachmentConfig) {
      allowedTypes.push(...imageExtensions);
      return allowedTypes;
    }

    // For images, check if gallery uploads are enabled OR if camera capture is enabled
    if (attachmentConfig?.image?.galleryUploads || attachmentConfig?.image?.enabled) {
      allowedTypes.push(...imageExtensions);
    }

    // For videos, check if gallery uploads are enabled OR if camera capture is enabled
    if (attachmentConfig?.video?.galleryUploads || attachmentConfig?.video?.enabled) {
      allowedTypes.push(...videoExtensions);
    }

    // For documents, check if enabled (documents are always from gallery)
    if (attachmentConfig?.documents?.enabled) {
      allowedTypes.push(...documentExtensions);
    }

    // If no types are enabled, return an empty array (no files allowed)
    return allowedTypes;
  };

  // Determine gallery-only file types for file input accept attribute
  const getGalleryFileTypes = (): string[] => {
    const allowedTypes: string[] = [];

    // If no config provided, don't allow gallery uploads (only camera)
    if (!attachmentConfig) {
      return allowedTypes;
    }

    // For images, only include if gallery uploads are enabled
    if (attachmentConfig?.image?.galleryUploads) {
      allowedTypes.push(...imageExtensions);
    }

    // For videos, only include if gallery uploads are enabled
    if (attachmentConfig?.video?.galleryUploads) {
      allowedTypes.push(...videoExtensions);
    }

    // For documents, check if enabled (documents are always from gallery)
    if (attachmentConfig?.documents?.enabled) {
      allowedTypes.push(...documentExtensions);
    }

    return allowedTypes;
  };

  // Get description of allowed file types for display
  const getAllowedTypesDescription = (): string => {
    const types: string[] = [];
    const cameraTypes: string[] = [];

    // If no config provided, default to camera photos only
    if (!attachmentConfig) {
      return "Camera: Photos";
    }

    // Gallery uploads
    if (attachmentConfig?.image?.galleryUploads) {
      types.push("Images");
    }

    if (attachmentConfig?.video?.galleryUploads) {
      types.push("Videos");
    }

    if (attachmentConfig?.documents?.enabled) {
      types.push("Documents");
    }

    // Camera capture
    if (attachmentConfig?.image?.enabled) {
      cameraTypes.push("Photos");
    }

    if (attachmentConfig?.video?.enabled) {
      cameraTypes.push("Videos");
    }

    let description = "";

    if (types.length > 0) {
      description += `Gallery: ${types.join(", ")}`;
    }

    if (cameraTypes.length > 0) {
      if (description) description += " | ";
      description += `Camera: ${cameraTypes.join(", ")}`;
    }

    return description || "No file types allowed";
  };

  const allowedTypes = getAllowedFileTypes();
  const galleryTypes = getGalleryFileTypes();

  const uploadOne = async (file: File) => {
    setPending((p) => [...p, file.name]);
    try {
      const fd = new FormData();
      fd.append("file", file);
      const r = await API.post(FILE_URL, fd, {
        headers: { "Content-Type": "multipart/form-data" },
      });
      if (r?.status === 200 && r.data?.files?.[0]?.originalname) {
        onFilesSelected([...files, r.data.files[0].originalname]);
      }
    } finally {
      setPending((p) => p.filter((n) => n !== file.name));
    }
  };

  const processList = async (list: FileList | File[]) => {
    setBusy(true);

    for (const original of Array.from(list)) {
      try {
        const ext = original.name
          .substring(original.name.lastIndexOf("."))
          .toLowerCase();
        if (!allowedTypes.includes(ext)) {
          alert(`File format not allowed: ${original.name}. Allowed types: ${getAllowedTypesDescription()}`);
          continue;
        }

        let file = original;

        // Process location data for images
        if (imageExtensions.includes(ext)) {
          const isJpeg = /\.jpe?g$/i.test(file.name);

          if (isJpeg) {
            // Try to inject GPS data - this will return the original file if no coords available
            file = await injectGPS(file);

            // Try to read the GPS data we just injected
            const gps = await exifr.gps(file).catch((err) => {
              console.error("Error reading GPS data:", err);
              return null;
            });

            if (gps && gps.latitude && gps.longitude) {
              // We have valid GPS data
              console.log("Valid GPS coordinates found:", gps.latitude, gps.longitude);

              // Try to get a human-readable address
              let label = await fetchAddress(gps.latitude, gps.longitude);

              // If we couldn't get an address, format the coordinates nicely
              if (!label) {
                // Format coordinates in a more readable way
                const formatCoord = (coord: number, isLat: boolean) => {
                  const direction = isLat
                    ? (coord >= 0 ? "N" : "S")
                    : (coord >= 0 ? "E" : "W");
                  return `${Math.abs(coord).toFixed(6)}° ${direction}`;
                };

                label = `Location: ${formatCoord(gps.latitude, true)}, ${formatCoord(gps.longitude, false)}`;
              } else {
                // Prepend "Location: " to the address for clarity
                label = `Location: ${label}`;
              }

              console.log("Adding location label:", label);
              file = await appendFooter(file, label, true);

              // Re-embed EXIF after canvas export (which strips metadata)
              file = await injectGPS(file);
            } else {
              // No GPS data available - add a footer indicating no location
              console.log("No location data available for:", file.name);
              file = await appendFooter(file, "No location data available", true);
            }
          } else {
            // For non-JPEG image files, we can't add EXIF data but we can still add a footer
            // Try to get coordinates directly
            const coords = await getCoords();

            if (coords) {
              // We have coordinates, try to get an address
              let label = await fetchAddress(coords.latitude, coords.longitude);

              // If we couldn't get an address, format the coordinates nicely
              if (!label) {
                const formatCoord = (coord: number, isLat: boolean) => {
                  const direction = isLat
                    ? (coord >= 0 ? "N" : "S")
                    : (coord >= 0 ? "E" : "W");
                  return `${Math.abs(coord).toFixed(6)}° ${direction}`;
                };

                label = `Location: ${formatCoord(coords.latitude, true)}, ${formatCoord(coords.longitude, false)}`;
              } else {
                label = `Location: ${label}`;
              }

              console.log("Adding location label to non-JPEG image:", label);
              file = await appendFooter(file, label, true);
            } else {
              // No coordinates available
              console.log("No location data available for non-JPEG image:", file.name);
              file = await appendFooter(file, "No location data available", true);
            }
          }
        }

        // Upload the file (with or without location data)
        await uploadOne(file);
      } catch (err) {
        console.error("Attachment upload error:", err);
        alert(`Could not process ${original.name}. Skipping.`);
      }
    }

    setBusy(false);
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;

    // Check if any of the selected files are images
    const hasImageFiles = Array.from(e.target.files).some(file => {
      const ext = file.name.substring(file.name.lastIndexOf(".")).toLowerCase();
      return imageExtensions.includes(ext);
    });

    // Only ask for location permission if there are image files
    if (hasImageFiles) {
      console.log("Image files detected, checking location permission");
      const geoOk = await ensureGeoAllowed();
      if (!geoOk) {
        const proceed = window.confirm(
          "Location access is needed to add location data to your photos.\n\n" +
            "• Click OK to allow location access now\n" +
            "• Click Cancel to continue without location data"
        );

        if (proceed) {
          // User wants to enable location - trigger the browser prompt
          console.log("Requesting location permission...");
          const permissionGranted = await requestLocationPermission();

          if (!permissionGranted) {
            // If still denied, show instructions and continue
            alert(
              "Location access was denied. To enable location for photos:\n\n" +
              "1. Click the location icon in your browser's address bar\n" +
              "2. Select 'Allow' for location access\n" +
              "3. Try uploading again for location data\n\n" +
              "Continuing to upload without location data..."
            );
          } else {
            console.log("Location permission granted successfully!");
          }
        } else {
          // If user clicks Cancel, continue without location data
          console.log("User chose to continue without location data");
        }
      }
    }

    processList(e.target.files);
    e.target.value = "";
  };

  // Camera capture for images
  const captureImage = async () => {
    try {
      // Check current permission status
      const geoOk = await ensureGeoAllowed();

      if (!geoOk) {
        // Permission not granted, ask user what they want to do
        const proceed = window.confirm(
          "Location access is needed to add location data to your photos.\n\n" +
            "• Click OK to allow location access now\n" +
            "• Click Cancel to continue without location data"
        );

        if (proceed) {
          // User wants to enable location - always try to trigger the browser prompt
          console.log("Requesting location permission...");
          const permissionGranted = await requestLocationPermission();

          if (!permissionGranted) {
            // If still denied, show instructions
            alert(
              "Location access was denied. To enable location for photos:\n\n" +
              "1. Click the location icon in your browser's address bar\n" +
              "2. Select 'Allow' for location access\n" +
              "3. Try taking the photo again\n\n" +
              "Continuing without location data..."
            );
          } else {
            console.log("Location permission granted successfully!");
          }
        } else {
          // If user clicks Cancel, continue without location data
          console.log("User chose to continue taking photo without location data");
        }
      }

      // Take the photo regardless of location permission
      const shot = await Camera.getPhoto({
        source: CameraSource.Camera,
        resultType: CameraResultType.Uri,
        quality: 80,
      });

      if (!shot.webPath) return;

      const blob = await fetch(shot.webPath).then((r) => r.blob());
      const camFile = new File([blob], `camera_${Date.now()}.jpg`, {
        type: "image/jpeg",
      });

      setBusy(true);
      processList([camFile]);  // Process with or without location data
    } catch (err) {
      console.error("Camera capture error:", err);
      alert("Could not capture photo. Please try again.");
    }
  };

  // Camera capture for videos
  const captureVideo = async () => {
    try {
      const shot = await Camera.getPhoto({
        source: CameraSource.Camera,
        resultType: CameraResultType.Uri,
        quality: 80,
        // Note: Capacitor Camera doesn't directly support video recording
        // You might need to use a different plugin for video recording
      });

      if (!shot.webPath) return;

      const blob = await fetch(shot.webPath).then((r) => r.blob());
      const videoFile = new File([blob], `video_${Date.now()}.mp4`, {
        type: "video/mp4",
      });

      setBusy(true);
      processList([videoFile]);
    } catch (err) {
      console.error("Video capture error:", err);
      alert("Could not capture video. Please try again.");
    }
  };

  // Check what upload options are available
  const canUploadFromGallery = () => {
    // If no config provided, don't allow gallery uploads
    if (!attachmentConfig) return false;

    return (attachmentConfig?.image?.galleryUploads) ||
           (attachmentConfig?.video?.galleryUploads) ||
           (attachmentConfig?.documents?.enabled);
  };

  const canTakePhoto = () => {
    // If no config provided, default to allowing photo capture
    if (!attachmentConfig) return true;

    return attachmentConfig?.image?.enabled;
  };

  const canRecordVideo = () => {
    // If no config provided, don't allow video recording
    if (!attachmentConfig) return false;

    return attachmentConfig?.video?.enabled;
  };

  // Check if any upload method is available
  const hasAnyUploadMethod = () => {
    return canUploadFromGallery() || canTakePhoto() || canRecordVideo();
  };

  // If no upload methods are available, show a message
  if (!hasAnyUploadMethod()) {
    return (
      <div className="attachment-uploader">
        {label && (
          <div className="mb-2">
            <label className="fw-bold">{label}</label>
          </div>
        )}
        <div className="alert alert-warning">
          No file uploads are enabled for this attachment.
        </div>
      </div>
    );
  }

  return (
    <div className="attachment-uploader mt-3 position-relative">
      {busy && (
        <div className="position-absolute top-0 start-0 w-100 h-100 bg-white bg-opacity-75 d-flex justify-content-center align-items-center"
             style={{ zIndex: 10, backdropFilter: 'blur(2px)' }}>
          <div className="d-flex flex-column align-items-center">
            <Spinner animation="border" variant="primary" />
            <span className="mt-2 text-primary fw-medium">Processing...</span>
          </div>
        </div>
      )}

      {label && (
        <Form.Label className="fw-medium mb-2">
          {label}
        </Form.Label>
      )}

      <div className="upload-buttons-container p-3 mb-3 bg-light rounded-3 border" style={{ backgroundColor: '#f0f1fe' }}>
        <div className="d-flex justify-content-center gap-2 flex-wrap">
          {/* Gallery Upload Button */}
          {canUploadFromGallery() && (
            <label className="btn px-3 py-2 d-flex align-items-center"
                   style={{
                     background: '#5a67d8',
                     borderRadius: '8px',
                     border: 'none',
                     color: 'white',
                     fontSize: '14px',
                     fontWeight: '500',
                     boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                     minWidth: '120px',
                     justifyContent: 'center'
                   }}
            >
              <i className="bi bi-cloud-arrow-up me-2" />
              Gallery
              <input
                hidden
                multiple
                disabled={disabled}
                type="file"
                accept={galleryTypes.join(",")}
                onChange={handleFileChange}
              />
            </label>
          )}

          {/* Take Photo Button */}
          {canTakePhoto() && (
            <button
              className="btn px-3 py-2 d-flex align-items-center"
              style={{
                background: '#38bdf8',
                borderRadius: '8px',
                border: 'none',
                color: 'white',
                fontSize: '14px',
                fontWeight: '500',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                minWidth: '120px',
                justifyContent: 'center'
              }}
              disabled={disabled}
              onClick={captureImage}
            >
              <i className="bi bi-camera me-2" />
              Take Photo
            </button>
          )}

          {/* Record Video Button */}
          {canRecordVideo() && (
            <button
              className="btn px-3 py-2 d-flex align-items-center"
              style={{
                background: '#f59e0b',
                borderRadius: '8px',
                border: 'none',
                color: 'white',
                fontSize: '14px',
                fontWeight: '500',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                minWidth: '120px',
                justifyContent: 'center'
              }}
              disabled={disabled}
              onClick={captureVideo}
            >
              <i className="bi bi-camera-video me-2" />
              Record Video
            </button>
          )}
        </div>

        <div className="text-center mt-2">
          <small className="text-muted">
            Accepted file types: {getAllowedTypesDescription()}
          </small>
        </div>
      </div>

      {pending.length > 0 && (
        <div className="mt-2 mb-3 p-2 bg-info bg-opacity-10 rounded-3 border border-info border-opacity-25">
          {pending.map((n) => (
            <div key={n} className="d-flex align-items-center gap-2 p-1">
              <Spinner animation="border" size="sm" variant="info" />
              <span className="text-info">Uploading {n}...</span>
            </div>
          ))}
        </div>
      )}

      {files.length > 0 && (
        <div className="uploaded-files-container mt-3">
          <div className="d-flex align-items-center mb-2">
            <i className="bi bi-images me-2 text-secondary"></i>
            <span className="text-secondary">Uploaded Files ({files.length})</span>
          </div>
          <div className="row g-2">
            {files.map((fn, i) => (
              <div key={i} className="col-6">
                <div
                  className="position-relative border rounded p-2"
                  style={{
                    backgroundColor: '#f8f9fa',
                    height: '100px'
                  }}
                >
                  <div
                    className="d-flex justify-content-center align-items-center h-100"
                    style={{ position: 'relative' }}
                  >
                    <ImageComponent size={80} fileName={fn} name={false} />
                  </div>
                  <button
                    className="btn btn-sm position-absolute top-0 end-0 p-0"
                    style={{
                      backgroundColor: '#f87171',
                      borderRadius: '50%',
                      width: '22px',
                      height: '22px',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      color: 'white',
                      border: 'none',
                      margin: '2px',
                      zIndex: 10
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      const next = [...files];
                      next.splice(i, 1);
                      onFilesSelected(next);
                    }}
                  >
                    <i className="bi bi-x" style={{ fontSize: '14px' }}></i>
                  </button>
                  <div className="small text-truncate mt-1" style={{ fontSize: '11px', color: '#666' }}>
                    {fn.split('/').pop()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AttachmentUploader;
