<!DOCTYPE html><html id="previewPage" data-theme="light" lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/57cae671176964c8.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/dd0c9f11e78b6897.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/47e96c737914dbc6.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/f834c3f21e6553d1.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/14831314538c9bba.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/b0288f40f3624e4f.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/143eeed8da4915af.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-05e9b7d736bedb12.js"/><script src="/_next/static/chunks/4bd1b696-760b913b742900c1.js" async=""></script><script src="/_next/static/chunks/1684-27cf822636589fb7.js" async=""></script><script src="/_next/static/chunks/main-app-aee89a841e90a376.js" async=""></script><script src="/_next/static/chunks/6078-824c0e1fb78de585.js" async=""></script><script src="/_next/static/chunks/635-fc754490e8f1c721.js" async=""></script><script src="/_next/static/chunks/7666-5aa1b0bf553cfd31.js" async=""></script><script src="/_next/static/chunks/6222-05ff697aae6edc8e.js" async=""></script><script src="/_next/static/chunks/app/layout-9719ca53aa52e84d.js" async=""></script><script src="/_next/static/chunks/6874-b40c7929c749ad65.js" async=""></script><script src="/_next/static/chunks/1955-be380e4885464387.js" async=""></script><script src="/_next/static/chunks/1531-6e49e700c3d39dad.js" async=""></script><script src="/_next/static/chunks/1727-e51dc7373f0f6346.js" async=""></script><script src="/_next/static/chunks/app/not-found-6f5d8f8dafe4f2c5.js" async=""></script><script src="/_next/static/chunks/381-81349531e413dc2c.js" async=""></script><script src="/_next/static/chunks/app/offline-status/page-949d28657e99d63b.js" async=""></script><meta name="apple-mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"/><meta name="mobile-web-app-capable" content="yes"/><meta name="theme-color" content="#ffffff"/><meta name="theme-color" content="#000000"/><link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&amp;family=Raleway:wght@100;200;300;400;500;600;700;800;900&amp;display=swap" rel="stylesheet"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body><div hidden=""><!--$--><!--/$--></div><div class="header-area" id="headerArea"><div class="container"><div class="header-content header-style-four position-relative d-flex align-items-center justify-content-between"><div class="back-button"><button class="border-0 bg-transparent p-0" style="cursor:pointer"><i class="bi bi-arrow-left-short fs-3"></i></button></div><div class="page-heading"><h6 class="mb-0">Offline Status</h6></div><div class="user-profile-wrapper"></div></div></div></div><div class="jsx-18905f5ac1e0b003 container-fluid px-3 py-4"><div class="jsx-18905f5ac1e0b003 d-flex align-items-center mb-4"><div style="width:40px;height:40px;background-color:#f0f9ff;color:#0ea5e9" class="jsx-18905f5ac1e0b003 rounded-circle d-flex align-items-center justify-content-center me-3"><i style="font-size:18px" class="jsx-18905f5ac1e0b003 bi bi-cloud-arrow-down"></i></div><div class="jsx-18905f5ac1e0b003"><h5 style="font-weight:600" class="jsx-18905f5ac1e0b003 mb-0">Offline Settings</h5><small class="jsx-18905f5ac1e0b003 text-muted">Manage your offline data and sync preferences</small></div></div><div class="jsx-18905f5ac1e0b003 bg-white rounded-3 p-3 mb-3 shadow-sm"><div class="jsx-18905f5ac1e0b003 d-flex align-items-center justify-content-between"><div class="jsx-18905f5ac1e0b003 d-flex align-items-center"><div style="width:32px;height:32px;background-color:#e8f5e8;color:#2e7d32" class="jsx-18905f5ac1e0b003 rounded-circle d-flex align-items-center justify-content-center me-3"><i style="font-size:14px" class="jsx-18905f5ac1e0b003 bi bi-wifi"></i></div><div class="jsx-18905f5ac1e0b003"><div style="font-weight:500;font-size:14px" class="jsx-18905f5ac1e0b003">Online</div><small class="jsx-18905f5ac1e0b003 text-muted">Connected to internet</small></div></div><button class="jsx-18905f5ac1e0b003 btn btn-primary btn-sm"><i class="jsx-18905f5ac1e0b003 bi bi-arrow-clockwise me-1"></i>Sync Now</button></div></div><div class="jsx-18905f5ac1e0b003 bg-white rounded-3 p-3 shadow-sm mb-3"><div class="jsx-18905f5ac1e0b003 d-flex justify-content-between align-items-center mb-3"><h6 style="font-size:14px;font-weight:600" class="jsx-18905f5ac1e0b003 mb-0">Sync Status</h6><button style="font-size:12px;color:#666" class="jsx-18905f5ac1e0b003 btn btn-link p-0">Details</button></div><div class="jsx-18905f5ac1e0b003 d-flex align-items-center mb-3"><div style="width:32px;height:32px;background-color:#4caf50;color:white" class="jsx-18905f5ac1e0b003 rounded-circle d-flex align-items-center justify-content-center me-3"><i style="font-size:14px" class="jsx-18905f5ac1e0b003 bi-check-circle "></i></div><div class="jsx-18905f5ac1e0b003"><div style="font-size:14px;font-weight:500;color:#4caf50" class="jsx-18905f5ac1e0b003">Synced</div><div style="font-size:12px;color:#666" class="jsx-18905f5ac1e0b003">Last sync: </div></div></div></div><div class="jsx-18905f5ac1e0b003 bg-white rounded-3 p-3 mb-3 shadow-sm"><h6 style="font-weight:600" class="jsx-18905f5ac1e0b003 mb-3">Offline Storage</h6><div class="jsx-18905f5ac1e0b003 row g-3 mb-3"><div class="jsx-18905f5ac1e0b003 col-4"><div class="jsx-18905f5ac1e0b003 text-center"><div style="font-size:20px;font-weight:600;color:#2196f3" class="jsx-18905f5ac1e0b003">0</div><small class="jsx-18905f5ac1e0b003 text-muted">Services</small><div style="font-size:10px;color:#666" class="jsx-18905f5ac1e0b003">Never</div></div></div><div class="jsx-18905f5ac1e0b003 col-4"><div class="jsx-18905f5ac1e0b003 text-center"><div style="font-size:20px;font-weight:600;color:#4caf50" class="jsx-18905f5ac1e0b003">0</div><small class="jsx-18905f5ac1e0b003 text-muted">Actions</small><div style="font-size:10px;color:#666" class="jsx-18905f5ac1e0b003">Never</div></div></div><div class="jsx-18905f5ac1e0b003 col-4"><div class="jsx-18905f5ac1e0b003 text-center"><div style="font-size:20px;font-weight:600;color:#ff9800" class="jsx-18905f5ac1e0b003">0</div><small class="jsx-18905f5ac1e0b003 text-muted">Queued</small><div style="font-size:10px;color:#666" class="jsx-18905f5ac1e0b003">Pending sync</div></div></div></div><div class="jsx-18905f5ac1e0b003 d-flex gap-2"><button class="jsx-18905f5ac1e0b003 btn btn-outline-danger btn-sm flex-fill"><i class="jsx-18905f5ac1e0b003 bi bi-trash me-1"></i>Clear Cache</button></div></div><div class="jsx-18905f5ac1e0b003 bg-white rounded-3 p-3 shadow-sm"><h6 style="font-weight:600" class="jsx-18905f5ac1e0b003 mb-3">Offline Features</h6><div class="jsx-18905f5ac1e0b003 list-group list-group-flush"><div class="jsx-18905f5ac1e0b003 list-group-item d-flex align-items-center px-0"><i class="jsx-18905f5ac1e0b003 bi bi-check-circle text-success me-3"></i><div class="jsx-18905f5ac1e0b003"><div style="font-size:14px;font-weight:500" class="jsx-18905f5ac1e0b003">View Services</div><small class="jsx-18905f5ac1e0b003 text-muted">Access your services while offline</small></div></div><div class="jsx-18905f5ac1e0b003 list-group-item d-flex align-items-center px-0"><i class="jsx-18905f5ac1e0b003 bi bi-check-circle text-success me-3"></i><div class="jsx-18905f5ac1e0b003"><div style="font-size:14px;font-weight:500" class="jsx-18905f5ac1e0b003">Browse Actions</div><small class="jsx-18905f5ac1e0b003 text-muted">View and filter your actions offline</small></div></div><div class="jsx-18905f5ac1e0b003 list-group-item d-flex align-items-center px-0"><i class="jsx-18905f5ac1e0b003 bi bi-check-circle text-success me-3"></i><div class="jsx-18905f5ac1e0b003"><div style="font-size:14px;font-weight:500" class="jsx-18905f5ac1e0b003">Auto Sync</div><small class="jsx-18905f5ac1e0b003 text-muted">Automatic sync when connection returns</small></div></div><div class="jsx-18905f5ac1e0b003 list-group-item d-flex align-items-center px-0"><i class="jsx-18905f5ac1e0b003 bi bi-check-circle text-success me-3"></i><div class="jsx-18905f5ac1e0b003"><div style="font-size:14px;font-weight:500" class="jsx-18905f5ac1e0b003">Queue Requests</div><small class="jsx-18905f5ac1e0b003 text-muted">Failed requests are queued for retry</small></div></div></div></div></div><!--$--><!--/$--> <script src="/_next/static/chunks/webpack-05e9b7d736bedb12.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[79705,[\"6078\",\"static/chunks/6078-824c0e1fb78de585.js\",\"635\",\"static/chunks/635-fc754490e8f1c721.js\",\"7666\",\"static/chunks/7666-5aa1b0bf553cfd31.js\",\"6222\",\"static/chunks/6222-05ff697aae6edc8e.js\",\"7177\",\"static/chunks/app/layout-9719ca53aa52e84d.js\"],\"default\"]\n3:I[87555,[],\"\"]\n4:I[31295,[],\"\"]\n5:I[91727,[\"6874\",\"static/chunks/6874-b40c7929c749ad65.js\",\"1955\",\"static/chunks/1955-be380e4885464387.js\",\"1531\",\"static/chunks/1531-6e49e700c3d39dad.js\",\"1727\",\"static/chunks/1727-e51dc7373f0f6346.js\",\"4345\",\"static/chunks/app/not-found-6f5d8f8dafe4f2c5.js\"],\"default\"]\n6:I[6874,[\"6874\",\"static/chunks/6874-b40c7929c749ad65.js\",\"1955\",\"static/chunks/1955-be380e4885464387.js\",\"1531\",\"static/chunks/1531-6e49e700c3d39dad.js\",\"1727\",\"static/chunks/1727-e51dc7373f0f6346.js\",\"4345\",\"static/chunks/app/not-found-6f5d8f8dafe4f2c5.js\"],\"\"]\n7:I[69243,[\"6078\",\"static/chunks/6078-824c0e1fb78de585.js\",\"635\",\"static/chunks/635-fc754490e8f1c721.js\",\"7666\",\"static/chunks/7666-5aa1b0bf553cfd31.js\",\"6222\",\"static/chunks/6222-05ff697aae6edc8e.js\",\"7177\",\"static/chunks/app/layout-9719ca53aa52e84d.js\"],\"\"]\n9:I[90894,[],\"ClientPageRoot\"]\na:I[56282,[\"6078\",\"static/chunks/6078-824c0e1fb78de585.js\",\"635\",\"static/chunks/635-fc754490e8f1c721.js\",\"381\",\"static/chunks/381-81349531e413dc2c.js\",\"1895\",\"static/chunks/app/offline-status/page-949d28657e99d63b.js\"],\"default\"]\nd:I[59665,[],\"OutletBoundary\"]\n10:I[74911,[],\"AsyncMetadataOutlet\"]\n12:I[59665,[],\"ViewportBoundary\"]\n14:I[59665,[],\"MetadataBoundary\"]\n16:I[26614,[],\"\"]\n:HL[\"/_next/static/css/57cae671176964c8.css\",\"style\"]\n:HL[\"/_next/static/css/dd0c9f11e78b6897.css\",\"style\"]\n:HL[\"/_next/static/css/47e96c737914dbc6.css\",\"style\"]\n:HL[\"/_next/static/css/f834c3f21e6553d1.css\",\"style\"]\n:HL[\"/_next/static/css/14831314538c9bba.css\",\"style\"]\n:HL[\"/_next/static/css/b0288f40f3624e4f.css\",\"style\"]\n:HL[\"/_next/static/css/143eeed8da4915af.css\",\"style\"]\n8:T568,\n         (function() {\n    var hostParts = window.location.hostname.split('.'); \n    var internalName = hostParts.length \u003e 1 ? ho"])</script><script>self.__next_f.push([1,"stParts[1] : ''; // Extract 'internal'\n\n    console.log(\"Internal Name:\", internalName); // Debugging purpose\n\n     document.title = \"Welcome to \" + internalName.charAt(0).toUpperCase() + internalName.slice(1) + \" Portal\";\n\n    var manifestLink = document.createElement(\"link\");\n    manifestLink.rel = \"manifest\";\n    manifestLink.href = \"/manifest-\" + internalName + \".json\"; // Load client-specific manifest\n    document.head.appendChild(manifestLink);\n\n    var iosIconSizes = [16,20,32, 36, 40, 48, 57, 72, 96, 114, 144, 192, 512];\n\n    // Generate Apple Touch Icons dynamically\n    iosIconSizes.forEach(size =\u003e {\n        var appleIcon = document.createElement(\"link\");\n        appleIcon.rel = \"apple-touch-icon\";\n        appleIcon.sizes = size + \"x\" + size;\n        appleIcon.href = \"/assets/iosicons/\" + internalName + \"/\" + size + \".png\"; // Load client-specific iOS icon\n        document.head.appendChild(appleIcon);\n    });\n\n    // Add Favicon dynamically\n    var faviconLink = document.createElement(\"link\");\n    faviconLink.rel = \"icon\";\n    faviconLink.type = \"image/png\";\n    faviconLink.href = \"/assets/iosicons/\" + internalName + \"/\" + 16+ \".png\"; // Load client-specific favicon\n    document.head.appendChild(faviconLink);\n\n})();\n\n        "])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"umzR2rTgz1T-81H90Y7RI\",\"p\":\"\",\"c\":[\"\",\"offline-status\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"offline-status\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/57cae671176964c8.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/dd0c9f11e78b6897.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/47e96c737914dbc6.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"3\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/f834c3f21e6553d1.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"4\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/14831314538c9bba.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"5\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/b0288f40f3624e4f.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"6\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/143eeed8da4915af.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"id\":\"previewPage\",\"data-theme\":\"light\",\"lang\":\"en\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"meta\",null,{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover\"}],[\"$\",\"meta\",null,{\"name\":\"apple-mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",null,{\"name\":\"apple-mobile-web-app-status-bar-style\",\"content\":\"black-translucent\"}],[\"$\",\"meta\",null,{\"name\":\"mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",null,{\"name\":\"theme-color\",\"content\":\"#ffffff\"}],[\"$\",\"link\",null,{\"href\":\"https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900\u0026family=Raleway:wght@100;200;300;400;500;600;700;800;900\u0026display=swap\",\"rel\":\"stylesheet\"}],[\"$\",\"meta\",null,{\"name\":\"theme-color\",\"content\":\"#000000\"}]]}],[\"$\",\"body\",null,{\"children\":[[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"$L5\",null,{\"links\":\"pages\",\"title\":\"Page Not Found\"}],[\"$\",\"div\",null,{\"className\":\"page-content-wrapper py-3\",\"children\":[\"$\",\"div\",null,{\"className\":\"custom-container\",\"children\":[\"$\",\"div\",null,{\"className\":\"card\",\"children\":[\"$\",\"div\",null,{\"className\":\"card-body px-5 text-center\",\"children\":[[\"$\",\"img\",null,{\"className\":\"mb-4\",\"src\":\"/assets/img/bg-img/39.png\",\"alt\":\"\"}],[\"$\",\"h4\",null,{\"children\":[\"OOPS... \",[\"$\",\"br\",null,{}],\" Page not found!\"]}],[\"$\",\"p\",null,{\"className\":\"mb-4\",\"children\":\"We couldnt find any results for your search. Try again.\"}],[\"$\",\"$L6\",null,{\"className\":\"btn btn-creative btn-danger\",\"href\":\"/home\",\"children\":\"Go to Home\"}]]}]}]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],\" \"]}],[\"$\",\"$L7\",null,{\"id\":\"dynamic-assets\",\"strategy\":\"afterInteractive\",\"children\":\"$8\"}]]}]]}],{\"children\":[\"offline-status\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L9\",null,{\"Component\":\"$a\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@b\",\"$@c\"]}],null,[\"$\",\"$Ld\",null,{\"children\":[\"$Le\",\"$Lf\",[\"$\",\"$L10\",null,{\"promise\":\"$@11\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"24e12a9BxgFKNP7ZwSF5nv\",{\"children\":[[\"$\",\"$L12\",null,{\"children\":\"$L13\"}],null]}],[\"$\",\"$L14\",null,{\"children\":\"$L15\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$16\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"17:\"$Sreact.suspense\"\n18:I[74911,[],\"AsyncMetadata\"]\nb:{}\nc:{}\n15:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$17\",null,{\"fallback\":null,\"children\":[\"$\",\"$L18\",null,{\"promise\":\"$@19\"}]}]}]\n"])</script><script>self.__next_f.push([1,"f:null\n"])</script><script>self.__next_f.push([1,"13:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\ne:null\n"])</script><script>self.__next_f.push([1,"11:{\"metadata\":[[\"$\",\"link\",\"0\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\n19:{\"metadata\":\"$11:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>