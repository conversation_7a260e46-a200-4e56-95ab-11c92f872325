(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6636],{26957:(e,s,t)=>{"use strict";t.d(s,{AM:()=>n,Dp:()=>d,F4:()=>W,FI:()=>A,H$:()=>a,J9:()=>u,Jo:()=>p,K9:()=>c,M6:()=>x,MO:()=>m,OT:()=>P,P4:()=>v,UR:()=>C,WD:()=>k,WH:()=>j,WU:()=>S,_i:()=>r,bW:()=>y,dG:()=>l,dm:()=>z,iJ:()=>R,mh:()=>L,oo:()=>h,pZ:()=>g,u3:()=>o,x2:()=>N,xE:()=>i,xo:()=>w,yo:()=>b,zP:()=>f});let a="https://client-api.acuizen.com",l=a+"/login-configs",i=a+"/services",r=e=>a+"/files/"+e+"/presigned-url",n=a+"/users/me",c=a+"/dynamic-titles",o=a+"/users/get_users",d=a+"/files",m=a+"/observation-reports",x=a+"/my-observation-reports",h=a+"/dropdowns",u=a+"/get-blob",f=a+"/permit-reports",p=a+"/users",b=a+"/toolbox-talks",g=a+"/my-toolbox-talks",j=e=>a+"/my-assigned-actions/"+e,N=e=>a+"/inspection-checklist-submit/"+e,v=e=>a+"/observation-reports/"+e,y=e=>a+"/inspection-task-submit/"+e,w=e=>a+"/inspections/"+e,S=e=>a+"/permit-report-submit/"+e,k=e=>a+"/permit-reports-acknowledge/"+e,C=e=>a+"/permit-reports-update-status/"+e,A=e=>a+"/observation-action-submit/"+e,z=a+"/risk-assessments",R=e=>a+"/risk-assessments/"+e,W=e=>a+"/ra-team-member-submit-signature/"+e,P=a+"/permit-reports",L=e=>a+"/permit-reports/"+e},29235:(e,s,t)=>{Promise.resolve().then(t.bind(t,33246))},33246:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(95155),l=t(12115),i=t(46554),r=t(6874),n=t.n(r),c=t(35695),o=t(34540),d=t(81359),m=t(38336);function x(){let[e,s]=(0,l.useState)({}),[t,r]=(0,l.useState)(!0),[x,h]=(0,l.useState)(!1),u=(0,c.useRouter)(),f=(0,o.wA)();(0,l.useEffect)(()=>{p()},[]);let p=async()=>{try{let e=await m.A.get("/users/me");200===e.status&&(Array.isArray(e.data)?s({roles:e.data}):s(e.data))}catch(e){console.error("Error fetching user profile:",e)}finally{r(!1)}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.default,{heading:"Profile"}),(0,a.jsx)("div",{className:"page-content-wrapper",style:{minHeight:"100vh",backgroundColor:"#f8f9fa"},children:(0,a.jsx)("div",{className:"container-fluid px-4 py-4",children:t?(0,a.jsx)("div",{className:"d-flex justify-content-center align-items-center py-5",children:(0,a.jsx)("div",{className:"spinner-border text-primary",role:"status",children:(0,a.jsx)("span",{className:"visually-hidden",children:"Loading..."})})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"card border-0 shadow-sm mb-4",style:{borderRadius:"16px"},children:(0,a.jsx)("div",{className:"card-body p-4",children:(0,a.jsxs)("div",{className:"d-flex align-items-center",children:[(0,a.jsx)("div",{className:"rounded-circle d-flex align-items-center justify-content-center me-3",style:{width:"80px",height:"80px",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",color:"white",fontSize:"24px",fontWeight:"600"},children:(()=>{let s=e.firstName||"",t=e.lastName||"";return"".concat(s.charAt(0)).concat(t.charAt(0)).toUpperCase()})()||"U"}),(0,a.jsxs)("div",{className:"flex-grow-1",children:[(0,a.jsx)("h4",{className:"mb-1",style:{fontWeight:"600",color:"#1f2937"},children:e.firstName?"".concat(e.firstName," "):e.username||"Current User"}),(0,a.jsx)("p",{className:"text-muted mb-0",style:{fontSize:"14px"},children:e.email||(e.roles&&e.roles.length>0?"".concat(e.roles.length," role").concat(e.roles.length>1?"s":""," assigned"):"No email available")}),e.department&&(0,a.jsx)("small",{className:"text-muted",children:e.department})]})]})})}),(0,a.jsx)("div",{className:"card border-0 shadow-sm mb-4",style:{borderRadius:"16px"},children:(0,a.jsxs)("div",{className:"card-body p-4",children:[(0,a.jsx)("h5",{className:"mb-3",style:{fontWeight:"600",color:"#1f2937"},children:"Profile Details"}),(0,a.jsxs)("div",{className:"row g-3",children:[(0,a.jsx)("div",{className:"col-12",children:(0,a.jsxs)("div",{className:"d-flex align-items-center p-3 bg-light rounded-3",children:[(0,a.jsx)("i",{className:"bi bi-person text-primary me-3",style:{fontSize:"18px"}}),(0,a.jsxs)("div",{children:[(0,a.jsx)("small",{className:"text-muted d-block",children:"Full Name"}),(0,a.jsx)("span",{style:{fontWeight:"500"},children:e.firstName?"".concat(e.firstName," "):"Not available"})]})]})}),(0,a.jsx)("div",{className:"col-12",children:(0,a.jsxs)("div",{className:"d-flex align-items-center p-3 bg-light rounded-3",children:[(0,a.jsx)("i",{className:"bi bi-envelope text-primary me-3",style:{fontSize:"18px"}}),(0,a.jsxs)("div",{children:[(0,a.jsx)("small",{className:"text-muted d-block",children:"Email"}),(0,a.jsx)("span",{style:{fontWeight:"500"},children:e.email||"Not available"})]})]})}),e.phone&&(0,a.jsx)("div",{className:"col-12",children:(0,a.jsxs)("div",{className:"d-flex align-items-center p-3 bg-light rounded-3",children:[(0,a.jsx)("i",{className:"bi bi-telephone text-primary me-3",style:{fontSize:"18px"}}),(0,a.jsxs)("div",{children:[(0,a.jsx)("small",{className:"text-muted d-block",children:"Phone"}),(0,a.jsx)("span",{style:{fontWeight:"500"},children:e.phone})]})]})}),e.roles&&e.roles.length>0&&(0,a.jsx)("div",{className:"col-12",children:(0,a.jsxs)("div",{className:"d-flex align-items-start p-3 bg-light rounded-3",children:[(0,a.jsx)("i",{className:"bi bi-briefcase text-primary me-3 mt-1",style:{fontSize:"18px"}}),(0,a.jsxs)("div",{className:"flex-grow-1",children:[(0,a.jsx)("small",{className:"text-muted d-block mb-2",children:"Roles & Permissions"}),(0,a.jsx)("div",{className:"d-flex flex-wrap gap-2",children:e.roles.map(e=>(0,a.jsx)("span",{className:"badge rounded-pill px-3 py-1",style:{backgroundColor:"#e3f2fd",color:"#1976d2",fontSize:"11px",fontWeight:"500"},children:e.name},e.id))})]})]})}),e.company&&(0,a.jsx)("div",{className:"col-12",children:(0,a.jsxs)("div",{className:"d-flex align-items-center p-3 bg-light rounded-3",children:[(0,a.jsx)("i",{className:"bi bi-building text-primary me-3",style:{fontSize:"18px"}}),(0,a.jsxs)("div",{children:[(0,a.jsx)("small",{className:"text-muted d-block",children:"Company"}),(0,a.jsx)("span",{style:{fontWeight:"500"},children:e.company})]})]})})]})]})}),(0,a.jsx)("div",{className:"card border-0 shadow-sm mb-5",style:{borderRadius:"16px"},children:(0,a.jsxs)("div",{className:"card-body p-4",children:[(0,a.jsx)("h5",{className:"mb-3",style:{fontWeight:"600",color:"#1f2937"},children:"Actions"}),(0,a.jsx)("div",{className:"d-grid gap-3",children:(0,a.jsxs)("button",{className:"btn btn-outline-danger rounded-3 p-3 d-flex align-items-center",onClick:()=>h(!0),style:{border:"2px solid #dc3545"},children:[(0,a.jsx)("i",{className:"bi bi-box-arrow-right me-3",style:{fontSize:"18px"}}),(0,a.jsxs)("div",{className:"text-start",children:[(0,a.jsx)("div",{style:{fontWeight:"500"},children:"Logout"}),(0,a.jsx)("small",{className:"text-muted",children:"Sign out of your account"})]})]})})]})})]})})}),x&&(0,a.jsx)("div",{className:"modal show d-block",style:{backgroundColor:"rgba(0,0,0,0.5)"},children:(0,a.jsx)("div",{className:"modal-dialog modal-dialog-centered",children:(0,a.jsx)("div",{className:"modal-content border-0",style:{borderRadius:"16px"},children:(0,a.jsxs)("div",{className:"modal-body p-4 text-center",children:[(0,a.jsx)("div",{className:"rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3",style:{width:"60px",height:"60px",backgroundColor:"#fee2e2",color:"#dc2626"},children:(0,a.jsx)("i",{className:"bi bi-box-arrow-right",style:{fontSize:"24px"}})}),(0,a.jsx)("h5",{className:"mb-2",style:{fontWeight:"600"},children:"Confirm Logout"}),(0,a.jsx)("p",{className:"text-muted mb-4",children:"Are you sure you want to sign out of your account?"}),(0,a.jsxs)("div",{className:"d-flex gap-3",children:[(0,a.jsx)("button",{className:"btn btn-outline-secondary flex-fill rounded-pill",onClick:()=>h(!1),children:"Cancel"}),(0,a.jsx)("button",{className:"btn btn-danger flex-fill rounded-pill",onClick:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),f(d.l.setLogout()),u.push("/")},children:"Logout"})]})]})})})}),(0,a.jsx)("div",{className:"fixed-bottom bg-white border-top shadow-sm",children:(0,a.jsx)("div",{className:"container-fluid",children:(0,a.jsxs)("div",{className:"row text-center py-2",children:[(0,a.jsx)("div",{className:"col",children:(0,a.jsx)(n(),{href:"/dashboard",className:"text-decoration-none text-muted",children:(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,a.jsx)("i",{className:"bi bi-grid-3x3-gap fs-5 mb-1"}),(0,a.jsx)("small",{style:{fontSize:"0.7rem"},children:"Dashboard"})]})})}),(0,a.jsx)("div",{className:"col",children:(0,a.jsx)(n(),{href:"/services",className:"text-decoration-none text-muted",children:(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,a.jsx)("i",{className:"bi bi-grid fs-5 mb-1"}),(0,a.jsx)("small",{style:{fontSize:"0.7rem"},children:"Services"})]})})}),(0,a.jsx)("div",{className:"col",children:(0,a.jsx)(n(),{href:"/home",className:"text-decoration-none text-muted",children:(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,a.jsx)("i",{className:"bi bi-house fs-5 mb-1"}),(0,a.jsx)("small",{style:{fontSize:"0.7rem"},children:"Home"})]})})}),(0,a.jsx)("div",{className:"col",children:(0,a.jsx)(n(),{href:"/history",className:"text-decoration-none text-muted",children:(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,a.jsx)("i",{className:"bi bi-clock-history fs-5 mb-1"}),(0,a.jsx)("small",{style:{fontSize:"0.7rem"},children:"History"})]})})}),(0,a.jsx)("div",{className:"col",children:(0,a.jsx)(n(),{href:"/profile",className:"text-decoration-none text-primary",children:(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,a.jsx)("i",{className:"bi bi-person-fill fs-5 mb-1"}),(0,a.jsx)("small",{style:{fontSize:"0.7rem"},children:"Profile"})]})})})]})})})]})}},35695:(e,s,t)=>{"use strict";var a=t(18999);t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},38336:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(96078),l=t(26957);let i=a.A.create({baseURL:l.H$,headers:{Accept:"application/json","Content-Type":"application/json"},mode:"no-cors"});i.interceptors.request.use(async e=>(e.metadata={requestTime:Date.now()},e),e=>Promise.reject(e)),i.interceptors.response.use(e=>{var s;return e.headers["x-request-time"]=null==(s=e.config.metadata)?void 0:s.requestTime,e},async e=>{let{offlineQueue:s}=await t.e(8836).then(t.bind(t,48836)),{offlineStorage:a}=await t.e(58).then(t.bind(t,60058));if(s.shouldQueue(e)){var l,i,r,n;let t=e.config;if(await s.addRequest(t.url,(null==(l=t.method)?void 0:l.toUpperCase())||"GET",t.data,t.headers),(null==(i=t.method)?void 0:i.toLowerCase())==="get")try{if(null==(r=t.url)?void 0:r.includes("/services")){let e=await a.getServices();if(e.length>0)return console.log("\uD83D\uDCF1 Serving services from offline cache"),{data:e,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:t}}if(null==(n=t.url)?void 0:n.includes("assigned-actions")){let e=new URLSearchParams(t.url.split("?")[1]).get("filter"),s="All";if(e)try{JSON.parse(decodeURIComponent(e))}catch(l){let e=t.url.split("/"),a=e.findIndex(e=>"assigned-actions"===e);-1!==a&&e[a+1]&&(s=e[a+1])}let l=await a.getActions(s);if(l.length>0)return console.log("\uD83D\uDCF1 Serving actions from offline cache"),{data:l,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:t}}}catch(e){console.error("❌ Error serving from cache:",e)}}return Promise.reject(e)});let r=i},46554:(e,s,t)=>{"use strict";t.d(s,{default:()=>d});var a=t(95155),l=t(12115),i=t(35695),r=t(38336),n=t(26957),c=t(34540),o=t(81359);let d=e=>{let{heading:s}=e,t=(0,i.useRouter)(),[d,m]=(0,l.useState)(""),x=(0,c.wA)();l.useEffect(()=>{h()},[]);let h=async()=>{try{let e=await r.A.get(n.AM);200===e.status?(m(e.data.firstName),x(o.l.setUser(e.data))):t.push("/")}catch(e){console.log(e)}};return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"header-area",id:"headerArea",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"header-content header-style-four position-relative d-flex align-items-center justify-content-between",children:[(0,a.jsx)("div",{className:"back-button",children:(0,a.jsx)("button",{onClick:()=>t.back(),className:"border-0 bg-transparent p-0",style:{cursor:"pointer"},children:(0,a.jsx)("i",{className:"bi bi-arrow-left-short fs-3"})})}),(0,a.jsx)("div",{className:"page-heading",children:(0,a.jsx)("h6",{className:"mb-0",children:s})}),(0,a.jsx)("div",{className:"user-profile-wrapper"})]})})})})}},81359:(e,s,t)=>{"use strict";t.d(s,{A:()=>i,l:()=>l});let a=(0,t(51990).Z0)({name:"login",initialState:{isLogin:!1,user:null},reducers:{setLogin(e){e.isLogin=!0},setLogout(e){e.isLogin=!1,e.user=null},setUser(e,s){e.user=s.payload}}}),l=a.actions,i=a}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,6078,635,8441,1684,7358],()=>s(29235)),_N_E=e.O()}]);