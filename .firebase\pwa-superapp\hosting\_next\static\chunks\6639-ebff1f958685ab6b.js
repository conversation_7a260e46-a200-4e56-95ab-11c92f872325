"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6639],{16639:(e,t,a)=>{a.d(t,{A:()=>L});var l=a(29300),r=a.n(l),o=a(38637),s=a.n(o),c=a(12115),n=a(95155);let i={type:s().string,tooltip:s().bool,as:s().elementType},d=c.forwardRef((e,t)=>{let{as:a="div",className:l,type:o="valid",tooltip:s=!1,...c}=e;return(0,n.jsx)(a,{...c,ref:t,className:r()(l,"".concat(o,"-").concat(s?"tooltip":"feedback"))})});d.displayName="Feedback",d.propTypes=i;var f=a(21991),m=a(97390);let p=c.forwardRef((e,t)=>{let{id:a,bsPrefix:l,className:o,type:s="checkbox",isValid:i=!1,isInvalid:d=!1,as:p="input",...u}=e,{controlId:x}=(0,c.useContext)(f.A);return l=(0,m.oU)(l,"form-check-input"),(0,n.jsx)(p,{...u,ref:t,type:s,id:a||x,className:r()(o,l,i&&"is-valid",d&&"is-invalid")})});p.displayName="FormCheckInput";let u=c.forwardRef((e,t)=>{let{bsPrefix:a,className:l,htmlFor:o,...s}=e,{controlId:i}=(0,c.useContext)(f.A);return a=(0,m.oU)(a,"form-check-label"),(0,n.jsx)("label",{...s,ref:t,htmlFor:o||i,className:r()(l,a)})});u.displayName="FormCheckLabel";let x=c.forwardRef((e,t)=>{let{id:a,bsPrefix:l,bsSwitchPrefix:o,inline:s=!1,reverse:i=!1,disabled:x=!1,isValid:h=!1,isInvalid:y=!1,feedbackTooltip:N=!1,feedback:b,feedbackType:j,className:v,style:w,title:F="",type:g="checkbox",label:C,children:k,as:R="input",...A}=e;l=(0,m.oU)(l,"form-check"),o=(0,m.oU)(o,"form-switch");let{controlId:U}=(0,c.useContext)(f.A),L=(0,c.useMemo)(()=>({controlId:a||U}),[U,a]),I=!k&&null!=C&&!1!==C||c.Children.toArray(k).some(e=>c.isValidElement(e)&&e.type===u),T=(0,n.jsx)(p,{...A,type:"switch"===g?"checkbox":g,ref:t,isValid:h,isInvalid:y,disabled:x,as:R});return(0,n.jsx)(f.A.Provider,{value:L,children:(0,n.jsx)("div",{style:w,className:r()(v,I&&l,s&&"".concat(l,"-inline"),i&&"".concat(l,"-reverse"),"switch"===g&&o),children:k||(0,n.jsxs)(n.Fragment,{children:[T,I&&(0,n.jsx)(u,{title:F,children:C}),b&&(0,n.jsx)(d,{type:j,tooltip:N,children:b})]})})})});x.displayName="FormCheck";let h=Object.assign(x,{Input:p,Label:u});a(94274);let y=c.forwardRef((e,t)=>{let{bsPrefix:a,type:l,size:o,htmlSize:s,id:i,className:d,isValid:p=!1,isInvalid:u=!1,plaintext:x,readOnly:h,as:y="input",...N}=e,{controlId:b}=(0,c.useContext)(f.A);return a=(0,m.oU)(a,"form-control"),(0,n.jsx)(y,{...N,type:l,size:s,ref:t,readOnly:h,id:i||b,className:r()(d,x?"".concat(a,"-plaintext"):a,o&&"".concat(a,"-").concat(o),"color"===l&&"".concat(a,"-color"),p&&"is-valid",u&&"is-invalid")})});y.displayName="FormControl";let N=Object.assign(y,{Feedback:d}),b=c.forwardRef((e,t)=>{let{className:a,bsPrefix:l,as:o="div",...s}=e;return l=(0,m.oU)(l,"form-floating"),(0,n.jsx)(o,{ref:t,className:r()(a,l),...s})});b.displayName="FormFloating";let j=c.forwardRef((e,t)=>{let{controlId:a,as:l="div",...r}=e,o=(0,c.useMemo)(()=>({controlId:a}),[a]);return(0,n.jsx)(f.A.Provider,{value:o,children:(0,n.jsx)(l,{...r,ref:t})})});j.displayName="FormGroup";var v=a(43630);let w=c.forwardRef((e,t)=>{let{bsPrefix:a,className:l,id:o,...s}=e,{controlId:i}=(0,c.useContext)(f.A);return a=(0,m.oU)(a,"form-range"),(0,n.jsx)("input",{...s,type:"range",ref:t,className:r()(l,a),id:o||i})});w.displayName="FormRange";let F=c.forwardRef((e,t)=>{let{bsPrefix:a,size:l,htmlSize:o,className:s,isValid:i=!1,isInvalid:d=!1,id:p,...u}=e,{controlId:x}=(0,c.useContext)(f.A);return a=(0,m.oU)(a,"form-select"),(0,n.jsx)("select",{...u,size:o,ref:t,className:r()(s,a,l&&"".concat(a,"-").concat(l),i&&"is-valid",d&&"is-invalid"),id:p||x})});F.displayName="FormSelect";let g=c.forwardRef((e,t)=>{let{bsPrefix:a,className:l,as:o="small",muted:s,...c}=e;return a=(0,m.oU)(a,"form-text"),(0,n.jsx)(o,{...c,ref:t,className:r()(l,a,s&&"text-muted")})});g.displayName="FormText";let C=c.forwardRef((e,t)=>(0,n.jsx)(h,{...e,ref:t,type:"switch"}));C.displayName="Switch";let k=Object.assign(C,{Input:h.Input,Label:h.Label}),R=c.forwardRef((e,t)=>{let{bsPrefix:a,className:l,children:o,controlId:s,label:c,...i}=e;return a=(0,m.oU)(a,"form-floating"),(0,n.jsxs)(j,{ref:t,className:r()(l,a),controlId:s,...i,children:[o,(0,n.jsx)("label",{htmlFor:s,children:c})]})});R.displayName="FloatingLabel";let A={_ref:s().any,validated:s().bool,as:s().elementType},U=c.forwardRef((e,t)=>{let{className:a,validated:l,as:o="form",...s}=e;return(0,n.jsx)(o,{...s,ref:t,className:r()(a,l&&"was-validated")})});U.displayName="Form",U.propTypes=A;let L=Object.assign(U,{Group:j,Control:N,Floating:b,Check:h,Switch:k,Label:v.A,Text:g,Range:w,Select:F,FloatingLabel:R})},21991:(e,t,a)=>{a.d(t,{A:()=>l});let l=a(12115).createContext({})},43630:(e,t,a)=>{a.d(t,{A:()=>f});var l=a(29300),r=a.n(l),o=a(12115);a(94274);var s=a(68136),c=a(21991),n=a(97390),i=a(95155);let d=o.forwardRef((e,t)=>{let{as:a="label",bsPrefix:l,column:d=!1,visuallyHidden:f=!1,className:m,htmlFor:p,...u}=e,{controlId:x}=(0,o.useContext)(c.A);l=(0,n.oU)(l,"form-label");let h="col-form-label";"string"==typeof d&&(h="".concat(h," ").concat(h,"-").concat(d));let y=r()(m,l,f&&"visually-hidden",d&&h);return(p=p||x,d)?(0,i.jsx)(s.A,{ref:t,as:"label",className:y,htmlFor:p,...u}):(0,i.jsx)(a,{ref:t,className:y,htmlFor:p,...u})});d.displayName="FormLabel";let f=d},68136:(e,t,a)=>{a.d(t,{A:()=>i});var l=a(29300),r=a.n(l),o=a(12115),s=a(97390),c=a(95155);let n=o.forwardRef((e,t)=>{let[{className:a,...l},{as:o="div",bsPrefix:n,spans:i}]=function(e){let{as:t,bsPrefix:a,className:l,...o}=e;a=(0,s.oU)(a,"col");let c=(0,s.gy)(),n=(0,s.Jm)(),i=[],d=[];return c.forEach(e=>{let t,l,r,s=o[e];delete o[e],"object"==typeof s&&null!=s?{span:t,offset:l,order:r}=s:t=s;let c=e!==n?"-".concat(e):"";t&&i.push(!0===t?"".concat(a).concat(c):"".concat(a).concat(c,"-").concat(t)),null!=r&&d.push("order".concat(c,"-").concat(r)),null!=l&&d.push("offset".concat(c,"-").concat(l))}),[{...o,className:r()(l,...i,...d)},{as:t,bsPrefix:a,spans:i}]}(e);return(0,c.jsx)(o,{...l,ref:t,className:r()(a,!i.length&&n)})});n.displayName="Col";let i=n},94274:e=>{e.exports=function(){}}}]);