(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5172],{17451:(e,s,a)=>{"use strict";a.d(s,{default:()=>h});var t=a(95155),l=a(26957),r=a(9e4),i=a(6874),o=a.n(i),n=a(12115),c=a(38336),d=a(34540),m=a(81359),u=a(35695);let h=()=>{a(81531);let[e,s]=(0,n.useState)(""),[i,h]=(0,n.useState)(""),p=(0,d.wA)(),f=(0,u.useRouter)(),{theme:g,handleDarkModeToggle:x}=(0,r.D)();(0,n.useEffect)(()=>{b(),v()},[]);let b=async()=>{try{if(window.localStorage){let e=localStorage.getItem("logo");if(!e)return void console.warn("No logo key found in localStorage");let a=(await c.A.get((0,l._i)(e),{headers:{"Content-Type":"application/json"}})).data;s(a)}else console.warn("localStorage is not available")}catch(e){console.error("Error fetching logo:",e)}},v=async()=>{try{let e=await c.A.get(l.AM);200===e.status?(h(e.data.firstName),p(m.l.setUser(e.data))):f.push("/")}catch(e){console.log(e)}};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"header-area",id:"headerArea",children:(0,t.jsx)("div",{className:"container",children:(0,t.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,t.jsxs)("div",{className:"navbar--toggler",id:"affanNavbarToggler","data-bs-toggle":"offcanvas","data-bs-target":"#affanOffcanvas","aria-controls":"affanOffcanvas",children:[(0,t.jsx)("span",{className:"d-block"}),(0,t.jsx)("span",{className:"d-block"}),(0,t.jsx)("span",{className:"d-block"})]}),(0,t.jsxs)("div",{className:"logo-wrapper text-center",children:[(0,t.jsx)(o(),{href:"/home",children:(0,t.jsx)("img",{src:e,alt:""})}),(0,t.jsx)("div",{className:"element-heading",children:(0,t.jsx)("h6",{children:"AcuiZen WorkHub"})})]}),(0,t.jsx)("div",{className:"setting-wrapper",children:(0,t.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn2",children:[(0,t.jsx)("i",{className:"bi bi-bell"}),(0,t.jsx)("span",{})]})})]})})}),(0,t.jsxs)("div",{className:"offcanvas offcanvas-start",id:"affanOffcanvas","data-bs-scroll":"true",tabIndex:-1,"aria-labelledby":"affanOffcanvsLabel",children:[(0,t.jsx)("button",{className:"btn-close btn-close-white text-reset",type:"button","data-bs-dismiss":"offcanvas","aria-label":"Close"}),(0,t.jsx)("div",{className:"offcanvas-body p-0",children:(0,t.jsxs)("div",{className:"sidenav-wrapper",children:[(0,t.jsxs)("div",{className:"sidenav-profile bg-gradient",children:[(0,t.jsx)("div",{className:"sidenav-style1"}),(0,t.jsx)("div",{className:"user-profile"}),(0,t.jsx)("div",{className:"user-info",children:(0,t.jsx)("h6",{className:"user-name mb-0",children:i})})]}),(0,t.jsxs)("ul",{className:"sidenav-nav ps-0",children:[(0,t.jsx)("li",{children:(0,t.jsxs)(o(),{href:"/home",children:[(0,t.jsx)("i",{className:"bi bi-house-door"})," Home"]})}),(0,t.jsx)("li",{children:(0,t.jsxs)("div",{className:"night-mode-nav",children:[(0,t.jsx)("i",{className:"bi bi-moon"}),"dark"===g?"Light":"Dark"," Mode",(0,t.jsx)("div",{className:"form-check form-switch",children:(0,t.jsx)("input",{className:"form-check-input form-check-success",id:"darkSwitch",type:"checkbox",checked:"dark"===g,onChange:x})})]})}),(0,t.jsx)("li",{children:(0,t.jsxs)("button",{className:"btn w-100 text-start",onClick:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("COGNITO_USER_DOMAIN"),localStorage.removeItem("enterprise_id"),f.push("/")},style:{border:"none",background:"none",padding:"10px 22px "},children:[(0,t.jsx)("i",{className:"bi bi-box-arrow-right",style:{fontSize:"20px",paddingRight:10}})," Logout"]})})]}),(0,t.jsx)("div",{className:"copyright-info",children:(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{id:"copyrightYear"}),new Date().getFullYear()," \xa9 Made by ",(0,t.jsx)("a",{target:"_blank",href:"https://www.acuizen.com",children:"AcuiZen"})]})})]})})]})]})}},26957:(e,s,a)=>{"use strict";a.d(s,{AM:()=>o,Dp:()=>d,F4:()=>F,FI:()=>A,H$:()=>t,J9:()=>p,Jo:()=>g,K9:()=>n,M6:()=>u,MO:()=>m,OT:()=>O,P4:()=>N,UR:()=>C,WD:()=>S,WH:()=>v,WU:()=>w,_i:()=>i,bW:()=>y,dG:()=>l,dm:()=>U,iJ:()=>I,mh:()=>_,oo:()=>h,pZ:()=>b,u3:()=>c,x2:()=>j,xE:()=>r,xo:()=>k,yo:()=>x,zP:()=>f});let t="https://client-api.acuizen.com",l=t+"/login-configs",r=t+"/services",i=e=>t+"/files/"+e+"/presigned-url",o=t+"/users/me",n=t+"/dynamic-titles",c=t+"/users/get_users",d=t+"/files",m=t+"/observation-reports",u=t+"/my-observation-reports",h=t+"/dropdowns",p=t+"/get-blob",f=t+"/permit-reports",g=t+"/users",x=t+"/toolbox-talks",b=t+"/my-toolbox-talks",v=e=>t+"/my-assigned-actions/"+e,j=e=>t+"/inspection-checklist-submit/"+e,N=e=>t+"/observation-reports/"+e,y=e=>t+"/inspection-task-submit/"+e,k=e=>t+"/inspections/"+e,w=e=>t+"/permit-report-submit/"+e,S=e=>t+"/permit-reports-acknowledge/"+e,C=e=>t+"/permit-reports-update-status/"+e,A=e=>t+"/observation-action-submit/"+e,U=t+"/risk-assessments",I=e=>t+"/risk-assessments/"+e,F=e=>t+"/ra-team-member-submit-signature/"+e,O=t+"/permit-reports",_=e=>t+"/permit-reports/"+e},35695:(e,s,a)=>{"use strict";var t=a(18999);a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(s,{useSearchParams:function(){return t.useSearchParams}})},38336:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});var t=a(96078),l=a(26957);let r=t.A.create({baseURL:l.H$,headers:{Accept:"application/json","Content-Type":"application/json"},mode:"no-cors"});r.interceptors.request.use(async e=>(e.metadata={requestTime:Date.now()},e),e=>Promise.reject(e)),r.interceptors.response.use(e=>{var s;return e.headers["x-request-time"]=null==(s=e.config.metadata)?void 0:s.requestTime,e},async e=>{let{offlineQueue:s}=await a.e(8836).then(a.bind(a,48836)),{offlineStorage:t}=await a.e(58).then(a.bind(a,60058));if(s.shouldQueue(e)){var l,r,i,o;let a=e.config;if(await s.addRequest(a.url,(null==(l=a.method)?void 0:l.toUpperCase())||"GET",a.data,a.headers),(null==(r=a.method)?void 0:r.toLowerCase())==="get")try{if(null==(i=a.url)?void 0:i.includes("/services")){let e=await t.getServices();if(e.length>0)return console.log("\uD83D\uDCF1 Serving services from offline cache"),{data:e,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:a}}if(null==(o=a.url)?void 0:o.includes("assigned-actions")){let e=new URLSearchParams(a.url.split("?")[1]).get("filter"),s="All";if(e)try{JSON.parse(decodeURIComponent(e))}catch(l){let e=a.url.split("/"),t=e.findIndex(e=>"assigned-actions"===e);-1!==t&&e[t+1]&&(s=e[t+1])}let l=await t.getActions(s);if(l.length>0)return console.log("\uD83D\uDCF1 Serving actions from offline cache"),{data:l,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:a}}}catch(e){console.error("❌ Error serving from cache:",e)}}return Promise.reject(e)});let i=r},38983:(e,s,a)=>{"use strict";a.d(s,{default:()=>o});var t=a(95155),l=a(6874),r=a.n(l);a(12115);let i=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],o=()=>(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,t.jsx)("div",{className:"container px-0",children:(0,t.jsx)("div",{className:"footer-nav position-relative",children:(0,t.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:i.map((e,s)=>(0,t.jsx)("li",{children:(0,t.jsxs)(r(),{href:"/".concat(e.link),children:[(0,t.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,t.jsx)("span",{children:e.title})]})},s))})})})})})},56675:(e,s,a)=>{"use strict";a.d(s,{default:()=>r});var t=a(95155),l=a(12115);let r=()=>{let[e,s]=(0,l.useState)({username:"@designing-world",fullname:"Jamil Rayhan",email:"<EMAIL>",job:"UX/UI Designer",portfolio:"https://themeforest.net/user/rk_theme",address:"28/C Green Road, BD",bio:"Working as UX/UI Designer at Designing World since 2016."}),a=e=>{let{id:a,value:t}=e.target;s(e=>({...e,[a]:t}))};return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("div",{className:"page-content-wrapper py-3",children:(0,t.jsxs)("div",{className:"container",children:[(0,t.jsx)("div",{className:"card user-info-card mb-3",children:(0,t.jsxs)("div",{className:"card-body d-flex align-items-center",children:[(0,t.jsxs)("div",{className:"user-profile me-3",children:[(0,t.jsx)("img",{src:"/assets/img/bg-img/2.jpg",alt:""}),(0,t.jsx)("i",{className:"bi bi-pencil"}),(0,t.jsx)("form",{onSubmit:e=>e.preventDefault(),children:(0,t.jsx)("input",{className:"form-control",type:"file"})})]}),(0,t.jsxs)("div",{className:"user-info",children:[(0,t.jsxs)("div",{className:"d-flex align-items-center",children:[(0,t.jsx)("h5",{className:"mb-1",children:"Affan Islam"}),(0,t.jsx)("span",{className:"badge bg-warning ms-2 rounded-pill",children:"Pro"})]}),(0,t.jsx)("p",{className:"mb-0",children:"UX/UI Designer"})]})]})}),(0,t.jsx)("div",{className:"card user-data-card",children:(0,t.jsx)("div",{className:"card-body",children:(0,t.jsxs)("form",{onSubmit:s=>{s.preventDefault(),console.log("Form submitted:",e)},children:[(0,t.jsxs)("div",{className:"form-group mb-3",children:[(0,t.jsx)("label",{className:"form-label",htmlFor:"username",children:"Username"}),(0,t.jsx)("input",{className:"form-control",id:"username",type:"text",value:e.username,placeholder:"Username",onChange:a})]}),(0,t.jsxs)("div",{className:"form-group mb-3",children:[(0,t.jsx)("label",{className:"form-label",htmlFor:"fullname",children:"Full Name"}),(0,t.jsx)("input",{className:"form-control",id:"fullname",type:"text",value:e.fullname,placeholder:"Full Name",readOnly:!0})]}),(0,t.jsxs)("div",{className:"form-group mb-3",children:[(0,t.jsx)("label",{className:"form-label",htmlFor:"email",children:"Email Address"}),(0,t.jsx)("input",{className:"form-control",id:"email",type:"text",value:e.email,placeholder:"Email Address",readOnly:!0})]}),(0,t.jsxs)("div",{className:"form-group mb-3",children:[(0,t.jsx)("label",{className:"form-label",htmlFor:"job",children:"Job Title"}),(0,t.jsx)("input",{className:"form-control",id:"job",type:"text",value:e.job,placeholder:"Job Title",onChange:a})]}),(0,t.jsxs)("div",{className:"form-group mb-3",children:[(0,t.jsx)("label",{className:"form-label",htmlFor:"portfolio",children:"Portfolio URL"}),(0,t.jsx)("input",{className:"form-control",id:"portfolio",type:"url",value:e.portfolio,placeholder:"Portfolio URL",onChange:a})]}),(0,t.jsxs)("div",{className:"form-group mb-3",children:[(0,t.jsx)("label",{className:"form-label",htmlFor:"address",children:"Address"}),(0,t.jsx)("input",{className:"form-control",id:"address",type:"text",value:e.address,placeholder:"Address",onChange:a})]}),(0,t.jsxs)("div",{className:"form-group mb-3",children:[(0,t.jsx)("label",{className:"form-label",htmlFor:"bio",children:"Bio"}),(0,t.jsx)("textarea",{className:"form-control",id:"bio",name:"bio",cols:30,rows:10,value:e.bio,placeholder:"Bio",onChange:a})]}),(0,t.jsx)("button",{className:"btn btn-success w-100",type:"submit",children:"Update Now"})]})})})]})})})}},71339:(e,s,a)=>{Promise.resolve().then(a.bind(a,56675)),Promise.resolve().then(a.bind(a,38983)),Promise.resolve().then(a.bind(a,17451))},81359:(e,s,a)=>{"use strict";a.d(s,{A:()=>r,l:()=>l});let t=(0,a(51990).Z0)({name:"login",initialState:{isLogin:!1,user:null},reducers:{setLogin(e){e.isLogin=!0},setLogout(e){e.isLogin=!1,e.user=null},setUser(e,s){e.user=s.payload}}}),l=t.actions,r=t},9e4:(e,s,a)=>{"use strict";a.d(s,{D:()=>l});var t=a(12115);let l=()=>{let[e,s]=(0,t.useState)("light"),[a,l]=(0,t.useState)(!1);(0,t.useEffect)(()=>{s(localStorage.getItem("theme")||"light"),l(!0)},[]),(0,t.useEffect)(()=>{a&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,a]);let r=(0,t.useCallback)(()=>{s(e=>"dark"===e?"light":"dark")},[]),i=(0,t.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let s=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(s),e.style.display="none")},20)},1e3))}r()},[r]);return{theme:e,toggleTheme:r,handleDarkModeToggle:i}}}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,6078,1955,635,1531,8441,1684,7358],()=>s(71339)),_N_E=e.O()}]);