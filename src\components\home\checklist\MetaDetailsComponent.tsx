"use client";

import React from "react";
import { Card, Row, Col } from "react-bootstrap";
import moment from "moment";
import { ApplicationDetails } from "../types/ChecklistTypes";

interface MetaDetailsComponentProps {
    applicationDetails: ApplicationDetails;
}

const MetaDetailsComponent: React.FC<MetaDetailsComponentProps> = ({ applicationDetails }) => {
    return (
        <Card className="mb-4 shadow-sm">
            <Card.Body>
                {/* Meta Rows */}
                <Row className="mb-3">
                    <Col xs={12} md={4} className="mb-2 mb-md-0">
                        <div className="d-flex align-items-center mb-1">
                            <i className="bi bi-tag me-2 text-primary"></i>
                            <h6 className="obs-title mb-0 small">Inspection Category</h6>
                        </div>
                        <p className="obs-content ms-4 mb-0 small">
                            {applicationDetails.inspectionCategory || "N/A"}
                        </p>
                    </Col>
                    <Col xs={12} md={4} className="mb-2 mb-md-0">
                        <div className="d-flex align-items-center mb-1">
                            <i className="bi bi-calendar-check me-2 text-primary"></i>
                            <h6 className="obs-title mb-0 small">Scheduled Date</h6>
                        </div>
                        <p className="obs-content ms-4 mb-0 small">
                            {applicationDetails.scheduledDate
                                ? moment(applicationDetails.scheduledDate).format("DD-MM-YYYY")
                                : "N/A"}
                        </p>
                    </Col>
                    <Col xs={12} md={4} className="mb-2 mb-md-0">
                        <div className="d-flex align-items-center mb-1">
                            <i className="bi bi-calendar-x me-2 text-danger"></i>
                            <h6 className="obs-title mb-0 small">Due Date</h6>
                        </div>
                        <p className="obs-content ms-4 mb-0 small">
                            {applicationDetails.dueDate
                                ? moment(applicationDetails.dueDate).format("DD-MM-YYYY")
                                : "N/A"}
                        </p>
                    </Col>
                </Row>

                <Row className="mb-3">
                    <Col xs={12} md={4} className="mb-2 mb-md-0">
                        <div className="d-flex align-items-center mb-1">
                            <i className="bi bi-person-badge me-2 text-primary"></i>
                            <h6 className="obs-title mb-0 small">Inspector</h6>
                        </div>
                        <p className="obs-content ms-4 mb-0 small">
                            {applicationDetails.inspector?.firstName || "N/A"}
                        </p>
                    </Col>
                    <Col xs={12} md={4} className="mb-2 mb-md-0">
                        <div className="d-flex align-items-center mb-1">
                            <i className="bi bi-list-check me-2 text-primary"></i>
                            <h6 className="obs-title mb-0 small">Checklist</h6>
                        </div>
                        <p className="obs-content ms-4 mb-0 small">
                            {applicationDetails.checklist?.name || "N/A"}
                        </p>
                    </Col>
                    <Col xs={12} md={4} className="mb-2 mb-md-0">
                        <div className="d-flex align-items-center mb-1">
                            <i className="bi bi-code-square me-2 text-primary"></i>
                            <h6 className="obs-title mb-0 small">Checklist Version</h6>
                        </div>
                        <p className="obs-content ms-4 mb-0 small">
                            {applicationDetails.checklistVersion || "N/A"}
                        </p>
                    </Col>
                </Row>

                <Row className="mb-3">
                    <Col xs={12}>
                        <div className="d-flex align-items-center mb-1">
                            <i className="bi bi-geo-alt me-2 text-primary"></i>
                            <h6 className="obs-title mb-0 small">Location</h6>
                        </div>
                        <p className="obs-content ms-4 mb-0 small">
                            {[
                                applicationDetails.locationOne,
                                applicationDetails.locationTwo,
                                applicationDetails.locationThree,
                                applicationDetails.locationFour,
                                applicationDetails.locationFive,
                                applicationDetails.locationSix,
                            ]
                                .filter((l) => l?.name)
                                .map((l) => l?.name)
                                .join(" > ") || "N/A"}
                        </p>
                    </Col>
                </Row>

                {/* Main heading for checklist interface */}
                <div className="border-top pt-3">
                    <h6 className="fw-semibold font-size-14 mb-0 text-primary d-flex align-items-center">
                        <i className="bi bi-clipboard-check me-2"></i>
                        Select all applicable sections and complete their checklist items
                    </h6>
                </div>
            </Card.Body>
        </Card>
    );
};

export default MetaDetailsComponent;
