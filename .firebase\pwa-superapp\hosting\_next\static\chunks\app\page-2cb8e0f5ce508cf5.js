(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{26957:(e,t,o)=>{"use strict";o.d(t,{AM:()=>a,Dp:()=>u,F4:()=>G,FI:()=>w,H$:()=>s,J9:()=>p,Jo:()=>g,K9:()=>c,M6:()=>m,MO:()=>d,OT:()=>L,P4:()=>f,UR:()=>b,WD:()=>C,WH:()=>E,WU:()=>k,_i:()=>i,bW:()=>T,dG:()=>r,dm:()=>R,iJ:()=>v,mh:()=>P,oo:()=>h,pZ:()=>I,u3:()=>l,x2:()=>N,xE:()=>n,xo:()=>S,yo:()=>O,zP:()=>_});let s="https://client-api.acuizen.com",r=s+"/login-configs",n=s+"/services",i=e=>s+"/files/"+e+"/presigned-url",a=s+"/users/me",c=s+"/dynamic-titles",l=s+"/users/get_users",u=s+"/files",d=s+"/observation-reports",m=s+"/my-observation-reports",h=s+"/dropdowns",p=s+"/get-blob",_=s+"/permit-reports",g=s+"/users",O=s+"/toolbox-talks",I=s+"/my-toolbox-talks",E=e=>s+"/my-assigned-actions/"+e,N=e=>s+"/inspection-checklist-submit/"+e,f=e=>s+"/observation-reports/"+e,T=e=>s+"/inspection-task-submit/"+e,S=e=>s+"/inspections/"+e,k=e=>s+"/permit-report-submit/"+e,C=e=>s+"/permit-reports-acknowledge/"+e,b=e=>s+"/permit-reports-update-status/"+e,w=e=>s+"/observation-action-submit/"+e,R=s+"/risk-assessments",v=e=>s+"/risk-assessments/"+e,G=e=>s+"/ra-team-member-submit-signature/"+e,L=s+"/permit-reports",P=e=>s+"/permit-reports/"+e},35695:(e,t,o)=>{"use strict";var s=o(18999);o.o(s,"useRouter")&&o.d(t,{useRouter:function(){return s.useRouter}}),o.o(s,"useSearchParams")&&o.d(t,{useSearchParams:function(){return s.useSearchParams}})},41881:(e,t,o)=>{"use strict";o.d(t,{default:()=>u});var s=o(95155),r=o(12115),n=o(35695),i=o(34540),a=o(81359),c=o(26957),l=o(96078);let u=()=>{let[e,t]=(0,r.useState)(""),[o,u]=(0,r.useState)(!1),[d,m]=(0,r.useState)(null),[h,p]=(0,r.useState)({}),[_,g]=(0,r.useState)(""),O=(0,i.wA)(),I=(0,n.useRouter)();(0,r.useEffect)(()=>{fetch(c.dG,{method:"GET",headers:{"Content-Type":"application/json"}}).then(e=>{if(!e.ok)throw Error("Failed to fetch enterprise details");return e.json()}).then(e=>{console.log("Enterprise Details:",e),p(e),localStorage.setItem("COGNITO_USER_DOMAIN",e.COGNITO_USER_DOMAIN||""),localStorage.setItem("COGNITO_USER_APP_CLIENT_ID",e.COGNITO_USER_APP_CLIENT_ID||""),e.LOGO&&(localStorage.setItem("logo",e.LOGO),E(e.LOGO))}).catch(e=>{console.error("Fetching Enterprise Details Error:",e),m(e.message||"Error fetching enterprise details")})},[]);let E=async e=>{try{let t=await l.A.get((0,c._i)(e),{headers:{"Content-Type":"application/json"}});g(t.data)}catch(e){console.error("Error fetching logo:",e)}};(0,r.useEffect)(()=>{if(!h.COGNITO_USER_DOMAIN||!h.COGNITO_USER_APP_CLIENT_ID)return;let e=new URLSearchParams(window.location.search).get("code");if(!e)return;let t=window.location.origin;fetch("".concat(h.COGNITO_USER_DOMAIN,"/oauth2/token"),{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:"grant_type=authorization_code&client_id=".concat(h.COGNITO_USER_APP_CLIENT_ID,"&code=").concat(e,"&redirect_uri=").concat(encodeURIComponent(t))}).then(e=>e.json()).then(e=>{console.log("OAuth Token Response:",e),e.access_token&&e.refresh_token?(localStorage.setItem("access_token",e.access_token),localStorage.setItem("refresh_token",e.refresh_token),O(a.l.setLogin()),I.push("/home")):(console.error("Invalid Token Response:",e),m("Failed to retrieve valid tokens"))}).catch(e=>{console.error("OAuth Token Error:",e),m(e.message||"Error exchanging code for tokens")})},[h,O,I]);let N=async()=>{u(!0),m(null);try{let e=await fetch(c.dG,{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Invalid Enterprise ID or No Configuration Found");let t=await e.json();if(!t.COGNITO_USER_DOMAIN||!t.COGNITO_USER_APP_CLIENT_ID)throw Error("Invalid login configuration. Please try again.");let o=window.location.origin,s="".concat(t.COGNITO_USER_DOMAIN,"/oauth2/authorize?client_id=").concat(t.COGNITO_USER_APP_CLIENT_ID,"&response_type=code&scope=email+openid+phone&redirect_uri=").concat(encodeURIComponent(o));localStorage.setItem("logo",t.LOGO),window.location.href=s}catch(e){console.error("Login Error:",e),m(e.message||"Something went wrong"),u(!1)}};return(0,s.jsx)("div",{className:"login-wrapper d-flex align-items-center justify-content-center",children:(0,s.jsxs)("div",{className:"custom-container",children:[(0,s.jsx)("div",{className:"text-center px-4",children:(0,s.jsx)("img",{className:"login-intro-img",src:_,alt:"Enterprise Logo"})}),(0,s.jsxs)("div",{className:"register-form mt-4",children:[(0,s.jsx)("h6",{className:"mb-4 text-center",children:"AcuiZen WorkHub"}),(0,s.jsx)("button",{className:"btn btn-primary w-100",onClick:N,disabled:o,children:o?(0,s.jsx)("div",{className:"spinner-border",role:"status",children:(0,s.jsx)("span",{className:"visually-hidden",children:"Loading..."})}):h.LOGIN_BUTTON_TEXT||"Log In"})]})]})})}},52999:(e,t,o)=>{"use strict";o.d(t,{default:()=>n});var s=o(35695),r=o(12115);function n(){let e=(0,s.useRouter)();return(0,r.useEffect)(()=>{localStorage.getItem("access_token")&&e.push("/home")},[e]),null}},68666:(e,t,o)=>{Promise.resolve().then(o.bind(o,52999)),Promise.resolve().then(o.bind(o,41881))},81359:(e,t,o)=>{"use strict";o.d(t,{A:()=>n,l:()=>r});let s=(0,o(51990).Z0)({name:"login",initialState:{isLogin:!1,user:null},reducers:{setLogin(e){e.isLogin=!0},setLogout(e){e.isLogin=!1,e.user=null},setUser(e,t){e.user=t.payload}}}),r=s.actions,n=s}},e=>{var t=t=>e(e.s=t);e.O(0,[6078,635,8441,1684,7358],()=>t(68666)),_N_E=e.O()}]);