import React from "react";

const SpinnersArea = () => {
	return (
		<>
			<div className="page-content-wrapper py-3">
				{/* <!-- Element Heading --> */}
				<div className="container">
					<div className="element-heading">
						<h6>Circle loader</h6>
					</div>
				</div>

				<div className="container">
					<div className="card bg-dark">
						<div className="card-body">
							{/* <!-- Circle Loader --> */}
							<div className="circle-loader">
								<div className="circle-big"></div>
							</div>
						</div>
					</div>
				</div>

				{/* <!-- Element Heading --> */}
				<div className="container">
					<div className="element-heading mt-3">
						<h6>Dot loader</h6>
					</div>
				</div>

				<div className="container">
					<div className="card bg-primary">
						<div className="card-body py-5">
							{/* <!-- Dot Loader --> */}
							<div className="dot-loader">
								<div className="dot1"></div>
								<div className="dot2"></div>
								<div className="dot3"></div>
							</div>
						</div>
					</div>
				</div>

				{/* <!-- Element Heading --> */}
				<div className="container">
					<div className="element-heading mt-3">
						<h6>Circle spinner</h6>
					</div>
				</div>

				<div className="container">
					<div className="card direction-rtl">
						<div className="card-body d-flex">
							{/* <!-- Circle Spinner --> */}
							<div className="circle-spinner">
								<div className="circle"></div>
							</div>

							{/* <!-- Circle Spinner --> */}
							<div className="circle-spinner circle-spinner-success">
								<div className="circle"></div>
							</div>

							{/* <!-- Circle Spinner --> */}
							<div className="circle-spinner circle-spinner-warning">
								<div className="circle"></div>
							</div>

							{/* <!-- Circle Spinner --> */}
							<div className="circle-spinner circle-spinner-danger">
								<div className="circle"></div>
							</div>

							{/* <!-- Circle Spinner --> */}
							<div className="circle-spinner circle-spinner-info">
								<div className="circle"></div>
							</div>

							{/* <!-- Circle Spinner --> */}
							<div className="circle-spinner circle-spinner-light">
								<div className="circle"></div>
							</div>

							{/* <!-- Circle Spinner --> */}
							<div className="circle-spinner circle-spinner-dark">
								<div className="circle"></div>
							</div>
						</div>
					</div>
				</div>

				{/* <!-- Element Heading --> */}
				<div className="container">
					<div className="element-heading mt-3">
						<h6>Border spinner</h6>
					</div>
				</div>

				<div className="container">
					<div className="card direction-rtl">
						<div className="card-body">
							<div className="spinner-border text-primary" role="status">
								<span className="visually-hidden">Loading...</span>
							</div>

							<div className="spinner-border text-secondary" role="status">
								<span className="visually-hidden">Loading...</span>
							</div>

							<div className="spinner-border text-success" role="status">
								<span className="visually-hidden">Loading...</span>
							</div>

							<div className="spinner-border text-warning" role="status">
								<span className="visually-hidden">Loading...</span>
							</div>

							<div className="spinner-border text-danger" role="status">
								<span className="visually-hidden">Loading...</span>
							</div>

							<div className="spinner-border text-info" role="status">
								<span className="visually-hidden">Loading...</span>
							</div>

							<div className="spinner-border text-light" role="status">
								<span className="visually-hidden">Loading...</span>
							</div>

							<div className="spinner-border text-dark" role="status">
								<span className="visually-hidden">Loading...</span>
							</div>
						</div>
					</div>
				</div>

				{/* <!-- Element Heading --> */}
				<div className="container">
					<div className="element-heading mt-3">
						<h6>Growing spinner</h6>
					</div>
				</div>

				<div className="container">
					<div className="card direction-rtl">
						<div className="card-body">
							<div className="spinner-grow text-primary" role="status">
								<span className="visually-hidden">Loading...</span>
							</div>

							<div className="spinner-grow text-secondary" role="status">
								<span className="visually-hidden">Loading...</span>
							</div>

							<div className="spinner-grow text-success" role="status">
								<span className="visually-hidden">Loading...</span>
							</div>

							<div className="spinner-grow text-warning" role="status">
								<span className="visually-hidden">Loading...</span>
							</div>

							<div className="spinner-grow text-danger" role="status">
								<span className="visually-hidden">Loading...</span>
							</div>

							<div className="spinner-grow text-info" role="status">
								<span className="visually-hidden">Loading...</span>
							</div>

							<div className="spinner-grow text-light" role="status">
								<span className="visually-hidden">Loading...</span>
							</div>

							<div className="spinner-grow text-dark" role="status">
								<span className="visually-hidden">Loading...</span>
							</div>
						</div>
					</div>
				</div>

				{/* <!-- Element Heading --> */}
				<div className="container">
					<div className="element-heading mt-3">
						<h6>Spinner small size</h6>
					</div>
				</div>

				<div className="container">
					<div className="card direction-rtl">
						<div className="card-body">
							<div
								className="spinner-grow spinner-grow-sm text-primary"
								role="status"
							>
								<span className="visually-hidden">Loading...</span>
							</div>

							<div
								className="spinner-grow spinner-grow-sm text-secondary"
								role="status"
							>
								<span className="visually-hidden">Loading...</span>
							</div>

							<div
								className="spinner-grow spinner-grow-sm text-success"
								role="status"
							>
								<span className="visually-hidden">Loading...</span>
							</div>

							<div
								className="spinner-grow spinner-grow-sm text-warning"
								role="status"
							>
								<span className="visually-hidden">Loading...</span>
							</div>

							<div
								className="spinner-grow spinner-grow-sm text-danger"
								role="status"
							>
								<span className="visually-hidden">Loading...</span>
							</div>

							<div
								className="spinner-grow spinner-grow-sm text-info"
								role="status"
							>
								<span className="visually-hidden">Loading...</span>
							</div>

							<div
								className="spinner-grow spinner-grow-sm text-light"
								role="status"
							>
								<span className="visually-hidden">Loading...</span>
							</div>

							<div
								className="spinner-grow spinner-grow-sm text-dark"
								role="status"
							>
								<span className="visually-hidden">Loading...</span>
							</div>

							<div className="border-top my-3"></div>

							<div
								className="spinner-border spinner-border-sm text-primary"
								role="status"
							>
								<span className="visually-hidden">Loading...</span>
							</div>

							<div
								className="spinner-border spinner-border-sm text-secondary"
								role="status"
							>
								<span className="visually-hidden">Loading...</span>
							</div>

							<div
								className="spinner-border spinner-border-sm text-success"
								role="status"
							>
								<span className="visually-hidden">Loading...</span>
							</div>

							<div
								className="spinner-border spinner-border-sm text-warning"
								role="status"
							>
								<span className="visually-hidden">Loading...</span>
							</div>

							<div
								className="spinner-border spinner-border-sm text-danger"
								role="status"
							>
								<span className="visually-hidden">Loading...</span>
							</div>

							<div
								className="spinner-border spinner-border-sm text-info"
								role="status"
							>
								<span className="visually-hidden">Loading...</span>
							</div>

							<div
								className="spinner-border spinner-border-sm text-light"
								role="status"
							>
								<span className="visually-hidden">Loading...</span>
							</div>

							<div
								className="spinner-border spinner-border-sm text-dark"
								role="status"
							>
								<span className="visually-hidden">Loading...</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</>
	);
};

export default SpinnersArea;
