import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import * as Haptics from 'expo-haptics';
import React, { useState } from 'react';
import { Modal, ScrollView, StyleSheet, TextInput, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function HistoryScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [selectedFilter, setSelectedFilter] = useState('All');

  // Search and filter states
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilterPopup, setShowFilterPopup] = useState(false);

  const filters = ['All', 'Reports', 'Permits', 'Assessments', 'Audits'];

  const historyItems = [
    {
      id: 'IR-2024-001',
      type: 'Incident Report',
      title: 'Slip and Fall - Warehouse A',
      date: '2024-01-15',
      status: 'Completed',
      icon: 'exclamationmark.triangle.fill' as const,
      color: '#FF3B30',
    },
    {
      id: 'RA-2024-003',
      type: 'Risk Assessment',
      title: 'Chemical Storage Area Review',
      date: '2024-01-14',
      status: 'In Progress',
      icon: 'shield.fill' as const,
      color: '#FF9500',
    },
    {
      id: 'PTW-2024-012',
      type: 'E-Permit',
      title: 'Hot Work - Maintenance Bay',
      date: '2024-01-13',
      status: 'Approved',
      icon: 'doc.text.fill' as const,
      color: '#34C759',
    },
    {
      id: 'OBS-2024-008',
      type: 'Observation',
      title: 'Unsafe Ladder Usage',
      date: '2024-01-12',
      status: 'Closed',
      icon: 'eye.fill' as const,
      color: '#007AFF',
    },
    {
      id: 'AUD-2024-002',
      type: 'Safety Audit',
      title: 'Monthly Safety Inspection',
      date: '2024-01-10',
      status: 'Completed',
      icon: 'checkmark.seal.fill' as const,
      color: '#5856D6',
    },
    {
      id: 'DOC-2024-005',
      type: 'Document',
      title: 'Safety Procedure Update',
      date: '2024-01-09',
      status: 'Published',
      icon: 'folder.fill' as const,
      color: '#AF52DE',
    },
  ];

  // Filter function
  const filteredItems = historyItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.id.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = selectedFilter === 'All' ||
                         (selectedFilter === 'Reports' && (item.type.includes('Report') || item.type.includes('Observation'))) ||
                         (selectedFilter === 'Permits' && item.type.includes('Permit')) ||
                         (selectedFilter === 'Assessments' && item.type.includes('Assessment')) ||
                         (selectedFilter === 'Audits' && item.type.includes('Audit'));
    return matchesSearch && matchesFilter;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
      case 'Approved':
      case 'Closed':
      case 'Published':
        return '#2E7D32'; // Dark green
      case 'In Progress':
        return '#F9A825'; // Dark yellow
      case 'Pending':
        return '#F57C00'; // Dark orange
      default:
        return colors.text;
    }
  };

  const getStatusBackgroundColor = (status: string) => {
    switch (status) {
      case 'Completed':
      case 'Approved':
      case 'Closed':
      case 'Published':
        return '#E8F5E9'; // Light green
      case 'In Progress':
        return '#FFF8E1'; // Light yellow
      case 'Pending':
        return '#FFF3E0'; // Light orange
      default:
        return '#F5F5F5';
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={['top']}>
      <ScrollView style={{ flex: 1 }}>
      <ThemedView style={styles.header}>
        <ThemedText type="title" style={styles.headerTitle}>
          History
        </ThemedText>
        <ThemedText style={styles.headerSubtitle}>
          View your activity history and records
        </ThemedText>

        {/* Search and Filter Row */}
        <View style={styles.searchFilterContainer}>
          <View style={styles.searchRowContainer}>
            <View style={styles.searchContainer}>
              <IconSymbol name="magnifyingglass.circle.fill" size={20} color="#8E8E93" style={styles.searchIcon} />
              <TextInput
                style={[styles.searchInput, { color: colors.text }]}
                placeholder="Search history..."
                placeholderTextColor="#8E8E93"
                value={searchQuery}
                onChangeText={setSearchQuery}
                clearButtonMode="while-editing"
              />
            </View>

            <TouchableOpacity
              style={[styles.filterIconButton, {
                borderColor: selectedFilter !== 'All' ? '#1976D2' : colors.border,
                backgroundColor: selectedFilter !== 'All' ? '#1976D2' : '#F2F2F7'
              }]}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setShowFilterPopup(true);
              }}
              activeOpacity={0.7}
            >
              <IconSymbol
                name="slider.horizontal.3"
                size={18}
                color={selectedFilter !== 'All' ? '#FFFFFF' : '#1976D2'}
              />
            </TouchableOpacity>

            {/* Clear Filter Button */}
            {selectedFilter !== 'All' && (
              <TouchableOpacity
                style={styles.clearFilterButton}
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  setSelectedFilter('All');
                }}
                activeOpacity={0.7}
              >
                <IconSymbol name="xmark.circle.fill" size={20} color="#FF3B30" />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </ThemedView>

      <ThemedView style={styles.historyContainer}>
        {filteredItems.map((item) => (
          <ThemedView key={item.id} style={[styles.historyCard, { borderColor: colors.border }]}>
            <View style={styles.cardHeader}>
              <View style={[styles.iconContainer, { backgroundColor: item.color + '20' }]}>
                <IconSymbol name={item.icon} size={20} color={item.color} />
              </View>
              <View style={styles.cardInfo}>
                <ThemedText type="defaultSemiBold" style={styles.cardTitle}>
                  {item.title}
                </ThemedText>
                <ThemedText style={styles.cardType}>
                  {item.type} • {item.id}
                </ThemedText>
              </View>
              <View style={styles.cardMeta}>
                <View style={[styles.statusBadge, { backgroundColor: getStatusBackgroundColor(item.status) }]}>
                  <ThemedText style={[styles.statusText, { color: getStatusColor(item.status) }]}>
                    {item.status}
                  </ThemedText>
                </View>
                <ThemedText style={styles.dateText}>{item.date}</ThemedText>
              </View>
            </View>
          </ThemedView>
        ))}
      </ThemedView>

      <ThemedView style={styles.footer}>
        <ThemedText style={styles.footerText}>
          Showing {filteredItems.length} of {historyItems.length} items
        </ThemedText>
      </ThemedView>

      {/* Filter Popup Modal */}
      <Modal
        visible={showFilterPopup}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowFilterPopup(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowFilterPopup(false)}
        >
          <View style={[styles.filterPopup, { backgroundColor: colors.background, borderColor: colors.border }]}>
            <View style={styles.filterPopupHeader}>
              <ThemedText type="defaultSemiBold" style={styles.filterPopupTitle}>
                Filter History
              </ThemedText>
              <TouchableOpacity
                onPress={() => setShowFilterPopup(false)}
                style={styles.closeButton}
              >
                <IconSymbol name="xmark.circle.fill" size={24} color="#8E8E93" />
              </TouchableOpacity>
            </View>

            <View style={styles.filterOptionsContainer}>
              {filters.map((filter) => (
                <TouchableOpacity
                  key={filter}
                  style={[
                    styles.filterOption,
                    {
                      backgroundColor: selectedFilter === filter ? '#E3F2FD' : 'transparent',
                      borderColor: colors.border,
                    },
                  ]}
                  onPress={() => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    setSelectedFilter(filter);
                    setShowFilterPopup(false);
                  }}
                >
                  <ThemedText
                    style={[
                      styles.filterOptionText,
                      { color: selectedFilter === filter ? '#1976D2' : colors.text },
                    ]}
                  >
                    {filter}
                  </ThemedText>
                  {selectedFilter === filter && (
                    <IconSymbol name="checkmark.circle.fill" size={20} color="#1976D2" />
                  )}
                </TouchableOpacity>
              ))}

              {/* Clear All Filters Option */}
              {selectedFilter !== 'All' && (
                <TouchableOpacity
                  style={styles.clearAllFiltersOption}
                  onPress={() => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                    setSelectedFilter('All');
                    setShowFilterPopup(false);
                  }}
                >
                  <IconSymbol name="xmark.circle" size={20} color="#FF3B30" />
                  <ThemedText style={styles.clearAllFiltersText}>
                    Clear All Filters
                  </ThemedText>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  headerTitle: {
    marginBottom: 8,
  },
  headerSubtitle: {
    opacity: 0.7,
  },
  filtersContainer: {
    marginBottom: 20,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
  },
  historyContainer: {
    marginBottom: 24,
  },
  historyCard: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginBottom: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  cardInfo: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 16,
    marginBottom: 4,
  },
  cardType: {
    fontSize: 14,
    opacity: 0.7,
  },
  cardMeta: {
    alignItems: 'flex-end',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  dateText: {
    fontSize: 12,
    opacity: 0.6,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  footerText: {
    fontSize: 14,
    opacity: 0.6,
  },
  // Search and filter styles
  searchFilterContainer: {
    marginTop: 16,
  },
  searchRowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F2F2F7',
    borderRadius: 10,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 4,
  },
  filterIconButton: {
    width: 44,
    height: 44,
    borderRadius: 10,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearFilterButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFF2F2',
    marginLeft: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 4,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  filterPopup: {
    width: '100%',
    maxWidth: 320,
    borderRadius: 16,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  filterPopupHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  filterPopupTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  filterOptionsContainer: {
    padding: 16,
  },
  filterOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 8,
  },
  filterOptionText: {
    fontSize: 16,
    fontWeight: '500',
  },
  clearAllFiltersOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: '#FFF2F2',
    borderWidth: 1,
    borderColor: '#FFE5E5',
    marginTop: 8,
  },
  clearAllFiltersText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF3B30',
    marginLeft: 12,
  },
});
