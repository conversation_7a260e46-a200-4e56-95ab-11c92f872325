// Offline-aware API service for services and actions
import API from './API';
import { offlineStorage, ServiceType, ActionType } from './offlineStorage';
import { offlineQueue } from './offlineQueue';
import { SERVICE_DETAILS, ASSIGNED_ACTION_URL_BY_ID } from '../constant';

export class OfflineAPI {
  // Services API with offline support
  async getServices(): Promise<ServiceType[]> {
    try {
      console.log('🌐 Fetching services from API...');
      const response = await API.get(SERVICE_DETAILS);
      
      if (response.status === 200 && Array.isArray(response.data)) {
        // Save to offline storage
        await offlineStorage.saveServices(response.data);
        console.log('✅ Services fetched and cached successfully');
        return response.data;
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.log('❌ Failed to fetch services from API, trying offline cache...');
      
      // Try to get from offline storage
      const cachedServices = await offlineStorage.getServices();
      if (cachedServices.length > 0) {
        console.log('📱 Serving services from offline cache');
        return cachedServices;
      }
      
      // If no cache available, throw the original error
      throw error;
    }
  }

  // Actions API with offline support
  async getActions(filter: string = 'All'): Promise<ActionType[]> {
    try {
      console.log(`🌐 Fetching actions from API (filter: ${filter})...`);
      
      const uriString = {
        include: [{ relation: "submittedBy" }]
      };
      
      const url = ASSIGNED_ACTION_URL_BY_ID(filter) + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
      const response = await API.get(url);
      
      if (response.status === 200 && Array.isArray(response.data)) {
        // Save to offline storage
        await offlineStorage.saveActions(response.data);
        console.log('✅ Actions fetched and cached successfully');
        return response.data;
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.log('❌ Failed to fetch actions from API, trying offline cache...');
      
      // Try to get from offline storage
      const cachedActions = await offlineStorage.getActions(filter);
      if (cachedActions.length > 0) {
        console.log('📱 Serving actions from offline cache');
        return cachedActions;
      }
      
      // If no cache available, throw the original error
      throw error;
    }
  }

  // Generic API method with offline support
  async request(config: any): Promise<any> {
    try {
      const response = await API.request(config);
      return response;
    } catch (error) {
      // Check if we should queue this request
      if (offlineQueue.shouldQueue(error)) {
        await offlineQueue.addRequest(
          config.url,
          config.method?.toUpperCase() || 'GET',
          config.data,
          config.headers
        );
      }
      throw error;
    }
  }

  // Check if data is fresh (less than 5 minutes old)
  async isDataFresh(type: 'services' | 'actions'): Promise<boolean> {
    const lastSync = await offlineStorage.getMetadata(`${type}LastSync`);
    if (!lastSync) return false;
    
    const fiveMinutes = 5 * 60 * 1000;
    return (Date.now() - lastSync) < fiveMinutes;
  }

  // Force refresh data from API
  async forceRefresh(type: 'services' | 'actions', filter?: string): Promise<any> {
    if (!navigator.onLine) {
      throw new Error('Cannot refresh data while offline');
    }

    if (type === 'services') {
      return this.getServices();
    } else if (type === 'actions') {
      return this.getActions(filter || 'All');
    }
  }

  // Get offline status and cache info
  async getOfflineStatus(): Promise<{
    isOnline: boolean;
    hasCache: boolean;
    cacheInfo: {
      services: number;
      actions: number;
      queue: number;
    };
    lastSync: {
      services: number | null;
      actions: number | null;
    };
  }> {
    const cacheInfo = await offlineStorage.getStorageInfo();
    const servicesLastSync = await offlineStorage.getMetadata('servicesLastSync');
    const actionsLastSync = await offlineStorage.getMetadata('actionsLastSync');

    return {
      isOnline: navigator.onLine,
      hasCache: cacheInfo.services > 0 || cacheInfo.actions > 0,
      cacheInfo,
      lastSync: {
        services: servicesLastSync,
        actions: actionsLastSync
      }
    };
  }

  // Clear all offline data
  async clearOfflineData(): Promise<void> {
    await offlineStorage.clearAllData();
    await offlineQueue.clearQueue();
    console.log('🗑️ All offline data cleared');
  }

  // Sync all data when online
  async syncAll(): Promise<void> {
    if (!navigator.onLine) {
      throw new Error('Cannot sync while offline');
    }

    console.log('🔄 Starting full sync...');
    
    try {
      // Sync services
      await this.getServices();
      
      // Sync actions for all filters
      const services = await offlineStorage.getServices();
      const filters = ['All', ...services.map(s => s.maskName)];
      
      for (const filter of filters) {
        await this.getActions(filter);
      }
      
      // Process offline queue
      await offlineQueue.processQueue();
      
      console.log('✅ Full sync completed');
    } catch (error) {
      console.error('❌ Sync failed:', error);
      throw error;
    }
  }

  // Subscribe to online/offline events
  onConnectionChange(callback: (isOnline: boolean) => void): () => void {
    const handleOnline = () => callback(true);
    const handleOffline = () => callback(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Return cleanup function
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }

  // Get data age in human readable format
  async getDataAge(type: 'services' | 'actions'): Promise<string> {
    const lastSync = await offlineStorage.getMetadata(`${type}LastSync`);
    if (!lastSync) return 'Never synced';

    const ageMs = Date.now() - lastSync;
    const ageMinutes = Math.floor(ageMs / (1000 * 60));
    const ageHours = Math.floor(ageMinutes / 60);
    const ageDays = Math.floor(ageHours / 24);

    if (ageDays > 0) {
      return `${ageDays} day${ageDays > 1 ? 's' : ''} ago`;
    } else if (ageHours > 0) {
      return `${ageHours} hour${ageHours > 1 ? 's' : ''} ago`;
    } else if (ageMinutes > 0) {
      return `${ageMinutes} minute${ageMinutes > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  }

  // Check if we have any offline data
  async hasOfflineData(): Promise<boolean> {
    const info = await offlineStorage.getStorageInfo();
    return info.services > 0 || info.actions > 0;
  }
}

// Export singleton instance
export const offlineAPI = new OfflineAPI();
