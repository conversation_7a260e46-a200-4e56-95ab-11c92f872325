(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[232],{11823:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var a=n(86608);function r(e){var t=function(e,t){if("object"!=(0,a.A)(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=(0,a.A)(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==(0,a.A)(t)?t:t+""}},20235:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var a=n(93495);function r(e,t){if(null==e)return{};var n,r,l=(0,a.A)(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(r=0;r<s.length;r++)n=s[r],-1===t.indexOf(n)&&({}).propertyIsEnumerable.call(e,n)&&(l[n]=e[n])}return l}},27347:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var a=n(95155),r=n(12115),l=n(16639),s=n(60902),o=n(56160);let i=e=>{var t;let{title:n,options:i,selectedValue:c,disabled:d,onChange:u,placeholder:h="Select Option",clearable:p=!1,multiSelect:m=!1,selectedValues:f=[],onMultiChange:v,maxSelections:y=2}=e,[b,g]=(0,r.useState)(!1),[x,j]=(0,r.useState)(""),I=i.filter(e=>e.label.toLowerCase().includes(x.toLowerCase())),A=m?f.length>0?f.map(e=>{var t;return null==(t=i.find(t=>t.value===e))?void 0:t.label}).join(", "):h:(null==(t=i.find(e=>e.value===c))?void 0:t.label)||h,w=()=>{g(!1)},N=e=>{if(m&&v){let t=f||[];t.includes(e)?v(t.filter(t=>t!==e)):t.length<y&&v([...t,e])}else null==u||u(e),g(!1)};return(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)(l.A.Label,{children:n}),(0,a.jsxs)("div",{className:"d-flex gap-2",children:[(0,a.jsx)(s.A,{disabled:d,variant:"outline-secondary",className:"flex-grow-1 text-start",onClick:()=>{g(!0),j("")},children:A}),p&&(m?f.length>0:c)&&(0,a.jsx)(s.A,{variant:"outline-danger",onClick:()=>{m&&v?v([]):null==u||u("")},children:"Clear"})]}),(0,a.jsxs)(o.A,{show:b,onHide:w,centered:!0,children:[(0,a.jsx)(o.A.Header,{closeButton:!0,children:(0,a.jsx)(o.A.Title,{children:n})}),(0,a.jsxs)(o.A.Body,{children:[(0,a.jsx)(l.A.Control,{type:"text",placeholder:"Search...",value:x,onChange:e=>j(e.target.value),className:"mb-3"}),I.length>0?(0,a.jsx)("div",{style:{maxHeight:"300px",overflowY:"auto"},children:I.map(e=>{let t=m?f.includes(e.value):e.value===c,n=m&&!t&&f.length>=y;return(0,a.jsxs)("button",{className:"list-group-item list-group-item-action mb-1 ".concat(t?"active":""," ").concat(n?"disabled":""),onClick:()=>!n&&N(e.value),disabled:n,children:[m&&(0,a.jsx)("input",{type:"checkbox",checked:t,onChange:()=>{},className:"me-2",disabled:n}),e.label]},e.value)})}):(0,a.jsx)("p",{className:"text-muted",children:"No options found."}),m&&(0,a.jsxs)("div",{className:"mt-2 text-muted small",children:["Selected: ",f.length,"/",y]})]}),(0,a.jsxs)(o.A.Footer,{children:[m&&(0,a.jsx)(s.A,{variant:"primary",onClick:w,className:"me-auto",children:"Done"}),(0,a.jsx)(s.A,{variant:"secondary",onClick:w,children:"Close"})]})]})]})}},28383:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var a=n(11823);function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(0,a.A)(r.key),r)}}function l(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}},30857:(e,t,n)=>{"use strict";function a(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}n.d(t,{A:()=>a})},38289:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var a=n(42222);function r(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,a.A)(e,t)}},67076:(e,t,n)=>{Promise.resolve().then(n.bind(n,83834))},79630:(e,t,n)=>{"use strict";function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(null,arguments)}n.d(t,{A:()=>a})},83834:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>b});var a=n(95155),r=n(12115),l=n(46554),s=n(16639),o=n(60902),i=n(16129),c=n(54239);n(35279);var d=n(23327),u=n(38336),h=n(24752),p=n.n(h),m=n(35695),f=n(26957),v=n(27347);let y={name:"",scheduledDate:null,dueDate:null,inspectionCategory:"",status:"",maskId:"",checklistVersion:"",checklistId:"",inspectorId:"",assignedById:"",locationOneId:"",locationTwoId:"",locationThreeId:"",locationFourId:"",locationFiveId:"",locationSixId:""};function b(){let e=(0,m.useRouter)(),[t,n]=(0,r.useState)(y),[h,b]=(0,r.useState)([]),[g,x]=(0,r.useState)([]),[j,I]=(0,r.useState)([]),[A,w]=(0,r.useState)([]),[N,C]=(0,r.useState)({}),D=async()=>{try{let e=await u.A.post(f.u3,{locationOneId:t.locationOneId,locationTwoId:t.locationTwoId,locationThreeId:t.locationThreeId,locationFourId:t.locationFourId,mode:"ins_inspector"});if(200===e.status){let t=e.data.map(e=>{var t;return{label:"".concat(e.firstName," ").concat(null!=(t=e.lastName)?t:"").trim(),value:e.id}});b(t)}}catch(e){console.error("Error fetching inspectors:",e)}},S=(0,r.useCallback)(async()=>{try{let t="".concat(f.oo,"?filter=").concat(encodeURIComponent(JSON.stringify({where:{maskId:"ins_category"},include:[{relation:"dropdownItems"}]}))),n=await u.A.get(t);if(200===n.status){var e;let t=(null==(e=n.data[0])?void 0:e.dropdownItems.map(e=>({label:e.name,value:e.name})))||[];x(t)}}catch(e){console.error("Error fetching inspection categories:",e)}},[]),k=async()=>{try{let e=await u.A.get("/checklists?filter=".concat(encodeURIComponent(JSON.stringify({where:{status:"Published"}}))));if(200===e.status){let t=e.data.map(e=>({label:"".concat(e.name," (v").concat(e.version,")"),value:e.id,version:e.version,category:e.category||""}));I(t)}}catch(e){console.error("Error fetching checklist options:",e)}};(0,r.useEffect)(()=>{S(),k()},[]),(0,r.useEffect)(()=>{D()},[t.locationOneId,t.locationTwoId,t.locationThreeId,t.locationFourId]);let O=()=>{let e={};return t.scheduledDate||(e.scheduledDate="Scheduled date is required"),t.dueDate?t.scheduledDate&&t.dueDate<t.scheduledDate&&(e.dueDate="Due date cannot be before scheduled date"):e.dueDate="Due date is required",t.inspectionCategory||(e.inspectionCategory="Inspection category is required"),t.checklistId||(e.checklistId="Checklist selection is required"),t.inspectorId||(e.inspectorId="Inspector is required"),C(e),0===Object.keys(e).length},E=async()=>{if(!O())return void p().fire("Validation","Please fix the highlighted errors.","error");try{var a,r,l,s;let o={...t,scheduledDate:null!=(l=null==(a=t.scheduledDate)?void 0:a.toISOString())?l:null,dueDate:null!=(s=null==(r=t.dueDate)?void 0:r.toISOString())?s:null},i=await u.A.post("/inspections",o);(200===i.status||201===i.status)&&(p().fire("Success","Inspection saved successfully","success"),n(y),C({}),e.back())}catch(e){console.error("Error saving inspection:",e),p().fire("Error","Failed to save inspection.","error")}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.default,{heading:"New Inspection"}),(0,a.jsx)("div",{className:"page-content-wrapper py-3",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"card",children:(0,a.jsxs)("div",{className:"card-body p-3",children:[(0,a.jsx)("div",{className:"bg-light shadow-sm p-2 mt-3 mb-3",children:(0,a.jsx)(d.A,{handleFilter:(e,t,a,r,l,s)=>{n(n=>({...n,locationOneId:e,locationTwoId:t,locationThreeId:a,locationFourId:r,locationFiveId:l,locationSixId:s}))},getLocation:t,disabled:!1})}),(0,a.jsx)(s.A.Group,{className:"row mb-3",children:(0,a.jsxs)("div",{className:"col-md-12",children:[(0,a.jsx)(s.A.Label,{className:"d-flex",children:"Scheduled Date"}),(0,a.jsx)(c.Ay,{selected:t.scheduledDate?new Date(t.scheduledDate):null,onChange:e=>n(t=>({...t,scheduledDate:e})),dateFormat:"dd/MM/yyyy",minDate:new Date,className:"form-control",placeholderText:"Select scheduled date"}),N.scheduledDate&&(0,a.jsx)("small",{className:"text-danger",children:N.scheduledDate})]})}),(0,a.jsx)(s.A.Group,{className:"row mb-3",children:(0,a.jsxs)("div",{className:"col-md-12",children:[(0,a.jsx)(s.A.Label,{className:"d-flex",children:"Due Date"}),(0,a.jsx)(c.Ay,{selected:t.dueDate?new Date(t.dueDate):null,onChange:e=>n(t=>({...t,dueDate:e})),dateFormat:"dd/MM/yyyy",minDate:t.scheduledDate||new Date,className:"form-control",placeholderText:"Select due date"}),N.dueDate&&(0,a.jsx)("small",{className:"text-danger",children:N.dueDate})]})}),(0,a.jsxs)(s.A.Group,{className:"mb-3",children:[(0,a.jsx)(s.A.Label,{children:"Inspection Category"}),(0,a.jsx)(i.Ay,{name:"inspectionCategory",value:g.find(e=>e.value===t.inspectionCategory)||null,options:g,onChange:e=>{let t=(null==e?void 0:e.value)||"";n(e=>({...e,inspectionCategory:t,checklistId:"",checklistVersion:""})),w(j.filter(e=>e.category.toLowerCase().includes(t.toLowerCase())))},placeholder:"Select Category",isClearable:!0}),N.inspectionCategory&&(0,a.jsx)("small",{className:"text-danger",children:N.inspectionCategory})]}),(0,a.jsxs)(s.A.Group,{className:"mb-3",children:[(0,a.jsx)(s.A.Label,{children:"Checklist"}),(0,a.jsx)(i.Ay,{name:"checklistId",value:A.find(e=>e.value===t.checklistId)||null,options:A,onChange:e=>{n(t=>({...t,checklistId:(null==e?void 0:e.value)||"",checklistVersion:(null==e?void 0:e.version)||""}))},placeholder:"Select Checklist",isClearable:!0,isDisabled:!t.inspectionCategory}),N.checklistId&&(0,a.jsx)("small",{className:"text-danger",children:N.checklistId})]}),(0,a.jsx)(v.A,{title:"Inspector",options:h,selectedValue:t.inspectorId,onChange:e=>{n(t=>({...t,inspectorId:e}))},placeholder:"Select Inspector",clearable:!0,disabled:!1}),N.inspectorId&&(0,a.jsx)("small",{className:"text-danger",children:N.inspectorId})]})})})}),(0,a.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,a.jsx)("div",{className:"container px-0",children:(0,a.jsx)("div",{className:"footer-nav position-relative",children:(0,a.jsx)("div",{className:"h-100 d-flex align-items-center justify-content-between ps-0 w-100",children:(0,a.jsx)(o.A,{variant:"primary",className:" w-100",onClick:E,children:"Submit"})})})})})]})}},86608:(e,t,n)=>{"use strict";function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}n.d(t,{A:()=>a})},88748:(e,t,n)=>{"use strict";function a(e){return(a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function r(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(r=function(){return!!e})()}n.d(t,{A:()=>s});var l=n(86608);function s(e){var t=r();return function(){var n,r=a(e);n=t?Reflect.construct(r,arguments,a(this).constructor):r.apply(this,arguments);if(n&&("object"==(0,l.A)(n)||"function"==typeof n))return n;if(void 0!==n)throw TypeError("Derived constructors may only return object or undefined");if(void 0===this)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return this}}}},e=>{var t=t=>e(e.s=t);e.O(0,[5302,8320,5579,2545,6078,635,4816,5898,6639,3678,124,6129,9314,8441,1684,7358],()=>t(67076)),_N_E=e.O()}]);