"use client";

import React from "react";
import { ChecklistComponent, ErrorBuckets, OptionType } from "../types/ChecklistTypes";
import SignatureCanvas from "react-signature-canvas";
import HeaderComponent from "./HeaderComponent";
import SectionHeaderComponent from "./SectionHeaderComponent";
import TextBodyComponent from "./TextBodyComponent";
import DateComponent from "./DateComponent";
import SignatureComponent from "./SignatureComponent";
import CheckpointComponent from "./CheckpointComponent";
import CheckpointGroupComponent from "./CheckpointGroupComponent";
import TextInputComponent from "./TextInputComponent";
import ImageInputComponent from "./ImageInputComponent";
import AttachmentInputComponent from "./AttachmentInputComponent";

interface ChecklistComponentRendererProps {
    component: ChecklistComponent;
    componentIndex: number;
    checklistData: ChecklistComponent[];
    setChecklistData: React.Dispatch<React.SetStateAction<ChecklistComponent[]>>;
    errorMap: ErrorBuckets;
    assessor: OptionType[];
    signRefs: React.MutableRefObject<{ [key: string]: SignatureCanvas | null }>;
    handleFileUpload: (files: string[], componentIndex: number, checkpointIndex?: number) => void;
    handleSaveSignature: (componentId: string, cIdx: number) => void;
    handleClearSignature: (componentId: string, cIdx: number) => void;
}

const ChecklistComponentRenderer: React.FC<ChecklistComponentRendererProps> = ({
    component,
    componentIndex,
    checklistData,
    setChecklistData,
    errorMap,
    assessor,
    signRefs,
    handleFileUpload,
    handleSaveSignature,
    handleClearSignature,
}) => {
    // Render different component types with proper separation
    switch (component.type) {
        case "header":
            return (
                <HeaderComponent 
                    component={component}
                    componentIndex={componentIndex}
                />
            );

        case "section-header":
            return (
                <SectionHeaderComponent 
                    component={component}
                    componentIndex={componentIndex}
                />
            );

        case "text-body":
            return (
                <TextBodyComponent 
                    component={component}
                    componentIndex={componentIndex}
                />
            );

        case "date":
            return (
                <DateComponent
                    component={component}
                    componentIndex={componentIndex}
                    checklistData={checklistData}
                    setChecklistData={setChecklistData}
                    errorMap={errorMap}
                />
            );

        case "sign":
            return (
                <SignatureComponent
                    component={component}
                    componentIndex={componentIndex}
                    checklistData={checklistData}
                    setChecklistData={setChecklistData}
                    errorMap={errorMap}
                    signRefs={signRefs}
                    handleSaveSignature={handleSaveSignature}
                    handleClearSignature={handleClearSignature}
                />
            );

        case "checkpoint":
            return (
                <CheckpointComponent
                    component={component}
                    componentIndex={componentIndex}
                    checklistData={checklistData}
                    setChecklistData={setChecklistData}
                    errorMap={errorMap}
                    assessor={assessor}
                    handleFileUpload={handleFileUpload}
                />
            );

        case "checkpoint-group":
            return (
                <CheckpointGroupComponent
                    component={component}
                    componentIndex={componentIndex}
                    checklistData={checklistData}
                    setChecklistData={setChecklistData}
                    errorMap={errorMap}
                    assessor={assessor}
                    handleFileUpload={handleFileUpload}
                />
            );

        case "text-input":
            return (
                <TextInputComponent
                    component={component}
                    componentIndex={componentIndex}
                    checklistData={checklistData}
                    setChecklistData={setChecklistData}
                    errorMap={errorMap}
                />
            );

        case "image-input":
            return (
                <ImageInputComponent
                    component={component}
                    componentIndex={componentIndex}
                    checklistData={checklistData}
                    setChecklistData={setChecklistData}
                    errorMap={errorMap}
                />
            );

        case "attachment-input":
            return (
                <AttachmentInputComponent
                    component={component}
                    componentIndex={componentIndex}
                    checklistData={checklistData}
                    setChecklistData={setChecklistData}
                    errorMap={errorMap}
                />
            );

        default:
            return null;
    }
};

export default ChecklistComponentRenderer;
