"use client";

import React, { useState, useEffect } from 'react';
import { offlineQueue, QueueStats } from '@/services/offlineQueue';
import { offlineAPI } from '@/services/offlineAPI';

interface SyncStatusProps {
  className?: string;
  compact?: boolean;
}

const SyncStatus: React.FC<SyncStatusProps> = ({ 
  className = '', 
  compact = false 
}) => {
  const [queueStats, setQueueStats] = useState<QueueStats>({ pending: 0, failed: 0, processing: false });
  const [isOnline, setIsOnline] = useState(true);
  const [lastSyncTime, setLastSyncTime] = useState<string>('');
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    // Initialize online status
    setIsOnline(navigator.onLine);

    // Subscribe to queue updates
    const unsubscribeQueue = offlineQueue.subscribe(setQueueStats);

    // Subscribe to connection changes
    const unsubscribeConnection = offlineAPI.onConnectionChange((online) => {
      setIsOnline(online);
      if (online) {
        // Auto-sync when coming back online
        setTimeout(() => {
          offlineQueue.processQueue();
        }, 1000);
      }
    });

    // Update last sync time
    updateLastSyncTime();

    return () => {
      unsubscribeQueue();
      unsubscribeConnection();
    };
  }, []);

  const updateLastSyncTime = async () => {
    try {
      const servicesAge = await offlineAPI.getDataAge('services');
      const actionsAge = await offlineAPI.getDataAge('actions');
      
      // Use the more recent sync time
      if (servicesAge === 'Never synced' && actionsAge === 'Never synced') {
        setLastSyncTime('Never synced');
      } else if (servicesAge === 'Never synced') {
        setLastSyncTime(actionsAge);
      } else if (actionsAge === 'Never synced') {
        setLastSyncTime(servicesAge);
      } else {
        // Compare and use the more recent one
        setLastSyncTime(servicesAge.includes('Just now') ? servicesAge : actionsAge);
      }
    } catch (error) {
      console.error('Error updating last sync time:', error);
    }
  };

  const handleSync = async () => {
    if (!isOnline) return;
    
    try {
      await offlineAPI.syncAll();
      updateLastSyncTime();
    } catch (error) {
      console.error('Sync failed:', error);
    }
  };

  const handleRetryFailed = async () => {
    try {
      await offlineQueue.retryFailedRequests();
    } catch (error) {
      console.error('Retry failed:', error);
    }
  };

  const getSyncStatusColor = () => {
    if (!isOnline) return '#f44336';
    if (queueStats.processing) return '#ff9800';
    if (queueStats.failed > 0) return '#f44336';
    if (queueStats.pending > 0) return '#2196f3';
    return '#4caf50';
  };

  const getSyncStatusText = () => {
    if (!isOnline) return 'Offline';
    if (queueStats.processing) return 'Syncing...';
    if (queueStats.failed > 0) return `${queueStats.failed} failed`;
    if (queueStats.pending > 0) return `${queueStats.pending} pending`;
    return 'Synced';
  };

  const getSyncIcon = () => {
    if (!isOnline) return 'bi-wifi-off';
    if (queueStats.processing) return 'bi-arrow-repeat';
    if (queueStats.failed > 0) return 'bi-exclamation-triangle';
    if (queueStats.pending > 0) return 'bi-cloud-upload';
    return 'bi-check-circle';
  };

  if (compact) {
    return (
      <div className={`d-flex align-items-center ${className}`}>
        <div
          className="rounded-circle d-flex align-items-center justify-content-center me-2"
          style={{
            width: '20px',
            height: '20px',
            backgroundColor: getSyncStatusColor(),
            color: 'white'
          }}
        >
          <i 
            className={`${getSyncIcon()} ${queueStats.processing ? 'spin' : ''}`}
            style={{ fontSize: '10px' }}
          ></i>
        </div>
        <span style={{ fontSize: '12px', color: getSyncStatusColor() }}>
          {getSyncStatusText()}
        </span>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-3 p-3 shadow-sm ${className}`}>
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-3">
        <h6 className="mb-0" style={{ fontSize: '14px', fontWeight: '600' }}>
          Sync Status
        </h6>
        <button
          className="btn btn-link p-0"
          onClick={() => setShowDetails(!showDetails)}
          style={{ fontSize: '12px', color: '#666' }}
        >
          {showDetails ? 'Hide' : 'Details'}
        </button>
      </div>

      {/* Status Indicator */}
      <div className="d-flex align-items-center mb-3">
        <div
          className="rounded-circle d-flex align-items-center justify-content-center me-3"
          style={{
            width: '32px',
            height: '32px',
            backgroundColor: getSyncStatusColor(),
            color: 'white'
          }}
        >
          <i 
            className={`${getSyncIcon()} ${queueStats.processing ? 'spin' : ''}`}
            style={{ fontSize: '14px' }}
          ></i>
        </div>
        <div>
          <div style={{ fontSize: '14px', fontWeight: '500', color: getSyncStatusColor() }}>
            {getSyncStatusText()}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            Last sync: {lastSyncTime}
          </div>
        </div>
      </div>

      {/* Details */}
      {showDetails && (
        <div className="border-top pt-3">
          <div className="row g-2 mb-3">
            <div className="col-4">
              <div className="text-center">
                <div style={{ fontSize: '18px', fontWeight: '600', color: '#2196f3' }}>
                  {queueStats.pending}
                </div>
                <div style={{ fontSize: '11px', color: '#666' }}>Pending</div>
              </div>
            </div>
            <div className="col-4">
              <div className="text-center">
                <div style={{ fontSize: '18px', fontWeight: '600', color: '#f44336' }}>
                  {queueStats.failed}
                </div>
                <div style={{ fontSize: '11px', color: '#666' }}>Failed</div>
              </div>
            </div>
            <div className="col-4">
              <div className="text-center">
                <div style={{ fontSize: '18px', fontWeight: '600', color: isOnline ? '#4caf50' : '#f44336' }}>
                  {isOnline ? 'ON' : 'OFF'}
                </div>
                <div style={{ fontSize: '11px', color: '#666' }}>Network</div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="d-flex gap-2">
            {isOnline && (
              <button
                className="btn btn-primary btn-sm flex-fill"
                onClick={handleSync}
                disabled={queueStats.processing}
                style={{ fontSize: '12px' }}
              >
                {queueStats.processing ? (
                  <>
                    <i className="bi bi-arrow-repeat spin me-1"></i>
                    Syncing...
                  </>
                ) : (
                  <>
                    <i className="bi bi-arrow-clockwise me-1"></i>
                    Sync Now
                  </>
                )}
              </button>
            )}
            
            {queueStats.failed > 0 && (
              <button
                className="btn btn-outline-warning btn-sm"
                onClick={handleRetryFailed}
                style={{ fontSize: '12px' }}
              >
                <i className="bi bi-arrow-repeat me-1"></i>
                Retry Failed
              </button>
            )}
          </div>
        </div>
      )}

      {/* Spinning animation */}
      <style jsx>{`
        .spin {
          animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default SyncStatus;
