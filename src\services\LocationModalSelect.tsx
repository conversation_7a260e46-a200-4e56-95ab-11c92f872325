import React, { useState } from "react";
import { <PERSON><PERSON>, Button, Form } from "react-bootstrap";

export interface OptionType {
  value: string;
  label: string;
}

interface LocationModalSelectProps {
  title: string;               // Tier label (e.g., "Tier I")
  options: OptionType[];       // The array of location options
  selectedValue: string;       // Currently selected location ID
  onChange: (newValue: string) => void; // Callback when user picks an option
  disabled: boolean
}

const LocationModalSelect: React.FC<LocationModalSelectProps> = ({
  title,
  options,
  selectedValue,
  onChange,
  disabled
}) => {
  const [show, setShow] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>("");

  // Filtered list based on search text
  const filteredOptions = options.filter((opt) =>
    opt.label.toLowerCase().includes(searchText.toLowerCase())
  );

  const handleOpen = () => {
    setSearchText(""); // Reset search on open
    setShow(true);
  };

  const handleClose = () => {
    setShow(false);
  };

  const handleSelect = (value: string) => {
    onChange(value);
    setShow(false); // Close modal after selection
  };

  // Get currently selected label to show on the button
  const selectedLabel =
    options.find((opt) => opt.value === selectedValue)?.label || "Select";

  return (
    <>
      <Form.Label>{title}</Form.Label>
      {/* The button to open the modal */}
      <Button
        variant="outline-secondary"
        className="w-100 text-start"
        onClick={handleOpen}
        disabled={disabled}
      >
        {selectedLabel}
      </Button>

      {/* The modal */}
      <Modal show={show} onHide={handleClose} centered>
        <Modal.Header closeButton>
          <Modal.Title>{title}</Modal.Title>
        </Modal.Header>

        <Modal.Body>
          {/* Search box */}
          <Form.Control
            type="text"
            placeholder="Search..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            className="mb-3"
          />

          {/* Option list */}
          {filteredOptions.length > 0 ? (
            <div style={{ maxHeight: "300px", overflowY: "auto" }} className="list-group">
              {filteredOptions.map((opt) => (
                <button
                  type="button"
                  key={opt.value}
                  className={`list-group-item list-group-item-action ${opt.value === selectedValue ? "active" : ""
                    }`}
                  onClick={() => handleSelect(opt.value)}
                >
                  {opt.label}
                </button>
              ))}
            </div>
          ) : (
            <p className="text-muted">No options found.</p>
          )}
        </Modal.Body>

        <Modal.Footer>
          <Button variant="secondary" onClick={handleClose}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default LocationModalSelect;
