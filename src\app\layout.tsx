import { ReactNode } from "react";
import ClientProvider from "@/components/ClientProvider"; // Import the Client-side wrapper
import "../styles/index.scss";
import Script from "next/script";
import "../styles/index.css";


// export const metadata = {
//   title: "My PWA",
//   description: "Dynamic PWA Manifest Example",
//   manifest: "/api/manifest", // Dynamic manifest for PWA
// };

export default function Layout({ children }: { children: ReactNode }) {
  return (
    <html id="previewPage" data-theme="light" lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="theme-color" content="#ffffff" />
        <link  href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&family=Raleway:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet" />
        {/* <link rel="manifest" href="/manifest.json" /> */}
        <meta name="theme-color" content="#000000" />
        {/* <link rel="apple-touch-icon" sizes="192x192" href="/assets/icons/Icon-192.png" />
        <link rel="apple-touch-icon" sizes="144x144" href="/assets/icons/Icon-144.png" /> */}
      </head>
      <body>
        <ClientProvider>{children}</ClientProvider> {/* Wrap with ClientProvider */}
      </body>

      <Script id="dynamic-assets" strategy="afterInteractive">
        {`
         (function() {
    var hostParts = window.location.hostname.split('.'); 
    var internalName = hostParts.length > 1 ? hostParts[1] : ''; // Extract 'internal'

    console.log("Internal Name:", internalName); // Debugging purpose

     document.title = "Welcome to " + internalName.charAt(0).toUpperCase() + internalName.slice(1) + " Portal";

    var manifestLink = document.createElement("link");
    manifestLink.rel = "manifest";
    manifestLink.href = "/manifest-" + internalName + ".json"; // Load client-specific manifest
    document.head.appendChild(manifestLink);

    var iosIconSizes = [16,20,32, 36, 40, 48, 57, 72, 96, 114, 144, 192, 512];

    // Generate Apple Touch Icons dynamically
    iosIconSizes.forEach(size => {
        var appleIcon = document.createElement("link");
        appleIcon.rel = "apple-touch-icon";
        appleIcon.sizes = size + "x" + size;
        appleIcon.href = "/assets/iosicons/" + internalName + "/" + size + ".png"; // Load client-specific iOS icon
        document.head.appendChild(appleIcon);
    });

    // Add Favicon dynamically
    var faviconLink = document.createElement("link");
    faviconLink.rel = "icon";
    faviconLink.type = "image/png";
    faviconLink.href = "/assets/iosicons/" + internalName + "/" + 16+ ".png"; // Load client-specific favicon
    document.head.appendChild(faviconLink);

})();

        `}
      </Script>

    </html>
  );
}
