"use client";

import React from "react";
import { ChecklistComponent, HeaderData } from "../types/ChecklistTypes";

interface HeaderComponentProps {
    component: ChecklistComponent;
    componentIndex: number;
}

const HeaderComponent: React.FC<HeaderComponentProps> = ({ component }) => {
    const headerData = component.data as HeaderData;
    
    return (
        <div className="mb-3 p-3 rounded  text-white">
            <h5 className="mb-0 fw-semibold d-flex align-items-center">
                <i className="bi bi-info-circle me-2"></i>
                {headerData.text}
            </h5>
        </div>
    );
};

export default HeaderComponent;
