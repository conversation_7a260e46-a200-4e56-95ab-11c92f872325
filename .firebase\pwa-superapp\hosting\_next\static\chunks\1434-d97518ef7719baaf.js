"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1434],{36651:(e,s,i)=>{i.d(s,{A:()=>r});var n=i(95155);i(12115);var t=i(6874),a=i.n(t);let r=e=>{let{title:s="Page Unavailable Offline",message:i="This page requires an internet connection. Please check your connection and try again.",showHomeLink:t=!0,className:r=""}=e;return(0,n.jsxs)("div",{className:"container-fluid px-3 py-5 ".concat(r),children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"mx-auto mb-4 d-flex align-items-center justify-content-center",style:{width:"80px",height:"80px",borderRadius:"50%",backgroundColor:"#fff3e0",color:"#f57c00"},children:(0,n.jsx)("i",{className:"bi bi-wifi-off",style:{fontSize:"32px"}})}),(0,n.jsx)("h4",{className:"mb-3",style:{fontWeight:"600",color:"#333"},children:s}),(0,n.jsx)("p",{className:"text-muted mb-4",style:{fontSize:"14px",lineHeight:"1.6"},children:i}),(0,n.jsxs)("div",{className:"bg-light rounded-3 p-3 mb-4",children:[(0,n.jsx)("h6",{className:"mb-3",style:{fontSize:"14px",fontWeight:"600"},children:"Available Offline:"}),(0,n.jsxs)("div",{className:"row g-2",children:[(0,n.jsx)("div",{className:"col-6",children:(0,n.jsx)(a(),{href:"/home",className:"text-decoration-none",children:(0,n.jsxs)("div",{className:"bg-white rounded-2 p-2 text-center",children:[(0,n.jsx)("i",{className:"bi bi-house text-primary mb-1",style:{fontSize:"16px"}}),(0,n.jsx)("div",{style:{fontSize:"12px",color:"#333"},children:"Home"})]})})}),(0,n.jsx)("div",{className:"col-6",children:(0,n.jsx)(a(),{href:"/services",className:"text-decoration-none",children:(0,n.jsxs)("div",{className:"bg-white rounded-2 p-2 text-center",children:[(0,n.jsx)("i",{className:"bi bi-grid text-success mb-1",style:{fontSize:"16px"}}),(0,n.jsx)("div",{style:{fontSize:"12px",color:"#333"},children:"Services"})]})})})]})]}),(0,n.jsxs)("div",{className:"d-flex flex-column gap-2",children:[(0,n.jsxs)("button",{className:"btn btn-primary",onClick:()=>window.location.reload(),style:{borderRadius:"12px"},children:[(0,n.jsx)("i",{className:"bi bi-arrow-clockwise me-2"}),"Try Again"]}),t&&(0,n.jsxs)(a(),{href:"/home",className:"btn btn-outline-primary",style:{borderRadius:"12px"},children:[(0,n.jsx)("i",{className:"bi bi-house me-2"}),"Go to Home"]})]}),(0,n.jsx)("div",{className:"mt-4 pt-3 border-top",children:(0,n.jsxs)("div",{className:"d-flex align-items-center justify-content-center",children:[(0,n.jsx)("div",{className:"rounded-circle d-flex align-items-center justify-content-center me-2",style:{width:"16px",height:"16px",backgroundColor:"#f44336",color:"white"},children:(0,n.jsx)("i",{className:"bi bi-wifi-off",style:{fontSize:"8px"}})}),(0,n.jsx)("small",{className:"text-muted",children:"No internet connection"})]})})]}),(0,n.jsx)("script",{dangerouslySetInnerHTML:{__html:"\n            window.addEventListener('online', function() {\n              console.log('\uD83C\uDF10 Back online! Reloading...');\n              window.location.reload();\n            });\n          "}})]})}},79459:(e,s,i)=>{i.d(s,{A:()=>f});var n=i(95155),t=i(12115),a=i(60058),r=i(11518),o=i.n(r),c=i(17227),l=i(48836);let d=e=>{let{className:s="",showDetails:i=!1}=e,[a,r]=(0,t.useState)(!0),[d,f]=(0,t.useState)({pending:0,failed:0,processing:!1}),[x,m]=(0,t.useState)(!1),[b,h]=(0,t.useState)({services:"Never",actions:"Never"});(0,t.useEffect)(()=>{r(navigator.onLine);let e=c.M.onConnectionChange(r),s=l.offlineQueue.subscribe(f);return j(),()=>{e(),s()}},[]);let j=async()=>{try{let e=await c.M.getDataAge("services"),s=await c.M.getDataAge("actions");h({services:e,actions:s})}catch(e){console.error("Error updating sync times:",e)}},g=async()=>{if(a)try{await c.M.syncAll(),j()}catch(e){console.error("Sync failed:",e)}},p=()=>a?d.processing?"#ff9800":d.pending>0?"#2196f3":"#4caf50":"#f44336",u=()=>a?d.processing?"Syncing...":d.pending>0?"".concat(d.pending," pending"):"Online":"Offline";return(0,n.jsxs)("div",{className:"jsx-18905f5ac1e0b003 "+"position-relative ".concat(s),children:[(0,n.jsxs)("div",{style:{cursor:i?"pointer":"default"},onClick:()=>i&&m(!x),className:"jsx-18905f5ac1e0b003 d-flex align-items-center",children:[(0,n.jsx)("div",{style:{width:"24px",height:"24px",backgroundColor:p(),color:"white"},className:"jsx-18905f5ac1e0b003 rounded-circle d-flex align-items-center justify-content-center me-2",children:(0,n.jsx)("i",{style:{fontSize:"12px"},className:"jsx-18905f5ac1e0b003 "+"".concat(!a?"bi-wifi-off":d.processing?"bi-arrow-repeat":d.pending>0?"bi-cloud-upload":"bi-wifi"," ").concat(d.processing?"spin":"")})}),i&&(0,n.jsxs)("div",{className:"jsx-18905f5ac1e0b003 d-flex flex-column",children:[(0,n.jsx)("span",{style:{fontSize:"12px",fontWeight:"500",color:p()},className:"jsx-18905f5ac1e0b003",children:u()}),!a&&(0,n.jsx)("span",{style:{fontSize:"10px",color:"#666"},className:"jsx-18905f5ac1e0b003",children:"Using cached data"})]})]}),x&&i&&(0,n.jsxs)("div",{style:{top:"100%",right:"0",marginTop:"8px",minWidth:"280px",zIndex:1e3,border:"1px solid #e0e0e0"},className:"jsx-18905f5ac1e0b003 position-absolute bg-white border rounded-3 shadow-lg p-3",children:[(0,n.jsxs)("div",{className:"jsx-18905f5ac1e0b003 d-flex justify-content-between align-items-center mb-3",children:[(0,n.jsx)("h6",{style:{fontSize:"14px",fontWeight:"600"},className:"jsx-18905f5ac1e0b003 mb-0",children:"Connection Status"}),(0,n.jsx)("button",{onClick:()=>m(!1),style:{fontSize:"16px",color:"#666"},className:"jsx-18905f5ac1e0b003 btn btn-link p-0",children:(0,n.jsx)("i",{className:"jsx-18905f5ac1e0b003 bi bi-x"})})]}),(0,n.jsxs)("div",{className:"jsx-18905f5ac1e0b003 mb-3",children:[(0,n.jsxs)("div",{className:"jsx-18905f5ac1e0b003 d-flex align-items-center mb-2",children:[(0,n.jsx)("div",{style:{width:"8px",height:"8px",backgroundColor:p()},className:"jsx-18905f5ac1e0b003 rounded-circle me-2"}),(0,n.jsx)("span",{style:{fontSize:"13px",fontWeight:"500"},className:"jsx-18905f5ac1e0b003",children:u()})]}),d.pending>0&&(0,n.jsxs)("div",{style:{fontSize:"12px",color:"#666",marginLeft:"16px"},className:"jsx-18905f5ac1e0b003",children:[d.pending," request",d.pending>1?"s":""," waiting to sync"]}),d.failed>0&&(0,n.jsxs)("div",{style:{fontSize:"12px",color:"#f44336",marginLeft:"16px"},className:"jsx-18905f5ac1e0b003",children:[d.failed," failed request",d.failed>1?"s":""]})]}),(0,n.jsxs)("div",{className:"jsx-18905f5ac1e0b003 mb-3",children:[(0,n.jsx)("div",{style:{fontSize:"12px",fontWeight:"500",marginBottom:"8px"},className:"jsx-18905f5ac1e0b003",children:"Last Sync:"}),(0,n.jsxs)("div",{style:{fontSize:"11px",color:"#666"},className:"jsx-18905f5ac1e0b003",children:[(0,n.jsxs)("div",{className:"jsx-18905f5ac1e0b003",children:["Services: ",b.services]}),(0,n.jsxs)("div",{className:"jsx-18905f5ac1e0b003",children:["Actions: ",b.actions]})]})]}),(0,n.jsxs)("div",{className:"jsx-18905f5ac1e0b003 d-flex gap-2",children:[a&&(0,n.jsx)("button",{onClick:g,disabled:d.processing,style:{fontSize:"12px"},className:"jsx-18905f5ac1e0b003 btn btn-primary btn-sm flex-fill",children:d.processing?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("i",{className:"jsx-18905f5ac1e0b003 bi bi-arrow-repeat spin me-1"}),"Syncing..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("i",{className:"jsx-18905f5ac1e0b003 bi bi-arrow-clockwise me-1"}),"Sync Now"]})}),d.failed>0&&(0,n.jsxs)("button",{onClick:()=>l.offlineQueue.retryFailedRequests(),style:{fontSize:"12px"},className:"jsx-18905f5ac1e0b003 btn btn-outline-warning btn-sm",children:[(0,n.jsx)("i",{className:"jsx-18905f5ac1e0b003 bi bi-arrow-repeat me-1"}),"Retry"]})]})]}),(0,n.jsx)(o(),{id:"18905f5ac1e0b003",children:".spin.jsx-18905f5ac1e0b003{-webkit-animation:spin 1s linear infinite;-moz-animation:spin 1s linear infinite;-o-animation:spin 1s linear infinite;animation:spin 1s linear infinite}@-webkit-keyframes spin{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{from{-moz-transform:rotate(0deg);transform:rotate(0deg)}to{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{from{-o-transform:rotate(0deg);transform:rotate(0deg)}to{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{from{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}"})]})},f=e=>{let{children:s,fallbackContent:i,showOfflineIndicator:r=!0,className:o=""}=e,[c,l]=(0,t.useState)(!0),[f,x]=(0,t.useState)(!1);(0,t.useEffect)(()=>{l(navigator.onLine),m();let e=()=>l(!0),s=()=>l(!1);return window.addEventListener("online",e),window.addEventListener("offline",s),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}},[]);let m=async()=>{try{let e=await a.offlineStorage.getStorageInfo();x(e.services>0||e.actions>0)}catch(e){console.error("Error checking offline data:",e)}};return c||f||!i?!c&&f?(0,n.jsxs)("div",{className:o,children:[(0,n.jsxs)("div",{className:"alert alert-warning d-flex align-items-center mb-0",style:{borderRadius:"0",borderLeft:"none",borderRight:"none",borderTop:"none",backgroundColor:"#fff3cd",borderColor:"#ffeaa7",color:"#856404"},children:[(0,n.jsx)("i",{className:"bi bi-wifi-off me-2"}),(0,n.jsx)("div",{className:"flex-grow-1",children:(0,n.jsx)("small",{style:{fontSize:"12px",fontWeight:"500"},children:"You're offline. Showing cached data."})}),r&&(0,n.jsx)(d,{showDetails:!1})]}),s]}):(0,n.jsxs)("div",{className:o,children:[r&&!c&&(0,n.jsx)("div",{className:"d-flex justify-content-end p-3",children:(0,n.jsx)(d,{showDetails:!0})}),s]}):(0,n.jsxs)("div",{className:o,children:[r&&(0,n.jsx)("div",{className:"d-flex justify-content-end p-3",children:(0,n.jsx)(d,{showDetails:!0})}),i]})}}}]);