"use client"; // ✅ Ensures this is a Client Component

import { ReactNode, useEffect } from "react";
import { Provider } from "react-redux";
import store from "@/store/index";
import setupInterceptors from "@/services/setupInterceptors";
import InstallButton from "@/components/installbutton";
import fcmService from "@/services/fcmService";
import unifiedNotificationService from "@/services/unifiedNotificationService";

export default function ClientProvider({ children }: { children: ReactNode }) {
  useEffect(() => {
    // Register service worker
    if ("serviceWorker" in navigator) {
      navigator.serviceWorker
        .register("/sw.js")
        .then(() => {
          console.log("✅ Service Worker registered");

          // Initialize FCM service after service worker is registered
          initializeFCM();

          // Listen for service worker messages
          // navigator.serviceWorker.addEventListener('message', (event) => {
          //   if (event.data && event.data.type === 'BACKGROUND_SYNC') {
          //     if (event.data.action === 'PROCESS_QUEUE') {
          //       // Import and process queue when background sync triggers
          //       import('@/services/offlineQueue').then(({ offlineQueue }) => {
          //         offlineQueue.processQueue();
          //       });
          //     }
          //   }
          // });
        })
        .catch((error) => console.log("❌ Service Worker registration failed:", error));
    } else {
      // Initialize FCM even if service worker is not supported
      initializeFCM();
    }
  }, []);

  const initializeFCM = async () => {
    try {
      // Initialize unified notification service (handles both FCM and iOS)
      console.log("🔄 Initializing unified notification service...");
      const unifiedSuccess = await unifiedNotificationService.initialize();

      if (unifiedSuccess) {
        console.log("✅ Unified Notification Service initialized");
        const deviceInfo = unifiedNotificationService.getDeviceInfo();
        console.log("📱 Device info:", deviceInfo);
      } else {
        console.log("⚠️ Unified notification service failed, falling back to FCM only");
        await fcmService.initialize();
        console.log("✅ FCM Service initialized (fallback)");
      }

      // Listen for service worker messages (notification clicks)
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.addEventListener('message', (event) => {
          if (event.data?.type === 'NOTIFICATION_CLICKED') {
            console.log('📱 Notification clicked, data:', event.data.data);
            // Handle notification click - you can navigate to specific pages here
            // For example: router.push('/notifications');
          }
        });
      }
    } catch (error) {
      console.error("❌ Notification service initialization failed:", error);
      // Fallback to original FCM service
      try {
        await fcmService.initialize();
        console.log("✅ FCM Service initialized (error fallback)");
      } catch (fcmError) {
        console.error("❌ FCM fallback also failed:", fcmError);
      }
    }
  };

  return (
    <Provider store={store}>
      {children}
      <InstallButton />
    </Provider>
  );
}

// Ensure interceptors are set up correctly
setupInterceptors(store);
