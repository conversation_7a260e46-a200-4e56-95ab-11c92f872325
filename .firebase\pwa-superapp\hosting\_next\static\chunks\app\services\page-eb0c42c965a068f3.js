(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5763],{1316:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var i=s(95155),r=s(11518),n=s.n(r),d=s(12115),a=s(34540),o=s(37759),l=s(6874),c=s.n(l),x=s(46554),h=s(79459),b=s(36651),u=s(17227);let m=e=>{let{type:t,className:s="",showAge:r=!0}=e,[n,a]=(0,d.useState)(!0),[o,l]=(0,d.useState)(""),[c,x]=(0,d.useState)(!0),[h,b]=(0,d.useState)(!1),m=(0,d.useCallback)(async()=>{try{a(navigator.onLine);let e=await u.M.getDataAge(t),s=await u.M.isDataFresh(t);l(e),x(s)}catch(e){console.error("Error updating cache status:",e)}},[t]);(0,d.useEffect)(()=>{m();let e=u.M.onConnectionChange(e=>{a(e),m()}),t=setInterval(m,6e4);return()=>{e(),clearInterval(t)}},[t,m]);let p=async()=>{if(n)try{await u.M.forceRefresh(t),m(),b(!1)}catch(e){console.error("Refresh failed:",e)}};if(n&&c)return null;let f=n&&c?{background:"#e8f5e8",color:"#2e7d32",border:"#4caf50"}:{background:"#fff3e0",color:"#f57c00",border:"#ffcc02"};return(0,i.jsxs)("div",{className:"position-relative ".concat(s),children:[(0,i.jsxs)("span",{className:"badge d-inline-flex align-items-center",style:{backgroundColor:f.background,color:f.color,border:"1px solid ".concat(f.border),fontSize:"10px",fontWeight:"500",padding:"4px 8px",borderRadius:"12px",cursor:n&&c?"default":"pointer"},onClick:()=>b(!h),children:[(0,i.jsx)("i",{className:"".concat(n?c?"bi-check-circle":"bi-clock-history":"bi-wifi-off"," me-1"),style:{fontSize:"10px"}}),n?c?"Fresh":"Cached":"Offline",r&&"Never synced"!==o&&(0,i.jsxs)("span",{className:"ms-1",style:{opacity:.8},children:["(",o,")"]})]}),h&&(0,i.jsxs)("div",{className:"position-absolute bg-dark text-white rounded px-2 py-1",style:{bottom:"100%",left:"50%",transform:"translateX(-50%)",marginBottom:"4px",fontSize:"11px",whiteSpace:"nowrap",zIndex:1e3,maxWidth:"200px",textAlign:"center"},children:[n?c?"".concat(t.charAt(0).toUpperCase()+t.slice(1)," data is up to date."):"Data is ".concat(o," old. Tap to refresh."):"Showing cached ".concat(t," data. Last updated ").concat(o,"."),n&&!c&&(0,i.jsx)("div",{className:"mt-1",children:(0,i.jsx)("button",{className:"btn btn-link text-white p-0",onClick:p,style:{fontSize:"10px",textDecoration:"underline"},children:"Refresh now"})}),(0,i.jsx)("div",{className:"position-absolute",style:{top:"100%",left:"50%",transform:"translateX(-50%)",width:0,height:0,borderLeft:"4px solid transparent",borderRight:"4px solid transparent",borderTop:"4px solid #333"}})]})]})},p={RA:"bi bi-shield-check","EPTW-GEN":"bi bi-file-lock",IR:"bi bi-exclamation-triangle",OBS:"bi bi-eye",INC:"bi bi-clipboard-check",KNOWLEDGE:"bi bi-book",TBT:"bi bi-chat-dots",OTT:"bi bi-list-task",INCINV:"bi bi-briefcase",INS:"bi bi-clipboard-check",DOC:"bi bi-file-earmark-text",GC:"bi bi-hand-thumbs-up",ATM:"bi bi-tools","Change Management":"bi bi-arrow-repeat"},f={RA:"#FF3B30","EPTW-GEN":"#34C759",IR:"#FF9500",OBS:"#007AFF",INC:"#FF2D92",KNOWLEDGE:"#5856D6",TBT:"#007AFF",OTT:"#AF52DE",INCINV:"#FF6B35",INS:"#32D74B",DOC:"#5856D6",GC:"#30D158",ATM:"#FF6B35","Change Management":"#007AFF"},j=()=>{let[e,t]=(0,d.useState)([]),[s,r]=(0,d.useState)(!0),[l,j]=(0,d.useState)("list"),[g,y]=(0,d.useState)(""),v=(0,a.wA)(),S=e.some(e=>"RA"===e.maskName),N=e.filter(e=>e.name.toLowerCase().includes(g.toLowerCase())||e.description.toLowerCase().includes(g.toLowerCase())||e.mobileShortName.toLowerCase().includes(g.toLowerCase())),_=(0,d.useCallback)(async()=>{try{let e=await u.M.getServices();t(e),v(o.X.setService(e)),r(!1)}catch(e){console.error("Error fetching services:",e),r(!1)}},[v]);(0,d.useEffect)(()=>{_()},[_]);let w=e=>e.replace(/^\/apps\//,"/"),F=(0,i.jsx)(b.A,{title:"Services Unavailable Offline",message:"Services data is not available offline. Please connect to the internet to view all services.",showHomeLink:!0});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(x.default,{heading:"Services"}),(0,i.jsx)(h.A,{fallbackContent:F,showOfflineIndicator:!0,children:(0,i.jsx)("div",{className:"jsx-1eeb709de6d8b2d0 page-content-wrapper",children:(0,i.jsxs)("div",{style:{backgroundColor:"#f8f9fa",minHeight:"100vh",padding:"16px"},className:"jsx-1eeb709de6d8b2d0 container-fluid px-3",children:[(0,i.jsx)("div",{className:"jsx-1eeb709de6d8b2d0 row",children:(0,i.jsx)("div",{className:"jsx-1eeb709de6d8b2d0 col-12",children:(0,i.jsxs)("div",{style:{marginBottom:"24px"},className:"jsx-1eeb709de6d8b2d0",children:[(0,i.jsxs)("div",{className:"jsx-1eeb709de6d8b2d0 d-flex justify-content-between align-items-start mb-3",children:[(0,i.jsx)("div",{style:{flex:1},className:"jsx-1eeb709de6d8b2d0",children:(0,i.jsx)("p",{style:{opacity:.7,fontSize:"16px"},className:"jsx-1eeb709de6d8b2d0 text-muted mb-0",children:"Access all safety management services"})}),(0,i.jsxs)("div",{style:{backgroundColor:"#F2F2F7",borderRadius:"8px",padding:"2px",alignSelf:"flex-end"},className:"jsx-1eeb709de6d8b2d0 d-flex",children:[(0,i.jsxs)("button",{onClick:()=>j("list"),style:{backgroundColor:"list"===l?"#007AFF":"transparent",border:"none",borderRadius:"6px",padding:"8px 12px",minWidth:"70px",fontSize:"14px",fontWeight:"500"},className:"jsx-1eeb709de6d8b2d0 "+"btn d-flex align-items-center ".concat("list"===l?"text-white":"text-muted"),children:[(0,i.jsx)("i",{style:{fontSize:"14px"},className:"jsx-1eeb709de6d8b2d0 bi bi-list me-1"}),"List"]}),(0,i.jsxs)("button",{onClick:()=>j("grid"),style:{backgroundColor:"grid"===l?"#007AFF":"transparent",border:"none",borderRadius:"6px",padding:"8px 12px",minWidth:"70px",fontSize:"14px",fontWeight:"500"},className:"jsx-1eeb709de6d8b2d0 "+"btn d-flex align-items-center ".concat("grid"===l?"text-white":"text-muted"),children:[(0,i.jsx)("i",{style:{fontSize:"14px"},className:"jsx-1eeb709de6d8b2d0 bi bi-grid me-1"}),"Grid"]})]})]}),(0,i.jsxs)("div",{style:{backgroundColor:"#F2F2F7",borderRadius:"10px",padding:"8px 12px"},className:"jsx-1eeb709de6d8b2d0 d-flex align-items-center mb-3",children:[(0,i.jsx)("i",{style:{fontSize:"20px",color:"#8E8E93"},className:"jsx-1eeb709de6d8b2d0 bi bi-search me-2"}),(0,i.jsx)("input",{type:"text",placeholder:"Search services...",value:g,onChange:e=>y(e.target.value),style:{fontSize:"16px",color:"#000000"},className:"jsx-1eeb709de6d8b2d0 form-control border-0 bg-transparent p-0"})]}),(0,i.jsxs)("div",{className:"jsx-1eeb709de6d8b2d0 d-flex align-items-center justify-content-between",children:[(0,i.jsx)("div",{className:"jsx-1eeb709de6d8b2d0 d-flex align-items-center",children:(0,i.jsx)(m,{type:"services",showAge:!1})}),(0,i.jsxs)("div",{className:"jsx-1eeb709de6d8b2d0 text-muted small",children:[N.length," ",1===N.length?"service":"services"," available"]})]})]})})}),(0,i.jsx)("div",{className:"jsx-1eeb709de6d8b2d0 row",children:(0,i.jsx)("div",{className:"jsx-1eeb709de6d8b2d0 col-12",children:s?(0,i.jsx)("div",{className:"jsx-1eeb709de6d8b2d0 d-flex justify-content-center align-items-center py-5",children:(0,i.jsx)("div",{role:"status",className:"jsx-1eeb709de6d8b2d0 spinner-border text-primary",children:(0,i.jsx)("span",{className:"jsx-1eeb709de6d8b2d0 visually-hidden",children:"Loading..."})})}):"list"===l?(0,i.jsxs)("div",{style:{marginBottom:"24px"},className:"jsx-1eeb709de6d8b2d0",children:[N.map(e=>(0,i.jsx)(c(),{href:w(e.url),className:"text-decoration-none",children:(0,i.jsx)("div",{style:{borderRadius:"12px",borderWidth:"1px",overflow:"hidden",transition:"all 0.2s ease",cursor:"pointer"},onMouseEnter:e=>{e.currentTarget.style.transform="translateY(-2px)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0,0,0,0.1)"},onMouseLeave:e=>{e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="none"},className:"jsx-1eeb709de6d8b2d0 bg-white border rounded-3 mb-3",children:(0,i.jsxs)("div",{className:"jsx-1eeb709de6d8b2d0 d-flex align-items-center p-3",children:[(0,i.jsx)("div",{style:{width:"56px",height:"56px",borderRadius:"28px",backgroundColor:(f[e.maskName]||"#007AFF")+"20"},className:"jsx-1eeb709de6d8b2d0 d-flex align-items-center justify-content-center me-3",children:(0,i.jsx)("i",{style:{fontSize:"32px",color:f[e.maskName]||"#007AFF"},className:"jsx-1eeb709de6d8b2d0 "+(p[e.maskName]||"bi bi-file")})}),(0,i.jsxs)("div",{style:{flex:1},className:"jsx-1eeb709de6d8b2d0",children:[(0,i.jsx)("h6",{style:{fontSize:"16px",marginBottom:"4px"},className:"jsx-1eeb709de6d8b2d0 fw-bold text-dark mb-1",children:e.name}),(0,i.jsx)("p",{style:{fontSize:"14px",opacity:.7},className:"jsx-1eeb709de6d8b2d0 text-muted mb-0",children:e.description})]}),(0,i.jsx)("i",{style:{opacity:.5,fontSize:"20px"},className:"jsx-1eeb709de6d8b2d0 bi bi-chevron-right text-muted"})]})})},e.id)),S&&(0,i.jsx)(c(),{href:"/tbt",className:"text-decoration-none",children:(0,i.jsx)("div",{style:{borderRadius:"12px",borderWidth:"1px",overflow:"hidden",transition:"all 0.2s ease",cursor:"pointer"},onMouseEnter:e=>{e.currentTarget.style.transform="translateY(-2px)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0,0,0,0.1)"},onMouseLeave:e=>{e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="none"},className:"jsx-1eeb709de6d8b2d0 bg-white border rounded-3 mb-3",children:(0,i.jsxs)("div",{className:"jsx-1eeb709de6d8b2d0 d-flex align-items-center p-3",children:[(0,i.jsx)("div",{style:{width:"56px",height:"56px",borderRadius:"28px",backgroundColor:"#007AFF20"},className:"jsx-1eeb709de6d8b2d0 d-flex align-items-center justify-content-center me-3",children:(0,i.jsx)("i",{style:{fontSize:"32px",color:"#007AFF"},className:"jsx-1eeb709de6d8b2d0 bi bi-chat-dots"})}),(0,i.jsxs)("div",{style:{flex:1},className:"jsx-1eeb709de6d8b2d0",children:[(0,i.jsx)("h6",{style:{fontSize:"16px",marginBottom:"4px"},className:"jsx-1eeb709de6d8b2d0 fw-bold text-dark mb-1",children:"TBT"}),(0,i.jsx)("p",{style:{fontSize:"14px",opacity:.7},className:"jsx-1eeb709de6d8b2d0 text-muted mb-0"})]}),(0,i.jsx)("i",{style:{opacity:.5,fontSize:"20px"},className:"jsx-1eeb709de6d8b2d0 bi bi-chevron-right text-muted"})]})})})]}):(0,i.jsxs)("div",{className:"jsx-1eeb709de6d8b2d0 row g-3 mb-4",children:[N.map(e=>(0,i.jsx)("div",{className:"jsx-1eeb709de6d8b2d0 col-6 col-md-4 col-lg-3",children:(0,i.jsx)(c(),{href:w(e.url),className:"text-decoration-none",children:(0,i.jsxs)("div",{style:{borderRadius:"16px",borderWidth:"1px",borderColor:"#E5E7EB",backgroundColor:"#FFFFFF",boxShadow:"0 2px 8px rgba(0,0,0,0.06)",transition:"all 0.3s ease",cursor:"pointer",minHeight:"120px",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center"},onMouseEnter:t=>{t.currentTarget.style.transform="translateY(-4px)",t.currentTarget.style.boxShadow="0 8px 25px rgba(0,0,0,0.12)",t.currentTarget.style.borderColor=(f[e.maskName]||"#007AFF")+"40"},onMouseLeave:e=>{e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="0 2px 8px rgba(0,0,0,0.06)",e.currentTarget.style.borderColor="#E5E7EB"},className:"jsx-1eeb709de6d8b2d0 bg-white border rounded-3 text-center p-3 h-100 service-grid-card",children:[(0,i.jsx)("div",{style:{width:"56px",height:"56px",borderRadius:"16px",backgroundColor:(f[e.maskName]||"#007AFF")+"15",border:"2px solid ".concat(f[e.maskName]||"#007AFF","20")},className:"jsx-1eeb709de6d8b2d0 d-flex align-items-center justify-content-center mb-3 icon-container",children:(0,i.jsx)("i",{style:{fontSize:"28px",color:f[e.maskName]||"#007AFF"},className:"jsx-1eeb709de6d8b2d0 "+(p[e.maskName]||"bi bi-file")})}),(0,i.jsx)("h6",{style:{fontSize:"14px",lineHeight:"18px",fontWeight:"600",marginBottom:"4px",display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical",overflow:"hidden",textOverflow:"ellipsis",minHeight:"36px"},className:"jsx-1eeb709de6d8b2d0 fw-bold text-dark mb-1",children:e.name}),(0,i.jsx)("p",{style:{fontSize:"11px",lineHeight:"14px",opacity:.7,display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical",overflow:"hidden",textOverflow:"ellipsis",minHeight:"28px"},className:"jsx-1eeb709de6d8b2d0 text-muted small mb-0",children:e.description})]})})},e.id)),S&&(0,i.jsx)("div",{className:"jsx-1eeb709de6d8b2d0 col-6 col-md-4 col-lg-3",children:(0,i.jsx)(c(),{href:"/tbt",className:"text-decoration-none",children:(0,i.jsxs)("div",{style:{borderRadius:"16px",borderWidth:"1px",borderColor:"#E5E7EB",backgroundColor:"#FFFFFF",boxShadow:"0 2px 8px rgba(0,0,0,0.06)",transition:"all 0.3s ease",cursor:"pointer",minHeight:"120px",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center"},onMouseEnter:e=>{e.currentTarget.style.transform="translateY(-4px)",e.currentTarget.style.boxShadow="0 8px 25px rgba(0,0,0,0.12)",e.currentTarget.style.borderColor="#007AFF40"},onMouseLeave:e=>{e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="0 2px 8px rgba(0,0,0,0.06)",e.currentTarget.style.borderColor="#E5E7EB"},className:"jsx-1eeb709de6d8b2d0 bg-white border rounded-3 text-center p-3 h-100 service-grid-card",children:[(0,i.jsx)("div",{style:{width:"56px",height:"56px",borderRadius:"16px",backgroundColor:"#007AFF15",border:"2px solid #007AFF20"},className:"jsx-1eeb709de6d8b2d0 d-flex align-items-center justify-content-center mb-3 icon-container",children:(0,i.jsx)("i",{style:{fontSize:"28px",color:"#007AFF"},className:"jsx-1eeb709de6d8b2d0 bi bi-chat-dots"})}),(0,i.jsx)("h6",{style:{fontSize:"14px",lineHeight:"18px",fontWeight:"600",marginBottom:"4px",minHeight:"36px"},className:"jsx-1eeb709de6d8b2d0 fw-bold text-dark mb-1",children:"TBT"}),(0,i.jsx)("p",{style:{fontSize:"11px",lineHeight:"14px",opacity:.7,display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical",overflow:"hidden",textOverflow:"ellipsis",minHeight:"28px"},className:"jsx-1eeb709de6d8b2d0 text-muted small mb-0"})]})})})]})})}),(0,i.jsx)("div",{className:"jsx-1eeb709de6d8b2d0 row",children:(0,i.jsx)("div",{className:"jsx-1eeb709de6d8b2d0 col-12",children:(0,i.jsx)("div",{style:{paddingBottom:"100px"},className:"jsx-1eeb709de6d8b2d0 text-center py-4",children:(0,i.jsx)("p",{style:{fontSize:"14px",opacity:.6},className:"jsx-1eeb709de6d8b2d0 text-muted mb-0",children:"Select a service to get started with safety management"})})})}),(0,i.jsx)("div",{className:"jsx-1eeb709de6d8b2d0 fixed-bottom bg-white border-top shadow-sm",children:(0,i.jsx)("div",{className:"jsx-1eeb709de6d8b2d0 container-fluid",children:(0,i.jsxs)("div",{className:"jsx-1eeb709de6d8b2d0 row text-center py-2",children:[(0,i.jsx)("div",{className:"jsx-1eeb709de6d8b2d0 col",children:(0,i.jsx)(c(),{href:"/dashboard",className:"text-decoration-none text-muted",children:(0,i.jsxs)("div",{className:"jsx-1eeb709de6d8b2d0 d-flex flex-column align-items-center",children:[(0,i.jsx)("i",{className:"jsx-1eeb709de6d8b2d0 bi bi-grid-3x3-gap fs-5 mb-1"}),(0,i.jsx)("small",{style:{fontSize:"0.7rem"},className:"jsx-1eeb709de6d8b2d0",children:"Dashboard"})]})})}),(0,i.jsx)("div",{className:"jsx-1eeb709de6d8b2d0 col",children:(0,i.jsx)(c(),{href:"/services",className:"text-decoration-none text-primary",children:(0,i.jsxs)("div",{className:"jsx-1eeb709de6d8b2d0 d-flex flex-column align-items-center",children:[(0,i.jsx)("i",{className:"jsx-1eeb709de6d8b2d0 bi bi-grid-fill fs-5 mb-1"}),(0,i.jsx)("small",{style:{fontSize:"0.7rem"},className:"jsx-1eeb709de6d8b2d0",children:"Services"})]})})}),(0,i.jsx)("div",{className:"jsx-1eeb709de6d8b2d0 col",children:(0,i.jsx)(c(),{href:"/home",className:"text-decoration-none text-muted",children:(0,i.jsxs)("div",{className:"jsx-1eeb709de6d8b2d0 d-flex flex-column align-items-center",children:[(0,i.jsx)("i",{className:"jsx-1eeb709de6d8b2d0 bi bi-house fs-5 mb-1"}),(0,i.jsx)("small",{style:{fontSize:"0.7rem"},className:"jsx-1eeb709de6d8b2d0",children:"Home"})]})})}),(0,i.jsx)("div",{className:"jsx-1eeb709de6d8b2d0 col",children:(0,i.jsx)(c(),{href:"/history",className:"text-decoration-none text-muted",children:(0,i.jsxs)("div",{className:"jsx-1eeb709de6d8b2d0 d-flex flex-column align-items-center",children:[(0,i.jsx)("i",{className:"jsx-1eeb709de6d8b2d0 bi bi-clock-history fs-5 mb-1"}),(0,i.jsx)("small",{style:{fontSize:"0.7rem"},className:"jsx-1eeb709de6d8b2d0",children:"History"})]})})}),(0,i.jsx)("div",{className:"jsx-1eeb709de6d8b2d0 col",children:(0,i.jsx)(c(),{href:"/profile",className:"text-decoration-none text-muted",children:(0,i.jsxs)("div",{className:"jsx-1eeb709de6d8b2d0 d-flex flex-column align-items-center",children:[(0,i.jsx)("i",{className:"jsx-1eeb709de6d8b2d0 bi bi-person fs-5 mb-1"}),(0,i.jsx)("small",{style:{fontSize:"0.7rem"},className:"jsx-1eeb709de6d8b2d0",children:"Profile"})]})})})]})})})]})})}),(0,i.jsx)(n(),{id:"1eeb709de6d8b2d0",children:".service-grid-card.jsx-1eeb709de6d8b2d0{-webkit-transition:all.3s cubic-bezier(.4,0,.2,1);-moz-transition:all.3s cubic-bezier(.4,0,.2,1);-o-transition:all.3s cubic-bezier(.4,0,.2,1);transition:all.3s cubic-bezier(.4,0,.2,1)}.service-grid-card.jsx-1eeb709de6d8b2d0:hover{-webkit-transform:translatey(-4px)scale(1.02);-moz-transform:translatey(-4px)scale(1.02);-ms-transform:translatey(-4px)scale(1.02);-o-transform:translatey(-4px)scale(1.02);transform:translatey(-4px)scale(1.02)}@media(max-width:576px){.service-grid-card.jsx-1eeb709de6d8b2d0{min-height:110px!important}.service-grid-card.jsx-1eeb709de6d8b2d0 .icon-container.jsx-1eeb709de6d8b2d0{width:48px!important;height:48px!important}.service-grid-card.jsx-1eeb709de6d8b2d0 .icon-container.jsx-1eeb709de6d8b2d0 i.jsx-1eeb709de6d8b2d0{font-size:24px!important}}"})]})}},11518:(e,t,s)=>{"use strict";e.exports=s(82269).style},35695:(e,t,s)=>{"use strict";var i=s(18999);s.o(i,"useRouter")&&s.d(t,{useRouter:function(){return i.useRouter}}),s.o(i,"useSearchParams")&&s.d(t,{useSearchParams:function(){return i.useSearchParams}})},37759:(e,t,s)=>{"use strict";s.d(t,{A:()=>n,X:()=>r});let i=(0,s(51990).Z0)({name:"service",initialState:{loading:!1,service:[]},reducers:{setService(e,t){e.service=t.payload}}}),r=i.actions,n=i},46554:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var i=s(95155),r=s(12115),n=s(35695),d=s(38336),a=s(26957),o=s(34540),l=s(81359);let c=e=>{let{heading:t}=e,s=(0,n.useRouter)(),[c,x]=(0,r.useState)(""),h=(0,o.wA)();r.useEffect(()=>{b()},[]);let b=async()=>{try{let e=await d.A.get(a.AM);200===e.status?(x(e.data.firstName),h(l.l.setUser(e.data))):s.push("/")}catch(e){console.log(e)}};return(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("div",{className:"header-area",id:"headerArea",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"header-content header-style-four position-relative d-flex align-items-center justify-content-between",children:[(0,i.jsx)("div",{className:"back-button",children:(0,i.jsx)("button",{onClick:()=>s.back(),className:"border-0 bg-transparent p-0",style:{cursor:"pointer"},children:(0,i.jsx)("i",{className:"bi bi-arrow-left-short fs-3"})})}),(0,i.jsx)("div",{className:"page-heading",children:(0,i.jsx)("h6",{className:"mb-0",children:t})}),(0,i.jsx)("div",{className:"user-profile-wrapper"})]})})})})}},68375:()=>{},72388:(e,t,s)=>{Promise.resolve().then(s.bind(s,1316))},82269:(e,t,s)=>{"use strict";var i=s(49509);s(68375);var r=s(12115),n=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(r),d=void 0!==i&&i.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},o=function(){function e(e){var t=void 0===e?{}:e,s=t.name,i=void 0===s?"stylesheet":s,r=t.optimizeForSpeed,n=void 0===r?d:r;l(a(i),"`name` must be a string"),this._name=i,this._deletedRulePlaceholder="#"+i+"-deleted-rule____{}",l("boolean"==typeof n,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=n,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var o="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=o?o.getAttribute("content"):null}var t,s=e.prototype;return s.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},s.isOptimizeForSpeed=function(){return this._optimizeForSpeed},s.inject=function(){var e=this;if(l(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(d||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,s){return"number"==typeof s?e._serverSheet.cssRules[s]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),s},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},s.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},s.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},s.insertRule=function(e,t){if(l(a(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var s=this.getSheet();"number"!=typeof t&&(t=s.cssRules.length);try{s.insertRule(e,t)}catch(t){return d||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var i=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,i))}return this._rulesCount++},s.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var s="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!s.cssRules[e])return e;s.deleteRule(e);try{s.insertRule(t,e)}catch(i){d||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),s.insertRule(this._deletedRulePlaceholder,e)}}else{var i=this._tags[e];l(i,"old rule at index `"+e+"` not found"),i.textContent=t}return e},s.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];l(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},s.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},s.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,s){return s?t=t.concat(Array.prototype.map.call(e.getSheetForTag(s).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},s.makeStyleTag=function(e,t,s){t&&l(a(t),"makeStyleTag accepts only strings as second parameter");var i=document.createElement("style");this._nonce&&i.setAttribute("nonce",this._nonce),i.type="text/css",i.setAttribute("data-"+e,""),t&&i.appendChild(document.createTextNode(t));var r=document.head||document.getElementsByTagName("head")[0];return s?r.insertBefore(i,s):r.appendChild(i),i},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var s=0;s<t.length;s++){var i=t[s];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(e.prototype,t),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var c=function(e){for(var t=5381,s=e.length;s;)t=33*t^e.charCodeAt(--s);return t>>>0},x={};function h(e,t){if(!t)return"jsx-"+e;var s=String(t),i=e+s;return x[i]||(x[i]="jsx-"+c(e+"-"+s)),x[i]}function b(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var s=e+t;return x[s]||(x[s]=t.replace(/__jsx-style-dynamic-selector/g,e)),x[s]}var u=function(){function e(e){var t=void 0===e?{}:e,s=t.styleSheet,i=void 0===s?null:s,r=t.optimizeForSpeed,n=void 0!==r&&r;this._sheet=i||new o({name:"styled-jsx",optimizeForSpeed:n}),this._sheet.inject(),i&&"boolean"==typeof n&&(this._sheet.setOptimizeForSpeed(n),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var s=this.getIdAndRules(e),i=s.styleId,r=s.rules;if(i in this._instancesCounts){this._instancesCounts[i]+=1;return}var n=r.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[i]=n,this._instancesCounts[i]=1},t.remove=function(e){var t=this,s=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(s in this._instancesCounts,"styleId: `"+s+"` not found"),this._instancesCounts[s]-=1,this._instancesCounts[s]<1){var i=this._fromServer&&this._fromServer[s];i?(i.parentNode.removeChild(i),delete this._fromServer[s]):(this._indices[s].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[s]),delete this._instancesCounts[s]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],s=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return s[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,s;return t=this.cssRules(),void 0===(s=e)&&(s={}),t.map(function(e){var t=e[0],i=e[1];return n.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:s.nonce?s.nonce:void 0,dangerouslySetInnerHTML:{__html:i}})})},t.getIdAndRules=function(e){var t=e.children,s=e.dynamic,i=e.id;if(s){var r=h(i,s);return{styleId:r,rules:Array.isArray(t)?t.map(function(e){return b(r,e)}):[b(r,t)]}}return{styleId:h(i),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=r.createContext(null);m.displayName="StyleSheetContext";var p=n.default.useInsertionEffect||n.default.useLayoutEffect,f="undefined"!=typeof window?new u:void 0;function j(e){var t=f||r.useContext(m);return t&&("undefined"==typeof window?t.add(e):p(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}j.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=j}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,6078,635,381,1434,8441,1684,7358],()=>t(72388)),_N_E=e.O()}]);