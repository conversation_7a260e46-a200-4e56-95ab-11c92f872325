
"use client";

import New from "@/components/inspection/New";
import HeaderSeven from "@/layouts/headers/HeaderSeven";
import React, { useMemo } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

interface ServiceType {
    id: string;
    name: string;
    description: string;
    maskName: string;
    applicability: string;
    status: boolean;
    url: string;
    mobileShortName?: string;
    created: string;
    updated: string;
}

const Inspection: React.FC = () => {
    // Get services from Redux state
    const services = useSelector((state: RootState) => state.service.service) as ServiceType[];

    // Find the inspection service and get its name
    const serviceName = useMemo(() => {
        if (!services || !Array.isArray(services)) return 'Inspection';

        // Look for inspection service by URL or maskName
        const inspectionService = services.find((service: ServiceType) =>
            service.url?.includes('/apps/inspection') ||
            service.maskName?.toLowerCase().includes('INS') ||
            service.name?.toLowerCase().includes('inspection')
        );

        return inspectionService?.name || inspectionService?.mobileShortName || 'Inspection';
    }, [services]);

    return (
        <>
            <HeaderSeven heading={serviceName} />

            <div className="page-content-wrapper">

                <New />
            </div>


        </>
    );
};

export default Inspection;
