"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { loginActions } from "@/store/login-slice";
import { FILE_DOWNLOAD, USER_LOGIN_DETAILS } from "@/constant";
import axios from "axios";

const Root = () => {
  const [enterpriseId, setEnterpriseId] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [details, setDetails] = useState<Record<string, any>>({});
  const [logo, setLogo] = useState<string>("");
  const [privacyPolicyAccepted, setPrivacyPolicyAccepted] = useState<boolean>(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState<boolean>(false);

  const dispatch = useDispatch();
  const router = useRouter();

  useEffect(() => {
    // Fetch enterprise details once component mounts
    fetch(USER_LOGIN_DETAILS, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Failed to fetch enterprise details");
        }
        return response.json();
      })
      .then((data) => {
        console.log("Enterprise Details:", data);
        setDetails(data);

        // Store relevant info in localStorage for future usage
        localStorage.setItem(
          "COGNITO_USER_DOMAIN",
          data.COGNITO_USER_DOMAIN || ""
        );
        localStorage.setItem(
          "COGNITO_USER_APP_CLIENT_ID",
          data.COGNITO_USER_APP_CLIENT_ID || ""
        );

        if (data.LOGO) {
          localStorage.setItem("logo", data.LOGO);
          fetchLogo(data.LOGO);
        }
      })
      .catch((err) => {
        console.error("Fetching Enterprise Details Error:", err);
        setError(err.message || "Error fetching enterprise details");
      });
  }, []);

  const fetchLogo = async (logoFilename: string) => {
    try {
      const response = await axios.get(FILE_DOWNLOAD(logoFilename), {
        headers: {
          "Content-Type": "application/json",
        },
      });
      setLogo(response.data);
    } catch (error) {
      console.error("Error fetching logo:", error);
    }
  };

  // Exchange code in URL for tokens once enterprise details are known
  useEffect(() => {
    if (!details.COGNITO_USER_DOMAIN || !details.COGNITO_USER_APP_CLIENT_ID) {
      return;
    }

    const searchParams = new URLSearchParams(window.location.search);
    const code = searchParams.get("code");
    if (!code) return;

    const baseUrl = window.location.origin;
    fetch(`${details.COGNITO_USER_DOMAIN}/oauth2/token`, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: `grant_type=authorization_code&client_id=${
        details.COGNITO_USER_APP_CLIENT_ID
      }&code=${code}&redirect_uri=${encodeURIComponent(baseUrl)}`,
    })
      .then((response) => response.json())
      .then((data) => {
        console.log("OAuth Token Response:", data);

        if (data.access_token && data.refresh_token) {
          localStorage.setItem("access_token", data.access_token);
          localStorage.setItem("refresh_token", data.refresh_token);
          dispatch(loginActions.setLogin());
          router.push("/home");
        } else {
          console.error("Invalid Token Response:", data);
          setError("Failed to retrieve valid tokens");
        }
      })
      .catch((err) => {
        console.error("OAuth Token Error:", err);
        setError(err.message || "Error exchanging code for tokens");
      });
  }, [details, dispatch, router]);

  // Trigger Cognito login redirect once enterprise details are known
  const handleLogin = async () => {
    setLoading(true);
    setError(null);

    try {
      // Double-check configuration before redirect
      const response = await fetch(USER_LOGIN_DETAILS, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });
      if (!response.ok) {
        throw new Error("Invalid Enterprise ID or No Configuration Found");
      }

      const data = await response.json();
      if (!data.COGNITO_USER_DOMAIN || !data.COGNITO_USER_APP_CLIENT_ID) {
        throw new Error("Invalid login configuration. Please try again.");
      }

      const baseUrl = window.location.origin;
      const redirectUrl = `${data.COGNITO_USER_DOMAIN}/oauth2/authorize?client_id=${
        data.COGNITO_USER_APP_CLIENT_ID
      }&response_type=code&scope=email+openid+phone&redirect_uri=${encodeURIComponent(
        baseUrl
      )}`;
      localStorage.setItem("logo", data.LOGO);
      window.location.href = redirectUrl;
    } catch (err: any) {
      console.error("Login Error:", err);
      setError(err.message || "Something went wrong");
      setLoading(false);
    }
  };

  return (
    <div className="login-wrapper d-flex align-items-center justify-content-center">
      <div className="custom-container">
        {/* TOP SECTION - AcuiZen WorkHub heading */}
        <div className="text-center mb-5">
          <h4 className="mb-0 font-raleway" style={{ color: '#1f0757', fontWeight: '600', fontSize: '24px' }}>
            AcuiZen WorkHub
          </h4>
        </div>

        {/* MIDDLE SECTION - Logo, Privacy Policy, and Button */}
        <div className="middle-section mb-5">
          <div className="text-center px-4 mb-4">
            <img className="login-intro-img" src={logo} alt="Enterprise Logo" />
          </div>

          <div className="register-form">
            {/* Privacy Policy Checkbox */}
            <div className="form-check mb-4">
              <input
                className="form-check-input"
                type="checkbox"
                id="privacyPolicy"
                checked={privacyPolicyAccepted}
                onChange={(e) => setPrivacyPolicyAccepted(e.target.checked)}
              />
              <label className="form-check-label" htmlFor="privacyPolicy">
                I agree to the{" "}
                <button
                  type="button"
                  className="btn btn-link text-primary p-0"
                  style={{ textDecoration: 'underline', border: 'none', background: 'none', fontSize: 'inherit' }}
                  onClick={() => setShowPrivacyModal(true)}
                >
                  Privacy Policy
                </button>
              </label>
            </div>

            <button
              className="btn btn-primary w-100"
              onClick={handleLogin}
              disabled={loading || !privacyPolicyAccepted}
            >
              {loading ? (
                <div className="spinner-border" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              ) : (
                details.LOGIN_BUTTON_TEXT || "Internal Login"
              )}
            </button>
          </div>
        </div>

        {/* BOTTOM SECTION - Features and Contact Info */}
        <div className="bottom-section text-center">
          <div className="features-section mb-4">
            <div className="row text-center">
              <div className="col-4">
                <i className="bi bi-shield-check" style={{ color: '#0d5afd', fontSize: '24px' }}></i>
                <p style={{ color: '#8480AE', fontSize: '12px', margin: '8px 0 0 0', fontWeight: '500' }}>Secure</p>
              </div>
              <div className="col-4">
                <i className="bi bi-people" style={{ color: '#0d5afd', fontSize: '24px' }}></i>
                <p style={{ color: '#8480AE', fontSize: '12px', margin: '8px 0 0 0', fontWeight: '500' }}>Collaborative</p>
              </div>
              <div className="col-4">
                <i className="bi bi-lightning" style={{ color: '#0d5afd', fontSize: '24px' }}></i>
                <p style={{ color: '#8480AE', fontSize: '12px', margin: '8px 0 0 0', fontWeight: '500' }}>Efficient</p>
              </div>
            </div>
          </div>

          <div className="contact-section">
            <p style={{ color: '#8480AE', fontSize: '13px', margin: '0 0 8px 0' }}>
              Need assistance? Contact support at{" "}
              <a href="mailto:<EMAIL>" className="text-primary" style={{ textDecoration: 'none', fontWeight: '500' }}>
                <EMAIL>
              </a>
            </p>
            <p style={{ color: '#8480AE', fontSize: '12px', margin: '0', opacity: '0.8' }}>
              © 2025 AcuiZen. All rights reserved.
            </p>
          </div>
        </div>

        {/* Privacy Policy Modal */}
        {showPrivacyModal && (
          <div className="modal fade show" style={{ display: 'block', backgroundColor: 'rgba(0,0,0,0.5)' }} tabIndex={-1}>
            <div className="modal-dialog modal-lg modal-dialog-scrollable">
              <div className="modal-content">
                <div className="modal-header">
                  <h5 className="modal-title" style={{ color: '#1f0757', fontWeight: '600' }}>
                    Privacy Policy
                  </h5>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={() => setShowPrivacyModal(false)}
                    aria-label="Close"
                  ></button>
                </div>
                <div className="modal-body" style={{ maxHeight: '60vh', overflowY: 'auto' }}>
                  <div style={{ color: '#1f0757', lineHeight: '1.6' }}>
                    <h6 style={{ color: '#1f0757', fontWeight: '600', marginBottom: '16px' }}>PRIVACY POLICY</h6>
                    <p style={{ color: '#8480AE', fontSize: '14px', marginBottom: '16px' }}>
                      By using or accessing the service in any manner, you acknowledge
                      that you accept the practices and policies outlined in this
                      Privacy Policy, and you hereby consent that we will collect,
                      use, and share your information in the following ways.
                    </p>

                    <h6 style={{ color: '#1f0757', fontWeight: '600', marginBottom: '12px' }}>WHAT DATA WE COLLECT AND WHY WE COLLECT</h6>
                    <p style={{ color: '#8480AE', fontSize: '14px', marginBottom: '16px' }}>
                      As is true of most websites, we gather certain information (such
                      as mobile provider, operating system, etc.) automatically and
                      store it in log files. We use this information, which does not
                      identify individual users, to analyze trends, to administer the
                      website, to track users movements around the website and to
                      gather demographic information about our user base as a whole.
                      We may link some of this automatically-collected data to certain
                      Personally Identifiable Information.
                    </p>

                    <h6 style={{ color: '#1f0757', fontWeight: '600', marginBottom: '12px' }}>PERSONALLY IDENTIFIABLE INFORMATION</h6>
                    <p style={{ color: '#8480AE', fontSize: '14px', marginBottom: '16px' }}>
                      If you are a Client, when you register with us via our Website,
                      we will ask you for some personally identifiable information,
                      such as your first and last name, company name, email address,
                      billing address, and credit card information. You may review and
                      update this personally identifiable information in your profile
                      by logging in and editing such information on your dashboard. If
                      you decide to delete all of your information, we may cancel your
                      account. We may retain an archived copy of your records as
                      required by law or for reasonable business purposes.
                    </p>

                    <p style={{ color: '#8480AE', fontSize: '14px', marginBottom: '16px' }}>
                      Due to the nature of the Service, except to assist Clients with
                      certain limited technical problems or as otherwise legally
                      compelled, we will not access any of the Content that you upload
                      to the Service.
                    </p>

                    <p style={{ color: '#8480AE', fontSize: '14px', marginBottom: '16px' }}>
                      Some Personally Identifiable Information may also be provided to
                      intermediaries and third parties who assist us with the Service,
                      but who may make no use of any such information other than to
                      assist us in providing the Service. Except as otherwise provided
                      in this Privacy Policy, however, we will not rent or sell your
                      Personally Identifiable Information to third parties.
                    </p>

                    <h6 style={{ color: '#1f0757', fontWeight: '600', marginBottom: '12px' }}>USE OF INFORMATION</h6>
                    <p style={{ color: '#8480AE', fontSize: '14px', marginBottom: '16px' }}>
                      For our Clients, we use personal information mainly to provide
                      the Services and contact our Clients regarding account
                      activities, new versions, and product offerings, or other
                      communications relevant to the Services. We do not sell or share
                      any personally identifiable or other information of End Users to
                      any third parties, except, of course, to the applicable Client
                      whose website you are using.
                    </p>

                    <p style={{ color: '#8480AE', fontSize: '14px', marginBottom: '16px' }}>
                      If you contact us by email or by filling out a registration
                      form, we may keep a record of your contact information and
                      correspondence and may use your email address, and any
                      information that you provide to us in your message, to respond
                      to you. In addition, we may use the personal information
                      described above to send you information regarding the Service.
                      If you decide at any time that you no longer wish to receive
                      such information or communications from us, email us at and
                      request to be removed from our list. The circumstances under
                      which we may share such information with third parties are
                      described below in &ldquo;Complying with Legal Process&rdquo;.
                    </p>

                    <h6 style={{ color: '#1f0757', fontWeight: '600', marginBottom: '12px' }}>STORAGE AND SECURITY OF INFORMATION</h6>
                    <p style={{ color: '#8480AE', fontSize: '14px', marginBottom: '0' }}>
                      AcuiZen operates secure data networks protected by industry-standard
                      firewalls and password protection systems. Our security and
                      privacy policies are periodically reviewed and enhanced as
                      necessary, and only authorized individuals have access to the
                      information provided by our Clients.
                    </p>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={() => setShowPrivacyModal(false)}
                  >
                    Close
                  </button>
                  <button
                    type="button"
                    className="btn btn-primary"
                    onClick={() => {
                      setPrivacyPolicyAccepted(true);
                      setShowPrivacyModal(false);
                    }}
                  >
                    Accept & Continue
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Root;
