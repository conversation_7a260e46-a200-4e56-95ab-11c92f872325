"use client";

import React, { useState, useEffect, ReactNode } from 'react';
import OfflinePageWrapper from './OfflinePageWrapper';
import OfflineFallback from './OfflineFallback';

interface OfflineAwareLayoutProps {
  children: ReactNode;
  pageTitle?: string;
  requiresOnline?: boolean;
  customFallback?: ReactNode;
  className?: string;
}

const OfflineAwareLayout: React.FC<OfflineAwareLayoutProps> = ({
  children,
  pageTitle = "Page",
  requiresOnline = false,
  customFallback,
  className = ''
}) => {
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    setIsOnline(navigator.onLine);

    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // If page requires online and we're offline, show fallback
  if (requiresOnline && !isOnline) {
    const fallbackContent = customFallback || (
      <OfflineFallback
        title={`${pageTitle} Unavailable Offline`}
        message={`${pageTitle} requires an internet connection. Please check your connection and try again.`}
        showHomeLink={true}
      />
    );

    return (
      <div className={className}>
        {fallbackContent}
      </div>
    );
  }

  // For pages that can work offline (with limited functionality)
  return (
    <OfflinePageWrapper 
      className={className}
      showOfflineIndicator={true}
    >
      {children}
    </OfflinePageWrapper>
  );
};

export default OfflineAwareLayout;
