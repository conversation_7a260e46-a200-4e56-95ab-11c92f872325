(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1143],{8491:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var n=s(95155),r=s(6874),o=s.n(r),c=s(78093),a=s(12115);let l=()=>{let[e,t]=(0,a.useState)(""),[s,r]=(0,a.useState)(!1),l=(0,a.useRef)(null),i=(0,a.useRef)(null),[d,m]=(0,a.useState)(null),p=e=>{m(e)},u=()=>{m(null)},h=(0,a.useRef)(null),g=(0,a.useRef)(null);return(0,a.useEffect)(()=>{l.current&&h.current&&function(e){var t,s;let n=document.createElement("style");document.body.prepend(n),n.innerHTML="\n    ".concat(e.containerElement," {\n        height: ").concat(e.height||4,"px;\n        background-color: #e2e9fe;\n        position: relative;\n        overflow: hidden;\n        border-radius: ").concat((null==(t=e.borderRadius)?void 0:t.toString())||"2","px;\n    }\n    ").concat(e.containerElement," .password-strength-meter-score {\n      height: inherit;\n      width: 0%;\n      transition: .5s ease;\n      background: ").concat(e.colorScore1||"#ea4c62",";\n    }\n    ").concat(e.containerElement," .password-strength-meter-score.psms-25 {width: 25%; background: ").concat(e.colorScore1||"#ea4c62",";}\n    ").concat(e.containerElement," .password-strength-meter-score.psms-50 {width: 50%; background: ").concat(e.colorScore2||"#f1b10f",";}\n    ").concat(e.containerElement," .password-strength-meter-score.psms-75 {width: 75%; background: ").concat(e.colorScore3||"#1787b8",";}\n    ").concat(e.containerElement," .password-strength-meter-score.psms-100 {width: 100%; background: ").concat(e.colorScore4||"#2ecc4a",";}\n  ");let r=document.getElementById(e.containerElement.slice(1));if(!r)return;r.classList.add("password-strength-meter");let o=document.createElement("div");o.classList.add("password-strength-meter-score"),r.appendChild(o);let c=document.getElementById(e.passwordInput.slice(1));if(!c)return;let a="";c.addEventListener("input",()=>{a=c.value,function(e){switch(e){case 1:o.className="password-strength-meter-score psms-25",i&&(i.textContent=d[1]||"Too simple"),r&&r.dispatchEvent(new Event("onScore1",{bubbles:!0}));break;case 2:o.className="password-strength-meter-score psms-50",i&&(i.textContent=d[2]||"Simple"),r&&r.dispatchEvent(new Event("onScore2",{bubbles:!0}));break;case 3:o.className="password-strength-meter-score psms-75",i&&(i.textContent=d[3]||"Thats OK"),r&&r.dispatchEvent(new Event("onScore3",{bubbles:!0}));break;case 4:o.className="password-strength-meter-score psms-100",i&&(i.textContent=d[4]||"Great password!"),r&&r.dispatchEvent(new Event("onScore4",{bubbles:!0}));break;default:o.className="password-strength-meter-score",i&&(i.textContent=d[0]||"No data"),r&&r.dispatchEvent(new Event("onScore0",{bubbles:!0}))}}(function(){let e=0,t=/(?=.*[a-z])/.test(a),s=/(?=.*[A-Z])/.test(a),n=/(?=.*[0-9])/.test(a),r=new RegExp("(?=.{".concat(l,",})")).test(a);return t&&e++,s&&e++,n&&e++,r&&e++,0===e&&a.length>0&&e++,e}())});let l=e.pswMinLength||8,i=e.showMessage?document.getElementById((null==(s=e.messageContainer)?void 0:s.slice(1))||""):null,d=e.messagesList||["No data","Too simple","Simple","Thats OK","Great password!"];i&&(i.textContent=d[0]||"No data")}({containerElement:"#pswmeter",passwordInput:"#psw-input",height:4,borderRadius:4,pswMinLength:10,showMessage:!0,messageContainer:"#pswmeter-message"})},[]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(c.A,{links:"login"}),(0,n.jsx)("div",{className:"login-wrapper d-flex align-items-center justify-content-center",children:(0,n.jsxs)("div",{className:"custom-container",children:[(0,n.jsx)("div",{className:"text-center px-4",children:(0,n.jsx)("img",{className:"login-intro-img",src:"/assets/img/bg-img/36.png",alt:""})}),(0,n.jsx)("div",{className:"register-form mt-4",children:(0,n.jsxs)("form",{children:[(0,n.jsx)("h6",{className:"mb-3 text-center",children:"Update your password"}),(0,n.jsx)("div",{className:"form-group text-start mb-3",children:(0,n.jsx)("input",{className:"form-control ".concat("code"===d?"form-control-clicked":""),onFocus:()=>p("code"),onBlur:u,type:"text",placeholder:"Enter 8 digit security code"})}),(0,n.jsxs)("div",{className:"form-group text-start mb-3 position-relative",children:[(0,n.jsx)("input",{className:"form-control ".concat("password"===d?"form-control-clicked":""),id:"psw-input",onFocus:()=>p("password"),type:s?"text":"password",value:e,onChange:e=>t(e.target.value),placeholder:"New password",ref:l,onClick:()=>{h.current&&g.current&&(h.current.style.display="block",g.current.style.display="block")},onBlur:()=>{m(null),h.current&&g.current&&(h.current.style.display="none",g.current.style.display="none")}}),(0,n.jsx)("div",{className:"position-absolute ".concat(s?"active":""),id:"password-visibility",onClick:()=>{r(e=>!e),l.current&&l.current.classList.toggle("form-control-clicked"),i.current&&i.current.classList.toggle("form-control-clicked")},style:{cursor:"pointer",top:"50%",right:"10px",transform:"translateY(-50%)"},children:s?(0,n.jsx)("i",{className:"bi bi-eye-slash"}):(0,n.jsx)("i",{className:"bi bi-eye"})})]}),(0,n.jsx)("div",{className:"mb",id:"pswmeter",ref:h,style:{display:"none"}}),(0,n.jsx)("div",{id:"pswmeter-message",ref:g,style:{display:"none"}}),(0,n.jsx)("div",{className:"form-group text-start mb-3",children:(0,n.jsx)("input",{className:"form-control ".concat("password2"===d?"form-control-clicked":""),onFocus:()=>p("password2"),onBlur:u,type:"password",placeholder:"Re-write password"})}),(0,n.jsx)(o(),{href:"/login",children:(0,n.jsx)("button",{className:"btn btn-primary w-100",type:"submit",children:"Update Password"})})]})})]})})]})}},72121:(e,t,s)=>{Promise.resolve().then(s.bind(s,8491))},78093:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var n=s(95155),r=s(6874),o=s.n(r);s(12115);let c=e=>{let{links:t}=e;return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)("div",{className:"login-back-button",children:(0,n.jsx)(o(),{href:"/".concat(t),children:(0,n.jsx)("i",{className:"bi bi-arrow-left-short"})})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,8441,1684,7358],()=>t(72121)),_N_E=e.O()}]);