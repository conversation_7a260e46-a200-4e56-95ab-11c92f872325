(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1205],{4178:(e,t,n)=>{"use strict";n.d(t,{A:()=>S});var r=n(12115),i=n(36209),l=n(41804);let o={maxZoomPixelRatio:1,zoomInMultiplier:2,doubleTapDelay:300,doubleClickDelay:500,doubleClickMaxStops:2,keyboardMoveDistance:50,wheelZoomDistanceFactor:100,pinchZoomDistanceFactor:100,scrollToZoom:!1},u=e=>({...o,...e});function a(){let{zoom:e}=(0,i.D$)();return u(e)}function c(e,t){return((e.clientX-t.clientX)**2+(e.clientY-t.clientY)**2)**.5}function s(e,t,n=100,r=2){return e*Math.min(1+Math.abs(t/n),r)**Math.sign(t)}let d=r.createContext(null),h=(0,i.Tz)("useZoom","ZoomControllerContext",d);function f({children:e}){let[t,n]=r.useState(),{slideRect:o}=(0,i.as)(),{imageRect:u,maxZoom:h}=function(e,t){var n,r;let l={width:0,height:0},o={width:0,height:0},{currentSlide:u}=(0,i.mp)(),{imageFit:c}=(0,i.D$)().carousel,{maxZoomPixelRatio:s}=a();if(e&&u){let a={...u,...t};if((0,i.tx)(a)){let t=(0,i.QJ)(a,c),u=Math.max(...((null==(n=a.srcSet)?void 0:n.map(e=>e.width))||[]).concat(a.width?[a.width]:[])),d=Math.max(...((null==(r=a.srcSet)?void 0:r.map(e=>e.height))||[]).concat(a.height?[a.height]:[]));u>0&&d>0&&e.width>0&&e.height>0&&(o={width:(o=t?{width:Math.round(Math.min(u,e.width/e.height*d)),height:Math.round(Math.min(d,e.height/e.width*u))}:{width:u,height:d}).width*s,height:o.height*s},l=t?{width:Math.min(e.width,o.width,u),height:Math.min(e.height,o.height,d)}:{width:Math.round(Math.min(e.width,e.height/d*u,u)),height:Math.round(Math.min(e.height,e.width/u*d,d))})}}let d=l.width?Math.max((0,i.LI)(o.width/l.width,5),1):1;return{imageRect:l,maxZoom:d}}(o,null==t?void 0:t.imageDimensions),{zoom:f,offsetX:m,offsetY:p,disabled:v,changeZoom:g,changeOffsets:y,zoomIn:w,zoomOut:b}=function(e,t,n){let[l,o]=r.useState(1),[u,c]=r.useState(0),[s,d]=r.useState(0),h=function(e,t,n,l){let o=r.useRef(),u=r.useRef(),{zoom:a}=(0,i.D$)().animation,c=(0,i.iJ)(),s=(0,i.DK)(()=>{var r,i,s;if(null==(r=o.current)||r.cancel(),o.current=void 0,u.current&&(null==l?void 0:l.current)){try{o.current=null==(s=(i=l.current).animate)?void 0:s.call(i,[{transform:u.current},{transform:`scale(${e}) translateX(${t}px) translateY(${n}px)`}],{duration:c?0:null!=a?a:500,easing:o.current?"ease-out":"ease-in-out"})}catch(e){console.error(e)}u.current=void 0,o.current&&(o.current.onfinish=()=>{o.current=void 0})}});return(0,i.Nf)(s,[e,t,n,s]),r.useCallback(()=>{u.current=(null==l?void 0:l.current)?window.getComputedStyle(l.current).transform:void 0},[l])}(l,u,s,n),{currentSlide:f,globalIndex:m}=(0,i.mp)(),{containerRect:p,slideRect:v}=(0,i.as)(),{zoomInMultiplier:g}=a(),y=f&&(0,i.tx)(f)?f.src:void 0,w=!y||!(null==n?void 0:n.current);(0,i.Nf)(()=>{o(1),c(0),d(0)},[m,y]);let b=r.useCallback((t,n,r)=>{let i=r||l,o=u-(t||0),a=s-(n||0),h=(e.width*i-v.width)/2/i,f=(e.height*i-v.height)/2/i;c(Math.min(Math.abs(o),Math.max(h,0))*Math.sign(o)),d(Math.min(Math.abs(a),Math.max(f,0))*Math.sign(a))},[l,u,s,v,e.width,e.height]),E=r.useCallback((e,n,r,u)=>{let a=(0,i.LI)(Math.min(Math.max(e+.001<t?e:t,1),t),5);a!==l&&(n||h(),b(r?r*(1/l-1/a):0,u?u*(1/l-1/a):0,a),o(a))},[l,t,b,h]),S=(0,i.DK)(()=>{l>1&&(l>t&&E(t,!0),b())});(0,i.Nf)(S,[p.width,p.height,S]);let _=r.useCallback(()=>E(l*g),[l,g,E]),x=r.useCallback(()=>E(l/g),[l,g,E]);return{zoom:l,offsetX:u,offsetY:s,disabled:w,changeOffsets:b,changeZoom:E,zoomIn:_,zoomOut:x}}(u,h,null==t?void 0:t.zoomWrapperRef),{on:E}=(0,i.D$)(),S=(0,i.DK)(()=>{var e;v||null==(e=E.zoom)||e.call(E,{zoom:f})});r.useEffect(S,[f,S]),function(e,t,n,o,u,d){let h=r.useRef([]),f=r.useRef(0),m=r.useRef(),{globalIndex:p}=(0,i.mp)(),{getOwnerWindow:v}=(0,i.uG)(),{containerRef:g,subscribeSensors:y}=(0,i.as)(),{keyboardMoveDistance:w,zoomInMultiplier:b,wheelZoomDistanceFactor:E,scrollToZoom:S,doubleTapDelay:_,doubleClickDelay:x,doubleClickMaxStops:C,pinchZoomDistanceFactor:M}=a(),R=r.useCallback(e=>{if(g.current){let{pageX:t,pageY:n}=e,{scrollX:r,scrollY:i}=v(),{left:l,top:o,width:u,height:a}=g.current.getBoundingClientRect();return[t-l-r-u/2,n-o-i-a/2]}return[]},[g,v]),k=(0,i.DK)(t=>{let n=()=>{t.preventDefault(),t.stopPropagation()};if(e>1){let e=(e,t)=>{n(),u(e,t)};"ArrowDown"===t.key?e(0,w):"ArrowUp"===t.key?e(0,-w):"ArrowLeft"===t.key?e(-w,0):"ArrowRight"===t.key&&e(w,0)}let r=e=>{n(),o(e)},i=()=>t.getModifierState("Meta");"+"===t.key||"="===t.key&&i()?r(e*b):"-"===t.key||"_"===t.key&&i()?r(e/b):"0"===t.key&&i()&&r(1)}),N=(0,i.DK)(t=>{if((t.ctrlKey||S)&&Math.abs(t.deltaY)>Math.abs(t.deltaX)){t.stopPropagation(),o(s(e,-t.deltaY,E),!0,...R(t));return}e>1&&(t.stopPropagation(),S||u(t.deltaX,t.deltaY))}),I=r.useCallback(e=>{let t=h.current;t.splice(0,t.length,...t.filter(t=>t.pointerId!==e.pointerId))},[]),P=r.useCallback(e=>{I(e),e.persist(),h.current.push(e)},[I]),L=(0,i.DK)(n=>{var r;let i=h.current;if("mouse"===n.pointerType&&n.buttons>1||!(null==(r=null==d?void 0:d.current)?void 0:r.contains(n.target)))return;e>1&&n.stopPropagation();let{timeStamp:l}=n;0===i.length&&l-f.current<("touch"===n.pointerType?_:x)?(f.current=0,o(e!==t?e*Math.max(t**(1/C),b):1,!1,...R(n))):f.current=l,P(n),2===i.length&&(m.current=c(i[0],i[1]))}),F=(0,i.DK)(t=>{let n=h.current,r=n.find(e=>e.pointerId===t.pointerId);if(2===n.length&&m.current){t.stopPropagation(),P(t);let r=c(n[0],n[1]),i=r-m.current;Math.abs(i)>0&&(o(s(e,i,M),!0,...n.map(e=>R(e)).reduce((e,t)=>t.map((t,n)=>e[n]+t/2))),m.current=r);return}e>1&&(t.stopPropagation(),r&&(1===n.length&&u((r.clientX-t.clientX)/e,(r.clientY-t.clientY)/e),P(t)))}),z=r.useCallback(e=>{let t=h.current;2===t.length&&t.find(t=>t.pointerId===e.pointerId)&&(m.current=void 0),I(e)},[I]),A=r.useCallback(()=>{let e=h.current;e.splice(0,e.length),f.current=0,m.current=void 0},[]);(0,i.yQ)(y,L,F,z,n),r.useEffect(A,[p,A]),r.useEffect(()=>n?()=>{}:(0,i.tP)(A,y(l.uF,k),y(l.j7,N)),[n,y,A,k,N])}(f,h,v,g,y,null==t?void 0:t.zoomWrapperRef);let _=r.useMemo(()=>({zoom:f,maxZoom:h,offsetX:m,offsetY:p,disabled:v,zoomIn:w,zoomOut:b,changeZoom:g}),[f,h,m,p,v,w,b,g]);r.useImperativeHandle(a().ref,()=>_,[_]);let x=r.useMemo(()=>({..._,setZoomWrapper:n}),[_,n]);return r.createElement(d.Provider,{value:x},e)}let m=(0,i.wt)("ZoomIn",r.createElement(r.Fragment,null,r.createElement("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}),r.createElement("path",{d:"M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"}))),p=(0,i.wt)("ZoomOut",r.createElement("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zM7 9h5v1H7z"})),v=r.forwardRef(function({zoomIn:e,onLoseFocus:t},n){let l=r.useRef(!1),o=r.useRef(!1),{zoom:u,maxZoom:a,zoomIn:c,zoomOut:s,disabled:d}=h(),{render:f}=(0,i.D$)(),v=d||(e?u>=a:u<=1);return r.useEffect(()=>{v&&l.current&&o.current&&t(),v||(l.current=!0)},[v,t]),r.createElement(i.K0,{ref:n,disabled:v,label:e?"Zoom in":"Zoom out",icon:e?m:p,renderIcon:e?f.iconZoomIn:f.iconZoomOut,onClick:e?c:s,onFocus:()=>{o.current=!0},onBlur:()=>{o.current=!1}})});function g(){let e=r.useRef(null),t=r.useRef(null),{focus:n}=(0,i.as)(),l=r.useCallback(e=>{var t,r;(null==(t=e.current)?void 0:t.disabled)?n():null==(r=e.current)||r.focus()},[n]),o=r.useCallback(()=>l(e),[l]),u=r.useCallback(()=>l(t),[l]);return r.createElement(r.Fragment,null,r.createElement(v,{zoomIn:!0,ref:e,onLoseFocus:u}),r.createElement(v,{ref:t,onLoseFocus:o}))}function y(){let{render:e}=(0,i.D$)(),t=h();return e.buttonZoom?r.createElement(r.Fragment,null,e.buttonZoom(t)):r.createElement(g,null)}function w({current:e,preload:t},{type:n,source:r}){switch(n){case"fetch":if(!e)return{current:r};return{current:e,preload:r};case"done":if(r===t)return{current:r};return{current:e,preload:t};default:throw Error(l.V)}}function b(e){var t,n;let[{current:l,preload:o},u]=r.useReducer(w,{}),{slide:a,rect:c,imageFit:s,render:d,interactive:h}=e,f=a.srcSet.sort((e,t)=>e.width-t.width),m=null!=(t=a.width)?t:f[f.length-1].width,p=null!=(n=a.height)?n:f[f.length-1].height,v=(0,i.QJ)(a,s),g=Math.max(...f.map(e=>e.width)),y=Math.min((v?Math.max:Math.min)(c.width,m*(c.height/p)),g),b=(0,i.Y5)(),E=(0,i.DK)(()=>{var e;let t=null!=(e=f.find(e=>e.width>=y*b))?e:f[f.length-1];(!l||f.findIndex(e=>e.src===l)<f.findIndex(e=>e===t))&&u({type:"fetch",source:t.src})});(0,i.Nf)(E,[c.width,c.height,b,E]);let S=(0,i.DK)(e=>u({type:"done",source:e})),_={WebkitTransform:h?"initial":"translateZ(0)"};return v||Object.assign(_,c.width/c.height<m/p?{width:"100%",height:"auto"}:{width:"auto",height:"100%"}),r.createElement(r.Fragment,null,o&&o!==l&&r.createElement(i.Z$,{key:"preload",...e,slide:{...a,src:o,srcSet:void 0},style:{position:"absolute",visibility:"hidden",..._},onLoad:()=>S(o),render:{...d,iconLoading:()=>null,iconError:()=>null}}),l&&r.createElement(i.Z$,{key:"current",...e,slide:{...a,src:l,srcSet:void 0},style:_}))}function E({render:e,slide:t,offset:n,rect:o}){var u,a;let[c,s]=r.useState(),d=r.useRef(null),{zoom:f,maxZoom:m,offsetX:p,offsetY:v,setZoomWrapper:g}=h(),y=f>1,{carousel:w,on:E}=(0,i.D$)(),{currentIndex:S}=(0,i.mp)();(0,i.Nf)(()=>0===n?(g({zoomWrapperRef:d,imageDimensions:c}),()=>g(void 0)):()=>{},[n,c,g]);let _=null==(u=e.slide)?void 0:u.call(e,{slide:t,offset:n,rect:o,zoom:f,maxZoom:m});if(!_&&(0,i.tx)(t)){let l={slide:t,offset:n,rect:o,render:e,imageFit:w.imageFit,imageProps:w.imageProps,onClick:0===n?()=>{var e;return null==(e=E.click)?void 0:e.call(E,{index:S})}:void 0};_=((null==(a=t.srcSet)?void 0:a.length)||0)>0?r.createElement(b,{...l,slide:t,interactive:y,rect:0===n?{width:o.width*f,height:o.height*f}:o}):r.createElement(i.Z$,{onLoad:e=>s({width:e.naturalWidth,height:e.naturalHeight}),...l})}return _?r.createElement("div",{ref:d,className:(0,i.$z)((0,i.oF)(l.CC),(0,i.oF)(l.tC),(0,i.oF)(l.pc),y&&(0,i.oF)(l.Ue)),style:0===n?{transform:`scale(${f}) translateX(${p}px) translateY(${v}px)`}:void 0},_):null}let S=({augment:e,addModule:t})=>{e(({zoom:e,toolbar:t,render:n,controller:o,...a})=>{let c=u(e);return{zoom:c,toolbar:(0,i.bo)(t,l.AH,r.createElement(y,null)),render:{...n,slide:e=>{var t;return(0,i.tx)(e.slide)?r.createElement(E,{render:n,...e}):null==(t=n.slide)?void 0:t.call(n,e)}},controller:{...o,preventDefaultWheelY:c.scrollToZoom},...a}}),t((0,i.Fi)(l.AH,f))}},11518:(e,t,n)=>{"use strict";e.exports=n(82269).style},36209:(e,t,n)=>{"use strict";n.d(t,{$z:()=>c,Ay:()=>eT,D$:()=>O,DK:()=>eu,Fi:()=>k,K0:()=>q,LI:()=>g,Nf:()=>en,QJ:()=>w,Tz:()=>p,Y5:()=>S,Z$:()=>em,as:()=>ew,bo:()=>C,iJ:()=>er,mp:()=>K,oF:()=>s,tP:()=>m,tx:()=>y,uG:()=>L,wt:()=>V,yQ:()=>ev});var r,i,l=n(12115),o=n(41804),u=n(47650);let a="yarl__";function c(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return[...t].filter(Boolean).join(" ")}function s(e){return"".concat(a).concat(e)}function d(e){return"--".concat(a).concat(e)}function h(e,t){return"".concat(e).concat(t?"_".concat(t):"")}function f(e){return t=>h(e,t)}function m(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return()=>{t.forEach(e=>{e()})}}function p(e,t,n){return()=>{let r=l.useContext(n);if(!r)throw Error("".concat(e," must be used within a ").concat(t,".Provider"));return r}}function v(){return"undefined"!=typeof window}function g(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=10**t;return Math.round((e+Number.EPSILON)*n)/n}function y(e){return void 0===e.type||"image"===e.type}function w(e,t){return e.imageFit===o.e7||e.imageFit!==o.Td&&t===o.e7}function b(e){return"string"==typeof e?Number.parseInt(e,10):e}function E(e){if("number"==typeof e)return{pixel:e};if("string"==typeof e){let t=b(e);return e.endsWith("%")?{percent:t}:{pixel:t}}return{pixel:0}}function S(){return(v()?null==window?void 0:window.devicePixelRatio:void 0)||1}function _(e,t){var n;return e[(n=e.length)>0?(t%n+n)%n:0]}function x(e,t){return e.length>0?_(e,t):void 0}function C(e,t,n){if(!n)return e;let{buttons:r,...i}=e,o=r.findIndex(e=>e===t),u=l.isValidElement(n)?l.cloneElement(n,{key:t},null):n;if(o>=0){let e=[...r];return e.splice(o,1,u),{buttons:e,...i}}return{buttons:[u,...r],...i}}let M=Number(l.version.split(".")[0])>=19,R={open:!1,close:()=>{},index:0,slides:[],render:{},plugins:[],toolbar:{buttons:[o.C]},labels:{},animation:{fade:250,swipe:500,easing:{fade:"ease",swipe:"ease-out",navigation:"ease-in-out"}},carousel:{finite:!1,preload:2,padding:"16px",spacing:"30%",imageFit:o.Td,imageProps:{}},controller:{ref:null,focus:!0,aria:!1,touchAction:"none",closeOnPullUp:!1,closeOnPullDown:!1,closeOnBackdropClick:!1,preventDefaultWheelX:!0,preventDefaultWheelY:!1},portal:{},noScroll:{disabled:!1},on:{},styles:{},className:""};function k(e,t){return{name:e,component:t}}function N(e,t){return{module:e,children:t}}function I(e,t,n){return e.flatMap(e=>{var r;return null!=(r=function e(t,n,r){return t.module.name===n?r(t):t.children?[N(t.module,t.children.flatMap(t=>{var i;return null!=(i=e(t,n,r))?i:[]}))]:[t]}(e,t,n))?r:[]})}let P=l.createContext(null),L=p("useDocument","DocumentContext",P);function F(e){let{nodeRef:t,children:n}=e,r=l.useMemo(()=>{let e=e=>{var n;return(null==(n=e||t.current)?void 0:n.ownerDocument)||document};return{getOwnerDocument:e,getOwnerWindow:t=>{var n;return(null==(n=e(t))?void 0:n.defaultView)||window}}},[t]);return l.createElement(P.Provider,{value:r},n)}let z=l.createContext(null),A=p("useEvents","EventsContext",z);function D(e){let{children:t}=e,[n]=l.useState({});l.useEffect(()=>()=>{Object.keys(n).forEach(e=>delete n[e])},[n]);let r=l.useMemo(()=>{let e=(e,t)=>{var r;null==(r=n[e])||r.splice(0,n[e].length,...n[e].filter(e=>e!==t))};return{publish:function(){for(var e,t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];let[l,o]=r;null==(e=n[l])||e.forEach(e=>e(o))},subscribe:(t,r)=>(n[t]||(n[t]=[]),n[t].push(r),()=>e(t,r)),unsubscribe:e}},[n]);return l.createElement(z.Provider,{value:r},t)}let T=l.createContext(null),O=p("useLightboxProps","LightboxPropsContext",T);function j(e){let{children:t,...n}=e;return l.createElement(T.Provider,{value:n},t)}let W=l.createContext(null),K=p("useLightboxState","LightboxStateContext",W),$=l.createContext(null),H=p("useLightboxDispatch","LightboxDispatchContext",$);function U(e,t){switch(t.type){case"swipe":{var n;let{slides:r}=e,i=(null==t?void 0:t.increment)||0,l=e.globalIndex+i,o=(n=r.length)>0?(l%n+n)%n:0,u=x(r,o);return{slides:r,currentIndex:o,globalIndex:l,currentSlide:u,animation:i||t.duration?{increment:i,duration:t.duration,easing:t.easing}:void 0}}case"update":if(t.slides!==e.slides||t.index!==e.currentIndex)return{slides:t.slides,currentIndex:t.index,globalIndex:t.index,currentSlide:x(t.slides,t.index)};return e;default:throw Error(o.V)}}function X(e){let{slides:t,index:n,children:r}=e,[i,o]=l.useReducer(U,{slides:t,currentIndex:n,globalIndex:n,currentSlide:x(t,n)});l.useEffect(()=>{o({type:"update",slides:t,index:n})},[t,n]);let u=l.useMemo(()=>({...i,state:i,dispatch:o}),[i,o]);return l.createElement($.Provider,{value:o},l.createElement(W.Provider,{value:u},r))}let Y=l.createContext(null),Z=p("useTimeouts","TimeoutsContext",Y);function B(e){let{children:t}=e,[n]=l.useState([]);l.useEffect(()=>()=>{n.forEach(e=>window.clearTimeout(e)),n.splice(0,n.length)},[n]);let r=l.useMemo(()=>{let e=e=>{n.splice(0,n.length,...n.filter(t=>t!==e))};return{setTimeout:(t,r)=>{let i=window.setTimeout(()=>{e(i),t()},r);return n.push(i),i},clearTimeout:t=>{void 0!==t&&(e(t),window.clearTimeout(t))}}},[n]);return l.createElement(Y.Provider,{value:r},t)}let q=l.forwardRef(function(e,t){var n;let{label:r,className:i,icon:u,renderIcon:a,onClick:d,style:h,...f}=e,{styles:m,labels:p}=O(),v=null!=(n=null==p?void 0:p[r])?n:r;return l.createElement("button",{ref:t,type:"button",title:v,"aria-label":v,className:c(s(o.Uj),i),onClick:d,style:{...h,...m.button},...f},a?a():l.createElement(u,{className:s(o.jR),style:m.icon}))});function V(e,t){var n=e,r=l.createElement("g",{fill:"currentColor"},l.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),t);let i=e=>l.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false",...e},r);return i.displayName=n,i}let J=V("Close",l.createElement("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),Q=V("Previous",l.createElement("path",{d:"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"})),G=V("Next",l.createElement("path",{d:"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"})),ee=V("Loading",l.createElement(l.Fragment,null,Array.from({length:8}).map((e,t,n)=>l.createElement("line",{key:t,x1:"12",y1:"6.5",x2:"12",y2:"1.8",strokeLinecap:"round",strokeWidth:"2.6",stroke:"currentColor",strokeOpacity:1/n.length*(t+1),transform:"rotate(".concat(360/n.length*t,", 12, 12)")})))),et=V("Error",l.createElement("path",{d:"M21.9,21.9l-8.49-8.49l0,0L3.59,3.59l0,0L2.1,2.1L0.69,3.51L3,5.83V19c0,1.1,0.9,2,2,2h13.17l2.31,2.31L21.9,21.9z M5,18 l3.5-4.5l2.5,3.01L12.17,15l3,3H5z M21,18.17L5.83,3H19c1.1,0,2,0.9,2,2V18.17z"})),en=v()?l.useLayoutEffect:l.useEffect;function er(){let[e,t]=l.useState(!1);return l.useEffect(()=>{var e,n;let r=null==(e=window.matchMedia)?void 0:e.call(window,"(prefers-reduced-motion: reduce)");t(null==r?void 0:r.matches);let i=e=>t(e.matches);return null==(n=null==r?void 0:r.addEventListener)||n.call(r,"change",i),()=>{var e;return null==(e=null==r?void 0:r.removeEventListener)?void 0:e.call(r,"change",i)}},[]),e}function ei(e,t){let n=l.useRef(),r=l.useRef(),i=er();return en(()=>{var l,o,u;if(e.current&&void 0!==n.current&&!i){let{keyframes:i,duration:a,easing:c,onfinish:s}=t(n.current,e.current.getBoundingClientRect(),function(e){let t=0,n=0,r=0,i=window.getComputedStyle(e).transform.match(/matrix.*\((.+)\)/);if(i){let e=i[1].split(",").map(b);6===e.length?(t=e[4],n=e[5]):16===e.length&&(t=e[12],n=e[13],r=e[14])}return{x:t,y:n,z:r}}(e.current))||{};if(i&&a){null==(l=r.current)||l.cancel(),r.current=void 0;try{r.current=null==(u=(o=e.current).animate)?void 0:u.call(o,i,{duration:a,easing:c})}catch(e){console.error(e)}r.current&&(r.current.onfinish=()=>{r.current=void 0,null==s||s()})}}n.current=void 0}),{prepareAnimation:e=>{n.current=e},isAnimationPlaying:()=>{var e;return(null==(e=r.current)?void 0:e.playState)==="running"}}}function el(){let e=l.useRef(null),t=l.useRef(),[n,r]=l.useState();return{setContainerRef:l.useCallback(n=>{e.current=n,t.current&&(t.current.disconnect(),t.current=void 0);let i=()=>{if(n){let e=window.getComputedStyle(n),t=e=>parseFloat(e)||0;r({width:Math.round(n.clientWidth-t(e.paddingLeft)-t(e.paddingRight)),height:Math.round(n.clientHeight-t(e.paddingTop)-t(e.paddingBottom))})}else r(void 0)};i(),n&&"undefined"!=typeof ResizeObserver&&(t.current=new ResizeObserver(i),t.current.observe(n))},[]),containerRef:e,containerRect:n}}function eo(){let e=l.useRef(),{setTimeout:t,clearTimeout:n}=Z();return l.useCallback((r,i)=>{n(e.current),e.current=t(r,i>0?i:0)},[t,n])}function eu(e){let t=l.useRef(e);return en(()=>{t.current=e}),l.useCallback(function(){for(var e,n=arguments.length,r=Array(n),i=0;i<n;i++)r[i]=arguments[i];return null==(e=t.current)?void 0:e.call(t,...r)},[])}function ea(e,t){"function"==typeof e?e(t):e&&(e.current=t)}function ec(e,t){return l.useMemo(()=>null==e&&null==t?null:n=>{ea(e,n),ea(t,n)},[e,t])}function es(){let[e,t]=l.useState(!1);return en(()=>{t("rtl"===window.getComputedStyle(window.document.documentElement).direction)},[]),e}function ed(e,t){let n=l.useRef(0),r=eo(),i=eu(function(){for(var t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];n.current=Date.now(),e(r)});return l.useCallback(function(){for(var e=arguments.length,l=Array(e),o=0;o<e;o++)l[o]=arguments[o];r(()=>{i(l)},t-(Date.now()-n.current))},[t,i,r])}let eh=f("slide"),ef=f("slide_image");function em(e){var t,n,r,i,u,a,d;let{slide:h,offset:f,render:m,rect:p,imageFit:g,imageProps:y,onClick:b,onLoad:E,onError:S,style:_}=e,[x,C]=l.useState(o.Qo),{publish:M}=A(),{setTimeout:R}=Z(),k=l.useRef(null);l.useEffect(()=>{0===f&&M((0,o.EC)(x))},[f,x,M]);let N=eu(e=>{("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{e.parentNode&&(C(o.P9),R(()=>{null==E||E(e)},0))})}),I=l.useCallback(e=>{k.current=e,(null==e?void 0:e.complete)&&N(e)},[N]),P=l.useCallback(e=>{N(e.currentTarget)},[N]),L=eu(()=>{C(o.I1),null==S||S()}),F=w(h,g),z=(e,t)=>Number.isFinite(e)?e:t,D=z(Math.max(...(null!=(n=null==(t=h.srcSet)?void 0:t.map(e=>e.width))?n:[]).concat(h.width?[h.width]:[]).filter(Boolean)),(null==(r=k.current)?void 0:r.naturalWidth)||0),T=z(Math.max(...(null!=(u=null==(i=h.srcSet)?void 0:i.map(e=>e.height))?u:[]).concat(h.height?[h.height]:[]).filter(Boolean)),(null==(a=k.current)?void 0:a.naturalHeight)||0),O=null==(d=h.srcSet)?void 0:d.sort((e,t)=>e.width-t.width).map(e=>"".concat(e.src," ").concat(e.width,"w")).join(", "),j=O&&p&&v()?"".concat(Math.round(Math.min(p&&!F&&h.width&&h.height?p.height/h.height*h.width:Number.MAX_VALUE,p.width)),"px"):void 0,{style:W,className:K,...$}=y||{};return l.createElement(l.Fragment,null,l.createElement("img",{ref:I,onLoad:P,onError:L,onClick:b,draggable:!1,className:c(s(ef()),F&&s(ef("cover")),x!==o.P9&&s(ef("loading")),K),style:{...D&&T?{maxWidth:"min(".concat(D,"px, 100%)"),maxHeight:"min(".concat(T,"px, 100%)")}:{maxWidth:"100%",maxHeight:"100%"},..._,...W},...$,alt:h.alt,sizes:j,srcSet:O,src:h.src}),x!==o.P9&&l.createElement("div",{className:s(eh(o.Bv))},x===o.Qo&&((null==m?void 0:m.iconLoading)?m.iconLoading():l.createElement(ee,{className:c(s(o.jR),s(eh(o.Qo)))})),x===o.I1&&((null==m?void 0:m.iconError)?m.iconError():l.createElement(et,{className:c(s(o.jR),s(eh(o.I1)))}))))}let ep=l.forwardRef(function(e,t){let{className:n,children:r,...i}=e,o=l.useRef(null);return l.createElement(F,{nodeRef:o},l.createElement("div",{ref:ec(t,o),className:c(s("root"),n),...i},r))});function ev(e,t,n,r,i){l.useEffect(()=>i?()=>{}:m(e(o.qq,t),e(o.RK,n),e(o.tu,r),e(o.tD,r),e(o.s,r)),[e,t,n,r,i])}!function(e){e[e.NONE=0]="NONE",e[e.SWIPE=1]="SWIPE",e[e.PULL=2]="PULL",e[e.ANIMATION=3]="ANIMATION"}(r||(r={})),function(e){e[e.NONE=0]="NONE",e[e.SWIPE=1]="SWIPE",e[e.PULL=2]="PULL"}(i||(i={}));let eg=f("container"),ey=l.createContext(null),ew=p("useController","ControllerContext",ey),eb=k(o.Kv,function(e){var t;let{children:n,...u}=e,{carousel:a,animation:h,controller:f,on:p,styles:v,render:y}=u,{closeOnPullUp:w,closeOnPullDown:b,preventDefaultWheelX:S,preventDefaultWheelY:_}=f,[x,C]=l.useState(),M=K(),R=H(),[k,N]=l.useState(r.NONE),I=l.useRef(0),P=l.useRef(0),F=l.useRef(1),{registerSensors:z,subscribeSensors:D}=function(){let[e]=l.useState({}),t=l.useCallback((t,n)=>{var r;null==(r=e[t])||r.forEach(e=>{n.isPropagationStopped()||e(n)})},[e]);return{registerSensors:l.useMemo(()=>({onPointerDown:e=>t(o.qq,e),onPointerMove:e=>t(o.RK,e),onPointerUp:e=>t(o.tu,e),onPointerLeave:e=>t(o.tD,e),onPointerCancel:e=>t(o.s,e),onKeyDown:e=>t(o.uF,e),onKeyUp:e=>t(o.t$,e),onWheel:e=>t(o.j7,e)}),[t]),subscribeSensors:l.useCallback((t,n)=>(e[t]||(e[t]=[]),e[t].unshift(n),()=>{let r=e[t];r&&r.splice(0,r.length,...r.filter(e=>e!==n))}),[e])}}(),{subscribe:T,publish:O}=A(),j=eo(),W=eo(),$=eo(),{containerRef:U,setContainerRef:X,containerRect:Y}=el(),B=ec(function(e){let{preventDefaultWheelX:t,preventDefaultWheelY:n}=e,r=l.useRef(null),i=eu(e=>{let r=Math.abs(e.deltaX)>Math.abs(e.deltaY);(r&&t||!r&&n||e.ctrlKey)&&e.preventDefault()});return l.useCallback(e=>{var t;e?e.addEventListener("wheel",i,{passive:!1}):null==(t=r.current)||t.removeEventListener("wheel",i),r.current=e},[i])}({preventDefaultWheelX:S,preventDefaultWheelY:_}),X),q=l.useRef(null),V=ec(q,void 0),{getOwnerDocument:J}=L(),Q=es(),G=e=>(Q?-1:1)*("number"==typeof e?e:1),ee=eu(()=>{var e;return null==(e=U.current)?void 0:e.focus()}),et=eu(()=>u),en=eu(()=>M),er=l.useCallback(e=>O(o.zn,e),[O]),ea=l.useCallback(e=>O(o.Nc,e),[O]),ed=l.useCallback(()=>O(o.C),[O]),eh=e=>!(a.finite&&(G(e)>0&&0===M.currentIndex||0>G(e)&&M.currentIndex===M.slides.length-1)),ef=e=>{var t;I.current=e,null==(t=U.current)||t.style.setProperty(d("swipe_offset"),"".concat(Math.round(e),"px"))},em=e=>{var t,n;P.current=e,F.current=Math.min(Math.max(g(1-(b&&e>0?e:w&&e<0?-e:0)/60*.5,2),.5),1),null==(t=U.current)||t.style.setProperty(d("pull_offset"),"".concat(Math.round(e),"px")),null==(n=U.current)||n.style.setProperty(d("pull_opacity"),"".concat(F.current))},{prepareAnimation:ep}=ei(q,(e,t,n)=>{if(q.current&&Y)return{keyframes:[{transform:"translate(0, ".concat(e.rect.y-t.y+n.y,"px)"),opacity:e.opacity},{transform:"translate(0, 0)",opacity:1}],duration:e.duration,easing:h.easing.fade}}),ew=(e,t)=>{if(w||b){em(e);let n=0;q.current&&(n=h.fade*(t?2:1),ep({rect:q.current.getBoundingClientRect(),opacity:F.current,duration:n})),$(()=>{em(0),N(r.NONE)},n),N(r.ANIMATION),t||ed()}},{prepareAnimation:eb,isAnimationPlaying:eE}=ei(q,(e,t,n)=>{var r;if(q.current&&Y&&(null==(r=M.animation)?void 0:r.duration)){let r=E(a.spacing),i=(r.percent?r.percent*Y.width/100:r.pixel)||0;return{keyframes:[{transform:"translate(".concat(G(M.globalIndex-e.index)*(Y.width+i)+e.rect.x-t.x+n.x,"px, 0)")},{transform:"translate(0, 0)"}],duration:M.animation.duration,easing:M.animation.easing}}}),eS=eu(e=>{var t,n;let i=e.offset||0,l=i?h.swipe:null!=(t=h.navigation)?t:h.swipe,u=i||eE()?h.easing.swipe:h.easing.navigation,{direction:a}=e,c=null!=(n=e.count)?n:1,s=r.ANIMATION,d=l*c;if(!a){let t=null==Y?void 0:Y.width,n=e.duration||0,r=t?l/t*Math.abs(i):l;0!==c?(n<r?d=d/r*Math.max(n,r/5):t&&(d=l/t*(t-Math.abs(i))),a=G(i)>0?o.zn:o.Nc):d=l/2}let f=0;a===o.zn?eh(G(1))?f=-c:(s=r.NONE,d=l):a===o.Nc&&(eh(G(-1))?f=c:(s=r.NONE,d=l)),W(()=>{ef(0),N(r.NONE)},d=Math.round(d)),q.current&&eb({rect:q.current.getBoundingClientRect(),index:M.globalIndex}),N(s),O(o.sI,{type:"swipe",increment:f,duration:d,easing:u})});l.useEffect(()=>{var e,t;(null==(e=M.animation)?void 0:e.increment)&&(null==(t=M.animation)?void 0:t.duration)&&j(()=>R({type:"swipe",increment:0}),M.animation.duration)},[M.animation,R,j]);let e_=[D,eh,(null==Y?void 0:Y.width)||0,h.swipe,()=>N(r.SWIPE),e=>ef(e),(e,t)=>eS({offset:e,duration:t,count:1}),e=>eS({offset:e,count:0})],ex=[()=>{b&&N(r.PULL)},e=>em(e),e=>ew(e),e=>ew(e,!0)];!function(e,t,n,r,o,u,a,c,s,d,h,f,m,p){let v=l.useRef(0),g=l.useRef([]),y=l.useRef(),w=l.useRef(0),b=l.useRef(i.NONE),E=l.useCallback(e=>{y.current===e.pointerId&&(y.current=void 0,b.current=i.NONE);let t=g.current;t.splice(0,t.length,...t.filter(t=>t.pointerId!==e.pointerId))},[]),S=l.useCallback(e=>{E(e),e.persist(),g.current.push(e)},[E]),_=eu(e=>{S(e)}),x=(e,t)=>d&&e>t||s&&e<-t,C=eu(e=>{if(g.current.find(t=>t.pointerId===e.pointerId)&&y.current===e.pointerId){let e=Date.now()-w.current,t=v.current;b.current===i.SWIPE?Math.abs(t)>.3*n||Math.abs(t)>5&&e<r?a(t,e):c(t):b.current===i.PULL&&(x(t,60)?m(t,e):p(t)),v.current=0,b.current=i.NONE}E(e)});ev(e,_,eu(e=>{let n=g.current.find(t=>t.pointerId===e.pointerId);if(n){let r=y.current===e.pointerId;if(0===e.buttons)return void(r&&0!==v.current?C(e):E(n));let l=e.clientX-n.clientX,a=e.clientY-n.clientY;if(void 0===y.current){let n=t=>{S(e),y.current=e.pointerId,w.current=Date.now(),b.current=t};Math.abs(l)>Math.abs(a)&&Math.abs(l)>30&&t(l)?(n(i.SWIPE),o()):Math.abs(a)>Math.abs(l)&&x(a,30)&&(n(i.PULL),h())}else r&&(b.current===i.SWIPE?(v.current=l,u(l)):b.current===i.PULL&&(v.current=a,f(a)))}}),C)}(...e_,w,b,...ex),function(e,t,n,i,u,a,c,s,d){let h=l.useRef(0),f=l.useRef(0),m=l.useRef(),p=l.useRef(),v=l.useRef(0),g=l.useRef(),y=l.useRef(0),{setTimeout:w,clearTimeout:b}=Z(),E=l.useCallback(()=>{m.current&&(b(m.current),m.current=void 0)},[b]),S=l.useCallback(()=>{p.current&&(b(p.current),p.current=void 0)},[b]),_=eu(()=>{e!==r.SWIPE&&(h.current=0,y.current=0,E(),S())});l.useEffect(_,[e,_]);let x=eu(e=>{p.current=void 0,h.current===e&&d(h.current)}),C=eu(t=>{if(t.ctrlKey||Math.abs(t.deltaY)>Math.abs(t.deltaX))return;let l=e=>{v.current=e,b(g.current),g.current=e>0?w(()=>{v.current=0,g.current=void 0},300):void 0};if(e===r.NONE){if(Math.abs(t.deltaX)<=1.2*Math.abs(v.current))return void l(t.deltaX);if(!n(-t.deltaX))return;if(f.current+=t.deltaX,E(),Math.abs(f.current)>30)f.current=0,l(0),y.current=Date.now(),a();else{let e=f.current;m.current=w(()=>{m.current=void 0,e===f.current&&(f.current=0)},u)}}else if(e===r.SWIPE){let e=h.current-t.deltaX;if(h.current=e=Math.min(Math.abs(e),i)*Math.sign(e),c(e),S(),Math.abs(e)>.2*i){l(t.deltaX),s(e,Date.now()-y.current);return}p.current=w(()=>x(e),2*u)}else l(t.deltaX)});l.useEffect(()=>t(o.j7,C),[t,C])}(k,...e_);let eC=eu(()=>{f.focus&&J().querySelector(".".concat(s(o.kJ)," .").concat(s(eg())))&&ee()});l.useEffect(eC,[eC]);let eM=eu(()=>{var e;null==(e=p.view)||e.call(p,{index:M.currentIndex})});l.useEffect(eM,[M.globalIndex,eM]),l.useEffect(()=>m(T(o.zn,e=>eS({direction:o.zn,...e})),T(o.Nc,e=>eS({direction:o.Nc,...e})),T(o.sI,e=>R(e))),[T,eS,R]);let eR=l.useMemo(()=>({prev:er,next:ea,close:ed,focus:ee,slideRect:Y?function(e,t){let n=E(t),r=void 0!==n.percent?e.width/100*n.percent:n.pixel;return{width:Math.max(e.width-2*r,0),height:Math.max(e.height-2*r,0)}}(Y,a.padding):{width:0,height:0},containerRect:Y||{width:0,height:0},subscribeSensors:D,containerRef:U,setCarouselRef:V,toolbarWidth:x,setToolbarWidth:C}),[er,ea,ed,ee,D,Y,U,V,x,C,a.padding]);return l.useImperativeHandle(f.ref,()=>({prev:er,next:ea,close:ed,focus:ee,getLightboxProps:et,getLightboxState:en}),[er,ea,ed,ee,et,en]),l.createElement("div",{ref:B,className:c(s(eg()),s(o.tC)),style:{...k===r.SWIPE?{[d("swipe_offset")]:"".concat(Math.round(I.current),"px")}:null,...k===r.PULL?{[d("pull_offset")]:"".concat(Math.round(P.current),"px"),[d("pull_opacity")]:"".concat(F.current)}:null,..."none"!==f.touchAction?{[d("controller_touch_action")]:f.touchAction}:null,...v.container},...f.aria?{role:"presentation","aria-live":"polite"}:null,tabIndex:-1,...z},Y&&l.createElement(ey.Provider,{value:eR},n,null==(t=y.controls)?void 0:t.call(y)))});function eE(e){return h(o.mY,e)}function eS(e){return h("slide",e)}function e_(e){var t,n,r,i;let u,{slide:a,offset:d}=e,h=l.useRef(null),{currentIndex:f}=K(),{slideRect:m,close:p,focus:v}=ew(),{render:g,carousel:{imageFit:w,imageProps:b},on:{click:E},controller:{closeOnBackdropClick:S},styles:{slide:_}}=O(),{getOwnerDocument:x}=L(),C=0!==d;return l.useEffect(()=>{var e;C&&(null==(e=h.current)?void 0:e.contains(x().activeElement))&&v()},[C,v,x]),l.createElement("div",{ref:h,className:c(s(eS()),!C&&s(eS("current")),s(o.tC)),...{inert:M?C:C?"":void 0},onClick:e=>{let t=h.current,n=e.target instanceof HTMLElement?e.target:void 0;S&&n&&t&&(n===t||Array.from(t.children).find(e=>e===n)&&n.classList.contains(s(o.pc)))&&p()},style:_},(!(u=null==(t=g.slide)?void 0:t.call(g,{slide:a,offset:d,rect:m}))&&y(a)&&(u=l.createElement(em,{slide:a,offset:d,render:g,rect:m,imageFit:w,imageProps:b,onClick:C?void 0:()=>null==E?void 0:E({index:f})})),u?l.createElement(l.Fragment,null,null==(n=g.slideHeader)?void 0:n.call(g,{slide:a}),(null!=(r=g.slideContainer)?r:e=>{let{children:t}=e;return t})({slide:a,children:u}),null==(i=g.slideFooter)?void 0:i.call(g,{slide:a})):null))}function ex(){let e=O().styles.slide;return l.createElement("div",{className:s("slide"),style:e})}let eC=k(o.mY,function(e){let{carousel:t}=e,{slides:n,currentIndex:r,globalIndex:i}=K(),{setCarouselRef:o}=ew(),u=E(t.spacing),a=E(t.padding),h=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return Math.min(e.preload,Math.max(e.finite?t.length-1:Math.floor(t.length/2),n))}(t,n,1),f=[];if(n.length>0)for(let e=r-h;e<=r+h;e+=1){let l=_(n,e),o=i-r+e,u=t.finite&&(e<0||e>n.length-1);f.push(u?{key:o}:{key:["".concat(o),y(l)?l.src:void 0].filter(Boolean).join("|"),offset:e-r,slide:l})}return l.createElement("div",{ref:o,className:c(s(eE()),f.length>0&&s(eE("with_slides"))),style:{["".concat(d(eE("slides_count")))]:f.length,["".concat(d(eE("spacing_px")))]:u.pixel||0,["".concat(d(eE("spacing_percent")))]:u.percent||0,["".concat(d(eE("padding_px")))]:a.pixel||0,["".concat(d(eE("padding_percent")))]:a.percent||0}},f.map(e=>{let{key:t,slide:n,offset:r}=e;return n?l.createElement(e_,{key:t,slide:n,offset:r}):l.createElement(ex,{key:t})}))});function eM(){let{carousel:e}=O(),{slides:t,currentIndex:n}=K();return{prevDisabled:0===t.length||e.finite&&0===n,nextDisabled:0===t.length||e.finite&&n===t.length-1}}function eR(e){let{label:t,icon:n,renderIcon:r,action:i,onClick:o,disabled:u,style:a}=e;return l.createElement(q,{label:t,icon:n,renderIcon:r,className:s("navigation_".concat(i)),disabled:u,onClick:o,style:a,...function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=l.useRef();return en(()=>{t&&n.current&&(n.current=!1,e())},[t,e]),{onFocus:l.useCallback(()=>{n.current=!0},[]),onBlur:l.useCallback(()=>{n.current=!1},[])}}(ew().focus,u)})}let ek=k(o.Ky,function(e){let{render:{buttonPrev:t,buttonNext:n,iconPrev:r,iconNext:i},styles:u}=e,{prev:a,next:c,subscribeSensors:s}=ew(),{prevDisabled:d,nextDisabled:h}=eM();return!function(e){var t;let n=es(),{publish:r}=A(),{animation:i}=O(),{prevDisabled:u,nextDisabled:a}=eM(),c=(null!=(t=i.navigation)?t:i.swipe)/2,s=ed(()=>r(o.zn),c),d=ed(()=>r(o.Nc),c),h=eu(e=>{switch(e.key){case o.or:r(o.C);break;case o.A9:(n?a:u)||(n?d:s)();break;case o.jn:(n?u:a)||(n?s:d)()}});l.useEffect(()=>e(o.uF,h),[e,h])}(s),l.createElement(l.Fragment,null,t?t():l.createElement(eR,{label:"Previous",action:o.zn,icon:Q,renderIcon:r,style:u.navigationPrev,disabled:d,onClick:a}),n?n():l.createElement(eR,{label:"Next",action:o.Nc,icon:G,renderIcon:i,style:u.navigationNext,disabled:h,onClick:c}))}),eN=s(o.HR),eI=s(o.vg);function eP(e,t,n){let r=window.getComputedStyle(e),i=n?"padding-left":"padding-right",l=n?r.paddingLeft:r.paddingRight,o=e.style.getPropertyValue(i);return e.style.setProperty(i,"".concat((b(l)||0)+t,"px")),()=>{o?e.style.setProperty(i,o):e.style.removeProperty(i)}}let eL=k(o.b6,function(e){let{noScroll:{disabled:t},children:n}=e,r=es(),{getOwnerDocument:i,getOwnerWindow:o}=L();return l.useEffect(()=>{if(t)return()=>{};let e=[],n=o(),{body:l,documentElement:u}=i(),a=Math.round(n.innerWidth-u.clientWidth);if(a>0){e.push(eP(l,a,r));let t=l.getElementsByTagName("*");for(let i=0;i<t.length;i+=1){let l=t[i];"style"in l&&"fixed"===n.getComputedStyle(l).getPropertyValue("position")&&!l.classList.contains(eI)&&e.push(eP(l,a,r))}}return l.classList.add(eN),()=>{l.classList.remove(eN),e.forEach(e=>e())}},[r,t,i,o]),l.createElement(l.Fragment,null,n)});function eF(e,t,n){let r=e.getAttribute(t);return e.setAttribute(t,n),()=>{r?e.setAttribute(t,r):e.removeAttribute(t)}}let ez=k(o.kJ,function(e){var t;let{children:n,animation:r,styles:i,className:a,on:f,portal:m,close:p}=e,[v,g]=l.useState(!1),[y,w]=l.useState(!1),b=l.useRef([]),E=l.useRef(null),{setTimeout:S}=Z(),{subscribe:_}=A(),x=er()?0:r.fade;l.useEffect(()=>(g(!0),()=>{g(!1),w(!1)}),[]);let C=eu(()=>{b.current.forEach(e=>e()),b.current=[]}),M=eu(()=>{var e;w(!1),C(),null==(e=f.exiting)||e.call(f),S(()=>{var e;null==(e=f.exited)||e.call(f),p()},x)});l.useEffect(()=>_(o.C,M),[_,M]);let k=eu(e=>{var t,n,r;e.scrollTop,w(!0),null==(t=f.entering)||t.call(f);let i=null!=(r=null==(n=e.parentNode)?void 0:n.children)?r:[];for(let t=0;t<i.length;t+=1){let n=i[t];-1===["TEMPLATE","SCRIPT","STYLE"].indexOf(n.tagName)&&n!==e&&(b.current.push(eF(n,"inert","")),b.current.push(eF(n,"aria-hidden","true")))}b.current.push(()=>{var e,t;null==(t=null==(e=E.current)?void 0:e.focus)||t.call(e)}),S(()=>{var e;null==(e=f.entered)||e.call(f)},x)}),N=l.useCallback(e=>{e?k(e):C()},[k,C]);return v?(0,u.createPortal)(l.createElement(ep,{ref:N,className:c(a,s(h(o.kJ,void 0)),s(o.vg),y&&s((t="open",h(o.kJ,t)))),role:"presentation","aria-live":"polite",style:{...r.fade!==R.animation.fade?{[d("fade_animation_duration")]:"".concat(x,"ms")}:null,...r.easing.fade!==R.animation.easing.fade?{[d("fade_animation_timing_function")]:r.easing.fade}:null,...i.root},onFocus:e=>{E.current||(E.current=e.relatedTarget)}},n),m.root||document.body):null}),eA=k(o.OT,function(e){let{children:t}=e;return l.createElement(l.Fragment,null,t)}),eD=k(o.pq,function(e){let{toolbar:{buttons:t},render:{buttonClose:n,iconClose:r},styles:i}=e,{close:u,setToolbarWidth:a}=ew(),{setContainerRef:c,containerRect:d}=el();en(()=>{a(null==d?void 0:d.width)},[a,null==d?void 0:d.width]);let f=()=>n?n():l.createElement(q,{key:o.C,label:"Close",icon:J,renderIcon:r,onClick:u});return l.createElement("div",{ref:c,style:i.toolbar,className:s(h(o.pq,void 0))},null==t?void 0:t.map(e=>e===o.C?f():e))});function eT(e){let{carousel:t,animation:n,render:r,toolbar:i,controller:u,noScroll:a,on:c,plugins:s,slides:d,index:h,...f}=e,{animation:m,carousel:p,render:v,toolbar:g,controller:y,noScroll:w,on:E,slides:S,index:_,plugins:x,...C}=R,{config:M,augmentation:k}=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=e,i=e=>{let t=[...r];for(;t.length>0;){let n=t.pop();if((null==n?void 0:n.module.name)===e)return!0;(null==n?void 0:n.children)&&t.push(...n.children)}return!1},l=(e,t)=>{if(""===e){r=[N(t,r)];return}r=I(r,e,e=>[N(t,[e])])},u=(e,t)=>{r=I(r,e,e=>[N(e.module,[N(t,e.children)])])},a=(e,t,n)=>{r=I(r,e,e=>{var r;return[N(e.module,[...n?[N(t)]:[],...null!=(r=e.children)?r:[],...n?[]:[N(t)]])]})},c=(e,t,n)=>{r=I(r,e,e=>[...n?[N(t)]:[],e,...n?[]:[N(t)]])},s=e=>{u(o.Kv,e)},d=(e,t)=>{r=I(r,e,e=>[N(t,e.children)])},h=e=>{r=I(r,e,e=>e.children)},f=e=>{n.push(e)};return t.forEach(e=>{e({contains:i,addParent:l,append:u,addChild:a,addSibling:c,addModule:s,replace:d,remove:h,augment:f})}),{config:r,augmentation:e=>n.reduce((e,t)=>t(e),e)}}([N(ez,[N(eL,[N(eb,[N(eC),N(eD),N(ek)])])])],s||x),P=k({animation:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{easing:n,...r}=e,{easing:i,...l}=t;return{easing:{...n,...i},...r,...l}}(m,n),carousel:{...p,...t},render:{...v,...r},toolbar:{...g,...i},controller:{...y,...u},noScroll:{...w,...a},on:{...E,...c},...C,...f});return P.open?l.createElement(j,{...P},l.createElement(X,{slides:d||S,index:b(h||_)},l.createElement(B,null,l.createElement(D,null,function e(t,n){var r;return l.createElement(t.module.component,{key:t.module.name,...n},null==(r=t.children)?void 0:r.map(t=>e(t,n)))}(N(eA,M),P))))):null}},41804:(e,t,n)=>{"use strict";n.d(t,{A9:()=>A,AH:()=>s,Bv:()=>m,C:()=>C,CC:()=>v,EC:()=>p,HR:()=>y,I1:()=>h,Kv:()=>i,Ky:()=>l,Nc:()=>_,OT:()=>a,P9:()=>f,Qo:()=>d,RK:()=>R,Td:()=>j,Ue:()=>E,Uj:()=>T,V:()=>K,b6:()=>o,e7:()=>W,j7:()=>F,jR:()=>O,jn:()=>D,kJ:()=>u,mY:()=>r,or:()=>z,pc:()=>b,pq:()=>c,qq:()=>M,s:()=>I,sI:()=>x,t$:()=>L,tC:()=>g,tD:()=>N,tu:()=>k,uF:()=>P,vg:()=>w,zn:()=>S});let r="carousel",i="controller",l="navigation",o="no-scroll",u="portal",a="root",c="toolbar",s="zoom",d="loading",h="error",f="complete",m="placeholder",p=e=>`active-slide-${e}`;p(d),p("playing"),p(h),p(f);let v="fullsize",g="flex_center",y="no_scroll",w="no_scroll_padding",b="slide_wrapper",E="slide_wrapper_interactive",S="prev",_="next",x="swipe",C="close",M="onPointerDown",R="onPointerMove",k="onPointerUp",N="onPointerLeave",I="onPointerCancel",P="onKeyDown",L="onKeyUp",F="onWheel",z="Escape",A="ArrowLeft",D="ArrowRight",T="button",O="icon",j="contain",W="cover",K="Unknown action type"},58561:()=>{},68375:()=>{},82269:(e,t,n)=>{"use strict";var r=n(49509);n(68375);var i=n(12115),l=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(i),o=void 0!==r&&r.env&&!0,u=function(e){return"[object String]"===Object.prototype.toString.call(e)},a=function(){function e(e){var t=void 0===e?{}:e,n=t.name,r=void 0===n?"stylesheet":n,i=t.optimizeForSpeed,l=void 0===i?o:i;c(u(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",c("boolean"==typeof l,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=l,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var a="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=a?a.getAttribute("content"):null}var t,n=e.prototype;return n.setOptimizeForSpeed=function(e){c("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),c(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},n.isOptimizeForSpeed=function(){return this._optimizeForSpeed},n.inject=function(){var e=this;if(c(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(o||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,n){return"number"==typeof n?e._serverSheet.cssRules[n]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),n},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},n.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},n.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},n.insertRule=function(e,t){if(c(u(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var n=this.getSheet();"number"!=typeof t&&(t=n.cssRules.length);try{n.insertRule(e,t)}catch(t){return o||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var r=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,r))}return this._rulesCount++},n.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var n="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!n.cssRules[e])return e;n.deleteRule(e);try{n.insertRule(t,e)}catch(r){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),n.insertRule(this._deletedRulePlaceholder,e)}}else{var r=this._tags[e];c(r,"old rule at index `"+e+"` not found"),r.textContent=t}return e},n.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];c(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},n.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},n.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,n){return n?t=t.concat(Array.prototype.map.call(e.getSheetForTag(n).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},n.makeStyleTag=function(e,t,n){t&&c(u(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var i=document.head||document.getElementsByTagName("head")[0];return n?i.insertBefore(r,n):i.appendChild(r),r},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,t),e}();function c(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var s=function(e){for(var t=5381,n=e.length;n;)t=33*t^e.charCodeAt(--n);return t>>>0},d={};function h(e,t){if(!t)return"jsx-"+e;var n=String(t),r=e+n;return d[r]||(d[r]="jsx-"+s(e+"-"+n)),d[r]}function f(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var n=e+t;return d[n]||(d[n]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[n]}var m=function(){function e(e){var t=void 0===e?{}:e,n=t.styleSheet,r=void 0===n?null:n,i=t.optimizeForSpeed,l=void 0!==i&&i;this._sheet=r||new a({name:"styled-jsx",optimizeForSpeed:l}),this._sheet.inject(),r&&"boolean"==typeof l&&(this._sheet.setOptimizeForSpeed(l),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var n=this.getIdAndRules(e),r=n.styleId,i=n.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var l=i.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=l,this._instancesCounts[r]=1},t.remove=function(e){var t=this,n=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(n in this._instancesCounts,"styleId: `"+n+"` not found"),this._instancesCounts[n]-=1,this._instancesCounts[n]<1){var r=this._fromServer&&this._fromServer[n];r?(r.parentNode.removeChild(r),delete this._fromServer[n]):(this._indices[n].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[n]),delete this._instancesCounts[n]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],n=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return n[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,n;return t=this.cssRules(),void 0===(n=e)&&(n={}),t.map(function(e){var t=e[0],r=e[1];return l.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:n.nonce?n.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,n=e.dynamic,r=e.id;if(n){var i=h(r,n);return{styleId:i,rules:Array.isArray(t)?t.map(function(e){return f(i,e)}):[f(i,t)]}}return{styleId:h(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),p=i.createContext(null);p.displayName="StyleSheetContext";var v=l.default.useInsertionEffect||l.default.useLayoutEffect,g="undefined"!=typeof window?new m:void 0;function y(e){var t=g||i.useContext(p);return t&&("undefined"==typeof window?t.add(e):v(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}y.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=y}}]);