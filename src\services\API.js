import axios from 'axios';
import { API_URL } from '../constant';

const API = axios.create({
    baseURL: API_URL,
    headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
    },
     mode: 'no-cors'
});

// Add request interceptor for offline handling
API.interceptors.request.use(
    async (config) => {
        // Add timestamp to track request freshness
        config.metadata = { requestTime: Date.now() };
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Add response interceptor for offline handling
API.interceptors.response.use(
    (response) => {
        // Add cache headers for offline detection
        response.headers['x-request-time'] = response.config.metadata?.requestTime;
        return response;
    },
    async (error) => {
        // Import here to avoid circular dependency
        const { offlineQueue } = await import('./offlineQueue');
        const { offlineStorage } = await import('./offlineStorage');

        // Check if request should be queued for retry
        if (offlineQueue.shouldQueue(error)) {
            const config = error.config;

            // Add to offline queue for retry when online
            await offlineQueue.addRequest(
                config.url,
                config.method?.toUpperCase() || 'GET',
                config.data,
                config.headers
            );

            // Try to serve from cache if available
            if (config.method?.toLowerCase() === 'get') {
                try {
                    // Check if this is a services request
                    if (config.url?.includes('/services')) {
                        const cachedServices = await offlineStorage.getServices();
                        if (cachedServices.length > 0) {
                            console.log('📱 Serving services from offline cache');
                            return {
                                data: cachedServices,
                                status: 200,
                                statusText: 'OK (Cached)',
                                headers: { 'x-served-from': 'offline-cache' },
                                config
                            };
                        }
                    }

                    // Check if this is an actions request
                    if (config.url?.includes('assigned-actions')) {
                        // Extract filter from URL if present
                        const urlParams = new URLSearchParams(config.url.split('?')[1]);
                        const filterParam = urlParams.get('filter');
                        let filter = 'All';

                        if (filterParam) {
                            try {
                                const filterObj = JSON.parse(decodeURIComponent(filterParam));
                                // Extract filter logic here if needed
                            } catch (e) {
                                // Use URL path to determine filter
                                const pathParts = config.url.split('/');
                                const filterIndex = pathParts.findIndex(part => part === 'assigned-actions');
                                if (filterIndex !== -1 && pathParts[filterIndex + 1]) {
                                    filter = pathParts[filterIndex + 1];
                                }
                            }
                        }

                        const cachedActions = await offlineStorage.getActions(filter);
                        if (cachedActions.length > 0) {
                            console.log('📱 Serving actions from offline cache');
                            return {
                                data: cachedActions,
                                status: 200,
                                statusText: 'OK (Cached)',
                                headers: { 'x-served-from': 'offline-cache' },
                                config
                            };
                        }
                    }
                } catch (cacheError) {
                    console.error('❌ Error serving from cache:', cacheError);
                }
            }
        }

        return Promise.reject(error);
    }
);

export default API;



