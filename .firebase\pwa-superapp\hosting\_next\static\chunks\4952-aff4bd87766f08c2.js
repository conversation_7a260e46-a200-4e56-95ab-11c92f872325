(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4952],{9838:function(t){t.exports=function(){var t=[function(t,e){"use strict";function n(t,e,n,o){return({red:o[4*(n*e+t)],green:o[4*(n*e+t)+1],blue:o[4*(n*e+t)+2],alpha:o[4*(n*e+t)+3]}).alpha}function o(t,e,o,i){for(var r=t?1:-1,a=t?0:o-1,s=a;t?s<o:s>-1;s+=r)for(var u=0;u<e;u++)if(n(u,s,e,i))return s;return null}function i(t,e,o,i){for(var r=t?1:-1,a=t?0:e-1,s=a;t?s<e:s>-1;s+=r)for(var u=0;u<o;u++)if(n(s,u,e,i))return s;return null}Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e=t.getContext("2d"),n=t.width,r=t.height,a=e.getImageData(0,0,n,r).data,s=o(!0,n,r,a),u=o(!1,n,r,a),c=i(!0,n,r,a),h=i(!1,n,r,a)-c+1,l=u-s+1,d=e.getImageData(c,s,h,l);return t.width=h,t.height=l,e.clearRect(0,0,h,l),e.putImageData(d,0,0),t}}];function e(o){if(n[o])return n[o].exports;var i=n[o]={exports:{},id:o,loaded:!1};return t[o].call(i.exports,i,i.exports,e),i.loaded=!0,i.exports}var n={};return e.m=t,e.c=n,e.p="",e(0)}()},11823:(t,e,n)=>{"use strict";n.d(e,{A:()=>i});var o=n(86608);function i(t){var e=function(t,e){if("object"!=(0,o.A)(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!=(0,o.A)(i))return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==(0,o.A)(e)?e:e+""}},20235:(t,e,n)=>{"use strict";n.d(e,{A:()=>i});var o=n(93495);function i(t,e){if(null==t)return{};var n,i,r=(0,o.A)(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],-1===e.indexOf(n)&&({}).propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}},24952:(t,e,n)=>{"use strict";n.d(e,{A:()=>y});var o=n(79630),i=n(20235),r=n(28383),a=n(30857),s=n(38289),u=n(88748),c=n(38637),h=n(12115);function l(t,e,n){this.x=t,this.y=e,this.time=n||new Date().getTime()}function d(t,e,n,o){this.startPoint=t,this.control1=e,this.control2=n,this.endPoint=o}function f(t,e){var n,o,i,r,a,s,u,c,h,l=this,d=e||{};(this.velocityFilterWeight=d.velocityFilterWeight||.7,this.minWidth=d.minWidth||.5,this.maxWidth=d.maxWidth||2.5,this.throttle="throttle"in d?d.throttle:16,this.minDistance="minDistance"in d?d.minDistance:5,this.throttle)?this._strokeMoveUpdate=(n=f.prototype._strokeUpdate,o=this.throttle,u=null,c=0,i||(i={}),h=function(){c=!1===i.leading?0:Date.now(),u=null,s=n.apply(r,a),u||(r=a=null)},function(){var t=Date.now();c||!1!==i.leading||(c=t);var e=o-(t-c);return r=this,a=arguments,e<=0||e>o?(u&&(clearTimeout(u),u=null),c=t,s=n.apply(r,a),u||(r=a=null)):u||!1===i.trailing||(u=setTimeout(h,e)),s}):this._strokeMoveUpdate=f.prototype._strokeUpdate,this.dotSize=d.dotSize||function(){return(this.minWidth+this.maxWidth)/2},this.penColor=d.penColor||"black",this.backgroundColor=d.backgroundColor||"rgba(0,0,0,0)",this.onBegin=d.onBegin,this.onEnd=d.onEnd,this._canvas=t,this._ctx=t.getContext("2d"),this.clear(),this._handleMouseDown=function(t){1===t.which&&(l._mouseButtonDown=!0,l._strokeBegin(t))},this._handleMouseMove=function(t){l._mouseButtonDown&&l._strokeMoveUpdate(t)},this._handleMouseUp=function(t){1===t.which&&l._mouseButtonDown&&(l._mouseButtonDown=!1,l._strokeEnd(t))},this._handleTouchStart=function(t){if(1===t.targetTouches.length){var e=t.changedTouches[0];l._strokeBegin(e)}},this._handleTouchMove=function(t){t.preventDefault();var e=t.targetTouches[0];l._strokeMoveUpdate(e)},this._handleTouchEnd=function(t){t.target===l._canvas&&(t.preventDefault(),l._strokeEnd(t))},this.on()}l.prototype.velocityFrom=function(t){return this.time!==t.time?this.distanceTo(t)/(this.time-t.time):1},l.prototype.distanceTo=function(t){return Math.sqrt(Math.pow(this.x-t.x,2)+Math.pow(this.y-t.y,2))},l.prototype.equals=function(t){return this.x===t.x&&this.y===t.y&&this.time===t.time},d.prototype.length=function(){for(var t=0,e=void 0,n=void 0,o=0;o<=10;o+=1){var i=o/10,r=this._point(i,this.startPoint.x,this.control1.x,this.control2.x,this.endPoint.x),a=this._point(i,this.startPoint.y,this.control1.y,this.control2.y,this.endPoint.y);if(o>0){var s=r-e,u=a-n;t+=Math.sqrt(s*s+u*u)}e=r,n=a}return t},d.prototype._point=function(t,e,n,o,i){return e*(1-t)*(1-t)*(1-t)+3*n*(1-t)*(1-t)*t+3*o*(1-t)*t*t+i*t*t*t},f.prototype.clear=function(){var t=this._ctx,e=this._canvas;t.fillStyle=this.backgroundColor,t.clearRect(0,0,e.width,e.height),t.fillRect(0,0,e.width,e.height),this._data=[],this._reset(),this._isEmpty=!0},f.prototype.fromDataURL=function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=new Image,i=n.ratio||window.devicePixelRatio||1,r=n.width||this._canvas.width/i,a=n.height||this._canvas.height/i;this._reset(),o.src=t,o.onload=function(){e._ctx.drawImage(o,0,0,r,a)},this._isEmpty=!1},f.prototype.toDataURL=function(t){var e;if("image/svg+xml"===t)return this._toSVG();for(var n=arguments.length,o=Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];return(e=this._canvas).toDataURL.apply(e,[t].concat(o))},f.prototype.on=function(){this._handleMouseEvents(),this._handleTouchEvents()},f.prototype.off=function(){this._canvas.removeEventListener("mousedown",this._handleMouseDown),this._canvas.removeEventListener("mousemove",this._handleMouseMove),document.removeEventListener("mouseup",this._handleMouseUp),this._canvas.removeEventListener("touchstart",this._handleTouchStart),this._canvas.removeEventListener("touchmove",this._handleTouchMove),this._canvas.removeEventListener("touchend",this._handleTouchEnd)},f.prototype.isEmpty=function(){return this._isEmpty},f.prototype._strokeBegin=function(t){this._data.push([]),this._reset(),this._strokeUpdate(t),"function"==typeof this.onBegin&&this.onBegin(t)},f.prototype._strokeUpdate=function(t){var e=t.clientX,n=t.clientY,o=this._createPoint(e,n),i=this._data[this._data.length-1],r=i&&i[i.length-1],a=r&&o.distanceTo(r)<this.minDistance;if(!(r&&a)){var s=this._addPoint(o),u=s.curve,c=s.widths;u&&c&&this._drawCurve(u,c.start,c.end),this._data[this._data.length-1].push({x:o.x,y:o.y,time:o.time,color:this.penColor})}},f.prototype._strokeEnd=function(t){var e=this.points.length>2,n=this.points[0];if(!e&&n&&this._drawDot(n),n){var o=this._data[this._data.length-1],i=o[o.length-1];n.equals(i)||o.push({x:n.x,y:n.y,time:n.time,color:this.penColor})}"function"==typeof this.onEnd&&this.onEnd(t)},f.prototype._handleMouseEvents=function(){this._mouseButtonDown=!1,this._canvas.addEventListener("mousedown",this._handleMouseDown),this._canvas.addEventListener("mousemove",this._handleMouseMove),document.addEventListener("mouseup",this._handleMouseUp)},f.prototype._handleTouchEvents=function(){this._canvas.style.msTouchAction="none",this._canvas.style.touchAction="none",this._canvas.addEventListener("touchstart",this._handleTouchStart),this._canvas.addEventListener("touchmove",this._handleTouchMove),this._canvas.addEventListener("touchend",this._handleTouchEnd)},f.prototype._reset=function(){this.points=[],this._lastVelocity=0,this._lastWidth=(this.minWidth+this.maxWidth)/2,this._ctx.fillStyle=this.penColor},f.prototype._createPoint=function(t,e,n){var o=this._canvas.getBoundingClientRect();return new l(t-o.left,e-o.top,n||new Date().getTime())},f.prototype._addPoint=function(t){var e=this.points,n=void 0;if(e.push(t),e.length>2){3===e.length&&e.unshift(e[0]);var o=this._calculateCurveControlPoints(e[0],e[1],e[2]).c2,i=this._calculateCurveControlPoints(e[1],e[2],e[3]).c1,r=new d(e[1],o,i,e[2]),a=this._calculateCurveWidths(r);return e.shift(),{curve:r,widths:a}}return{}},f.prototype._calculateCurveControlPoints=function(t,e,n){var o=t.x-e.x,i=t.y-e.y,r=e.x-n.x,a=e.y-n.y,s={x:(t.x+e.x)/2,y:(t.y+e.y)/2},u={x:(e.x+n.x)/2,y:(e.y+n.y)/2},c=Math.sqrt(o*o+i*i),h=Math.sqrt(r*r+a*a),d=s.x-u.x,f=s.y-u.y,p=h/(c+h),v={x:u.x+d*p,y:u.y+f*p},y=e.x-v.x,_=e.y-v.y;return{c1:new l(s.x+y,s.y+_),c2:new l(u.x+y,u.y+_)}},f.prototype._calculateCurveWidths=function(t){var e=t.startPoint,n=t.endPoint,o={start:null,end:null},i=this.velocityFilterWeight*n.velocityFrom(e)+(1-this.velocityFilterWeight)*this._lastVelocity,r=this._strokeWidth(i);return o.start=this._lastWidth,o.end=r,this._lastVelocity=i,this._lastWidth=r,o},f.prototype._strokeWidth=function(t){return Math.max(this.maxWidth/(t+1),this.minWidth)},f.prototype._drawPoint=function(t,e,n){var o=this._ctx;o.moveTo(t,e),o.arc(t,e,n,0,2*Math.PI,!1),this._isEmpty=!1},f.prototype._drawCurve=function(t,e,n){var o=this._ctx,i=n-e,r=Math.floor(t.length());o.beginPath();for(var a=0;a<r;a+=1){var s=a/r,u=s*s,c=u*s,h=1-s,l=h*h,d=l*h,f=d*t.startPoint.x;f+=3*l*s*t.control1.x+3*h*u*t.control2.x+c*t.endPoint.x;var p=d*t.startPoint.y;p+=3*l*s*t.control1.y+3*h*u*t.control2.y+c*t.endPoint.y;var v=e+c*i;this._drawPoint(f,p,v)}o.closePath(),o.fill()},f.prototype._drawDot=function(t){var e=this._ctx,n="function"==typeof this.dotSize?this.dotSize():this.dotSize;e.beginPath(),this._drawPoint(t.x,t.y,n),e.closePath(),e.fill()},f.prototype._fromData=function(t,e,n){for(var o=0;o<t.length;o+=1){var i=t[o];if(i.length>1)for(var r=0;r<i.length;r+=1){var a=i[r],s=new l(a.x,a.y,a.time),u=a.color;if(0===r)this.penColor=u,this._reset(),this._addPoint(s);else if(r!==i.length-1){var c=this._addPoint(s),h=c.curve,d=c.widths;h&&d&&e(h,d,u)}}else this._reset(),n(i[0])}},f.prototype._toSVG=function(){var t=this,e=this._data,n=this._canvas,o=Math.max(window.devicePixelRatio||1,1),i=n.width/o,r=n.height/o,a=document.createElementNS("http://www.w3.org/2000/svg","svg");a.setAttributeNS(null,"width",n.width),a.setAttributeNS(null,"height",n.height),this._fromData(e,function(t,e,n){var o=document.createElement("path");if(!isNaN(t.control1.x)&&!isNaN(t.control1.y)&&!isNaN(t.control2.x)&&!isNaN(t.control2.y)){var i="M "+t.startPoint.x.toFixed(3)+","+t.startPoint.y.toFixed(3)+" "+("C "+t.control1.x.toFixed(3)+",")+t.control1.y.toFixed(3)+" "+(t.control2.x.toFixed(3)+",")+t.control2.y.toFixed(3)+" "+t.endPoint.x.toFixed(3)+","+t.endPoint.y.toFixed(3);o.setAttribute("d",i),o.setAttribute("stroke-width",(2.25*e.end).toFixed(3)),o.setAttribute("stroke",n),o.setAttribute("fill","none"),o.setAttribute("stroke-linecap","round"),a.appendChild(o)}},function(e){var n=document.createElement("circle"),o="function"==typeof t.dotSize?t.dotSize():t.dotSize;n.setAttribute("r",o),n.setAttribute("cx",e.x),n.setAttribute("cy",e.y),n.setAttribute("fill",e.color),a.appendChild(n)});var s='<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"'+(' viewBox="0 0 '+i+" ")+r+'" width="'+i+'" height="'+r+'">',u=a.innerHTML;if(void 0===u){var c=document.createElement("dummy"),h=a.childNodes;c.innerHTML="";for(var l=0;l<h.length;l+=1)c.appendChild(h[l].cloneNode(!0));u=c.innerHTML}return"data:image/svg+xml;base64,"+btoa(s+u+"</svg>")},f.prototype.fromData=function(t){var e=this;this.clear(),this._fromData(t,function(t,n){return e._drawCurve(t,n.start,n.end)},function(t){return e._drawDot(t)}),this._data=t},f.prototype.toData=function(){return this._data};var p=n(9838),v=["canvasProps","clearOnResize"],y=function(t){(0,s.A)(n,t);var e=(0,u.A)(n);function n(){var t;(0,a.A)(this,n);for(var r=arguments.length,s=Array(r),u=0;u<r;u++)s[u]=arguments[u];return(t=e.call.apply(e,[this].concat(s))).staticThis=t.constructor,t._sigPad=null,t._canvas=null,t.setRef=function(e){t._canvas=e,null===t._canvas&&(t._sigPad=null)},t._excludeOurProps=function(){var e=t.props;return e.canvasProps,e.clearOnResize,(0,i.A)(e,v)},t.componentDidMount=function(){var e=t.getCanvas();t._sigPad=new f(e,t._excludeOurProps()),t._resizeCanvas(),t.on()},t.componentWillUnmount=function(){t.off()},t.componentDidUpdate=function(){Object.assign(t._sigPad,t._excludeOurProps())},t.getCanvas=function(){if(null===t._canvas)throw t.staticThis.refNullError;return t._canvas},t.getTrimmedCanvas=function(){var e=t.getCanvas(),n=document.createElement("canvas");return n.width=e.width,n.height=e.height,n.getContext("2d").drawImage(e,0,0),p(n)},t.getSignaturePad=function(){if(null===t._sigPad)throw t.staticThis.refNullError;return t._sigPad},t._checkClearOnResize=function(){t.props.clearOnResize&&t._resizeCanvas()},t._resizeCanvas=function(){var e,n,o=null!=(e=t.props.canvasProps)?e:{},i=o.width,r=o.height;if(void 0===i||void 0===r){var a=t.getCanvas(),s=Math.max(null!=(n=window.devicePixelRatio)?n:1,1);void 0===i&&(a.width=a.offsetWidth*s),void 0===r&&(a.height=a.offsetHeight*s),a.getContext("2d").scale(s,s),t.clear()}},t.render=function(){var e=t.props.canvasProps;return h.createElement("canvas",(0,o.A)({ref:t.setRef},e))},t.on=function(){return window.addEventListener("resize",t._checkClearOnResize),t.getSignaturePad().on()},t.off=function(){return window.removeEventListener("resize",t._checkClearOnResize),t.getSignaturePad().off()},t.clear=function(){return t.getSignaturePad().clear()},t.isEmpty=function(){return t.getSignaturePad().isEmpty()},t.fromDataURL=function(e,n){return t.getSignaturePad().fromDataURL(e,n)},t.toDataURL=function(e,n){return t.getSignaturePad().toDataURL(e,n)},t.fromData=function(e){return t.getSignaturePad().fromData(e)},t.toData=function(){return t.getSignaturePad().toData()},t}return(0,r.A)(n)}(h.Component);y.propTypes={velocityFilterWeight:c.number,minWidth:c.number,maxWidth:c.number,minDistance:c.number,dotSize:c.oneOfType([c.number,c.func]),penColor:c.string,throttle:c.number,onEnd:c.func,onBegin:c.func,canvasProps:c.object,clearOnResize:c.bool},y.defaultProps={clearOnResize:!0},y.refNullError=Error("react-signature-canvas is currently mounting or unmounting: React refs are null during this phase.")},28383:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});var o=n(11823);function i(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,(0,o.A)(i.key),i)}}function r(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}},30857:(t,e,n)=>{"use strict";function o(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}n.d(e,{A:()=>o})},38289:(t,e,n)=>{"use strict";n.d(e,{A:()=>i});var o=n(42222);function i(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&(0,o.A)(t,e)}},79630:(t,e,n)=>{"use strict";function o(){return(o=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)({}).hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t}).apply(null,arguments)}n.d(e,{A:()=>o})},86608:(t,e,n)=>{"use strict";function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}n.d(e,{A:()=>o})},88748:(t,e,n)=>{"use strict";function o(t){return(o=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function i(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(i=function(){return!!t})()}n.d(e,{A:()=>a});var r=n(86608);function a(t){var e=i();return function(){var n,i=o(t);n=e?Reflect.construct(i,arguments,o(this).constructor):i.apply(this,arguments);if(n&&("object"==(0,r.A)(n)||"function"==typeof n))return n;if(void 0!==n)throw TypeError("Derived constructors may only return object or undefined");if(void 0===this)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return this}}}}]);