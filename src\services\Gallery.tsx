import React, { useState } from "react";
import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css"; // Import Lightbox styles
import { Zoom } from 'yet-another-react-lightbox/plugins'


interface ImageDisplayProps {
  imageSrc: string;
}

const ImageDisplay: React.FC<ImageDisplayProps> = ({ imageSrc }) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);

  return (
    <div className="container">
      <div className="card">
        <div className="body-blue text-center">
          {/* Display Image */}
          <img
            src={imageSrc}
            alt="Displayed"
            className="display-image"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setIsOpen(true);
            }}
            style={{ cursor: 'pointer' }}
          />

          {/* Lightbox Modal */}
          {isOpen && (
            <Lightbox
              open={isOpen}
              close={() => setIsOpen(false)}
              slides={[{ src: imageSrc }]}
              plugins={[Zoom]}
              carousel={{finite:true}}
            />
          )}
        </div>
      </div>

      {/* CSS to style the image */}
      <style jsx>{`
        .display-image {
          max-width: 80px;
          max-height: 80px;
          width: auto;
          height: auto;
          object-fit: cover;
          cursor: pointer !important;
          transition: transform 0.3s ease-in-out;
          border-radius: 4px;
          border: 1px solid #dee2e6;
          pointer-events: auto;
          user-select: none;
        }

        /* Hover zoom effect */
        .display-image:hover {
          transform: scale(1.05);
          box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .container {
          padding: 0;
          margin: 0;
          max-width: none;
          width: fit-content;
        }

        .card {
          border: none;
          background: none;
          margin: 0;
          padding: 0;
          width: fit-content;
        }

        .body-blue {
          padding: 0;
          width: fit-content;
        }
      `}</style>
    </div>
  );
};

export default ImageDisplay;
