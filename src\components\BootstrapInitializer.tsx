"use client";

import { useEffect } from 'react';

const BootstrapInitializer = () => {
  useEffect(() => {
    // Dynamically import Bootstrap JavaScript only on client side
    // const initBootstrap = async () => {
    //   if (typeof window !== 'undefined') {
    //     try {
    //       // Import Bootstrap JavaScript
    //       await import('bootstrap/dist/js/bootstrap.bundle.min.js');
    //       console.log('✅ Bootstrap JavaScript loaded successfully');
    //     } catch (error) {
    //       console.error('❌ Failed to load Bootstrap JavaScript:', error);
    //     }
    //   }
    // };

    // initBootstrap();
  }, []);

  return null; // This component doesn't render anything
};

export default BootstrapInitializer;
