# Firebase Cloud Messaging (FCM) Setup Instructions

This guide will help you set up Firebase Cloud Messaging for push notifications in your Next.js PWA.

## 🚀 Quick Start

### 1. Firebase Project Setup

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your existing project `pwa-superapp` or create a new one
3. Navigate to **Project Settings** > **General**
4. Scroll down to **Your apps** section
5. If you don't have a web app, click **Add app** and select **Web**
6. Copy the Firebase configuration object

### 2. Enable Cloud Messaging

1. In Firebase Console, go to **Project Settings** > **Cloud Messaging**
2. Scroll down to **Web configuration**
3. Click **Generate key pair** to create a VAPID key
4. Copy the generated VAPID key

### 3. Environment Variables

1. Copy `.env.example` to `.env.local`:
   ```bash
   cp .env.example .env.local
   ```

2. Update `.env.local` with your Firebase configuration:
   ```env
   NEXT_PUBLIC_FIREBASE_API_KEY=your-actual-api-key
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=pwa-superapp.firebaseapp.com
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=pwa-superapp
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=pwa-superapp.appspot.com
   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-actual-sender-id
   NEXT_PUBLIC_FIREBASE_APP_ID=your-actual-app-id
   NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your-actual-measurement-id
   NEXT_PUBLIC_FIREBASE_VAPID_KEY=your-actual-vapid-key
   ```

### 4. Update Service Worker

Update `public/sw.js` with your actual Firebase configuration:

```javascript
const firebaseConfig = {
  apiKey: "your-actual-api-key",
  authDomain: "pwa-superapp.firebaseapp.com",
  projectId: "pwa-superapp",
  storageBucket: "pwa-superapp.appspot.com",
  messagingSenderId: "your-actual-sender-id",
  appId: "your-actual-app-id",
  measurementId: "your-actual-measurement-id"
};
```

## 🧪 Testing

### 1. Start Development Server
```bash
npm run dev
```

### 2. Test Notification Permission
1. Navigate to `/settings` page
2. You should see a "Push Notifications" card
3. Click "Enable Notifications" button
4. Allow notifications when prompted
5. FCM token should be generated and displayed

### 3. Test Push Notifications

You can test push notifications using Firebase Console:

1. Go to Firebase Console > **Messaging**
2. Click **Send your first message**
3. Enter notification title and text
4. Click **Send test message**
5. Enter the FCM token from your app
6. Click **Test**

## 📱 Features Implemented

- ✅ FCM token generation
- ✅ Notification permission handling
- ✅ Background message handling (service worker)
- ✅ Foreground message handling
- ✅ Notification click handling
- ✅ Token refresh functionality
- ✅ Settings page integration
- ✅ Error handling and user feedback

## 🔧 Customization

### Notification Icons
Update notification icons in the service worker (`public/sw.js`):
```javascript
icon: '/assets/icons/Icon-192.png',
badge: '/assets/icons/Icon-72.png',
```

### Backend Integration
The FCM service automatically sends tokens to `/api/fcm/register-token`. Create this API endpoint to store tokens in your database.

### Custom Notification Handling
Modify `src/services/fcmService.ts` to customize notification behavior.

## 🐛 Troubleshooting

### Common Issues

1. **"Firebase messaging is not supported"**
   - Ensure you're testing on HTTPS or localhost
   - Check browser compatibility

2. **"Failed to generate FCM token"**
   - Verify VAPID key is correct
   - Check Firebase project configuration
   - Ensure notifications are not blocked in browser

3. **Service worker not registering**
   - Check browser console for errors
   - Verify `public/sw.js` syntax
   - Clear browser cache and reload

### Browser Support
- Chrome: ✅ Full support
- Firefox: ✅ Full support  
- Safari: ⚠️ Limited support (iOS 16.4+)
- Edge: ✅ Full support

## 📚 Next Steps

1. **Backend Integration**: Create API endpoints to store and manage FCM tokens
2. **Notification Targeting**: Implement user segmentation for targeted notifications
3. **Analytics**: Track notification delivery and engagement
4. **Advanced Features**: Add notification scheduling, rich media, etc.

## 🔗 Useful Links

- [Firebase Cloud Messaging Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Web Push Protocol](https://web.dev/push-notifications/)
- [Service Worker API](https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API)
