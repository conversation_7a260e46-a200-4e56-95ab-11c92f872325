"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Spinner } from 'react-bootstrap';
import fcmService from '@/services/fcmService';

interface NotificationPermissionProps {
  onTokenGenerated?: (token: string) => void;
  showCard?: boolean;
}

const NotificationPermission: React.FC<NotificationPermissionProps> = ({
  onTokenGenerated,
  showCard = true
}) => {
  const [permissionStatus, setPermissionStatus] = useState<NotificationPermission>('default');
  const [fcmToken, setFcmToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSupported, setIsSupported] = useState(true);

  useEffect(() => {
    checkNotificationSupport();
    checkPermissionStatus();
    loadStoredToken();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const checkNotificationSupport = () => {
    if (typeof window === 'undefined' || !('Notification' in window)) {
      setIsSupported(false);
      setError('Push notifications are not supported in this browser');
    }
  };

  const checkPermissionStatus = () => {
    if (typeof window !== 'undefined' && 'Notification' in window) {
      setPermissionStatus(Notification.permission);
    }
  };

  const loadStoredToken = async () => {
    try {
      const storedToken = await fcmService.getStoredToken();
      if (storedToken) {
        setFcmToken(storedToken);
        onTokenGenerated?.(storedToken);
      }
    } catch (error) {
      console.error('Error loading stored token:', error);
    }
  };

  const requestPermissionAndGenerateToken = async () => {
    if (!isSupported) return;

    setIsLoading(true);
    setError(null);

    try {
      // Initialize FCM service
      await fcmService.initialize();

      // Generate FCM token
      const token = await fcmService.generateFCMToken();

      if (token) {
        setFcmToken(token);
        setPermissionStatus('granted');
        onTokenGenerated?.(token);

        // Show success message
        console.log('✅ Push notifications enabled successfully!');
      } else {
        setError('Failed to generate FCM token. Please try again.');
      }
    } catch (error: any) {
      console.error('Error enabling notifications:', error);
      setError(error.message || 'Failed to enable push notifications');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshToken = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const newToken = await fcmService.refreshToken();
      if (newToken) {
        setFcmToken(newToken);
        onTokenGenerated?.(newToken);
        console.log('✅ FCM token refreshed successfully!');
      } else {
        setError('Failed to refresh FCM token');
      }
    } catch (error: any) {
      console.error('Error refreshing token:', error);
      setError(error.message || 'Failed to refresh token');
    } finally {
      setIsLoading(false);
    }
  };

  const copyTokenToClipboard = () => {
    if (fcmToken) {
      navigator.clipboard.writeText(fcmToken);
      console.log('📋 FCM token copied to clipboard');
    }
  };

  const renderContent = () => (
    <>
      <div className="d-flex align-items-center justify-content-between mb-3">
        <h6 className="mb-0">Push Notifications</h6>
        <div className="d-flex align-items-center">
          {permissionStatus === 'granted' && (
            <span className="badge bg-success me-2">
              <i className="bi bi-check-circle me-1"></i>
              Enabled
            </span>
          )}
          {permissionStatus === 'denied' && (
            <span className="badge bg-danger me-2">
              <i className="bi bi-x-circle me-1"></i>
              Blocked
            </span>
          )}
          {permissionStatus === 'default' && (
            <span className="badge bg-warning me-2">
              <i className="bi bi-question-circle me-1"></i>
              Not Set
            </span>
          )}
        </div>
      </div>

      {!isSupported && (
        <Alert variant="warning">
          <i className="bi bi-exclamation-triangle me-2"></i>
          Push notifications are not supported in this browser.
        </Alert>
      )}

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError(null)}>
          <i className="bi bi-exclamation-circle me-2"></i>
          {error}
        </Alert>
      )}

      {isSupported && permissionStatus === 'default' && (
        <div className="text-center">
          <p className="text-muted mb-3">
            Enable push notifications to receive important updates and alerts.
          </p>
          <Button
            variant="primary"
            onClick={requestPermissionAndGenerateToken}
            disabled={isLoading}
            className="d-flex align-items-center mx-auto"
          >
            {isLoading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Enabling...
              </>
            ) : (
              <>
                <i className="bi bi-bell me-2"></i>
                Enable Notifications
              </>
            )}
          </Button>
        </div>
      )}

      {permissionStatus === 'denied' && (
        <Alert variant="info">
          <i className="bi bi-info-circle me-2"></i>
          Notifications are blocked. Please enable them in your browser settings and refresh the page.
        </Alert>
      )}

      {permissionStatus === 'granted' && fcmToken && (
        <div>
          <Alert variant="success">
            <i className="bi bi-check-circle me-2"></i>
            Push notifications are enabled! You&apos;ll receive important updates.
          </Alert>

          <div className="d-flex gap-2 mb-3">
            <Button
              variant="outline-secondary"
              size="sm"
              onClick={refreshToken}
              disabled={isLoading}
            >
              {isLoading ? (
                <Spinner animation="border" size="sm" />
              ) : (
                <>
                  <i className="bi bi-arrow-clockwise me-1"></i>
                  Refresh Token
                </>
              )}
            </Button>

            <Button
              variant="outline-info"
              size="sm"
              onClick={copyTokenToClipboard}
            >
              <i className="bi bi-clipboard me-1"></i>
              Copy Token
            </Button>
          </div>

          <details className="mt-3">
            <summary className="text-muted" style={{ cursor: 'pointer' }}>
              <small>View FCM Token (for developers)</small>
            </summary>
            <div className="mt-2 p-2 bg-light rounded">
              <small className="font-monospace text-break">{fcmToken}</small>
            </div>
          </details>
        </div>
      )}
    </>
  );

  if (!showCard) {
    return <div className="notification-permission">{renderContent()}</div>;
  }

  return (
    <Card className="notification-permission-card">
      <Card.Body>
        {renderContent()}
      </Card.Body>
    </Card>
  );
};

export default NotificationPermission;
