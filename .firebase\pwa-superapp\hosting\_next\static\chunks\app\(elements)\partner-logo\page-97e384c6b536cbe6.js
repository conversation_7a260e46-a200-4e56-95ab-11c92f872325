(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2238],{21217:(e,s,t)=>{"use strict";t.d(s,{default:()=>d});var a=t(95155),i=t(9e4),l=t(38808),c=t(12115);let r=e=>{let{handleShowSetting:s,showSetting:t}=e,{theme:c,handleDarkModeToggle:r}=(0,i.D)(),{viewMode:A,handleRTLToggling:n}=(0,l.L)();return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{id:"setting-popup-overlay",className:t?"active":"",onClick:s}),(0,a.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(t?"active":""),id:"settingCard",children:(0,a.jsx)("div",{className:"card-body",children:(0,a.jsxs)("div",{className:"container",children:[(0,a.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,a.jsx)("p",{className:"mb-0",children:"Settings"}),(0,a.jsx)("div",{onClick:s,className:"btn-close",id:"settingCardClose"})]}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,a.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,a.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===c,onChange:r}),(0,a.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===c?"Light":"Dark"," mode"]})]})}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===A,onChange:n}),(0,a.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===A?"LTR":"RTL"," mode"]})]})})]})})})]})};var A=t(6874),n=t.n(A);let d=e=>{let{links:s,title:t}=e,[i,l]=(0,c.useState)(!1),A=()=>l(!i);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"header-area",id:"headerArea",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,a.jsx)("div",{className:"back-button",children:(0,a.jsx)(n(),{href:"/".concat(s),children:(0,a.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,a.jsx)("div",{className:"page-heading",children:(0,a.jsx)("h6",{className:"mb-0",children:t})}),(0,a.jsx)("div",{className:"setting-wrapper",onClick:A,children:(0,a.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,a.jsx)("i",{className:"bi bi-gear"}),(0,a.jsx)("span",{})]})})]})})}),(0,a.jsx)(r,{showSetting:i,handleShowSetting:A})]})}},28732:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a={src:"/_next/static/media/2.c1610991.png",height:50,width:207,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAMAAABSSm3fAAAAIVBMVEURNoERNoEAoOQAn+EHbLMDf8YSPIUfAE8OWKEAoeEHb7TaVQJZAAAAC3RSTlNFa3AuZUTtEG1Xw3ndIEcAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAaSURBVHicY2DjYGBgZ+VkZuBiZmBkZGFiAgACkAA+jAXGcQAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:2}},38808:(e,s,t)=>{"use strict";t.d(s,{L:()=>i});var a=t(12115);let i=()=>{let[e,s]=(0,a.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,a.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let t=()=>{s(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:t,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let s=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(s),e.style.display="none")},20)},1e3))}t()}}}},38983:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});var a=t(95155),i=t(6874),l=t.n(i);t(12115);let c=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],r=()=>(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,a.jsx)("div",{className:"container px-0",children:(0,a.jsx)("div",{className:"footer-nav position-relative",children:(0,a.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:c.map((e,s)=>(0,a.jsx)("li",{children:(0,a.jsxs)(l(),{href:"/".concat(e.link),children:[(0,a.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,a.jsx)("span",{children:e.title})]})},s))})})})})})},52373:(e,s,t)=>{Promise.resolve().then(t.bind(t,95410)),Promise.resolve().then(t.bind(t,95068)),Promise.resolve().then(t.bind(t,38983)),Promise.resolve().then(t.bind(t,21217))},69282:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a={src:"/_next/static/media/4.4a15fd7d.png",height:50,width:154,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAADCAMAAACZFr56AAAAKlBMVEUkJy4qLTApKTMoLDEqLzKUsW514dcXKS8mLDEqLjCOWFb/cynkRkyhcF2QcnpeAAAADnRSTlMnlhs7Vosadi59fx9X2nn/IXwAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAiSURBVHicBcGHAQAwCMMwh9XJ/+8ikUW4hdN/STriPthgAwXdAGiP7JCxAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:3}},70247:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a={src:"/_next/static/media/1.1994caae.png",height:50,width:160,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAADCAMAAACZFr56AAAAElBMVEX/WV//Wl//WV3/WF3/Zmb/YWE7bk+JAAAABnRSTlMlg1I0AhXSDFVaAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAHUlEQVR4nBXBgQ0AMAgDINbq/y+bgZhlSPs+icIBAloAJLB3p28AAAAASUVORK5CYII=",blurWidth:8,blurHeight:3}},9e4:(e,s,t)=>{"use strict";t.d(s,{D:()=>i});var a=t(12115);let i=()=>{let[e,s]=(0,a.useState)("light"),[t,i]=(0,a.useState)(!1);(0,a.useEffect)(()=>{s(localStorage.getItem("theme")||"light"),i(!0)},[]),(0,a.useEffect)(()=>{t&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,t]);let l=(0,a.useCallback)(()=>{s(e=>"dark"===e?"light":"dark")},[]),c=(0,a.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let s=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(s),e.style.display="none")},20)},1e3))}l()},[l]);return{theme:e,toggleTheme:l,handleDarkModeToggle:c}}},95068:(e,s,t)=>{"use strict";t.d(s,{default:()=>m});var a=t(95155);t(12115);var i=t(70247),l=t(28732),c=t(99093),r=t(69282),A=t(99744),n=t(66766),d=t(67269),h=t(27677);let o=[i.A,l.A,c.A,r.A,A.A],m=()=>(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"card",children:(0,a.jsx)("div",{className:"card-body",children:(0,a.jsxs)("div",{className:"partner-logo-slide-wrapper",children:[(0,a.jsx)(h.RC,{slidesPerView:3,loop:!0,spaceBetween:15,pagination:{el:".tns-nav",clickable:!0},modules:[d.dK],breakpoints:{0:{slidesPerView:1},576:{slidesPerView:2},768:{slidesPerView:3}},className:"partner-slide",children:o.map((e,s)=>(0,a.jsx)(h.qr,{children:(0,a.jsx)("div",{className:"card partner-slide-card border my-2 bg-white",children:(0,a.jsx)("div",{className:"card-body p-3",children:(0,a.jsx)("a",{href:"#",children:(0,a.jsx)(n.default,{src:e,alt:""})})})})},s))}),(0,a.jsx)("div",{className:"tns-nav","aria-label":"Carousel Pagination"})]})})})})})},95410:(e,s,t)=>{"use strict";t.d(s,{default:()=>m});var a=t(95155);t(12115);var i=t(70247),l=t(28732),c=t(99093),r=t(69282),A=t(99744),n=t(66766),d=t(67269),h=t(27677);let o=[i.A,l.A,c.A,r.A,A.A],m=()=>(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"card bg-info",children:(0,a.jsx)("div",{className:"card-body",children:(0,a.jsxs)("div",{className:"partner-logo-slide-wrapper-2",children:[(0,a.jsx)(h.RC,{slidesPerView:3,loop:!0,spaceBetween:15,pagination:{el:".tns-nav",clickable:!0},modules:[d.dK],breakpoints:{0:{slidesPerView:1},576:{slidesPerView:2},768:{slidesPerView:3}},className:"partner-slide2",children:o.map((e,s)=>(0,a.jsx)(h.qr,{children:(0,a.jsx)("div",{className:"card partner-slide-card border my-2 bg-white",children:(0,a.jsx)("div",{className:"card-body p-3",children:(0,a.jsx)("a",{href:"#",children:(0,a.jsx)(n.default,{src:e,alt:""})})})})},s))}),(0,a.jsx)("div",{className:"tns-nav","aria-label":"Carousel Pagination"})]})})})})})},99093:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a={src:"/_next/static/media/3.5fcb5ef0.png",height:50,width:166,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAMAAABSSm3fAAAAFVBMVEUmKCsiJiubaTUqKSqzdzW2eTOPYS17nSqkAAAAB3RSTlOJJUCaaVRZHuMJ2gAAAAlwSFlzAAALEwAACxMBAJqcGAAAABdJREFUeJxjYGYAAWYGBkYmVjYWJkZGAADRAB3JaZ/QAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:2}},99744:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a={src:"/_next/static/media/6.a5b8186e.png",height:50,width:194,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAMAAABSSm3fAAAAElBMVEUhISEhISEiIiIiIiIgICAiIiJMsL3vAAAABnRSTlONpmSXeDy3fiv5AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAGklEQVR4nGNgZmJhYWBlYmJgZmZgYGBkZAQAAXIAIPQXPRQAAAAASUVORK5CYII=",blurWidth:8,blurHeight:2}}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,7677,6766,7269,8441,1684,7358],()=>s(52373)),_N_E=e.O()}]);