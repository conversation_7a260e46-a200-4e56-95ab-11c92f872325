"use client";

import React, { useState, useEffect } from "react";
import HeaderSeven from "@/layouts/headers/HeaderSeven";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { loginActions } from "@/store/login-slice";
import API from "@/services/API";

interface UserRole {
  id: string;
  name: string;
  maskName: string;
  maskId: string;
}

interface UserProfile {
  firstName?: string;
  lastName?: string;
  email?: string;
  username?: string;
  department?: string;
  company?: string;
  phone?: string;
  lastLogin?: string;
  roles?: UserRole[];
}

export default function ProfilePage() {
  const [userProfile, setUserProfile] = useState<UserProfile>({});
  const [loading, setLoading] = useState(true);
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const router = useRouter();
  const dispatch = useDispatch();

  useEffect(() => {
    fetchUserProfile();
  }, []);

  const fetchUserProfile = async () => {
    try {
      const response = await API.get("/users/me");
      if (response.status === 200) {
        // If the response is an array of roles, we need to handle it differently
        if (Array.isArray(response.data)) {
          setUserProfile({ roles: response.data });
        } else {
          setUserProfile(response.data);
        }
      }
    } catch (error) {
      console.error("Error fetching user profile:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    // Clear localStorage
    localStorage.removeItem("access_token");
    localStorage.removeItem("refresh_token");
    
    // Clear Redux state
    dispatch(loginActions.setLogout());
    
    // Redirect to login
    router.push("/");
  };

  const getInitials = () => {
    const firstName = userProfile.firstName || "";
    const lastName = userProfile.lastName || "";
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  return (
    <>
      <HeaderSeven heading="Profile" />
      
      <div className="page-content-wrapper" style={{ minHeight: "100vh", backgroundColor: "#f8f9fa" }}>
        <div className="container-fluid px-4 py-4">
          
          {loading ? (
            <div className="d-flex justify-content-center align-items-center py-5">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          ) : (
            <>
              {/* Profile Header Card */}
              <div className="card border-0 shadow-sm mb-4" style={{ borderRadius: "16px" }}>
                <div className="card-body p-4">
                  <div className="d-flex align-items-center">
                    {/* Avatar */}
                    <div 
                      className="rounded-circle d-flex align-items-center justify-content-center me-3"
                      style={{
                        width: "80px",
                        height: "80px",
                        background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                        color: "white",
                        fontSize: "24px",
                        fontWeight: "600"
                      }}
                    >
                      {getInitials() || "U"}
                    </div>
                    
                    {/* User Info */}
                    <div className="flex-grow-1">
                      <h4 className="mb-1" style={{ fontWeight: "600", color: "#1f2937" }}>
                        {userProfile.firstName 
                          ? `${userProfile.firstName} `
                          : userProfile.username || "Current User"}
                      </h4>
                      <p className="text-muted mb-0" style={{ fontSize: "14px" }}>
                        {userProfile.email || (userProfile.roles && userProfile.roles.length > 0
                          ? `${userProfile.roles.length} role${userProfile.roles.length > 1 ? 's' : ''} assigned`
                          : "No email available")}
                      </p>
                      {userProfile.department && (
                        <small className="text-muted">
                          {userProfile.department}
                        </small>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Profile Details */}
              <div className="card border-0 shadow-sm mb-4" style={{ borderRadius: "16px" }}>
                <div className="card-body p-4">
                  <h5 className="mb-3" style={{ fontWeight: "600", color: "#1f2937" }}>
                    Profile Details
                  </h5>
                  
                  <div className="row g-3">
                    <div className="col-12">
                      <div className="d-flex align-items-center p-3 bg-light rounded-3">
                        <i className="bi bi-person text-primary me-3" style={{ fontSize: "18px" }}></i>
                        <div>
                          <small className="text-muted d-block">Full Name</small>
                          <span style={{ fontWeight: "500" }}>
                            {userProfile.firstName 
                              ? `${userProfile.firstName} `
                              : "Not available"}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="col-12">
                      <div className="d-flex align-items-center p-3 bg-light rounded-3">
                        <i className="bi bi-envelope text-primary me-3" style={{ fontSize: "18px" }}></i>
                        <div>
                          <small className="text-muted d-block">Email</small>
                          <span style={{ fontWeight: "500" }}>
                            {userProfile.email || "Not available"}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    {userProfile.phone && (
                      <div className="col-12">
                        <div className="d-flex align-items-center p-3 bg-light rounded-3">
                          <i className="bi bi-telephone text-primary me-3" style={{ fontSize: "18px" }}></i>
                          <div>
                            <small className="text-muted d-block">Phone</small>
                            <span style={{ fontWeight: "500" }}>{userProfile.phone}</span>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {userProfile.roles && userProfile.roles.length > 0 && (
                      <div className="col-12">
                        <div className="d-flex align-items-start p-3 bg-light rounded-3">
                          <i className="bi bi-briefcase text-primary me-3 mt-1" style={{ fontSize: "18px" }}></i>
                          <div className="flex-grow-1">
                            <small className="text-muted d-block mb-2">Roles & Permissions</small>
                            <div className="d-flex flex-wrap gap-2">
                              {userProfile.roles.map((role) => (
                                <span
                                  key={role.id}
                                  className="badge rounded-pill px-3 py-1"
                                  style={{
                                    backgroundColor: '#e3f2fd',
                                    color: '#1976d2',
                                    fontSize: '11px',
                                    fontWeight: '500'
                                  }}
                                >
                                  {role.name}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {userProfile.company && (
                      <div className="col-12">
                        <div className="d-flex align-items-center p-3 bg-light rounded-3">
                          <i className="bi bi-building text-primary me-3" style={{ fontSize: "18px" }}></i>
                          <div>
                            <small className="text-muted d-block">Company</small>
                            <span style={{ fontWeight: "500" }}>{userProfile.company}</span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="card border-0 shadow-sm mb-5" style={{ borderRadius: "16px" }}>
                <div className="card-body p-4">
                  <h5 className="mb-3" style={{ fontWeight: "600", color: "#1f2937" }}>
                    Actions
                  </h5>
                  
                  <div className="d-grid gap-3">
                    <button
                      className="btn btn-outline-danger rounded-3 p-3 d-flex align-items-center"
                      onClick={() => setShowLogoutModal(true)}
                      style={{ border: "2px solid #dc3545" }}
                    >
                      <i className="bi bi-box-arrow-right me-3" style={{ fontSize: "18px" }}></i>
                      <div className="text-start">
                        <div style={{ fontWeight: "500" }}>Logout</div>
                        <small className="text-muted">Sign out of your account</small>
                      </div>
                    </button>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Logout Confirmation Modal */}
      {showLogoutModal && (
        <div className="modal show d-block" style={{ backgroundColor: "rgba(0,0,0,0.5)" }}>
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content border-0" style={{ borderRadius: "16px" }}>
              <div className="modal-body p-4 text-center">
                <div 
                  className="rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                  style={{
                    width: "60px",
                    height: "60px",
                    backgroundColor: "#fee2e2",
                    color: "#dc2626"
                  }}
                >
                  <i className="bi bi-box-arrow-right" style={{ fontSize: "24px" }}></i>
                </div>
                
                <h5 className="mb-2" style={{ fontWeight: "600" }}>Confirm Logout</h5>
                <p className="text-muted mb-4">
                  Are you sure you want to sign out of your account?
                </p>
                
                <div className="d-flex gap-3">
                  <button
                    className="btn btn-outline-secondary flex-fill rounded-pill"
                    onClick={() => setShowLogoutModal(false)}
                  >
                    Cancel
                  </button>
                  <button
                    className="btn btn-danger flex-fill rounded-pill"
                    onClick={handleLogout}
                  >
                    Logout
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Footer Navigation */}
      <div className="fixed-bottom bg-white border-top shadow-sm">
        <div className="container-fluid">
          <div className="row text-center py-2">
            <div className="col">
              <Link href="/dashboard" className="text-decoration-none text-muted">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-grid-3x3-gap fs-5 mb-1"></i>
                  <small style={{ fontSize: '0.7rem' }}>Dashboard</small>
                </div>
              </Link>
            </div>
            <div className="col">
              <Link href="/services" className="text-decoration-none text-muted">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-grid fs-5 mb-1"></i>
                  <small style={{ fontSize: '0.7rem' }}>Services</small>
                </div>
              </Link>
            </div>
            <div className="col">
              <Link href="/home" className="text-decoration-none text-muted">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-house fs-5 mb-1"></i>
                  <small style={{ fontSize: '0.7rem' }}>Home</small>
                </div>
              </Link>
            </div>
            <div className="col">
              <Link href="/history" className="text-decoration-none text-muted">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-clock-history fs-5 mb-1"></i>
                  <small style={{ fontSize: '0.7rem' }}>History</small>
                </div>
              </Link>
            </div>
            <div className="col">
              <Link href="/profile" className="text-decoration-none text-primary">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-person-fill fs-5 mb-1"></i>
                  <small style={{ fontSize: '0.7rem' }}>Profile</small>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
