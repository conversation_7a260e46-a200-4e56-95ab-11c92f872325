"use client";

import React, { useState } from "react";
import HeaderSeven from "@/layouts/headers/HeaderSeven";
import Link from "next/link";

export default function HistoryPage() {
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilterPopup, setShowFilterPopup] = useState(false);

  const filters = ['All', 'Reports', 'Permits', 'Assessments', 'Audits'];

  // History data - exact copy from test.tsx
  const historyItems = [
    {
      id: 'IR-2024-001',
      type: 'Incident Report',
      title: 'Slip and Fall - Warehouse A',
      date: '2024-01-15',
      status: 'Completed',
      icon: 'bi bi-exclamation-triangle-fill',
      color: '#FF3B30',
    },
    {
      id: 'RA-2024-003',
      type: 'Risk Assessment',
      title: 'Chemical Storage Area Review',
      date: '2024-01-14',
      status: 'In Progress',
      icon: 'bi bi-shield-fill',
      color: '#FF9500',
    },
    {
      id: 'PTW-2024-012',
      type: 'E-Permit',
      title: 'Hot Work - Maintenance Bay',
      date: '2024-01-13',
      status: 'Approved',
      icon: 'bi bi-file-text-fill',
      color: '#34C759',
    },
    {
      id: 'OBS-2024-008',
      type: 'Observation',
      title: 'Unsafe Ladder Usage',
      date: '2024-01-12',
      status: 'Closed',
      icon: 'bi bi-eye-fill',
      color: '#007AFF',
    },
    {
      id: 'AUD-2024-002',
      type: 'Safety Audit',
      title: 'Monthly Safety Inspection',
      date: '2024-01-10',
      status: 'Completed',
      icon: 'bi bi-patch-check-fill',
      color: '#5856D6',
    },
    {
      id: 'DOC-2024-005',
      type: 'Document',
      title: 'Safety Procedure Update',
      date: '2024-01-09',
      status: 'Published',
      icon: 'bi bi-folder-fill',
      color: '#AF52DE',
    },
  ];

  // Filter function - exact copy from test.tsx
  const filteredItems = historyItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.id.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = selectedFilter === 'All' ||
                         (selectedFilter === 'Reports' && (item.type.includes('Report') || item.type.includes('Observation'))) ||
                         (selectedFilter === 'Permits' && item.type.includes('Permit')) ||
                         (selectedFilter === 'Assessments' && item.type.includes('Assessment')) ||
                         (selectedFilter === 'Audits' && item.type.includes('Audit'));
    return matchesSearch && matchesFilter;
  });

  // Helper functions - exact copy from test.tsx
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
      case 'Approved':
      case 'Closed':
      case 'Published':
        return '#2E7D32'; // Dark green
      case 'In Progress':
        return '#F9A825'; // Dark yellow
      case 'Pending':
        return '#F57C00'; // Dark orange
      default:
        return '#666666';
    }
  };

  const getStatusBackgroundColor = (status: string) => {
    switch (status) {
      case 'Completed':
      case 'Approved':
      case 'Closed':
      case 'Published':
        return '#E8F5E9'; // Light green
      case 'In Progress':
        return '#FFF8E1'; // Light yellow
      case 'Pending':
        return '#FFF3E0'; // Light orange
      default:
        return '#F5F5F5';
    }
  };

  return (
    <>
      <HeaderSeven heading="History" />

      <div className="page-content-wrapper" style={{ minHeight: "100vh", backgroundColor: "#f8f9fa" }}>
        <div className="container-fluid px-3" style={{ padding: '16px' }}>

          {/* Header Section - Exact copy from test.tsx */}
          <div style={{ marginBottom: '24px' }}>
            {/* <h2 className="fw-bold mb-2 text-dark" style={{ fontSize: '28px', marginBottom: '8px' }}>
              History
            </h2> */}
            <p className="text-muted mb-0" style={{ opacity: 0.7, fontSize: '16px' }}>
              View your activity history and records
            </p>

            {/* Search and Filter Row - Exact copy from test.tsx */}
            <div style={{ marginTop: '16px' }}>
              <div className="d-flex align-items-center gap-3 mb-3">
                <div
                  className="d-flex align-items-center flex-grow-1"
                  style={{
                    backgroundColor: '#F2F2F7',
                    borderRadius: '10px',
                    padding: '8px 12px'
                  }}
                >
                  <i className="bi bi-search me-2" style={{ fontSize: '20px', color: '#8E8E93' }}></i>
                  <input
                    type="text"
                    className="form-control border-0 bg-transparent p-0"
                    placeholder="Search history..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    style={{
                      fontSize: '16px',
                      color: '#000000'
                    }}
                  />
                </div>

                <button
                  className="btn d-flex align-items-center justify-content-center"
                  onClick={() => setShowFilterPopup(true)}
                  style={{
                    width: '44px',
                    height: '44px',
                    borderRadius: '10px',
                    borderWidth: '1px',
                    borderColor: selectedFilter !== 'All' ? '#1976D2' : '#E5E7EB',
                    backgroundColor: selectedFilter !== 'All' ? '#1976D2' : '#F2F2F7'
                  }}
                >
                  <i
                    className="bi bi-sliders"
                    style={{
                      fontSize: '18px',
                      color: selectedFilter !== 'All' ? '#FFFFFF' : '#1976D2'
                    }}
                  ></i>
                </button>

                {/* Clear Filter Button */}
                {selectedFilter !== 'All' && (
                  <button
                    className="btn d-flex align-items-center justify-content-center"
                    onClick={() => setSelectedFilter('All')}
                    style={{
                      width: '32px',
                      height: '32px',
                      borderRadius: '16px',
                      backgroundColor: '#FFF2F2',
                      border: 'none'
                    }}
                  >
                    <i className="bi bi-x-circle-fill" style={{ fontSize: '20px', color: '#FF3B30' }}></i>
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* History Container - Exact copy from test.tsx */}
          <div style={{ marginBottom: '24px' }}>
            {filteredItems.map((item) => (
              <div
                key={item.id}
                className="bg-white border rounded-3 p-3 mb-3"
                style={{
                  borderRadius: '12px',
                  borderWidth: '1px',
                  borderColor: '#E5E7EB',
                  transition: 'all 0.2s ease',
                  cursor: 'pointer'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                <div className="d-flex align-items-center">
                  {/* Icon Container */}
                  <div
                    className="d-flex align-items-center justify-content-center me-3"
                    style={{
                      width: '40px',
                      height: '40px',
                      borderRadius: '20px',
                      backgroundColor: item.color + '20'
                    }}
                  >
                    <i
                      className={item.icon}
                      style={{
                        fontSize: '20px',
                        color: item.color
                      }}
                    ></i>
                  </div>

                  {/* Card Info */}
                  <div style={{ flex: 1 }}>
                    <h6 className="fw-bold text-dark mb-1" style={{ fontSize: '16px', marginBottom: '4px' }}>
                      {item.title}
                    </h6>
                    <p className="text-muted mb-0" style={{ fontSize: '14px', opacity: 0.7 }}>
                      {item.type} • {item.id}
                    </p>
                  </div>

                  {/* Card Meta */}
                  <div className="text-end">
                    <div
                      className="badge rounded-pill mb-1"
                      style={{
                        backgroundColor: getStatusBackgroundColor(item.status),
                        color: getStatusColor(item.status),
                        fontSize: '12px',
                        fontWeight: '600',
                        padding: '4px 8px'
                      }}
                    >
                      {item.status}
                    </div>
                    <div style={{ fontSize: '12px', opacity: 0.6, color: '#666666' }}>
                      {item.date}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Footer - Exact copy from test.tsx */}
          <div className="text-center py-4" style={{ paddingBottom: '100px' }}>
            <p className="text-muted mb-0" style={{ fontSize: '14px', opacity: 0.6 }}>
              Showing {filteredItems.length} of {historyItems.length} items
            </p>
          </div>

          {/* Filter Popup Modal - Exact copy from test.tsx */}
          {showFilterPopup && (
            <div
              className="modal fade show d-block"
              style={{
                backgroundColor: 'rgba(0,0,0,0.5)',
                zIndex: 1050
              }}
              onClick={() => setShowFilterPopup(false)}
            >
              <div
                className="modal-dialog modal-dialog-centered"
                style={{ maxWidth: '320px' }}
                onClick={(e) => e.stopPropagation()}
              >
                <div
                  className="modal-content"
                  style={{
                    borderRadius: '16px',
                    border: '1px solid #E5E7EB',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                  }}
                >
                  {/* Header */}
                  <div
                    className="modal-header border-bottom"
                    style={{
                      borderColor: '#F0F0F0 !important',
                      padding: '20px'
                    }}
                  >
                    <div className="d-flex justify-content-between align-items-center w-100">
                      <h5 className="modal-title mb-0" style={{ fontSize: '18px', fontWeight: '600' }}>
                        Filter History
                      </h5>
                      <button
                        type="button"
                        className="btn-close"
                        onClick={() => setShowFilterPopup(false)}
                        style={{ fontSize: '12px' }}
                      ></button>
                    </div>
                  </div>

                  {/* Filter Options */}
                  <div className="modal-body" style={{ padding: '16px' }}>
                    {filters.map((filter) => (
                      <button
                        key={filter}
                        className="btn w-100 d-flex justify-content-between align-items-center mb-2"
                        onClick={() => {
                          setSelectedFilter(filter);
                          setShowFilterPopup(false);
                        }}
                        style={{
                          backgroundColor: selectedFilter === filter ? '#E3F2FD' : 'transparent',
                          borderColor: '#E5E7EB',
                          borderWidth: '1px',
                          borderRadius: '12px',
                          padding: '16px',
                          textAlign: 'left'
                        }}
                      >
                        <span
                          style={{
                            fontSize: '16px',
                            fontWeight: '500',
                            color: selectedFilter === filter ? '#1976D2' : '#000000'
                          }}
                        >
                          {filter}
                        </span>
                        {selectedFilter === filter && (
                          <i className="bi bi-check-circle-fill" style={{ fontSize: '20px', color: '#1976D2' }}></i>
                        )}
                      </button>
                    ))}

                    {/* Clear All Filters Option */}
                    {selectedFilter !== 'All' && (
                      <button
                        className="btn w-100 d-flex align-items-center mt-3"
                        onClick={() => {
                          setSelectedFilter('All');
                          setShowFilterPopup(false);
                        }}
                        style={{
                          backgroundColor: '#FFF2F2',
                          borderColor: '#FFE5E5',
                          borderWidth: '1px',
                          borderRadius: '12px',
                          padding: '16px'
                        }}
                      >
                        <i className="bi bi-x-circle-fill" style={{ fontSize: '20px', color: '#FF3B30', marginRight: '12px' }}></i>
                        <span style={{ fontSize: '16px', fontWeight: '600', color: '#FF3B30' }}>
                          Clear All Filters
                        </span>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

        </div>
      </div>

      {/* Footer Navigation */}
      <div className="fixed-bottom bg-white border-top shadow-sm">
        <div className="container-fluid">
          <div className="row text-center py-2">
            <div className="col">
              <Link href="/dashboard" className="text-decoration-none text-muted">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-grid-3x3-gap fs-5 mb-1"></i>
                  <small style={{ fontSize: '0.7rem' }}>Dashboard</small>
                </div>
              </Link>
            </div>
            <div className="col">
              <Link href="/services" className="text-decoration-none text-muted">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-grid fs-5 mb-1"></i>
                  <small style={{ fontSize: '0.7rem' }}>Services</small>
                </div>
              </Link>
            </div>
            <div className="col">
              <Link href="/home" className="text-decoration-none text-muted">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-house fs-5 mb-1"></i>
                  <small style={{ fontSize: '0.7rem' }}>Home</small>
                </div>
              </Link>
            </div>
            <div className="col">
              <Link href="/history" className="text-decoration-none text-primary">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-clock-fill fs-5 mb-1"></i>
                  <small style={{ fontSize: '0.7rem' }}>History</small>
                </div>
              </Link>
            </div>
            <div className="col">
              <Link href="/profile" className="text-decoration-none text-muted">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-person fs-5 mb-1"></i>
                  <small style={{ fontSize: '0.7rem' }}>Profile</small>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
