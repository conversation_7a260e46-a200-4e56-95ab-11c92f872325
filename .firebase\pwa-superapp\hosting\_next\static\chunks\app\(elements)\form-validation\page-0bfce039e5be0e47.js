(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2262],{21217:(e,s,l)=>{"use strict";l.d(s,{default:()=>d});var t=l(95155),a=l(9e4),i=l(38808),r=l(12115);let c=e=>{let{handleShowSetting:s,showSetting:l}=e,{theme:r,handleDarkModeToggle:c}=(0,a.D)(),{viewMode:n,handleRTLToggling:o}=(0,i.L)();return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{id:"setting-popup-overlay",className:l?"active":"",onClick:s}),(0,t.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(l?"active":""),id:"settingCard",children:(0,t.jsx)("div",{className:"card-body",children:(0,t.jsxs)("div",{className:"container",children:[(0,t.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,t.jsx)("p",{className:"mb-0",children:"Settings"}),(0,t.jsx)("div",{onClick:s,className:"btn-close",id:"settingCardClose"})]}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,t.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,t.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===r,onChange:c}),(0,t.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===r?"Light":"Dark"," mode"]})]})}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===n,onChange:o}),(0,t.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===n?"LTR":"RTL"," mode"]})]})})]})})})]})};var n=l(6874),o=l.n(n);let d=e=>{let{links:s,title:l}=e,[a,i]=(0,r.useState)(!1),n=()=>i(!a);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"header-area",id:"headerArea",children:(0,t.jsx)("div",{className:"container",children:(0,t.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,t.jsx)("div",{className:"back-button",children:(0,t.jsx)(o(),{href:"/".concat(s),children:(0,t.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,t.jsx)("div",{className:"page-heading",children:(0,t.jsx)("h6",{className:"mb-0",children:l})}),(0,t.jsx)("div",{className:"setting-wrapper",onClick:n,children:(0,t.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,t.jsx)("i",{className:"bi bi-gear"}),(0,t.jsx)("span",{})]})})]})})}),(0,t.jsx)(c,{showSetting:a,handleShowSetting:n})]})}},31359:(e,s,l)=>{Promise.resolve().then(l.bind(l,64053)),Promise.resolve().then(l.bind(l,38983)),Promise.resolve().then(l.bind(l,21217))},38808:(e,s,l)=>{"use strict";l.d(s,{L:()=>a});var t=l(12115);let a=()=>{let[e,s]=(0,t.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,t.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let l=()=>{s(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:l,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let s=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(s),e.style.display="none")},20)},1e3))}l()}}}},38983:(e,s,l)=>{"use strict";l.d(s,{default:()=>c});var t=l(95155),a=l(6874),i=l.n(a);l(12115);let r=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],c=()=>(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,t.jsx)("div",{className:"container px-0",children:(0,t.jsx)("div",{className:"footer-nav position-relative",children:(0,t.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:r.map((e,s)=>(0,t.jsx)("li",{children:(0,t.jsxs)(i(),{href:"/".concat(e.link),children:[(0,t.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,t.jsx)("span",{children:e.title})]})},s))})})})})})},64053:(e,s,l)=>{"use strict";l.d(s,{default:()=>a});var t=l(95155);l(12115);let a=()=>(0,t.jsx)(t.Fragment,{children:(0,t.jsxs)("div",{className:"page-content-wrapper py-3",children:[(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("div",{className:"element-heading",children:(0,t.jsx)("h6",{children:"Form validation"})})}),(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("div",{className:"card-body",children:(0,t.jsxs)("form",{className:"was-validated",onSubmit:e=>e.preventDefault(),children:[(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{className:"form-label",htmlFor:"exampleInputText",children:"Input text"}),(0,t.jsx)("input",{className:"form-control",id:"exampleInputText",type:"text",placeholder:"Designing World",required:!0})]}),(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{className:"form-label",htmlFor:"exampleInputemail",children:"Input email"}),(0,t.jsx)("input",{className:"form-control",id:"exampleInputemail",type:"email",placeholder:"<EMAIL>",required:!0})]}),(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{className:"form-label",htmlFor:"exampleInputpassword",children:"Input password"}),(0,t.jsx)("input",{className:"form-control",id:"exampleInputpassword",type:"password",required:!0})]}),(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{className:"form-label",htmlFor:"exampleInputnumber",children:"Input number"}),(0,t.jsx)("input",{className:"form-control",id:"exampleInputnumber",type:"number",placeholder:"12",required:!0})]}),(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{className:"form-label",htmlFor:"exampleInputtelephone",children:"Input telephone"}),(0,t.jsx)("input",{className:"form-control",id:"exampleInputtelephone",type:"tel",placeholder:"+880 ************",required:!0})]}),(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{className:"form-label",htmlFor:"exampleInputurl",children:"Input url"}),(0,t.jsx)("input",{className:"form-control",id:"exampleInputurl",type:"url",placeholder:"https://themeforest.net/user/designing-world",required:!0})]}),(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{className:"form-label",htmlFor:"exampleTextarea1",children:"Input textarea"}),(0,t.jsx)("textarea",{className:"form-control",id:"exampleTextarea1",name:"textarea",cols:3,rows:5,placeholder:"Write something...",required:!0})]}),(0,t.jsxs)("button",{className:"btn btn-primary w-100 d-flex align-items-center justify-content-center",type:"button",children:["Send Message",(0,t.jsx)("i",{className:"bi bi-arrow-right fz-16 ms-1"})]})]})})})]})})},9e4:(e,s,l)=>{"use strict";l.d(s,{D:()=>a});var t=l(12115);let a=()=>{let[e,s]=(0,t.useState)("light"),[l,a]=(0,t.useState)(!1);(0,t.useEffect)(()=>{s(localStorage.getItem("theme")||"light"),a(!0)},[]),(0,t.useEffect)(()=>{l&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,l]);let i=(0,t.useCallback)(()=>{s(e=>"dark"===e?"light":"dark")},[]),r=(0,t.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let s=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(s),e.style.display="none")},20)},1e3))}i()},[i]);return{theme:e,toggleTheme:i,handleDarkModeToggle:r}}}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,8441,1684,7358],()=>s(31359)),_N_E=e.O()}]);