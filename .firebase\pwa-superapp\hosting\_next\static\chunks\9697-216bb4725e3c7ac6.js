(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9697],{15743:(e,t)=>{!function(){"use strict";var i={};function n(e){return c(">"+d("B",e.length),e)}function a(e){return c(">"+d("H",e.length),e)}function r(e){return c(">"+d("L",e.length),e)}function s(e,t,i){var s,o,l=Object.keys(e).length,h=c(">H",[l]);s=["0th","1st"].indexOf(t)>-1?2+12*l+4:2+12*l;var u="",p="";for(var o in e)if("string"==typeof o&&(o=parseInt(o)),!("0th"==t&&[34665,34853].indexOf(o)>-1)){if("Exif"==t&&40965==o)continue;if("1st"==t&&[513,514].indexOf(o)>-1)continue;var g=e[o],y=c(">H",[o]),S=m[t][o].type,C=c(">H",[f[S]]);"number"==typeof g&&(g=[g]);var P=function(e,t,i){var s,o,l,h,u="",p="";if("Byte"==t)(s=e.length)<=4?p=n(e)+d("\0",4-s):(p=c(">L",[i]),u=n(e));else if("Short"==t)(s=e.length)<=2?p=a(e)+d("\0\0",2-s):(p=c(">L",[i]),u=a(e));else if("Long"==t)(s=e.length)<=1?p=r(e):(p=c(">L",[i]),u=r(e));else if("Ascii"==t)(s=(o=e+"\0").length)>4?(p=c(">L",[i]),u=o):p=o+d("\0",4-s);else if("Rational"==t){if("number"==typeof e[0])s=1,l=e[0],h=e[1],o=c(">L",[l])+c(">L",[h]);else{s=e.length,o="";for(var f=0;f<s;f++)l=e[f][0],h=e[f][1],o+=c(">L",[l])+c(">L",[h])}p=c(">L",[i]),u=o}else if("SRational"==t){if("number"==typeof e[0])s=1,l=e[0],h=e[1],o=c(">l",[l])+c(">l",[h]);else{s=e.length,o="";for(var f=0;f<s;f++)l=e[f][0],h=e[f][1],o+=c(">l",[l])+c(">l",[h])}p=c(">L",[i]),u=o}else"Undefined"==t&&((s=e.length)>4?(p=c(">L",[i]),u=e):p=e+d("\0",4-s));return[c(">L",[s]),p,u]}(g,S,8+s+i+p.length),b=P[0],w=P[1],I=P[2];u+=y+C+b+w,p+=I}return[h+u,p]}function o(e){var t;if("\xff\xd8"==e.slice(0,2))(t=function(e){for(var t,i=0;i<e.length;i++)if("\xff\xe1"==(t=e[i]).slice(0,2)&&"Exif\0\0"==t.slice(4,10))return t;return null}(p(e)))?this.tiftag=t.slice(10):this.tiftag=null;else if(["II","MM"].indexOf(e.slice(0,2))>-1)this.tiftag=e;else if("Exif"==e.slice(0,4))this.tiftag=e.slice(6);else throw Error("Given file is neither JPEG nor TIFF.")}if(i.version="1.0.4",i.remove=function(e){var t=!1;if("\xff\xd8"==e.slice(0,2));else if("data:image/jpeg;base64,"==e.slice(0,23)||"data:image/jpg;base64,"==e.slice(0,22))e=h(e.split(",")[1]),t=!0;else throw Error("Given data is not jpeg.");var i=p(e).filter(function(e){return"\xff\xe1"!=e.slice(0,2)||"Exif\0\0"!=e.slice(4,10)}).join("");return t&&(i="data:image/jpeg;base64,"+l(i)),i},i.insert=function(e,t){var i,n,a,r,s=!1;if("Exif\0\0"!=e.slice(0,6))throw Error("Given data is not exif.");if("\xff\xd8"==t.slice(0,2));else if("data:image/jpeg;base64,"==t.slice(0,23)||"data:image/jpg;base64,"==t.slice(0,22))t=h(t.split(",")[1]),s=!0;else throw Error("Given data is not jpeg.");var o="\xff\xe1"+c(">H",[e.length+2])+e,u=(i=p(t),n=o,a=!1,r=[],i.forEach(function(e,t){"\xff\xe1"==e.slice(0,2)&&"Exif\0\0"==e.slice(4,10)&&(a?r.unshift(t):(i[t]=n,a=!0))}),r.forEach(function(e){i.splice(e,1)}),!a&&n&&(i=[i[0],n].concat(i.slice(1))),i.join(""));return s&&(u="data:image/jpeg;base64,"+l(u)),u},i.load=function(e){if("string"==typeof e)if("\xff\xd8"==e.slice(0,2))t=e;else if("data:image/jpeg;base64,"==e.slice(0,23)||"data:image/jpg;base64,"==e.slice(0,22))t=h(e.split(",")[1]);else if("Exif"==e.slice(0,4))t=e.slice(6);else throw Error("'load' gots invalid file data.");else throw Error("'load' gots invalid type argument.");var t,i={"0th":{},Exif:{},GPS:{},Interop:{},"1st":{},thumbnail:null},n=new o(t);if(null===n.tiftag)return i;"II"==n.tiftag.slice(0,2)?n.endian_mark="<":n.endian_mark=">";var a=u(n.endian_mark+"L",n.tiftag.slice(4,8))[0];i["0th"]=n.get_ifd(a,"0th");var r=i["0th"].first_ifd_pointer;if(delete i["0th"].first_ifd_pointer,34665 in i["0th"]&&(a=i["0th"][34665],i.Exif=n.get_ifd(a,"Exif")),34853 in i["0th"]&&(a=i["0th"][34853],i.GPS=n.get_ifd(a,"GPS")),40965 in i.Exif&&(a=i.Exif[40965],i.Interop=n.get_ifd(a,"Interop")),"\0\0\0\0"!=r&&(a=u(n.endian_mark+"L",r)[0],i["1st"]=n.get_ifd(a,"1st"),513 in i["1st"]&&514 in i["1st"])){var s=i["1st"][513]+i["1st"][514],l=n.tiftag.slice(i["1st"][513],s);i.thumbnail=l}return i},i.dump=function(e){var t=JSON.parse(JSON.stringify(e)),n=!1,a=!1,r=!1,o=!1;l="0th"in t?t["0th"]:{},"Exif"in t&&Object.keys(t.Exif).length||"Interop"in t&&Object.keys(t.Interop).length?(l[34665]=1,n=!0,h=t.Exif,"Interop"in t&&Object.keys(t.Interop).length?(h[40965]=1,r=!0,u=t.Interop):Object.keys(h).indexOf(i.ExifIFD.InteroperabilityTag.toString())>-1&&delete h[40965]):Object.keys(l).indexOf(i.ImageIFD.ExifTag.toString())>-1&&delete l[34665],"GPS"in t&&Object.keys(t.GPS).length?(l[i.ImageIFD.GPSTag]=1,a=!0,d=t.GPS):Object.keys(l).indexOf(i.ImageIFD.GPSTag.toString())>-1&&delete l[i.ImageIFD.GPSTag],"1st"in t&&"thumbnail"in t&&null!=t.thumbnail&&(o=!0,t["1st"][513]=1,t["1st"][514]=1,m=t["1st"]);var l,h,u,d,m,g,y,S,C=s(l,"0th",0),P=C[0].length+12*n+12*a+4+C[1].length,b="",w=0,I="",k=0,L="",v=0,A="";if(n&&(w=(g=s(h,"Exif",P))[0].length+12*r+g[1].length),a&&(k=(I=s(d,"GPS",P+w).join("")).length),r){var T=P+w+k;v=(L=s(u,"Interop",T).join("")).length}if(o){var T=P+w+k+v;if(y=s(m,"1st",T),(S=function(e){for(var t=p(e);"\xff\xe0"<=t[1].slice(0,2)&&"\xff\xef">=t[1].slice(0,2);)t=[t[0]].concat(t.slice(2));return t.join("")}(t.thumbnail)).length>64e3)throw Error("Given thumbnail is too large. max 64kB")}var D="",R="",x="",O="\0\0\0\0";if(n){var E=8+P,M=c(">L",[E]),F=c(">H",[34665]),U=c(">H",[f.Long]),B=c(">L",[1]);D=F+U+B+M}if(a){var E=8+P+w,M=c(">L",[E]),F=c(">H",[34853]),U=c(">H",[f.Long]),B=c(">L",[1]);R=F+U+B+M}if(r){var E=8+P+w+k,M=c(">L",[E]),F=c(">H",[40965]),U=c(">H",[f.Long]),B=c(">L",[1]);x=F+U+B+M}if(o){var E=8+P+w+k+v;O=c(">L",[E]);var G="\x02\x01\0\x04\0\0\0\x01"+c(">L",[E+y[0].length+24+4+y[1].length]),N="\x02\x02\0\x04\0\0\0\x01"+c(">L",[S.length]);A=y[0]+G+N+"\0\0\0\0"+y[1]+S}var j=C[0]+D+R+O+C[1];return n&&(b=g[0]+x+g[1]),"Exif\0\0MM\0*\0\0\0\b"+j+b+I+L+A},o.prototype={get_ifd:function(e,t){var i,n={},a=u(this.endian_mark+"H",this.tiftag.slice(e,e+2))[0],r=e+2;i=["0th","1st"].indexOf(t)>-1?"Image":t;for(var s=0;s<a;s++){e=r+12*s;var o=u(this.endian_mark+"H",this.tiftag.slice(e,e+2))[0],l=[u(this.endian_mark+"H",this.tiftag.slice(e+2,e+4))[0],u(this.endian_mark+"L",this.tiftag.slice(e+4,e+8))[0],this.tiftag.slice(e+8,e+12)];o in m[i]&&(n[o]=this.convert_value(l))}return"0th"==t&&(e=r+12*a,n.first_ifd_pointer=this.tiftag.slice(e,e+4)),n},convert_value:function(e){var t,i=null,n=e[0],a=e[1],r=e[2];if(1==n)a>4?(t=u(this.endian_mark+"L",r)[0],i=u(this.endian_mark+d("B",a),this.tiftag.slice(t,t+a))):i=u(this.endian_mark+d("B",a),r.slice(0,a));else if(2==n)a>4?(t=u(this.endian_mark+"L",r)[0],i=this.tiftag.slice(t,t+a-1)):i=r.slice(0,a-1);else if(3==n)a>2?(t=u(this.endian_mark+"L",r)[0],i=u(this.endian_mark+d("H",a),this.tiftag.slice(t,t+2*a))):i=u(this.endian_mark+d("H",a),r.slice(0,2*a));else if(4==n)a>1?(t=u(this.endian_mark+"L",r)[0],i=u(this.endian_mark+d("L",a),this.tiftag.slice(t,t+4*a))):i=u(this.endian_mark+d("L",a),r);else if(5==n)if(t=u(this.endian_mark+"L",r)[0],a>1){i=[];for(var s=0;s<a;s++)i.push([u(this.endian_mark+"L",this.tiftag.slice(t+8*s,t+4+8*s))[0],u(this.endian_mark+"L",this.tiftag.slice(t+4+8*s,t+8+8*s))[0]])}else i=[u(this.endian_mark+"L",this.tiftag.slice(t,t+4))[0],u(this.endian_mark+"L",this.tiftag.slice(t+4,t+8))[0]];else if(7==n)a>4?(t=u(this.endian_mark+"L",r)[0],i=this.tiftag.slice(t,t+a)):i=r.slice(0,a);else if(9==n)a>1?(t=u(this.endian_mark+"L",r)[0],i=u(this.endian_mark+d("l",a),this.tiftag.slice(t,t+4*a))):i=u(this.endian_mark+d("l",a),r);else if(10==n)if(t=u(this.endian_mark+"L",r)[0],a>1){i=[];for(var s=0;s<a;s++)i.push([u(this.endian_mark+"l",this.tiftag.slice(t+8*s,t+4+8*s))[0],u(this.endian_mark+"l",this.tiftag.slice(t+4+8*s,t+8+8*s))[0]])}else i=[u(this.endian_mark+"l",this.tiftag.slice(t,t+4))[0],u(this.endian_mark+"l",this.tiftag.slice(t+4,t+8))[0]];else throw Error("Exif might be wrong. Got incorrect value type to decode. type:"+n);return i instanceof Array&&1==i.length?i[0]:i}},"undefined"!=typeof window&&"function"==typeof window.btoa)var l=window.btoa;if(void 0===l)var l=function(e){for(var t,i,n,a,r,s,o,l="",h=0,c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";h<e.length;)t=e.charCodeAt(h++),i=e.charCodeAt(h++),n=e.charCodeAt(h++),a=t>>2,r=(3&t)<<4|i>>4,s=(15&i)<<2|n>>6,o=63&n,isNaN(i)?s=o=64:isNaN(n)&&(o=64),l=l+c.charAt(a)+c.charAt(r)+c.charAt(s)+c.charAt(o);return l};if("undefined"!=typeof window&&"function"==typeof window.atob)var h=window.atob;if(void 0===h)var h=function(e){var t,i,n,a,r,s,o,l="",h=0,c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";for(e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"");h<e.length;)a=c.indexOf(e.charAt(h++)),r=c.indexOf(e.charAt(h++)),s=c.indexOf(e.charAt(h++)),o=c.indexOf(e.charAt(h++)),t=a<<2|r>>4,i=(15&r)<<4|s>>2,n=(3&s)<<6|o,l+=String.fromCharCode(t),64!=s&&(l+=String.fromCharCode(i)),64!=o&&(l+=String.fromCharCode(n));return l};function c(e,t){if(!(t instanceof Array))throw Error("'pack' error. Got invalid type argument.");if(e.length-1!=t.length)throw Error("'pack' error. "+(e.length-1)+" marks, "+t.length+" elements.");if("<"==e[0])i=!0;else if(">"==e[0])i=!1;else throw Error("");for(var i,n="",a=1,r=null,s=null,o=null;s=e[a];){if("b"==s.toLowerCase()){if(r=t[a-1],"b"==s&&r<0&&(r+=256),r>255||r<0)throw Error("'pack' error.");o=String.fromCharCode(r)}else if("H"==s){if((r=t[a-1])>65535||r<0)throw Error("'pack' error.");o=String.fromCharCode(Math.floor(r%65536/256))+String.fromCharCode(r%256),i&&(o=o.split("").reverse().join(""))}else if("l"==s.toLowerCase()){if(r=t[a-1],"l"==s&&r<0&&(r+=0x100000000),r>0xffffffff||r<0)throw Error("'pack' error.");o=String.fromCharCode(Math.floor(r/0x1000000))+String.fromCharCode(Math.floor(r%0x1000000/65536))+String.fromCharCode(Math.floor(r%65536/256))+String.fromCharCode(r%256),i&&(o=o.split("").reverse().join(""))}else throw Error("'pack' error.");n+=o,a+=1}return n}function u(e,t){if("string"!=typeof t)throw Error("'unpack' error. Got invalid type argument.");for(var i,n=0,a=1;a<e.length;a++)if("b"==e[a].toLowerCase())n+=1;else if("h"==e[a].toLowerCase())n+=2;else if("l"==e[a].toLowerCase())n+=4;else throw Error("'unpack' error. Got invalid mark.");if(n!=t.length)throw Error("'unpack' error. Mismatch between symbol and string length. "+n+":"+t.length);if("<"==e[0])i=!0;else if(">"==e[0])i=!1;else throw Error("'unpack' error.");for(var r=[],s=0,o=1,l=null,h=null,c=null,u="";h=e[o];){if("b"==h.toLowerCase())c=1,l=(u=t.slice(s,s+c)).charCodeAt(0),"b"==h&&l>=128&&(l-=256);else if("H"==h)c=2,u=t.slice(s,s+c),i&&(u=u.split("").reverse().join("")),l=256*u.charCodeAt(0)+u.charCodeAt(1);else if("l"==h.toLowerCase())c=4,u=t.slice(s,s+c),i&&(u=u.split("").reverse().join("")),l=0x1000000*u.charCodeAt(0)+65536*u.charCodeAt(1)+256*u.charCodeAt(2)+u.charCodeAt(3),"l"==h&&l>=0x80000000&&(l-=0x100000000);else throw Error("'unpack' error. "+h);r.push(l),s+=c,o+=1}return r}function d(e,t){for(var i="",n=0;n<t;n++)i+=e;return i}function p(e){if("\xff\xd8"!=e.slice(0,2))throw Error("Given data isn't JPEG.");for(var t=2,i=["\xff\xd8"];;){if("\xff\xda"==e.slice(t,t+2)){i.push(e.slice(t));break}var n=u(">H",e.slice(t+2,t+4))[0],a=t+n+2;if(i.push(e.slice(t,a)),(t=a)>=e.length)throw Error("Wrong JPEG data.")}return i}var f={Byte:1,Ascii:2,Short:3,Long:4,Rational:5,Undefined:7,SLong:9,SRational:10},m={Image:{11:{name:"ProcessingSoftware",type:"Ascii"},254:{name:"NewSubfileType",type:"Long"},255:{name:"SubfileType",type:"Short"},256:{name:"ImageWidth",type:"Long"},257:{name:"ImageLength",type:"Long"},258:{name:"BitsPerSample",type:"Short"},259:{name:"Compression",type:"Short"},262:{name:"PhotometricInterpretation",type:"Short"},263:{name:"Threshholding",type:"Short"},264:{name:"CellWidth",type:"Short"},265:{name:"CellLength",type:"Short"},266:{name:"FillOrder",type:"Short"},269:{name:"DocumentName",type:"Ascii"},270:{name:"ImageDescription",type:"Ascii"},271:{name:"Make",type:"Ascii"},272:{name:"Model",type:"Ascii"},273:{name:"StripOffsets",type:"Long"},274:{name:"Orientation",type:"Short"},277:{name:"SamplesPerPixel",type:"Short"},278:{name:"RowsPerStrip",type:"Long"},279:{name:"StripByteCounts",type:"Long"},282:{name:"XResolution",type:"Rational"},283:{name:"YResolution",type:"Rational"},284:{name:"PlanarConfiguration",type:"Short"},290:{name:"GrayResponseUnit",type:"Short"},291:{name:"GrayResponseCurve",type:"Short"},292:{name:"T4Options",type:"Long"},293:{name:"T6Options",type:"Long"},296:{name:"ResolutionUnit",type:"Short"},301:{name:"TransferFunction",type:"Short"},305:{name:"Software",type:"Ascii"},306:{name:"DateTime",type:"Ascii"},315:{name:"Artist",type:"Ascii"},316:{name:"HostComputer",type:"Ascii"},317:{name:"Predictor",type:"Short"},318:{name:"WhitePoint",type:"Rational"},319:{name:"PrimaryChromaticities",type:"Rational"},320:{name:"ColorMap",type:"Short"},321:{name:"HalftoneHints",type:"Short"},322:{name:"TileWidth",type:"Short"},323:{name:"TileLength",type:"Short"},324:{name:"TileOffsets",type:"Short"},325:{name:"TileByteCounts",type:"Short"},330:{name:"SubIFDs",type:"Long"},332:{name:"InkSet",type:"Short"},333:{name:"InkNames",type:"Ascii"},334:{name:"NumberOfInks",type:"Short"},336:{name:"DotRange",type:"Byte"},337:{name:"TargetPrinter",type:"Ascii"},338:{name:"ExtraSamples",type:"Short"},339:{name:"SampleFormat",type:"Short"},340:{name:"SMinSampleValue",type:"Short"},341:{name:"SMaxSampleValue",type:"Short"},342:{name:"TransferRange",type:"Short"},343:{name:"ClipPath",type:"Byte"},344:{name:"XClipPathUnits",type:"Long"},345:{name:"YClipPathUnits",type:"Long"},346:{name:"Indexed",type:"Short"},347:{name:"JPEGTables",type:"Undefined"},351:{name:"OPIProxy",type:"Short"},512:{name:"JPEGProc",type:"Long"},513:{name:"JPEGInterchangeFormat",type:"Long"},514:{name:"JPEGInterchangeFormatLength",type:"Long"},515:{name:"JPEGRestartInterval",type:"Short"},517:{name:"JPEGLosslessPredictors",type:"Short"},518:{name:"JPEGPointTransforms",type:"Short"},519:{name:"JPEGQTables",type:"Long"},520:{name:"JPEGDCTables",type:"Long"},521:{name:"JPEGACTables",type:"Long"},529:{name:"YCbCrCoefficients",type:"Rational"},530:{name:"YCbCrSubSampling",type:"Short"},531:{name:"YCbCrPositioning",type:"Short"},532:{name:"ReferenceBlackWhite",type:"Rational"},700:{name:"XMLPacket",type:"Byte"},18246:{name:"Rating",type:"Short"},18249:{name:"RatingPercent",type:"Short"},32781:{name:"ImageID",type:"Ascii"},33421:{name:"CFARepeatPatternDim",type:"Short"},33422:{name:"CFAPattern",type:"Byte"},33423:{name:"BatteryLevel",type:"Rational"},33432:{name:"Copyright",type:"Ascii"},33434:{name:"ExposureTime",type:"Rational"},34377:{name:"ImageResources",type:"Byte"},34665:{name:"ExifTag",type:"Long"},34675:{name:"InterColorProfile",type:"Undefined"},34853:{name:"GPSTag",type:"Long"},34857:{name:"Interlace",type:"Short"},34858:{name:"TimeZoneOffset",type:"Long"},34859:{name:"SelfTimerMode",type:"Short"},37387:{name:"FlashEnergy",type:"Rational"},37388:{name:"SpatialFrequencyResponse",type:"Undefined"},37389:{name:"Noise",type:"Undefined"},37390:{name:"FocalPlaneXResolution",type:"Rational"},37391:{name:"FocalPlaneYResolution",type:"Rational"},37392:{name:"FocalPlaneResolutionUnit",type:"Short"},37393:{name:"ImageNumber",type:"Long"},37394:{name:"SecurityClassification",type:"Ascii"},37395:{name:"ImageHistory",type:"Ascii"},37397:{name:"ExposureIndex",type:"Rational"},37398:{name:"TIFFEPStandardID",type:"Byte"},37399:{name:"SensingMethod",type:"Short"},40091:{name:"XPTitle",type:"Byte"},40092:{name:"XPComment",type:"Byte"},40093:{name:"XPAuthor",type:"Byte"},40094:{name:"XPKeywords",type:"Byte"},40095:{name:"XPSubject",type:"Byte"},50341:{name:"PrintImageMatching",type:"Undefined"},50706:{name:"DNGVersion",type:"Byte"},50707:{name:"DNGBackwardVersion",type:"Byte"},50708:{name:"UniqueCameraModel",type:"Ascii"},50709:{name:"LocalizedCameraModel",type:"Byte"},50710:{name:"CFAPlaneColor",type:"Byte"},50711:{name:"CFALayout",type:"Short"},50712:{name:"LinearizationTable",type:"Short"},50713:{name:"BlackLevelRepeatDim",type:"Short"},50714:{name:"BlackLevel",type:"Rational"},50715:{name:"BlackLevelDeltaH",type:"SRational"},50716:{name:"BlackLevelDeltaV",type:"SRational"},50717:{name:"WhiteLevel",type:"Short"},50718:{name:"DefaultScale",type:"Rational"},50719:{name:"DefaultCropOrigin",type:"Short"},50720:{name:"DefaultCropSize",type:"Short"},50721:{name:"ColorMatrix1",type:"SRational"},50722:{name:"ColorMatrix2",type:"SRational"},50723:{name:"CameraCalibration1",type:"SRational"},50724:{name:"CameraCalibration2",type:"SRational"},50725:{name:"ReductionMatrix1",type:"SRational"},50726:{name:"ReductionMatrix2",type:"SRational"},50727:{name:"AnalogBalance",type:"Rational"},50728:{name:"AsShotNeutral",type:"Short"},50729:{name:"AsShotWhiteXY",type:"Rational"},50730:{name:"BaselineExposure",type:"SRational"},50731:{name:"BaselineNoise",type:"Rational"},50732:{name:"BaselineSharpness",type:"Rational"},50733:{name:"BayerGreenSplit",type:"Long"},50734:{name:"LinearResponseLimit",type:"Rational"},50735:{name:"CameraSerialNumber",type:"Ascii"},50736:{name:"LensInfo",type:"Rational"},50737:{name:"ChromaBlurRadius",type:"Rational"},50738:{name:"AntiAliasStrength",type:"Rational"},50739:{name:"ShadowScale",type:"SRational"},50740:{name:"DNGPrivateData",type:"Byte"},50741:{name:"MakerNoteSafety",type:"Short"},50778:{name:"CalibrationIlluminant1",type:"Short"},50779:{name:"CalibrationIlluminant2",type:"Short"},50780:{name:"BestQualityScale",type:"Rational"},50781:{name:"RawDataUniqueID",type:"Byte"},50827:{name:"OriginalRawFileName",type:"Byte"},50828:{name:"OriginalRawFileData",type:"Undefined"},50829:{name:"ActiveArea",type:"Short"},50830:{name:"MaskedAreas",type:"Short"},50831:{name:"AsShotICCProfile",type:"Undefined"},50832:{name:"AsShotPreProfileMatrix",type:"SRational"},50833:{name:"CurrentICCProfile",type:"Undefined"},50834:{name:"CurrentPreProfileMatrix",type:"SRational"},50879:{name:"ColorimetricReference",type:"Short"},50931:{name:"CameraCalibrationSignature",type:"Byte"},50932:{name:"ProfileCalibrationSignature",type:"Byte"},50934:{name:"AsShotProfileName",type:"Byte"},50935:{name:"NoiseReductionApplied",type:"Rational"},50936:{name:"ProfileName",type:"Byte"},50937:{name:"ProfileHueSatMapDims",type:"Long"},50938:{name:"ProfileHueSatMapData1",type:"Float"},50939:{name:"ProfileHueSatMapData2",type:"Float"},50940:{name:"ProfileToneCurve",type:"Float"},50941:{name:"ProfileEmbedPolicy",type:"Long"},50942:{name:"ProfileCopyright",type:"Byte"},50964:{name:"ForwardMatrix1",type:"SRational"},50965:{name:"ForwardMatrix2",type:"SRational"},50966:{name:"PreviewApplicationName",type:"Byte"},50967:{name:"PreviewApplicationVersion",type:"Byte"},50968:{name:"PreviewSettingsName",type:"Byte"},50969:{name:"PreviewSettingsDigest",type:"Byte"},50970:{name:"PreviewColorSpace",type:"Long"},50971:{name:"PreviewDateTime",type:"Ascii"},50972:{name:"RawImageDigest",type:"Undefined"},50973:{name:"OriginalRawFileDigest",type:"Undefined"},50974:{name:"SubTileBlockSize",type:"Long"},50975:{name:"RowInterleaveFactor",type:"Long"},50981:{name:"ProfileLookTableDims",type:"Long"},50982:{name:"ProfileLookTableData",type:"Float"},51008:{name:"OpcodeList1",type:"Undefined"},51009:{name:"OpcodeList2",type:"Undefined"},51022:{name:"OpcodeList3",type:"Undefined"}},Exif:{33434:{name:"ExposureTime",type:"Rational"},33437:{name:"FNumber",type:"Rational"},34850:{name:"ExposureProgram",type:"Short"},34852:{name:"SpectralSensitivity",type:"Ascii"},34855:{name:"ISOSpeedRatings",type:"Short"},34856:{name:"OECF",type:"Undefined"},34864:{name:"SensitivityType",type:"Short"},34865:{name:"StandardOutputSensitivity",type:"Long"},34866:{name:"RecommendedExposureIndex",type:"Long"},34867:{name:"ISOSpeed",type:"Long"},34868:{name:"ISOSpeedLatitudeyyy",type:"Long"},34869:{name:"ISOSpeedLatitudezzz",type:"Long"},36864:{name:"ExifVersion",type:"Undefined"},36867:{name:"DateTimeOriginal",type:"Ascii"},36868:{name:"DateTimeDigitized",type:"Ascii"},37121:{name:"ComponentsConfiguration",type:"Undefined"},37122:{name:"CompressedBitsPerPixel",type:"Rational"},37377:{name:"ShutterSpeedValue",type:"SRational"},37378:{name:"ApertureValue",type:"Rational"},37379:{name:"BrightnessValue",type:"SRational"},37380:{name:"ExposureBiasValue",type:"SRational"},37381:{name:"MaxApertureValue",type:"Rational"},37382:{name:"SubjectDistance",type:"Rational"},37383:{name:"MeteringMode",type:"Short"},37384:{name:"LightSource",type:"Short"},37385:{name:"Flash",type:"Short"},37386:{name:"FocalLength",type:"Rational"},37396:{name:"SubjectArea",type:"Short"},37500:{name:"MakerNote",type:"Undefined"},37510:{name:"UserComment",type:"Ascii"},37520:{name:"SubSecTime",type:"Ascii"},37521:{name:"SubSecTimeOriginal",type:"Ascii"},37522:{name:"SubSecTimeDigitized",type:"Ascii"},40960:{name:"FlashpixVersion",type:"Undefined"},40961:{name:"ColorSpace",type:"Short"},40962:{name:"PixelXDimension",type:"Long"},40963:{name:"PixelYDimension",type:"Long"},40964:{name:"RelatedSoundFile",type:"Ascii"},40965:{name:"InteroperabilityTag",type:"Long"},41483:{name:"FlashEnergy",type:"Rational"},41484:{name:"SpatialFrequencyResponse",type:"Undefined"},41486:{name:"FocalPlaneXResolution",type:"Rational"},41487:{name:"FocalPlaneYResolution",type:"Rational"},41488:{name:"FocalPlaneResolutionUnit",type:"Short"},41492:{name:"SubjectLocation",type:"Short"},41493:{name:"ExposureIndex",type:"Rational"},41495:{name:"SensingMethod",type:"Short"},41728:{name:"FileSource",type:"Undefined"},41729:{name:"SceneType",type:"Undefined"},41730:{name:"CFAPattern",type:"Undefined"},41985:{name:"CustomRendered",type:"Short"},41986:{name:"ExposureMode",type:"Short"},41987:{name:"WhiteBalance",type:"Short"},41988:{name:"DigitalZoomRatio",type:"Rational"},41989:{name:"FocalLengthIn35mmFilm",type:"Short"},41990:{name:"SceneCaptureType",type:"Short"},41991:{name:"GainControl",type:"Short"},41992:{name:"Contrast",type:"Short"},41993:{name:"Saturation",type:"Short"},41994:{name:"Sharpness",type:"Short"},41995:{name:"DeviceSettingDescription",type:"Undefined"},41996:{name:"SubjectDistanceRange",type:"Short"},42016:{name:"ImageUniqueID",type:"Ascii"},42032:{name:"CameraOwnerName",type:"Ascii"},42033:{name:"BodySerialNumber",type:"Ascii"},42034:{name:"LensSpecification",type:"Rational"},42035:{name:"LensMake",type:"Ascii"},42036:{name:"LensModel",type:"Ascii"},42037:{name:"LensSerialNumber",type:"Ascii"},42240:{name:"Gamma",type:"Rational"}},GPS:{0:{name:"GPSVersionID",type:"Byte"},1:{name:"GPSLatitudeRef",type:"Ascii"},2:{name:"GPSLatitude",type:"Rational"},3:{name:"GPSLongitudeRef",type:"Ascii"},4:{name:"GPSLongitude",type:"Rational"},5:{name:"GPSAltitudeRef",type:"Byte"},6:{name:"GPSAltitude",type:"Rational"},7:{name:"GPSTimeStamp",type:"Rational"},8:{name:"GPSSatellites",type:"Ascii"},9:{name:"GPSStatus",type:"Ascii"},10:{name:"GPSMeasureMode",type:"Ascii"},11:{name:"GPSDOP",type:"Rational"},12:{name:"GPSSpeedRef",type:"Ascii"},13:{name:"GPSSpeed",type:"Rational"},14:{name:"GPSTrackRef",type:"Ascii"},15:{name:"GPSTrack",type:"Rational"},16:{name:"GPSImgDirectionRef",type:"Ascii"},17:{name:"GPSImgDirection",type:"Rational"},18:{name:"GPSMapDatum",type:"Ascii"},19:{name:"GPSDestLatitudeRef",type:"Ascii"},20:{name:"GPSDestLatitude",type:"Rational"},21:{name:"GPSDestLongitudeRef",type:"Ascii"},22:{name:"GPSDestLongitude",type:"Rational"},23:{name:"GPSDestBearingRef",type:"Ascii"},24:{name:"GPSDestBearing",type:"Rational"},25:{name:"GPSDestDistanceRef",type:"Ascii"},26:{name:"GPSDestDistance",type:"Rational"},27:{name:"GPSProcessingMethod",type:"Undefined"},28:{name:"GPSAreaInformation",type:"Undefined"},29:{name:"GPSDateStamp",type:"Ascii"},30:{name:"GPSDifferential",type:"Short"},31:{name:"GPSHPositioningError",type:"Rational"}},Interop:{1:{name:"InteroperabilityIndex",type:"Ascii"}}};m["0th"]=m.Image,m["1st"]=m.Image,i.TAGS=m,i.ImageIFD={ProcessingSoftware:11,NewSubfileType:254,SubfileType:255,ImageWidth:256,ImageLength:257,BitsPerSample:258,Compression:259,PhotometricInterpretation:262,Threshholding:263,CellWidth:264,CellLength:265,FillOrder:266,DocumentName:269,ImageDescription:270,Make:271,Model:272,StripOffsets:273,Orientation:274,SamplesPerPixel:277,RowsPerStrip:278,StripByteCounts:279,XResolution:282,YResolution:283,PlanarConfiguration:284,GrayResponseUnit:290,GrayResponseCurve:291,T4Options:292,T6Options:293,ResolutionUnit:296,TransferFunction:301,Software:305,DateTime:306,Artist:315,HostComputer:316,Predictor:317,WhitePoint:318,PrimaryChromaticities:319,ColorMap:320,HalftoneHints:321,TileWidth:322,TileLength:323,TileOffsets:324,TileByteCounts:325,SubIFDs:330,InkSet:332,InkNames:333,NumberOfInks:334,DotRange:336,TargetPrinter:337,ExtraSamples:338,SampleFormat:339,SMinSampleValue:340,SMaxSampleValue:341,TransferRange:342,ClipPath:343,XClipPathUnits:344,YClipPathUnits:345,Indexed:346,JPEGTables:347,OPIProxy:351,JPEGProc:512,JPEGInterchangeFormat:513,JPEGInterchangeFormatLength:514,JPEGRestartInterval:515,JPEGLosslessPredictors:517,JPEGPointTransforms:518,JPEGQTables:519,JPEGDCTables:520,JPEGACTables:521,YCbCrCoefficients:529,YCbCrSubSampling:530,YCbCrPositioning:531,ReferenceBlackWhite:532,XMLPacket:700,Rating:18246,RatingPercent:18249,ImageID:32781,CFARepeatPatternDim:33421,CFAPattern:33422,BatteryLevel:33423,Copyright:33432,ExposureTime:33434,ImageResources:34377,ExifTag:34665,InterColorProfile:34675,GPSTag:34853,Interlace:34857,TimeZoneOffset:34858,SelfTimerMode:34859,FlashEnergy:37387,SpatialFrequencyResponse:37388,Noise:37389,FocalPlaneXResolution:37390,FocalPlaneYResolution:37391,FocalPlaneResolutionUnit:37392,ImageNumber:37393,SecurityClassification:37394,ImageHistory:37395,ExposureIndex:37397,TIFFEPStandardID:37398,SensingMethod:37399,XPTitle:40091,XPComment:40092,XPAuthor:40093,XPKeywords:40094,XPSubject:40095,PrintImageMatching:50341,DNGVersion:50706,DNGBackwardVersion:50707,UniqueCameraModel:50708,LocalizedCameraModel:50709,CFAPlaneColor:50710,CFALayout:50711,LinearizationTable:50712,BlackLevelRepeatDim:50713,BlackLevel:50714,BlackLevelDeltaH:50715,BlackLevelDeltaV:50716,WhiteLevel:50717,DefaultScale:50718,DefaultCropOrigin:50719,DefaultCropSize:50720,ColorMatrix1:50721,ColorMatrix2:50722,CameraCalibration1:50723,CameraCalibration2:50724,ReductionMatrix1:50725,ReductionMatrix2:50726,AnalogBalance:50727,AsShotNeutral:50728,AsShotWhiteXY:50729,BaselineExposure:50730,BaselineNoise:50731,BaselineSharpness:50732,BayerGreenSplit:50733,LinearResponseLimit:50734,CameraSerialNumber:50735,LensInfo:50736,ChromaBlurRadius:50737,AntiAliasStrength:50738,ShadowScale:50739,DNGPrivateData:50740,MakerNoteSafety:50741,CalibrationIlluminant1:50778,CalibrationIlluminant2:50779,BestQualityScale:50780,RawDataUniqueID:50781,OriginalRawFileName:50827,OriginalRawFileData:50828,ActiveArea:50829,MaskedAreas:50830,AsShotICCProfile:50831,AsShotPreProfileMatrix:50832,CurrentICCProfile:50833,CurrentPreProfileMatrix:50834,ColorimetricReference:50879,CameraCalibrationSignature:50931,ProfileCalibrationSignature:50932,AsShotProfileName:50934,NoiseReductionApplied:50935,ProfileName:50936,ProfileHueSatMapDims:50937,ProfileHueSatMapData1:50938,ProfileHueSatMapData2:50939,ProfileToneCurve:50940,ProfileEmbedPolicy:50941,ProfileCopyright:50942,ForwardMatrix1:50964,ForwardMatrix2:50965,PreviewApplicationName:50966,PreviewApplicationVersion:50967,PreviewSettingsName:50968,PreviewSettingsDigest:50969,PreviewColorSpace:50970,PreviewDateTime:50971,RawImageDigest:50972,OriginalRawFileDigest:50973,SubTileBlockSize:50974,RowInterleaveFactor:50975,ProfileLookTableDims:50981,ProfileLookTableData:50982,OpcodeList1:51008,OpcodeList2:51009,OpcodeList3:51022,NoiseProfile:51041},i.ExifIFD={ExposureTime:33434,FNumber:33437,ExposureProgram:34850,SpectralSensitivity:34852,ISOSpeedRatings:34855,OECF:34856,SensitivityType:34864,StandardOutputSensitivity:34865,RecommendedExposureIndex:34866,ISOSpeed:34867,ISOSpeedLatitudeyyy:34868,ISOSpeedLatitudezzz:34869,ExifVersion:36864,DateTimeOriginal:36867,DateTimeDigitized:36868,ComponentsConfiguration:37121,CompressedBitsPerPixel:37122,ShutterSpeedValue:37377,ApertureValue:37378,BrightnessValue:37379,ExposureBiasValue:37380,MaxApertureValue:37381,SubjectDistance:37382,MeteringMode:37383,LightSource:37384,Flash:37385,FocalLength:37386,SubjectArea:37396,MakerNote:37500,UserComment:37510,SubSecTime:37520,SubSecTimeOriginal:37521,SubSecTimeDigitized:37522,FlashpixVersion:40960,ColorSpace:40961,PixelXDimension:40962,PixelYDimension:40963,RelatedSoundFile:40964,InteroperabilityTag:40965,FlashEnergy:41483,SpatialFrequencyResponse:41484,FocalPlaneXResolution:41486,FocalPlaneYResolution:41487,FocalPlaneResolutionUnit:41488,SubjectLocation:41492,ExposureIndex:41493,SensingMethod:41495,FileSource:41728,SceneType:41729,CFAPattern:41730,CustomRendered:41985,ExposureMode:41986,WhiteBalance:41987,DigitalZoomRatio:41988,FocalLengthIn35mmFilm:41989,SceneCaptureType:41990,GainControl:41991,Contrast:41992,Saturation:41993,Sharpness:41994,DeviceSettingDescription:41995,SubjectDistanceRange:41996,ImageUniqueID:42016,CameraOwnerName:42032,BodySerialNumber:42033,LensSpecification:42034,LensMake:42035,LensModel:42036,LensSerialNumber:42037,Gamma:42240},i.GPSIFD={GPSVersionID:0,GPSLatitudeRef:1,GPSLatitude:2,GPSLongitudeRef:3,GPSLongitude:4,GPSAltitudeRef:5,GPSAltitude:6,GPSTimeStamp:7,GPSSatellites:8,GPSStatus:9,GPSMeasureMode:10,GPSDOP:11,GPSSpeedRef:12,GPSSpeed:13,GPSTrackRef:14,GPSTrack:15,GPSImgDirectionRef:16,GPSImgDirection:17,GPSMapDatum:18,GPSDestLatitudeRef:19,GPSDestLatitude:20,GPSDestLongitudeRef:21,GPSDestLongitude:22,GPSDestBearingRef:23,GPSDestBearing:24,GPSDestDistanceRef:25,GPSDestDistance:26,GPSProcessingMethod:27,GPSAreaInformation:28,GPSDateStamp:29,GPSDifferential:30,GPSHPositioningError:31},i.InteropIFD={InteroperabilityIndex:1},i.GPSHelper={degToDmsRational:function(e){var t=Math.abs(e),i=t%1*60;return[[Math.floor(t),1],[Math.floor(i),1],[Math.round(i%1*6e3),100]]},dmsRationalToDeg:function(e,t){return(e[0][0]/e[0][1]+e[1][0]/e[1][1]/60+e[2][0]/e[2][1]/3600)*("S"===t||"W"===t?-1:1)}},e.exports&&(t=e.exports=i),t.piexif=i}()},68086:(e,t,i)=>{"use strict";i.d(t,{NZ:()=>ey});var n=i(49509),a=i(47131).hp,r="undefined"!=typeof self?self:global;let s="undefined"!=typeof navigator,o=s&&"undefined"==typeof HTMLImageElement,l=!("undefined"==typeof global||void 0===n||!n.versions||!n.versions.node),h=r.Buffer,c=r.BigInt,u=!!h,d=e=>e;function p(e,t=d){if(l)try{return"function"==typeof require?Promise.resolve(t(require(e))):import(e).then(t)}catch(t){console.warn(`Couldn't load ${e}`)}}let f=r.fetch;if(!r.fetch){let e=p("http",e=>e),t=p("https",e=>e),i=(n,{headers:r}={})=>new Promise(async(s,o)=>{let{port:l,hostname:h,pathname:c,protocol:u,search:d}=new URL(n),p={method:"GET",hostname:h,path:encodeURI(c)+d,headers:r};""!==l&&(p.port=Number(l));let f=("https:"===u?await t:await e).request(p,e=>{if(301===e.statusCode||302===e.statusCode)return i(new URL(e.headers.location,n).toString(),{headers:r}).then(s).catch(o);s({status:e.statusCode,arrayBuffer:()=>new Promise(t=>{let i=[];e.on("data",e=>i.push(e)),e.on("end",()=>t(a.concat(i)))})})});f.on("error",o),f.end()});f=i}function m(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}let g=e=>S(e)?void 0:e,y=e=>void 0!==e;function S(e){return void 0===e||(e instanceof Map?0===e.size:0===Object.values(e).filter(y).length)}function C(e){let t=Error(e);throw delete t.stack,t}function P(e){return""===(e=(function(e){for(;e.endsWith("\0");)e=e.slice(0,-1);return e})(e).trim())?void 0:e}function b(e){let t,i=(t=0,e.ifd0.enabled&&(t+=1024),e.exif.enabled&&(t+=2048),e.makerNote&&(t+=2048),e.userComment&&(t+=1024),e.gps.enabled&&(t+=512),e.interop.enabled&&(t+=100),e.ifd1.enabled&&(t+=1024),t+2048);return e.jfif.enabled&&(i+=50),e.xmp.enabled&&(i+=2e4),e.iptc.enabled&&(i+=14e3),e.icc.enabled&&(i+=6e3),i}let w=e=>String.fromCharCode.apply(null,e),I="undefined"!=typeof TextDecoder?new TextDecoder("utf-8"):void 0;function k(e){return I?I.decode(e):u?a.from(e).toString("utf8"):decodeURIComponent(escape(w(e)))}class L{static from(e,t){return e instanceof this&&e.le===t?e:new L(e,void 0,void 0,t)}constructor(e,t=0,i,n){if("boolean"==typeof n&&(this.le=n),Array.isArray(e)&&(e=new Uint8Array(e)),0===e)this.byteOffset=0,this.byteLength=0;else if(e instanceof ArrayBuffer){void 0===i&&(i=e.byteLength-t);let n=new DataView(e,t,i);this._swapDataView(n)}else if(e instanceof Uint8Array||e instanceof DataView||e instanceof L){void 0===i&&(i=e.byteLength-t),(t+=e.byteOffset)+i>e.byteOffset+e.byteLength&&C("Creating view outside of available memory in ArrayBuffer");let n=new DataView(e.buffer,t,i);this._swapDataView(n)}else if("number"==typeof e){let t=new DataView(new ArrayBuffer(e));this._swapDataView(t)}else C("Invalid input argument for BufferView: "+e)}_swapArrayBuffer(e){this._swapDataView(new DataView(e))}_swapBuffer(e){this._swapDataView(new DataView(e.buffer,e.byteOffset,e.byteLength))}_swapDataView(e){this.dataView=e,this.buffer=e.buffer,this.byteOffset=e.byteOffset,this.byteLength=e.byteLength}_lengthToEnd(e){return this.byteLength-e}set(e,t,i=L){return e instanceof DataView||e instanceof L?e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e instanceof Uint8Array||C("BufferView.set(): Invalid data argument."),this.toUint8().set(e,t),new i(this,t,e.byteLength)}subarray(e,t){return t=t||this._lengthToEnd(e),new L(this,e,t)}toUint8(){return new Uint8Array(this.buffer,this.byteOffset,this.byteLength)}getUint8Array(e,t){return new Uint8Array(this.buffer,this.byteOffset+e,t)}getString(e=0,t=this.byteLength){return k(this.getUint8Array(e,t))}getLatin1String(e=0,t=this.byteLength){return w(this.getUint8Array(e,t))}getUnicodeString(e=0,t=this.byteLength){let i=[];for(let n=0;n<t&&e+n<this.byteLength;n+=2)i.push(this.getUint16(e+n));return w(i)}getInt8(e){return this.dataView.getInt8(e)}getUint8(e){return this.dataView.getUint8(e)}getInt16(e,t=this.le){return this.dataView.getInt16(e,t)}getInt32(e,t=this.le){return this.dataView.getInt32(e,t)}getUint16(e,t=this.le){return this.dataView.getUint16(e,t)}getUint32(e,t=this.le){return this.dataView.getUint32(e,t)}getFloat32(e,t=this.le){return this.dataView.getFloat32(e,t)}getFloat64(e,t=this.le){return this.dataView.getFloat64(e,t)}getFloat(e,t=this.le){return this.dataView.getFloat32(e,t)}getDouble(e,t=this.le){return this.dataView.getFloat64(e,t)}getUintBytes(e,t,i){switch(t){case 1:return this.getUint8(e,i);case 2:return this.getUint16(e,i);case 4:return this.getUint32(e,i);case 8:return this.getUint64&&this.getUint64(e,i)}}getUint(e,t,i){switch(t){case 8:return this.getUint8(e,i);case 16:return this.getUint16(e,i);case 32:return this.getUint32(e,i);case 64:return this.getUint64&&this.getUint64(e,i)}}toString(e){return this.dataView.toString(e,this.constructor.name)}ensureChunk(){}}function v(e,t){C(`${e} '${t}' was not loaded, try using full build of exifr.`)}class A extends Map{constructor(e){super(),this.kind=e}get(e,t){var i;return this.has(e)||v(this.kind,e),t&&(e in t||(i=this.kind,C(`Unknown ${i} '${e}'.`)),t[e].enabled||v(this.kind,e)),super.get(e)}keyList(){return Array.from(this.keys())}}var T=new A("file parser"),D=new A("segment parser"),R=new A("file reader");function x(e,t){return e.startsWith("data:")||e.length>1e4?E(e,t,"base64"):l&&e.includes("://")?O(e,t,"url",M):l?E(e,t,"fs"):s?O(e,t,"url",M):void C("Invalid input argument")}async function O(e,t,i,n){return R.has(i)?E(e,t,i):n?async function(e,t){return new L(await t(e))}(e,n):void C(`Parser ${i} is not loaded`)}async function E(e,t,i){let n=new(R.get(i))(e,t);return await n.read(),n}let M=e=>f(e).then(e=>e.arrayBuffer()),F=e=>new Promise((t,i)=>{let n=new FileReader;n.onloadend=()=>t(n.result||new ArrayBuffer),n.onerror=i,n.readAsArrayBuffer(e)});class U extends Map{get tagKeys(){return this.allKeys||(this.allKeys=Array.from(this.keys())),this.allKeys}get tagValues(){return this.allValues||(this.allValues=Array.from(this.values())),this.allValues}}function B(e,t,i){let n=new U;for(let[e,t]of i)n.set(e,t);if(Array.isArray(t))for(let i of t)e.set(i,n);else e.set(t,n);return n}function G(e,t,i){let n,a=e.get(t);for(n of i)a.set(n[0],n[1])}let N=new Map,j=new Map,V=new Map,H=["chunked","firstChunkSize","firstChunkSizeNode","firstChunkSizeBrowser","chunkSize","chunkLimit"],z=["jfif","xmp","icc","iptc","ihdr"],_=["tiff",...z],W=["ifd0","ifd1","exif","gps","interop"],X=[..._,...W],K=["makerNote","userComment"],J=["translateKeys","translateValues","reviveValues","multiSegment"],$=[...J,"sanitize","mergeOutput","silentErrors"];class Y{get translate(){return this.translateKeys||this.translateValues||this.reviveValues}}class q extends Y{get needed(){return this.enabled||this.deps.size>0}constructor(e,t,i,n){if(super(),m(this,"enabled",!1),m(this,"skip",new Set),m(this,"pick",new Set),m(this,"deps",new Set),m(this,"translateKeys",!1),m(this,"translateValues",!1),m(this,"reviveValues",!1),this.key=e,this.enabled=t,this.parse=this.enabled,this.applyInheritables(n),this.canBeFiltered=W.includes(e),this.canBeFiltered&&(this.dict=N.get(e)),void 0!==i)if(Array.isArray(i))this.parse=this.enabled=!0,this.canBeFiltered&&i.length>0&&this.translateTagSet(i,this.pick);else if("object"==typeof i){if(this.enabled=!0,this.parse=!1!==i.parse,this.canBeFiltered){let{pick:e,skip:t}=i;e&&e.length>0&&this.translateTagSet(e,this.pick),t&&t.length>0&&this.translateTagSet(t,this.skip)}this.applyInheritables(i)}else!0===i||!1===i?this.parse=this.enabled=i:C(`Invalid options argument: ${i}`)}applyInheritables(e){let t,i;for(t of J)void 0!==(i=e[t])&&(this[t]=i)}translateTagSet(e,t){if(this.dict){let i,n,{tagKeys:a,tagValues:r}=this.dict;for(i of e)"string"==typeof i?(-1===(n=r.indexOf(i))&&(n=a.indexOf(Number(i))),-1!==n&&t.add(Number(a[n]))):t.add(i)}else for(let i of e)t.add(i)}finalizeFilters(){!this.enabled&&this.deps.size>0?(this.enabled=!0,en(this.pick,this.deps)):this.enabled&&this.pick.size>0&&en(this.pick,this.deps)}}var Q={jfif:!1,tiff:!0,xmp:!1,icc:!1,iptc:!1,ifd0:!0,ifd1:!1,exif:!0,gps:!0,interop:!1,ihdr:void 0,makerNote:!1,userComment:!1,multiSegment:!1,skip:[],pick:[],translateKeys:!0,translateValues:!0,reviveValues:!0,sanitize:!0,mergeOutput:!0,silentErrors:!0,chunked:!0,firstChunkSize:void 0,firstChunkSizeNode:512,firstChunkSizeBrowser:65536,chunkSize:65536,chunkLimit:5},Z=new Map;class ee extends Y{static useCached(e){let t=Z.get(e);return void 0!==t||(t=new this(e),Z.set(e,t)),t}constructor(e){super(),!0===e?this.setupFromTrue():void 0===e?this.setupFromUndefined():Array.isArray(e)?this.setupFromArray(e):"object"==typeof e?this.setupFromObject(e):C(`Invalid options argument ${e}`),void 0===this.firstChunkSize&&(this.firstChunkSize=s?this.firstChunkSizeBrowser:this.firstChunkSizeNode),this.mergeOutput&&(this.ifd1.enabled=!1),this.filterNestedSegmentTags(),this.traverseTiffDependencyTree(),this.checkLoadedPlugins()}setupFromUndefined(){let e;for(e of H)this[e]=Q[e];for(e of $)this[e]=Q[e];for(e of K)this[e]=Q[e];for(e of X)this[e]=new q(e,Q[e],void 0,this)}setupFromTrue(){let e;for(e of H)this[e]=Q[e];for(e of $)this[e]=Q[e];for(e of K)this[e]=!0;for(e of X)this[e]=new q(e,!0,void 0,this)}setupFromArray(e){let t;for(t of H)this[t]=Q[t];for(t of $)this[t]=Q[t];for(t of K)this[t]=Q[t];for(t of X)this[t]=new q(t,!1,void 0,this);this.setupGlobalFilters(e,void 0,W)}setupFromObject(e){let t;for(t of(W.ifd0=W.ifd0||W.image,W.ifd1=W.ifd1||W.thumbnail,Object.assign(this,e),H))this[t]=ei(e[t],Q[t]);for(t of $)this[t]=ei(e[t],Q[t]);for(t of K)this[t]=ei(e[t],Q[t]);for(t of _)this[t]=new q(t,Q[t],e[t],this);for(t of W)this[t]=new q(t,Q[t],e[t],this.tiff);this.setupGlobalFilters(e.pick,e.skip,W,X),!0===e.tiff?this.batchEnableWithBool(W,!0):!1===e.tiff?this.batchEnableWithUserValue(W,e):Array.isArray(e.tiff)?this.setupGlobalFilters(e.tiff,void 0,W):"object"==typeof e.tiff&&this.setupGlobalFilters(e.tiff.pick,e.tiff.skip,W)}batchEnableWithBool(e,t){for(let i of e)this[i].enabled=t}batchEnableWithUserValue(e,t){for(let i of e){let e=t[i];this[i].enabled=!1!==e&&void 0!==e}}setupGlobalFilters(e,t,i,n=i){if(e&&e.length){for(let e of n)this[e].enabled=!1;for(let[t,n]of et(e,i))en(this[t].pick,n),this[t].enabled=!0}else if(t&&t.length)for(let[e,n]of et(t,i))en(this[e].skip,n)}filterNestedSegmentTags(){let{ifd0:e,exif:t,xmp:i,iptc:n,icc:a}=this;this.makerNote?t.deps.add(37500):t.skip.add(37500),this.userComment?t.deps.add(37510):t.skip.add(37510),i.enabled||e.skip.add(700),n.enabled||e.skip.add(33723),a.enabled||e.skip.add(34675)}traverseTiffDependencyTree(){let{ifd0:e,exif:t,gps:i,interop:n}=this;for(let a of(n.needed&&(t.deps.add(40965),e.deps.add(40965)),t.needed&&e.deps.add(34665),i.needed&&e.deps.add(34853),this.tiff.enabled=W.some(e=>!0===this[e].enabled)||this.makerNote||this.userComment,W))this[a].finalizeFilters()}get onlyTiff(){return!z.map(e=>this[e].enabled).some(e=>!0===e)&&this.tiff.enabled}checkLoadedPlugins(){for(let e of _)this[e].enabled&&!D.has(e)&&v("segment parser",e)}}function et(e,t){let i,n,a,r,s=[];for(a of t){for(r of(i=N.get(a),n=[],i))(e.includes(r[0])||e.includes(r[1]))&&n.push(r[0]);n.length&&s.push([a,n])}return s}function ei(e,t){return void 0!==e?e:void 0!==t?t:void 0}function en(e,t){for(let i of t)e.add(i)}m(ee,"default",Q);class ea{constructor(e){m(this,"parsers",{}),m(this,"output",{}),m(this,"errors",[]),m(this,"pushToErrors",e=>this.errors.push(e)),this.options=ee.useCached(e)}async read(e){var t,i;this.file=await (t=e,i=this.options,"string"==typeof t?x(t,i):s&&!o&&t instanceof HTMLImageElement?x(t.src,i):t instanceof Uint8Array||t instanceof ArrayBuffer||t instanceof DataView?new L(t):s&&t instanceof Blob?O(t,i,"blob",F):void C("Invalid input argument"))}setup(){if(this.fileParser)return;let{file:e}=this,t=e.getUint16(0);for(let[i,n]of T)if(n.canHandle(e,t))return this.fileParser=new n(this.options,this.file,this.parsers),e[i]=!0;this.file.close&&this.file.close(),C("Unknown file format")}async parse(){let{output:e,errors:t}=this;return this.setup(),this.options.silentErrors?(await this.executeParsers().catch(this.pushToErrors),t.push(...this.fileParser.errors)):await this.executeParsers(),this.file.close&&this.file.close(),this.options.silentErrors&&t.length>0&&(e.errors=t),g(e)}async executeParsers(){let{output:e}=this;await this.fileParser.parse();let t=Object.values(this.parsers).map(async t=>{let i=await t.parse();t.assignToOutput(e,i)});this.options.silentErrors&&(t=t.map(e=>e.catch(this.pushToErrors))),await Promise.all(t)}async extractThumbnail(){var e;this.setup();let{options:t,file:i}=this,n=D.get("tiff",t);if(i.tiff?e={start:0,type:"tiff"}:i.jpeg&&(e=await this.fileParser.getOrFindSegment("tiff")),void 0===e)return;let a=await this.fileParser.ensureSegmentChunk(e),r=this.parsers.tiff=new n(a,t,i),s=await r.extractThumbnail();return i.close&&i.close(),s}}async function er(e,t){let i=new ea(t);return await i.read(e),i.parse()}var es=Object.freeze({__proto__:null,parse:er,Exifr:ea,fileParsers:T,segmentParsers:D,fileReaders:R,tagKeys:N,tagValues:j,tagRevivers:V,createDictionary:B,extendDictionary:G,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:F,chunkedProps:H,otherSegments:z,segments:_,tiffBlocks:W,segmentsAndBlocks:X,tiffExtractables:K,inheritables:J,allFormatters:$,Options:ee});class eo{constructor(e,t,i){m(this,"errors",[]),m(this,"ensureSegmentChunk",async e=>{let t=e.start,i=e.size||65536;if(this.file.chunked)if(this.file.available(t,i))e.chunk=this.file.subarray(t,i);else try{e.chunk=await this.file.readChunk(t,i)}catch(t){C(`Couldn't read segment: ${JSON.stringify(e)}. ${t.message}`)}else this.file.byteLength>t+i?e.chunk=this.file.subarray(t,i):void 0===e.size?e.chunk=this.file.subarray(t):C("Segment unreachable: "+JSON.stringify(e));return e.chunk}),this.extendOptions&&this.extendOptions(e),this.options=e,this.file=t,this.parsers=i}injectSegment(e,t){this.options[e].enabled&&this.createParser(e,t)}createParser(e,t){let i=new(D.get(e))(t,this.options,this.file);return this.parsers[e]=i}createParsers(e){for(let t of e){let{type:e,chunk:i}=t,n=this.options[e];if(n&&n.enabled){let t=this.parsers[e];t&&t.append||t||this.createParser(e,i)}}}async readSegments(e){let t=e.map(this.ensureSegmentChunk);await Promise.all(t)}}class el{static findPosition(e,t){let i=e.getUint16(t+2)+2,n="function"==typeof this.headerLength?this.headerLength(e,t,i):this.headerLength,a=t+n,r=i-n;return{offset:t,length:i,headerLength:n,start:a,size:r,end:a+r}}static parse(e,t={}){return new this(e,new ee({[this.type]:t}),e).parse()}normalizeInput(e){return e instanceof L?e:new L(e)}constructor(e,t={},i){m(this,"errors",[]),m(this,"raw",new Map),m(this,"handleError",e=>{if(!this.options.silentErrors)throw e;this.errors.push(e.message)}),this.chunk=this.normalizeInput(e),this.file=i,this.type=this.constructor.type,this.globalOptions=this.options=t,this.localOptions=t[this.type],this.canTranslate=this.localOptions&&this.localOptions.translate}translate(){this.canTranslate&&(this.translated=this.translateBlock(this.raw,this.type))}get output(){return this.translated?this.translated:this.raw?Object.fromEntries(this.raw):void 0}translateBlock(e,t){let i=V.get(t),n=j.get(t),a=N.get(t),r=this.options[t],s=r.reviveValues&&!!i,o=r.translateValues&&!!n,l=r.translateKeys&&!!a,h={};for(let[t,r]of e)s&&i.has(t)?r=i.get(t)(r):o&&n.has(t)&&(r=this.translateValue(r,n.get(t))),l&&a.has(t)&&(t=a.get(t)||t),h[t]=r;return h}translateValue(e,t){return t[e]||t.DEFAULT||e}assignToOutput(e,t){this.assignObjectToOutput(e,this.constructor.type,t)}assignObjectToOutput(e,t,i){if(this.globalOptions.mergeOutput)return Object.assign(e,i);e[t]?Object.assign(e[t],i):e[t]=i}}m(el,"headerLength",4),m(el,"type",void 0),m(el,"multiSegment",!1),m(el,"canHandle",()=>!1);class eh extends eo{constructor(...e){super(...e),m(this,"appSegments",[]),m(this,"jpegSegments",[]),m(this,"unknownSegments",[])}static canHandle(e,t){return 65496===t}async parse(){await this.findAppSegments(),await this.readSegments(this.appSegments),this.mergeMultiSegments(),this.createParsers(this.mergedAppSegments||this.appSegments)}setupSegmentFinderArgs(e){!0===e?(this.findAll=!0,this.wanted=new Set(D.keyList())):(e=void 0===e?D.keyList().filter(e=>this.options[e].enabled):e.filter(e=>this.options[e].enabled&&D.has(e)),this.findAll=!1,this.remaining=new Set(e),this.wanted=new Set(e)),this.unfinishedMultiSegment=!1}async findAppSegments(e=0,t){this.setupSegmentFinderArgs(t);let{file:i,findAll:n,wanted:a,remaining:r}=this;if(!n&&this.file.chunked&&(n=Array.from(a).some(e=>{let t=D.get(e),i=this.options[e];return t.multiSegment&&i.multiSegment}))&&await this.file.readWhole(),e=this.findAppSegmentsInRange(e,i.byteLength),!this.options.onlyTiff&&i.chunked){let t=!1;for(;r.size>0&&!t&&(i.canReadNextChunk||this.unfinishedMultiSegment);){let{nextChunkOffset:n}=i,a=this.appSegments.some(e=>!this.file.available(e.offset||e.start,e.length||e.size));if(t=e>n&&!a?!await i.readNextChunk(e):!await i.readNextChunk(n),void 0===(e=this.findAppSegmentsInRange(e,i.byteLength)))return}}}findAppSegmentsInRange(e,t){t-=2;let i,n,a,r,s,o,{file:l,findAll:h,wanted:c,remaining:u,options:d}=this;for(;e<t;e++)if(255===l.getUint8(e)){var p;if((p=i=l.getUint8(e+1))>=224&&p<=239){if(n=l.getUint16(e+2),(a=function(e,t,i){for(let[n,a]of D)if(a.canHandle(e,t,i))return n}(l,e,n))&&c.has(a)&&(s=(r=D.get(a)).findPosition(l,e),o=d[a],s.type=a,this.appSegments.push(s),!h&&(r.multiSegment&&o.multiSegment?(this.unfinishedMultiSegment=s.chunkNumber<s.chunkCount,this.unfinishedMultiSegment||u.delete(a)):u.delete(a),0===u.size)))break;d.recordUnknownSegments&&((s=el.findPosition(l,e)).marker=i,this.unknownSegments.push(s)),e+=n+1}else if(192===i||194===i||196===i||219===i||221===i||218===i||254===i){if(n=l.getUint16(e+2),218===i&&!1!==d.stopAfterSos)return;d.recordJpegSegments&&this.jpegSegments.push({offset:e,length:n,marker:i}),e+=n+1}}return e}mergeMultiSegments(){if(!this.appSegments.some(e=>e.multiSegment))return;let e=function(e,t){let i,n,a,r=new Map;for(let s=0;s<e.length;s++)n=(i=e[s])[t],r.has(n)?a=r.get(n):r.set(n,a=[]),a.push(i);return Array.from(r)}(this.appSegments,"type");this.mergedAppSegments=e.map(([e,t])=>{let i=D.get(e,this.options);return i.handleMultiSegments?{type:e,chunk:i.handleMultiSegments(t)}:t[0]})}getSegment(e){return this.appSegments.find(t=>t.type===e)}async getOrFindSegment(e){let t=this.getSegment(e);return void 0===t&&(await this.findAppSegments(0,[e]),t=this.getSegment(e)),t}}m(eh,"type","jpeg"),T.set("jpeg",eh);let ec=[void 0,1,1,2,4,8,1,1,2,4,8,4,8,4];class eu extends el{parseHeader(){var e=this.chunk.getUint16();18761===e?this.le=!0:19789===e&&(this.le=!1),this.chunk.le=this.le,this.headerParsed=!0}parseTags(e,t,i=new Map){let{pick:n,skip:a}=this.options[t],r=(n=new Set(n)).size>0,s=0===a.size,o=this.chunk.getUint16(e);e+=2;for(let l=0;l<o;l++){let o=this.chunk.getUint16(e);if(r){if(n.has(o)&&(i.set(o,this.parseTag(e,o,t)),n.delete(o),0===n.size))break}else!s&&a.has(o)||i.set(o,this.parseTag(e,o,t));e+=12}return i}parseTag(e,t,i){let{chunk:n}=this,a=n.getUint16(e+2),r=n.getUint32(e+4),s=ec[a];if(s*r<=4?e+=8:e=n.getUint32(e+8),(a<1||a>13)&&C(`Invalid TIFF value type. block: ${i.toUpperCase()}, tag: ${t.toString(16)}, type: ${a}, offset ${e}`),e>n.byteLength&&C(`Invalid TIFF value offset. block: ${i.toUpperCase()}, tag: ${t.toString(16)}, type: ${a}, offset ${e} is outside of chunk size ${n.byteLength}`),1===a)return n.getUint8Array(e,r);if(2===a)return P(n.getString(e,r));if(7===a)return n.getUint8Array(e,r);if(1===r)return this.parseTagValue(a,e);{let t=new(function(e){switch(e){case 1:return Uint8Array;case 3:return Uint16Array;case 4:return Uint32Array;case 5:case 10:default:return Array;case 6:return Int8Array;case 8:return Int16Array;case 9:return Int32Array;case 11:return Float32Array;case 12:return Float64Array}}(a))(r);for(let i=0;i<r;i++)t[i]=this.parseTagValue(a,e),e+=s;return t}}parseTagValue(e,t){let{chunk:i}=this;switch(e){case 1:return i.getUint8(t);case 3:return i.getUint16(t);case 4:case 13:return i.getUint32(t);case 5:return i.getUint32(t)/i.getUint32(t+4);case 6:return i.getInt8(t);case 8:return i.getInt16(t);case 9:return i.getInt32(t);case 10:return i.getInt32(t)/i.getInt32(t+4);case 11:return i.getFloat(t);case 12:return i.getDouble(t);default:C(`Invalid tiff type ${e}`)}}}class ed extends eu{static canHandle(e,t){return 225===e.getUint8(t+1)&&0x45786966===e.getUint32(t+4)&&0===e.getUint16(t+8)}async parse(){this.parseHeader();let{options:e}=this;return e.ifd0.enabled&&await this.parseIfd0Block(),e.exif.enabled&&await this.safeParse("parseExifBlock"),e.gps.enabled&&await this.safeParse("parseGpsBlock"),e.interop.enabled&&await this.safeParse("parseInteropBlock"),e.ifd1.enabled&&await this.safeParse("parseThumbnailBlock"),this.createOutput()}safeParse(e){let t=this[e]();return void 0!==t.catch&&(t=t.catch(this.handleError)),t}findIfd0Offset(){void 0===this.ifd0Offset&&(this.ifd0Offset=this.chunk.getUint32(4))}findIfd1Offset(){if(void 0===this.ifd1Offset){this.findIfd0Offset();let e=this.chunk.getUint16(this.ifd0Offset),t=this.ifd0Offset+2+12*e;this.ifd1Offset=this.chunk.getUint32(t)}}parseBlock(e,t){let i=new Map;return this[t]=i,this.parseTags(e,t,i),i}async parseIfd0Block(){if(this.ifd0)return;let{file:e}=this;this.findIfd0Offset(),this.ifd0Offset<8&&C("Malformed EXIF data"),!e.chunked&&this.ifd0Offset>e.byteLength&&C(`IFD0 offset points to outside of file.
this.ifd0Offset: ${this.ifd0Offset}, file.byteLength: ${e.byteLength}`),e.tiff&&await e.ensureChunk(this.ifd0Offset,b(this.options));let t=this.parseBlock(this.ifd0Offset,"ifd0");return 0!==t.size?(this.exifOffset=t.get(34665),this.interopOffset=t.get(40965),this.gpsOffset=t.get(34853),this.xmp=t.get(700),this.iptc=t.get(33723),this.icc=t.get(34675),this.options.sanitize&&(t.delete(34665),t.delete(40965),t.delete(34853),t.delete(700),t.delete(33723),t.delete(34675)),t):void 0}async parseExifBlock(){if(this.exif||(this.ifd0||await this.parseIfd0Block(),void 0===this.exifOffset))return;this.file.tiff&&await this.file.ensureChunk(this.exifOffset,b(this.options));let e=this.parseBlock(this.exifOffset,"exif");return this.interopOffset||(this.interopOffset=e.get(40965)),this.makerNote=e.get(37500),this.userComment=e.get(37510),this.options.sanitize&&(e.delete(40965),e.delete(37500),e.delete(37510)),this.unpack(e,41728),this.unpack(e,41729),e}unpack(e,t){let i=e.get(t);i&&1===i.length&&e.set(t,i[0])}async parseGpsBlock(){if(this.gps||(this.ifd0||await this.parseIfd0Block(),void 0===this.gpsOffset))return;let e=this.parseBlock(this.gpsOffset,"gps");return e&&e.has(2)&&e.has(4)&&(e.set("latitude",ep(...e.get(2),e.get(1))),e.set("longitude",ep(...e.get(4),e.get(3)))),e}async parseInteropBlock(){if(!this.interop&&(this.ifd0||await this.parseIfd0Block(),void 0!==this.interopOffset||this.exif||await this.parseExifBlock(),void 0!==this.interopOffset))return this.parseBlock(this.interopOffset,"interop")}async parseThumbnailBlock(e=!1){if(!this.ifd1&&!this.ifd1Parsed&&(!this.options.mergeOutput||e))return this.findIfd1Offset(),this.ifd1Offset>0&&(this.parseBlock(this.ifd1Offset,"ifd1"),this.ifd1Parsed=!0),this.ifd1}async extractThumbnail(){if(this.headerParsed||this.parseHeader(),this.ifd1Parsed||await this.parseThumbnailBlock(!0),void 0===this.ifd1)return;let e=this.ifd1.get(513),t=this.ifd1.get(514);return this.chunk.getUint8Array(e,t)}get image(){return this.ifd0}get thumbnail(){return this.ifd1}createOutput(){let e,t,i,n={};for(t of W)if(!S(e=this[t]))if(i=this.canTranslate?this.translateBlock(e,t):Object.fromEntries(e),this.options.mergeOutput){if("ifd1"===t)continue;Object.assign(n,i)}else n[t]=i;return this.makerNote&&(n.makerNote=this.makerNote),this.userComment&&(n.userComment=this.userComment),n}assignToOutput(e,t){if(this.globalOptions.mergeOutput)Object.assign(e,t);else for(let[i,n]of Object.entries(t))this.assignObjectToOutput(e,i,n)}}function ep(e,t,i,n){var a=e+t/60+i/3600;return"S"!==n&&"W"!==n||(a*=-1),a}m(ed,"type","tiff"),m(ed,"headerLength",10),D.set("tiff",ed);var ef=Object.freeze({__proto__:null,default:es,Exifr:ea,fileParsers:T,segmentParsers:D,fileReaders:R,tagKeys:N,tagValues:j,tagRevivers:V,createDictionary:B,extendDictionary:G,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:F,chunkedProps:H,otherSegments:z,segments:_,tiffBlocks:W,segmentsAndBlocks:X,tiffExtractables:K,inheritables:J,allFormatters:$,Options:ee,parse:er});let em={ifd0:!1,ifd1:!1,exif:!1,gps:!1,interop:!1,sanitize:!1,reviveValues:!0,translateKeys:!1,translateValues:!1,mergeOutput:!1},eg=Object.assign({},em,{firstChunkSize:4e4,gps:[1,2,3,4]});async function ey(e){let t=new ea(eg);await t.read(e);let i=await t.parse();if(i&&i.gps){let{latitude:e,longitude:t}=i.gps;return{latitude:e,longitude:t}}}let eS=Object.assign({},em,{tiff:!1,ifd1:!0,mergeOutput:!1});async function eC(e){let t=new ea(eS);await t.read(e);let i=await t.extractThumbnail();return i&&u?h.from(i):i}async function eP(e){let t=await this.thumbnail(e);if(void 0!==t){let e=new Blob([t]);return URL.createObjectURL(e)}}let eb=Object.assign({},em,{firstChunkSize:4e4,ifd0:[274]});async function ew(e){let t=new ea(eb);await t.read(e);let i=await t.parse();if(i&&i.ifd0)return i.ifd0[274]}let eI=Object.freeze({1:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:0,rad:0},2:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:0,rad:0},3:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:180,rad:180*Math.PI/180},4:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:180,rad:180*Math.PI/180},5:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:90,rad:90*Math.PI/180},6:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:90,rad:90*Math.PI/180},7:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:270,rad:270*Math.PI/180},8:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:270,rad:270*Math.PI/180}}),ek=!0,eL=!0;if("object"==typeof navigator){let e=navigator.userAgent;if(e.includes("iPad")||e.includes("iPhone")){let t=e.match(/OS (\d+)_(\d+)/);if(t){let[,e,i]=t;ek=Number(e)+.1*Number(i)<13.4,eL=!1}}else if(e.includes("OS X 10")){let[,t]=e.match(/OS X 10[_.](\d+)/);ek=eL=15>Number(t)}if(e.includes("Chrome/")){let[,t]=e.match(/Chrome\/(\d+)/);ek=eL=81>Number(t)}else if(e.includes("Firefox/")){let[,t]=e.match(/Firefox\/(\d+)/);ek=eL=77>Number(t)}}async function ev(e){let t=await ew(e);return Object.assign({canvas:ek,css:eL},eI[t])}class eA extends L{constructor(...e){super(...e),m(this,"ranges",new eT),0!==this.byteLength&&this.ranges.add(0,this.byteLength)}_tryExtend(e,t,i){if(0===e&&0===this.byteLength&&i){let e=new DataView(i.buffer||i,i.byteOffset,i.byteLength);this._swapDataView(e)}else{let i=e+t;if(i>this.byteLength){let{dataView:e}=this._extend(i);this._swapDataView(e)}}}_extend(e){let t,i=new DataView((t=u?h.allocUnsafe(e):new Uint8Array(e)).buffer,t.byteOffset,t.byteLength);return t.set(new Uint8Array(this.buffer,this.byteOffset,this.byteLength),0),{uintView:t,dataView:i}}subarray(e,t,i=!1){return t=t||this._lengthToEnd(e),i&&this._tryExtend(e,t),this.ranges.add(e,t),super.subarray(e,t)}set(e,t,i=!1){i&&this._tryExtend(t,e.byteLength,e);let n=super.set(e,t);return this.ranges.add(t,n.byteLength),n}async ensureChunk(e,t){this.chunked&&(this.ranges.available(e,t)||await this.readChunk(e,t))}available(e,t){return this.ranges.available(e,t)}}class eT{constructor(){m(this,"list",[])}get length(){return this.list.length}add(e,t,i=0){let n=e+t,a=this.list.filter(t=>{var i,a,r,s,o,l;return i=e,a=t.offset,r=n,i<=a&&a<=r||(s=e,o=t.end,l=n,s<=o&&o<=l)});if(a.length>0){e=Math.min(e,...a.map(e=>e.offset)),t=(n=Math.max(n,...a.map(e=>e.end)))-e;let i=a.shift();i.offset=e,i.length=t,i.end=n,this.list=this.list.filter(e=>!a.includes(e))}else this.list.push({offset:e,length:t,end:n})}available(e,t){let i=e+t;return this.list.some(t=>t.offset<=e&&i<=t.end)}}class eD extends eA{constructor(e,t){super(0),m(this,"chunksRead",0),this.input=e,this.options=t}async readWhole(){this.chunked=!1,await this.readChunk(this.nextChunkOffset)}async readChunked(){this.chunked=!0,await this.readChunk(0,this.options.firstChunkSize)}async readNextChunk(e=this.nextChunkOffset){if(this.fullyRead)return this.chunksRead++,!1;let t=this.options.chunkSize,i=await this.readChunk(e,t);return!!i&&i.byteLength===t}async readChunk(e,t){if(this.chunksRead++,0!==(t=this.safeWrapAddress(e,t)))return this._readChunk(e,t)}safeWrapAddress(e,t){return void 0!==this.size&&e+t>this.size?Math.max(0,this.size-e):t}get nextChunkOffset(){if(0!==this.ranges.list.length)return this.ranges.list[0].length}get canReadNextChunk(){return this.chunksRead<this.options.chunkLimit}get fullyRead(){return void 0!==this.size&&this.nextChunkOffset===this.size}read(){return this.options.chunked?this.readChunked():this.readWhole()}close(){}}R.set("blob",class extends eD{async readWhole(){this.chunked=!1;let e=await F(this.input);this._swapArrayBuffer(e)}readChunked(){return this.chunked=!0,this.size=this.input.size,super.readChunked()}async _readChunk(e,t){let i=t?e+t:void 0,n=this.input.slice(e,i),a=await F(n);return this.set(a,e,!0)}});var eR=Object.freeze({__proto__:null,default:ef,Exifr:ea,fileParsers:T,segmentParsers:D,fileReaders:R,tagKeys:N,tagValues:j,tagRevivers:V,createDictionary:B,extendDictionary:G,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:F,chunkedProps:H,otherSegments:z,segments:_,tiffBlocks:W,segmentsAndBlocks:X,tiffExtractables:K,inheritables:J,allFormatters:$,Options:ee,parse:er,gpsOnlyOptions:eg,gps:ey,thumbnailOnlyOptions:eS,thumbnail:eC,thumbnailUrl:eP,orientationOnlyOptions:eb,orientation:ew,rotations:eI,get rotateCanvas(){return ek},get rotateCss(){return eL},rotation:ev});R.set("url",class extends eD{async readWhole(){this.chunked=!1;let e=await M(this.input);e instanceof ArrayBuffer?this._swapArrayBuffer(e):e instanceof Uint8Array&&this._swapBuffer(e)}async _readChunk(e,t){let i=t?e+t-1:void 0,n=this.options.httpHeaders||{};(e||i)&&(n.range=`bytes=${[e,i].join("-")}`);let a=await f(this.input,{headers:n}),r=await a.arrayBuffer(),s=r.byteLength;if(416!==a.status)return s!==t&&(this.size=e+s),this.set(r,e,!0)}}),L.prototype.getUint64=function(e){let t=this.getUint32(e),i=this.getUint32(e+4);return t<1048575?t<<32|i:(console.warn("Using BigInt because of type 64uint but JS can only handle 53b numbers."),c(t)<<c(32)|c(i))};class ex extends eo{parseBoxes(e=0){let t=[];for(;e<this.file.byteLength-4;){let i=this.parseBoxHead(e);if(t.push(i),0===i.length)break;e+=i.length}return t}parseSubBoxes(e){e.boxes=this.parseBoxes(e.start)}findBox(e,t){return void 0===e.boxes&&this.parseSubBoxes(e),e.boxes.find(e=>e.kind===t)}parseBoxHead(e){let t=this.file.getUint32(e),i=this.file.getString(e+4,4),n=e+8;return 1===t&&(t=this.file.getUint64(e+8),n+=8),{offset:e,length:t,kind:i,start:n}}parseBoxFullHead(e){if(void 0!==e.version)return;let t=this.file.getUint32(e.start);e.version=t>>24,e.start+=4}}class eO extends ex{static canHandle(e,t){if(0!==t)return!1;let i=e.getUint16(2);if(i>50)return!1;let n=16,a=[];for(;n<i;)a.push(e.getString(n,4)),n+=4;return a.includes(this.type)}async parse(){let e=this.file.getUint32(0),t=this.parseBoxHead(e);for(;"meta"!==t.kind;)e+=t.length,await this.file.ensureChunk(e,16),t=this.parseBoxHead(e);await this.file.ensureChunk(t.offset,t.length),this.parseBoxFullHead(t),this.parseSubBoxes(t),this.options.icc.enabled&&await this.findIcc(t),this.options.tiff.enabled&&await this.findExif(t)}async registerSegment(e,t,i){await this.file.ensureChunk(t,i);let n=this.file.subarray(t,i);this.createParser(e,n)}async findIcc(e){let t=this.findBox(e,"iprp");if(void 0===t)return;let i=this.findBox(t,"ipco");if(void 0===i)return;let n=this.findBox(i,"colr");void 0!==n&&await this.registerSegment("icc",n.offset+12,n.length)}async findExif(e){let t=this.findBox(e,"iinf");if(void 0===t)return;let i=this.findBox(e,"iloc");if(void 0===i)return;let n=this.findExifLocIdInIinf(t),a=this.findExtentInIloc(i,n);if(void 0===a)return;let[r,s]=a;await this.file.ensureChunk(r,s);let o=4+this.file.getUint32(r);r+=o,s-=o,await this.registerSegment("tiff",r,s)}findExifLocIdInIinf(e){this.parseBoxFullHead(e);let t,i,n,a=e.start,r=this.file.getUint16(a);for(a+=2;r--;){if(t=this.parseBoxHead(a),this.parseBoxFullHead(t),i=t.start,t.version>=2&&(n=3===t.version?4:2,"Exif"===this.file.getString(i+n+2,4)))return this.file.getUintBytes(i,n);a+=t.length}}get8bits(e){let t=this.file.getUint8(e);return[t>>4,15&t]}findExtentInIloc(e,t){this.parseBoxFullHead(e);let i=e.start,[n,a]=this.get8bits(i++),[r,s]=this.get8bits(i++),o=2===e.version?4:2,l=2*(1===e.version||2===e.version),h=s+n+a,c=2===e.version?4:2,u=this.file.getUintBytes(i,c);for(i+=c;u--;){let e=this.file.getUintBytes(i,o);i+=o+l+2+r;let c=this.file.getUint16(i);if(i+=2,e===t)return c>1&&console.warn("ILOC box has more than one extent but we're only processing one\nPlease create an issue at https://github.com/MikeKovarik/exifr with this file"),[this.file.getUintBytes(i+s,n),this.file.getUintBytes(i+s+n,a)];i+=c*h}}}class eE extends eO{}m(eE,"type","heic");class eM extends eO{}m(eM,"type","avif"),T.set("heic",eE),T.set("avif",eM),B(N,["ifd0","ifd1"],[[256,"ImageWidth"],[257,"ImageHeight"],[258,"BitsPerSample"],[259,"Compression"],[262,"PhotometricInterpretation"],[270,"ImageDescription"],[271,"Make"],[272,"Model"],[273,"StripOffsets"],[274,"Orientation"],[277,"SamplesPerPixel"],[278,"RowsPerStrip"],[279,"StripByteCounts"],[282,"XResolution"],[283,"YResolution"],[284,"PlanarConfiguration"],[296,"ResolutionUnit"],[301,"TransferFunction"],[305,"Software"],[306,"ModifyDate"],[315,"Artist"],[316,"HostComputer"],[317,"Predictor"],[318,"WhitePoint"],[319,"PrimaryChromaticities"],[513,"ThumbnailOffset"],[514,"ThumbnailLength"],[529,"YCbCrCoefficients"],[530,"YCbCrSubSampling"],[531,"YCbCrPositioning"],[532,"ReferenceBlackWhite"],[700,"ApplicationNotes"],[33432,"Copyright"],[33723,"IPTC"],[34665,"ExifIFD"],[34675,"ICC"],[34853,"GpsIFD"],[330,"SubIFD"],[40965,"InteropIFD"],[40091,"XPTitle"],[40092,"XPComment"],[40093,"XPAuthor"],[40094,"XPKeywords"],[40095,"XPSubject"]]),B(N,"exif",[[33434,"ExposureTime"],[33437,"FNumber"],[34850,"ExposureProgram"],[34852,"SpectralSensitivity"],[34855,"ISO"],[34858,"TimeZoneOffset"],[34859,"SelfTimerMode"],[34864,"SensitivityType"],[34865,"StandardOutputSensitivity"],[34866,"RecommendedExposureIndex"],[34867,"ISOSpeed"],[34868,"ISOSpeedLatitudeyyy"],[34869,"ISOSpeedLatitudezzz"],[36864,"ExifVersion"],[36867,"DateTimeOriginal"],[36868,"CreateDate"],[36873,"GooglePlusUploadCode"],[36880,"OffsetTime"],[36881,"OffsetTimeOriginal"],[36882,"OffsetTimeDigitized"],[37121,"ComponentsConfiguration"],[37122,"CompressedBitsPerPixel"],[37377,"ShutterSpeedValue"],[37378,"ApertureValue"],[37379,"BrightnessValue"],[37380,"ExposureCompensation"],[37381,"MaxApertureValue"],[37382,"SubjectDistance"],[37383,"MeteringMode"],[37384,"LightSource"],[37385,"Flash"],[37386,"FocalLength"],[37393,"ImageNumber"],[37394,"SecurityClassification"],[37395,"ImageHistory"],[37396,"SubjectArea"],[37500,"MakerNote"],[37510,"UserComment"],[37520,"SubSecTime"],[37521,"SubSecTimeOriginal"],[37522,"SubSecTimeDigitized"],[37888,"AmbientTemperature"],[37889,"Humidity"],[37890,"Pressure"],[37891,"WaterDepth"],[37892,"Acceleration"],[37893,"CameraElevationAngle"],[40960,"FlashpixVersion"],[40961,"ColorSpace"],[40962,"ExifImageWidth"],[40963,"ExifImageHeight"],[40964,"RelatedSoundFile"],[41483,"FlashEnergy"],[41486,"FocalPlaneXResolution"],[41487,"FocalPlaneYResolution"],[41488,"FocalPlaneResolutionUnit"],[41492,"SubjectLocation"],[41493,"ExposureIndex"],[41495,"SensingMethod"],[41728,"FileSource"],[41729,"SceneType"],[41730,"CFAPattern"],[41985,"CustomRendered"],[41986,"ExposureMode"],[41987,"WhiteBalance"],[41988,"DigitalZoomRatio"],[41989,"FocalLengthIn35mmFormat"],[41990,"SceneCaptureType"],[41991,"GainControl"],[41992,"Contrast"],[41993,"Saturation"],[41994,"Sharpness"],[41996,"SubjectDistanceRange"],[42016,"ImageUniqueID"],[42032,"OwnerName"],[42033,"SerialNumber"],[42034,"LensInfo"],[42035,"LensMake"],[42036,"LensModel"],[42037,"LensSerialNumber"],[42080,"CompositeImage"],[42081,"CompositeImageCount"],[42082,"CompositeImageExposureTimes"],[42240,"Gamma"],[59932,"Padding"],[59933,"OffsetSchema"],[65e3,"OwnerName"],[65001,"SerialNumber"],[65002,"Lens"],[65100,"RawFile"],[65101,"Converter"],[65102,"WhiteBalance"],[65105,"Exposure"],[65106,"Shadows"],[65107,"Brightness"],[65108,"Contrast"],[65109,"Saturation"],[65110,"Sharpness"],[65111,"Smoothness"],[65112,"MoireFilter"],[40965,"InteropIFD"]]),B(N,"gps",[[0,"GPSVersionID"],[1,"GPSLatitudeRef"],[2,"GPSLatitude"],[3,"GPSLongitudeRef"],[4,"GPSLongitude"],[5,"GPSAltitudeRef"],[6,"GPSAltitude"],[7,"GPSTimeStamp"],[8,"GPSSatellites"],[9,"GPSStatus"],[10,"GPSMeasureMode"],[11,"GPSDOP"],[12,"GPSSpeedRef"],[13,"GPSSpeed"],[14,"GPSTrackRef"],[15,"GPSTrack"],[16,"GPSImgDirectionRef"],[17,"GPSImgDirection"],[18,"GPSMapDatum"],[19,"GPSDestLatitudeRef"],[20,"GPSDestLatitude"],[21,"GPSDestLongitudeRef"],[22,"GPSDestLongitude"],[23,"GPSDestBearingRef"],[24,"GPSDestBearing"],[25,"GPSDestDistanceRef"],[26,"GPSDestDistance"],[27,"GPSProcessingMethod"],[28,"GPSAreaInformation"],[29,"GPSDateStamp"],[30,"GPSDifferential"],[31,"GPSHPositioningError"]]),B(j,["ifd0","ifd1"],[[274,{1:"Horizontal (normal)",2:"Mirror horizontal",3:"Rotate 180",4:"Mirror vertical",5:"Mirror horizontal and rotate 270 CW",6:"Rotate 90 CW",7:"Mirror horizontal and rotate 90 CW",8:"Rotate 270 CW"}],[296,{1:"None",2:"inches",3:"cm"}]]);let eF=B(j,"exif",[[34850,{0:"Not defined",1:"Manual",2:"Normal program",3:"Aperture priority",4:"Shutter priority",5:"Creative program",6:"Action program",7:"Portrait mode",8:"Landscape mode"}],[37121,{0:"-",1:"Y",2:"Cb",3:"Cr",4:"R",5:"G",6:"B"}],[37383,{0:"Unknown",1:"Average",2:"CenterWeightedAverage",3:"Spot",4:"MultiSpot",5:"Pattern",6:"Partial",255:"Other"}],[37384,{0:"Unknown",1:"Daylight",2:"Fluorescent",3:"Tungsten (incandescent light)",4:"Flash",9:"Fine weather",10:"Cloudy weather",11:"Shade",12:"Daylight fluorescent (D 5700 - 7100K)",13:"Day white fluorescent (N 4600 - 5400K)",14:"Cool white fluorescent (W 3900 - 4500K)",15:"White fluorescent (WW 3200 - 3700K)",17:"Standard light A",18:"Standard light B",19:"Standard light C",20:"D55",21:"D65",22:"D75",23:"D50",24:"ISO studio tungsten",255:"Other"}],[37385,{0:"Flash did not fire",1:"Flash fired",5:"Strobe return light not detected",7:"Strobe return light detected",9:"Flash fired, compulsory flash mode",13:"Flash fired, compulsory flash mode, return light not detected",15:"Flash fired, compulsory flash mode, return light detected",16:"Flash did not fire, compulsory flash mode",24:"Flash did not fire, auto mode",25:"Flash fired, auto mode",29:"Flash fired, auto mode, return light not detected",31:"Flash fired, auto mode, return light detected",32:"No flash function",65:"Flash fired, red-eye reduction mode",69:"Flash fired, red-eye reduction mode, return light not detected",71:"Flash fired, red-eye reduction mode, return light detected",73:"Flash fired, compulsory flash mode, red-eye reduction mode",77:"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected",79:"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected",89:"Flash fired, auto mode, red-eye reduction mode",93:"Flash fired, auto mode, return light not detected, red-eye reduction mode",95:"Flash fired, auto mode, return light detected, red-eye reduction mode"}],[41495,{1:"Not defined",2:"One-chip color area sensor",3:"Two-chip color area sensor",4:"Three-chip color area sensor",5:"Color sequential area sensor",7:"Trilinear sensor",8:"Color sequential linear sensor"}],[41728,{1:"Film Scanner",2:"Reflection Print Scanner",3:"Digital Camera"}],[41729,{1:"Directly photographed"}],[41985,{0:"Normal",1:"Custom",2:"HDR (no original saved)",3:"HDR (original saved)",4:"Original (for HDR)",6:"Panorama",7:"Portrait HDR",8:"Portrait"}],[41986,{0:"Auto",1:"Manual",2:"Auto bracket"}],[41987,{0:"Auto",1:"Manual"}],[41990,{0:"Standard",1:"Landscape",2:"Portrait",3:"Night",4:"Other"}],[41991,{0:"None",1:"Low gain up",2:"High gain up",3:"Low gain down",4:"High gain down"}],[41996,{0:"Unknown",1:"Macro",2:"Close",3:"Distant"}],[42080,{0:"Unknown",1:"Not a Composite Image",2:"General Composite Image",3:"Composite Image Captured While Shooting"}]]),eU={1:"No absolute unit of measurement",2:"Inch",3:"Centimeter"};eF.set(37392,eU),eF.set(41488,eU);let eB={0:"Normal",1:"Low",2:"High"};function eG(e){return"object"==typeof e&&void 0!==e.length?e[0]:e}function eN(e){let t=Array.from(e).slice(1);return t[1]>15&&(t=t.map(e=>String.fromCharCode(e))),"0"!==t[2]&&0!==t[2]||t.pop(),t.join(".")}function ej(e){if("string"==typeof e){var[t,i,n,a,r,s]=e.trim().split(/[-: ]/g).map(Number),o=new Date(t,i-1,n);return Number.isNaN(a)||Number.isNaN(r)||Number.isNaN(s)||(o.setHours(a),o.setMinutes(r),o.setSeconds(s)),Number.isNaN(+o)?e:o}}function eV(e){var t,i;if("string"==typeof e)return e;let n=[];if(0===e[1]&&0===e[e.length-1])for(let i=0;i<e.length;i+=2){n.push((t=e[i+1],t<<8|e[i]))}else for(let t=0;t<e.length;t+=2){n.push((i=e[t],i<<8|e[t+1]))}return P(String.fromCodePoint(...n))}eF.set(41992,eB),eF.set(41993,eB),eF.set(41994,eB),B(V,["ifd0","ifd1"],[[50827,function(e){return"string"!=typeof e?k(e):e}],[306,ej],[40091,eV],[40092,eV],[40093,eV],[40094,eV],[40095,eV]]),B(V,"exif",[[40960,eN],[36864,eN],[36867,ej],[36868,ej],[40962,eG],[40963,eG]]),B(V,"gps",[[0,e=>Array.from(e).join(".")],[7,e=>Array.from(e).join(":")]]);class eH extends el{static canHandle(e,t){return 225===e.getUint8(t+1)&&0x68747470===e.getUint32(t+4)&&"http://ns.adobe.com/"===e.getString(t+4,20)}static headerLength(e,t){return"http://ns.adobe.com/xmp/extension/"===e.getString(t+4,34)?79:33}static findPosition(e,t){let i=super.findPosition(e,t);return i.multiSegment=i.extended=79===i.headerLength,i.multiSegment?(i.chunkCount=e.getUint8(t+72),i.chunkNumber=e.getUint8(t+76),0!==e.getUint8(t+77)&&i.chunkNumber++):(i.chunkCount=1/0,i.chunkNumber=-1),i}static handleMultiSegments(e){return e.map(e=>e.chunk.getString()).join("")}normalizeInput(e){return"string"==typeof e?e:L.from(e).getString()}parse(e=this.chunk){if(!this.localOptions.parse)return e;e=function(e){let t={},i={};for(let e of eq)t[e]=[],i[e]=0;return e.replace(eQ,(e,n,a)=>{if("<"===n){let n=++i[a];return t[a].push(n),`${e}#${n}`}return`${e}#${t[a].pop()}`})}(e);let t=e_.findAll(e,"rdf","Description");0===t.length&&t.push(new e_("rdf","Description",void 0,e));let i,n={};for(let e of t)for(let t of e.properties)i=eJ(t.ns,n),eW(t,i);for(let e in n)void 0===(n[e]=g(n[e]))&&delete n[e];return g(n)}assignToOutput(e,t){if(this.localOptions.parse)for(let[i,n]of Object.entries(t))switch(i){case"tiff":this.assignObjectToOutput(e,"ifd0",n);break;case"exif":this.assignObjectToOutput(e,"exif",n);break;case"xmlns":break;default:this.assignObjectToOutput(e,i,n)}else e.xmp=t}}m(eH,"type","xmp"),m(eH,"multiSegment",!0),D.set("xmp",eH);class ez{static findAll(e){return e$(e,/([a-zA-Z0-9-]+):([a-zA-Z0-9-]+)=("[^"]*"|'[^']*')/gm).map(ez.unpackMatch)}static unpackMatch(e){let t=e[1],i=e[2],n=e[3].slice(1,-1);return new ez(t,i,n=eY(n))}constructor(e,t,i){this.ns=e,this.name=t,this.value=i}serialize(){return this.value}}class e_{static findAll(e,t,i){if(void 0!==t||void 0!==i){t=t||"[\\w\\d-]+",i=i||"[\\w\\d-]+";var n=RegExp(`<(${t}):(${i})(#\\d+)?((\\s+?[\\w\\d-:]+=("[^"]*"|'[^']*'))*\\s*)(\\/>|>([\\s\\S]*?)<\\/\\1:\\2\\3>)`,"gm")}else n=/<([\w\d-]+):([\w\d-]+)(#\d+)?((\s+?[\w\d-:]+=("[^"]*"|'[^']*'))*\s*)(\/>|>([\s\S]*?)<\/\1:\2\3>)/gm;return e$(e,n).map(e_.unpackMatch)}static unpackMatch(e){return new e_(e[1],e[2],e[4],e[8])}constructor(e,t,i,n){this.ns=e,this.name=t,this.attrString=i,this.innerXml=n,this.attrs=ez.findAll(i),this.children=e_.findAll(n),this.value=0===this.children.length?eY(n):void 0,this.properties=[...this.attrs,...this.children]}get isPrimitive(){return void 0!==this.value&&0===this.attrs.length&&0===this.children.length}get isListContainer(){return 1===this.children.length&&this.children[0].isList}get isList(){let{ns:e,name:t}=this;return"rdf"===e&&("Seq"===t||"Bag"===t||"Alt"===t)}get isListItem(){return"rdf"===this.ns&&"li"===this.name}serialize(){if(0===this.properties.length&&void 0===this.value)return;if(this.isPrimitive)return this.value;if(this.isListContainer)return this.children[0].serialize();if(this.isList)return eK(this.children.map(eX));if(this.isListItem&&1===this.children.length&&0===this.attrs.length)return this.children[0].serialize();let e={};for(let t of this.properties)eW(t,e);return void 0!==this.value&&(e.value=this.value),g(e)}}function eW(e,t){let i=e.serialize();void 0!==i&&(t[e.name]=i)}var eX=e=>e.serialize(),eK=e=>1===e.length?e[0]:e,eJ=(e,t)=>t[e]?t[e]:t[e]={};function e$(e,t){let i,n=[];if(!e)return n;for(;null!==(i=t.exec(e));)n.push(i);return n}function eY(e){if(null==e||"null"===e||"undefined"===e||""===e||""===e.trim())return;let t=Number(e);if(!Number.isNaN(t))return t;let i=e.toLowerCase();return"true"===i||"false"!==i&&e.trim()}let eq=["rdf:li","rdf:Seq","rdf:Bag","rdf:Alt","rdf:Description"],eQ=RegExp(`(<|\\/)(${eq.join("|")})`,"g");Object.freeze({__proto__:null,default:eR,Exifr:ea,fileParsers:T,segmentParsers:D,fileReaders:R,tagKeys:N,tagValues:j,tagRevivers:V,createDictionary:B,extendDictionary:G,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:F,chunkedProps:H,otherSegments:z,segments:_,tiffBlocks:W,segmentsAndBlocks:X,tiffExtractables:K,inheritables:J,allFormatters:$,Options:ee,parse:er,gpsOnlyOptions:eg,gps:ey,thumbnailOnlyOptions:eS,thumbnail:eC,thumbnailUrl:eP,orientationOnlyOptions:eb,orientation:ew,rotations:eI,get rotateCanvas(){return ek},get rotateCss(){return eL},rotation:ev});let eZ=p("fs",e=>e.promises);R.set("fs",class extends eD{async readWhole(){this.chunked=!1,this.fs=await eZ;let e=await this.fs.readFile(this.input);this._swapBuffer(e)}async readChunked(){this.chunked=!0,this.fs=await eZ,await this.open(),await this.readChunk(0,this.options.firstChunkSize)}async open(){void 0===this.fh&&(this.fh=await this.fs.open(this.input,"r"),this.size=(await this.fh.stat(this.input)).size)}async _readChunk(e,t){void 0===this.fh&&await this.open(),e+t>this.size&&(t=this.size-e);var i=this.subarray(e,t,!0);return await this.fh.read(i.dataView,0,t,e),i}async close(){if(this.fh){let e=this.fh;this.fh=void 0,await e.close()}}}),R.set("base64",class extends eD{constructor(...e){super(...e),this.input=this.input.replace(/^data:([^;]+);base64,/gim,""),this.size=this.input.length/4*3,this.input.endsWith("==")?this.size-=2:this.input.endsWith("=")&&(this.size-=1)}async _readChunk(e,t){let i,n,a=this.input;void 0===e?(e=0,i=0,n=0):(i=4*Math.floor(e/3),n=e-i/4*3),void 0===t&&(t=this.size);let r=i+4*Math.ceil((e+t)/3);a=a.slice(i,r);let s=Math.min(t,this.size-e);if(u){let t=h.from(a,"base64").slice(n,n+s);return this.set(t,e,!0)}{let t=this.subarray(e,s,!0),i=atob(a),r=t.toUint8();for(let e=0;e<s;e++)r[e]=i.charCodeAt(n+e);return t}}});class e0 extends eo{static canHandle(e,t){return 18761===t||19789===t}extendOptions(e){let{ifd0:t,xmp:i,iptc:n,icc:a}=e;i.enabled&&t.deps.add(700),n.enabled&&t.deps.add(33723),a.enabled&&t.deps.add(34675),t.finalizeFilters()}async parse(){let{tiff:e,xmp:t,iptc:i,icc:n}=this.options;if(e.enabled||t.enabled||i.enabled||n.enabled){let e=Math.max(b(this.options),this.options.chunkSize);await this.file.ensureChunk(0,e),this.createParser("tiff",this.file),this.parsers.tiff.parseHeader(),await this.parsers.tiff.parseIfd0Block(),this.adaptTiffPropAsSegment("xmp"),this.adaptTiffPropAsSegment("iptc"),this.adaptTiffPropAsSegment("icc")}}adaptTiffPropAsSegment(e){if(this.parsers.tiff[e]){let t=this.parsers.tiff[e];this.injectSegment(e,t)}}}m(e0,"type","tiff"),T.set("tiff",e0);let e3=p("zlib"),e1=["ihdr","iccp","text","itxt","exif"];class e2 extends eo{constructor(...e){super(...e),m(this,"catchError",e=>this.errors.push(e)),m(this,"metaChunks",[]),m(this,"unknownChunks",[])}static canHandle(e,t){return 35152===t&&0x89504e47===e.getUint32(0)&&0xd0a1a0a===e.getUint32(4)}async parse(){let{file:e}=this;await this.findPngChunksInRange(8,e.byteLength),await this.readSegments(this.metaChunks),this.findIhdr(),this.parseTextChunks(),await this.findExif().catch(this.catchError),await this.findXmp().catch(this.catchError),await this.findIcc().catch(this.catchError)}async findPngChunksInRange(e,t){let{file:i}=this;for(;e<t;){let t=i.getUint32(e),n=i.getUint32(e+4),a=i.getString(e+4,4).toLowerCase(),r=t+4+4+4,s={type:a,offset:e,length:r,start:e+4+4,size:t,marker:n};e1.includes(a)?this.metaChunks.push(s):this.unknownChunks.push(s),e+=r}}parseTextChunks(){for(let e of this.metaChunks.filter(e=>"text"===e.type)){let[t,i]=this.file.getString(e.start,e.size).split("\0");this.injectKeyValToIhdr(t,i)}}injectKeyValToIhdr(e,t){let i=this.parsers.ihdr;i&&i.raw.set(e,t)}findIhdr(){let e=this.metaChunks.find(e=>"ihdr"===e.type);e&&!1!==this.options.ihdr.enabled&&this.createParser("ihdr",e.chunk)}async findExif(){let e=this.metaChunks.find(e=>"exif"===e.type);e&&this.injectSegment("tiff",e.chunk)}async findXmp(){for(let e of this.metaChunks.filter(e=>"itxt"===e.type))"XML:com.adobe.xmp"===e.chunk.getString(0,17)&&this.injectSegment("xmp",e.chunk)}async findIcc(){let e=this.metaChunks.find(e=>"iccp"===e.type);if(!e)return;let{chunk:t}=e,i=t.getUint8Array(0,81),n=0;for(;n<80&&0!==i[n];)n++;let a=n+2,r=t.getString(0,n);if(this.injectKeyValToIhdr("ProfileName",r),l){let e=await e3,i=t.getUint8Array(a);i=e.inflateSync(i),this.injectSegment("icc",i)}}}m(e2,"type","png"),T.set("png",e2),B(N,"interop",[[1,"InteropIndex"],[2,"InteropVersion"],[4096,"RelatedImageFileFormat"],[4097,"RelatedImageWidth"],[4098,"RelatedImageHeight"]]),G(N,"ifd0",[[11,"ProcessingSoftware"],[254,"SubfileType"],[255,"OldSubfileType"],[263,"Thresholding"],[264,"CellWidth"],[265,"CellLength"],[266,"FillOrder"],[269,"DocumentName"],[280,"MinSampleValue"],[281,"MaxSampleValue"],[285,"PageName"],[286,"XPosition"],[287,"YPosition"],[290,"GrayResponseUnit"],[297,"PageNumber"],[321,"HalftoneHints"],[322,"TileWidth"],[323,"TileLength"],[332,"InkSet"],[337,"TargetPrinter"],[18246,"Rating"],[18249,"RatingPercent"],[33550,"PixelScale"],[34264,"ModelTransform"],[34377,"PhotoshopSettings"],[50706,"DNGVersion"],[50707,"DNGBackwardVersion"],[50708,"UniqueCameraModel"],[50709,"LocalizedCameraModel"],[50736,"DNGLensInfo"],[50739,"ShadowScale"],[50740,"DNGPrivateData"],[33920,"IntergraphMatrix"],[33922,"ModelTiePoint"],[34118,"SEMInfo"],[34735,"GeoTiffDirectory"],[34736,"GeoTiffDoubleParams"],[34737,"GeoTiffAsciiParams"],[50341,"PrintIM"],[50721,"ColorMatrix1"],[50722,"ColorMatrix2"],[50723,"CameraCalibration1"],[50724,"CameraCalibration2"],[50725,"ReductionMatrix1"],[50726,"ReductionMatrix2"],[50727,"AnalogBalance"],[50728,"AsShotNeutral"],[50729,"AsShotWhiteXY"],[50730,"BaselineExposure"],[50731,"BaselineNoise"],[50732,"BaselineSharpness"],[50734,"LinearResponseLimit"],[50735,"CameraSerialNumber"],[50741,"MakerNoteSafety"],[50778,"CalibrationIlluminant1"],[50779,"CalibrationIlluminant2"],[50781,"RawDataUniqueID"],[50827,"OriginalRawFileName"],[50828,"OriginalRawFileData"],[50831,"AsShotICCProfile"],[50832,"AsShotPreProfileMatrix"],[50833,"CurrentICCProfile"],[50834,"CurrentPreProfileMatrix"],[50879,"ColorimetricReference"],[50885,"SRawType"],[50898,"PanasonicTitle"],[50899,"PanasonicTitle2"],[50931,"CameraCalibrationSig"],[50932,"ProfileCalibrationSig"],[50933,"ProfileIFD"],[50934,"AsShotProfileName"],[50936,"ProfileName"],[50937,"ProfileHueSatMapDims"],[50938,"ProfileHueSatMapData1"],[50939,"ProfileHueSatMapData2"],[50940,"ProfileToneCurve"],[50941,"ProfileEmbedPolicy"],[50942,"ProfileCopyright"],[50964,"ForwardMatrix1"],[50965,"ForwardMatrix2"],[50966,"PreviewApplicationName"],[50967,"PreviewApplicationVersion"],[50968,"PreviewSettingsName"],[50969,"PreviewSettingsDigest"],[50970,"PreviewColorSpace"],[50971,"PreviewDateTime"],[50972,"RawImageDigest"],[50973,"OriginalRawFileDigest"],[50981,"ProfileLookTableDims"],[50982,"ProfileLookTableData"],[51043,"TimeCodes"],[51044,"FrameRate"],[51058,"TStop"],[51081,"ReelName"],[51089,"OriginalDefaultFinalSize"],[51090,"OriginalBestQualitySize"],[51091,"OriginalDefaultCropSize"],[51105,"CameraLabel"],[51107,"ProfileHueSatMapEncoding"],[51108,"ProfileLookTableEncoding"],[51109,"BaselineExposureOffset"],[51110,"DefaultBlackRender"],[51111,"NewRawImageDigest"],[51112,"RawToPreviewGain"]]);let e4=[[273,"StripOffsets"],[279,"StripByteCounts"],[288,"FreeOffsets"],[289,"FreeByteCounts"],[291,"GrayResponseCurve"],[292,"T4Options"],[293,"T6Options"],[300,"ColorResponseUnit"],[320,"ColorMap"],[324,"TileOffsets"],[325,"TileByteCounts"],[326,"BadFaxLines"],[327,"CleanFaxData"],[328,"ConsecutiveBadFaxLines"],[330,"SubIFD"],[333,"InkNames"],[334,"NumberofInks"],[336,"DotRange"],[338,"ExtraSamples"],[339,"SampleFormat"],[340,"SMinSampleValue"],[341,"SMaxSampleValue"],[342,"TransferRange"],[343,"ClipPath"],[344,"XClipPathUnits"],[345,"YClipPathUnits"],[346,"Indexed"],[347,"JPEGTables"],[351,"OPIProxy"],[400,"GlobalParametersIFD"],[401,"ProfileType"],[402,"FaxProfile"],[403,"CodingMethods"],[404,"VersionYear"],[405,"ModeNumber"],[433,"Decode"],[434,"DefaultImageColor"],[435,"T82Options"],[437,"JPEGTables"],[512,"JPEGProc"],[515,"JPEGRestartInterval"],[517,"JPEGLosslessPredictors"],[518,"JPEGPointTransforms"],[519,"JPEGQTables"],[520,"JPEGDCTables"],[521,"JPEGACTables"],[559,"StripRowCounts"],[999,"USPTOMiscellaneous"],[18247,"XP_DIP_XML"],[18248,"StitchInfo"],[28672,"SonyRawFileType"],[28688,"SonyToneCurve"],[28721,"VignettingCorrection"],[28722,"VignettingCorrParams"],[28724,"ChromaticAberrationCorrection"],[28725,"ChromaticAberrationCorrParams"],[28726,"DistortionCorrection"],[28727,"DistortionCorrParams"],[29895,"SonyCropTopLeft"],[29896,"SonyCropSize"],[32781,"ImageID"],[32931,"WangTag1"],[32932,"WangAnnotation"],[32933,"WangTag3"],[32934,"WangTag4"],[32953,"ImageReferencePoints"],[32954,"RegionXformTackPoint"],[32955,"WarpQuadrilateral"],[32956,"AffineTransformMat"],[32995,"Matteing"],[32996,"DataType"],[32997,"ImageDepth"],[32998,"TileDepth"],[33300,"ImageFullWidth"],[33301,"ImageFullHeight"],[33302,"TextureFormat"],[33303,"WrapModes"],[33304,"FovCot"],[33305,"MatrixWorldToScreen"],[33306,"MatrixWorldToCamera"],[33405,"Model2"],[33421,"CFARepeatPatternDim"],[33422,"CFAPattern2"],[33423,"BatteryLevel"],[33424,"KodakIFD"],[33445,"MDFileTag"],[33446,"MDScalePixel"],[33447,"MDColorTable"],[33448,"MDLabName"],[33449,"MDSampleInfo"],[33450,"MDPrepDate"],[33451,"MDPrepTime"],[33452,"MDFileUnits"],[33589,"AdventScale"],[33590,"AdventRevision"],[33628,"UIC1Tag"],[33629,"UIC2Tag"],[33630,"UIC3Tag"],[33631,"UIC4Tag"],[33918,"IntergraphPacketData"],[33919,"IntergraphFlagRegisters"],[33921,"INGRReserved"],[34016,"Site"],[34017,"ColorSequence"],[34018,"IT8Header"],[34019,"RasterPadding"],[34020,"BitsPerRunLength"],[34021,"BitsPerExtendedRunLength"],[34022,"ColorTable"],[34023,"ImageColorIndicator"],[34024,"BackgroundColorIndicator"],[34025,"ImageColorValue"],[34026,"BackgroundColorValue"],[34027,"PixelIntensityRange"],[34028,"TransparencyIndicator"],[34029,"ColorCharacterization"],[34030,"HCUsage"],[34031,"TrapIndicator"],[34032,"CMYKEquivalent"],[34152,"AFCP_IPTC"],[34232,"PixelMagicJBIGOptions"],[34263,"JPLCartoIFD"],[34306,"WB_GRGBLevels"],[34310,"LeafData"],[34687,"TIFF_FXExtensions"],[34688,"MultiProfiles"],[34689,"SharedData"],[34690,"T88Options"],[34732,"ImageLayer"],[34750,"JBIGOptions"],[34856,"Opto-ElectricConvFactor"],[34857,"Interlace"],[34908,"FaxRecvParams"],[34909,"FaxSubAddress"],[34910,"FaxRecvTime"],[34929,"FedexEDR"],[34954,"LeafSubIFD"],[37387,"FlashEnergy"],[37388,"SpatialFrequencyResponse"],[37389,"Noise"],[37390,"FocalPlaneXResolution"],[37391,"FocalPlaneYResolution"],[37392,"FocalPlaneResolutionUnit"],[37397,"ExposureIndex"],[37398,"TIFF-EPStandardID"],[37399,"SensingMethod"],[37434,"CIP3DataFile"],[37435,"CIP3Sheet"],[37436,"CIP3Side"],[37439,"StoNits"],[37679,"MSDocumentText"],[37680,"MSPropertySetStorage"],[37681,"MSDocumentTextPosition"],[37724,"ImageSourceData"],[40965,"InteropIFD"],[40976,"SamsungRawPointersOffset"],[40977,"SamsungRawPointersLength"],[41217,"SamsungRawByteOrder"],[41218,"SamsungRawUnknown"],[41484,"SpatialFrequencyResponse"],[41485,"Noise"],[41489,"ImageNumber"],[41490,"SecurityClassification"],[41491,"ImageHistory"],[41494,"TIFF-EPStandardID"],[41995,"DeviceSettingDescription"],[42112,"GDALMetadata"],[42113,"GDALNoData"],[44992,"ExpandSoftware"],[44993,"ExpandLens"],[44994,"ExpandFilm"],[44995,"ExpandFilterLens"],[44996,"ExpandScanner"],[44997,"ExpandFlashLamp"],[46275,"HasselbladRawImage"],[48129,"PixelFormat"],[48130,"Transformation"],[48131,"Uncompressed"],[48132,"ImageType"],[48256,"ImageWidth"],[48257,"ImageHeight"],[48258,"WidthResolution"],[48259,"HeightResolution"],[48320,"ImageOffset"],[48321,"ImageByteCount"],[48322,"AlphaOffset"],[48323,"AlphaByteCount"],[48324,"ImageDataDiscard"],[48325,"AlphaDataDiscard"],[50215,"OceScanjobDesc"],[50216,"OceApplicationSelector"],[50217,"OceIDNumber"],[50218,"OceImageLogic"],[50255,"Annotations"],[50459,"HasselbladExif"],[50547,"OriginalFileName"],[50560,"USPTOOriginalContentType"],[50656,"CR2CFAPattern"],[50710,"CFAPlaneColor"],[50711,"CFALayout"],[50712,"LinearizationTable"],[50713,"BlackLevelRepeatDim"],[50714,"BlackLevel"],[50715,"BlackLevelDeltaH"],[50716,"BlackLevelDeltaV"],[50717,"WhiteLevel"],[50718,"DefaultScale"],[50719,"DefaultCropOrigin"],[50720,"DefaultCropSize"],[50733,"BayerGreenSplit"],[50737,"ChromaBlurRadius"],[50738,"AntiAliasStrength"],[50752,"RawImageSegmentation"],[50780,"BestQualityScale"],[50784,"AliasLayerMetadata"],[50829,"ActiveArea"],[50830,"MaskedAreas"],[50935,"NoiseReductionApplied"],[50974,"SubTileBlockSize"],[50975,"RowInterleaveFactor"],[51008,"OpcodeList1"],[51009,"OpcodeList2"],[51022,"OpcodeList3"],[51041,"NoiseProfile"],[51114,"CacheVersion"],[51125,"DefaultUserCrop"],[51157,"NikonNEFInfo"],[65024,"KdcIFD"]];G(N,"ifd0",e4),G(N,"exif",e4),B(j,"gps",[[23,{M:"Magnetic North",T:"True North"}],[25,{K:"Kilometers",M:"Miles",N:"Nautical Miles"}]]);class e5 extends el{static canHandle(e,t){return 224===e.getUint8(t+1)&&0x4a464946===e.getUint32(t+4)&&0===e.getUint8(t+8)}parse(){return this.parseTags(),this.translate(),this.output}parseTags(){this.raw=new Map([[0,this.chunk.getUint16(0)],[2,this.chunk.getUint8(2)],[3,this.chunk.getUint16(3)],[5,this.chunk.getUint16(5)],[7,this.chunk.getUint8(7)],[8,this.chunk.getUint8(8)]])}}m(e5,"type","jfif"),m(e5,"headerLength",9),D.set("jfif",e5),B(N,"jfif",[[0,"JFIFVersion"],[2,"ResolutionUnit"],[3,"XResolution"],[5,"YResolution"],[7,"ThumbnailWidth"],[8,"ThumbnailHeight"]]);class e8 extends el{parse(){return this.parseTags(),this.translate(),this.output}parseTags(){this.raw=new Map([[0,this.chunk.getUint32(0)],[4,this.chunk.getUint32(4)],[8,this.chunk.getUint8(8)],[9,this.chunk.getUint8(9)],[10,this.chunk.getUint8(10)],[11,this.chunk.getUint8(11)],[12,this.chunk.getUint8(12)],...Array.from(this.raw)])}}m(e8,"type","ihdr"),D.set("ihdr",e8),B(N,"ihdr",[[0,"ImageWidth"],[4,"ImageHeight"],[8,"BitDepth"],[9,"ColorType"],[10,"Compression"],[11,"Filter"],[12,"Interlace"]]),B(j,"ihdr",[[9,{0:"Grayscale",2:"RGB",3:"Palette",4:"Grayscale with Alpha",6:"RGB with Alpha",DEFAULT:"Unknown"}],[10,{0:"Deflate/Inflate",DEFAULT:"Unknown"}],[11,{0:"Adaptive",DEFAULT:"Unknown"}],[12,{0:"Noninterlaced",1:"Adam7 Interlace",DEFAULT:"Unknown"}]]);class e7 extends el{static canHandle(e,t){return 226===e.getUint8(t+1)&&0x4943435f===e.getUint32(t+4)}static findPosition(e,t){let i=super.findPosition(e,t);return i.chunkNumber=e.getUint8(t+16),i.chunkCount=e.getUint8(t+17),i.multiSegment=i.chunkCount>1,i}static handleMultiSegments(e){return new L(function(e){let t=e[0].constructor,i=0;for(let t of e)i+=t.length;let n=new t(i),a=0;for(let t of e)n.set(t,a),a+=t.length;return n}(e.map(e=>e.chunk.toUint8())))}parse(){return this.raw=new Map,this.parseHeader(),this.parseTags(),this.translate(),this.output}parseHeader(){let{raw:e}=this;for(let[t,i]of(this.chunk.byteLength<84&&C("ICC header is too short"),Object.entries(e9))){t=parseInt(t,10);let n=i(this.chunk,t);"\0\0\0\0"!==n&&e.set(t,n)}}parseTags(){let e,t,i,n,a,{raw:r}=this,s=this.chunk.getUint32(128),o=132,l=this.chunk.byteLength;for(;s--;){if(e=this.chunk.getString(o,4),t=this.chunk.getUint32(o+4),i=this.chunk.getUint32(o+8),n=this.chunk.getString(t,4),t+i>l)return void console.warn("reached the end of the first ICC chunk. Enable options.tiff.multiSegment to read all ICC segments.");void 0!==(a=this.parseTag(n,t,i))&&"\0\0\0\0"!==a&&r.set(e,a),o+=12}}parseTag(e,t,i){switch(e){case"desc":return this.parseDesc(t);case"mluc":return this.parseMluc(t);case"text":return this.parseText(t,i);case"sig ":return this.parseSig(t)}if(!(t+i>this.chunk.byteLength))return this.chunk.getUint8Array(t,i)}parseDesc(e){let t=this.chunk.getUint32(e+8)-1;return P(this.chunk.getString(e+12,t))}parseText(e,t){return P(this.chunk.getString(e+8,t-8))}parseSig(e){return P(this.chunk.getString(e+8,4))}parseMluc(e){let{chunk:t}=this,i=t.getUint32(e+8),n=t.getUint32(e+12),a=e+16,r=[];for(let s=0;s<i;s++){let i=t.getString(a+0,2),s=t.getString(a+2,2),o=t.getUint32(a+4),l=t.getUint32(a+8)+e,h=P(t.getUnicodeString(l,o));r.push({lang:i,country:s,text:h}),a+=n}return 1===i?r[0].text:r}translateValue(e,t){return"string"==typeof e?t[e]||t[e.toLowerCase()]||e:t[e]||e}}m(e7,"type","icc"),m(e7,"multiSegment",!0),m(e7,"headerLength",18);let e9={4:e6,8:function(e,t){return[e.getUint8(t),e.getUint8(t+1)>>4,e.getUint8(t+1)%16].map(e=>e.toString(10)).join(".")},12:e6,16:e6,20:e6,24:function(e,t){let i=e.getUint16(t),n=e.getUint16(t+2)-1,a=e.getUint16(t+4),r=e.getUint16(t+6);return new Date(Date.UTC(i,n,a,r,e.getUint16(t+8),e.getUint16(t+10)))},36:e6,40:e6,48:e6,52:e6,64:(e,t)=>e.getUint32(t),80:e6};function e6(e,t){return P(e.getString(t,4))}D.set("icc",e7),B(N,"icc",[[4,"ProfileCMMType"],[8,"ProfileVersion"],[12,"ProfileClass"],[16,"ColorSpaceData"],[20,"ProfileConnectionSpace"],[24,"ProfileDateTime"],[36,"ProfileFileSignature"],[40,"PrimaryPlatform"],[44,"CMMFlags"],[48,"DeviceManufacturer"],[52,"DeviceModel"],[56,"DeviceAttributes"],[64,"RenderingIntent"],[68,"ConnectionSpaceIlluminant"],[80,"ProfileCreator"],[84,"ProfileID"],["Header","ProfileHeader"],["MS00","WCSProfiles"],["bTRC","BlueTRC"],["bXYZ","BlueMatrixColumn"],["bfd","UCRBG"],["bkpt","MediaBlackPoint"],["calt","CalibrationDateTime"],["chad","ChromaticAdaptation"],["chrm","Chromaticity"],["ciis","ColorimetricIntentImageState"],["clot","ColorantTableOut"],["clro","ColorantOrder"],["clrt","ColorantTable"],["cprt","ProfileCopyright"],["crdi","CRDInfo"],["desc","ProfileDescription"],["devs","DeviceSettings"],["dmdd","DeviceModelDesc"],["dmnd","DeviceMfgDesc"],["dscm","ProfileDescriptionML"],["fpce","FocalPlaneColorimetryEstimates"],["gTRC","GreenTRC"],["gXYZ","GreenMatrixColumn"],["gamt","Gamut"],["kTRC","GrayTRC"],["lumi","Luminance"],["meas","Measurement"],["meta","Metadata"],["mmod","MakeAndModel"],["ncl2","NamedColor2"],["ncol","NamedColor"],["ndin","NativeDisplayInfo"],["pre0","Preview0"],["pre1","Preview1"],["pre2","Preview2"],["ps2i","PS2RenderingIntent"],["ps2s","PostScript2CSA"],["psd0","PostScript2CRD0"],["psd1","PostScript2CRD1"],["psd2","PostScript2CRD2"],["psd3","PostScript2CRD3"],["pseq","ProfileSequenceDesc"],["psid","ProfileSequenceIdentifier"],["psvm","PS2CRDVMSize"],["rTRC","RedTRC"],["rXYZ","RedMatrixColumn"],["resp","OutputResponse"],["rhoc","ReflectionHardcopyOrigColorimetry"],["rig0","PerceptualRenderingIntentGamut"],["rig2","SaturationRenderingIntentGamut"],["rpoc","ReflectionPrintOutputColorimetry"],["sape","SceneAppearanceEstimates"],["scoe","SceneColorimetryEstimates"],["scrd","ScreeningDesc"],["scrn","Screening"],["targ","CharTarget"],["tech","Technology"],["vcgt","VideoCardGamma"],["view","ViewingConditions"],["vued","ViewingCondDesc"],["wtpt","MediaWhitePoint"]]);let te={"4d2p":"Erdt Systems",AAMA:"Aamazing Technologies",ACER:"Acer",ACLT:"Acolyte Color Research",ACTI:"Actix Sytems",ADAR:"Adara Technology",ADBE:"Adobe",ADI:"ADI Systems",AGFA:"Agfa Graphics",ALMD:"Alps Electric",ALPS:"Alps Electric",ALWN:"Alwan Color Expertise",AMTI:"Amiable Technologies",AOC:"AOC International",APAG:"Apago",APPL:"Apple Computer",AST:"AST","AT&T":"AT&T",BAEL:"BARBIERI electronic",BRCO:"Barco NV",BRKP:"Breakpoint",BROT:"Brother",BULL:"Bull",BUS:"Bus Computer Systems","C-IT":"C-Itoh",CAMR:"Intel",CANO:"Canon",CARR:"Carroll Touch",CASI:"Casio",CBUS:"Colorbus PL",CEL:"Crossfield",CELx:"Crossfield",CGS:"CGS Publishing Technologies International",CHM:"Rochester Robotics",CIGL:"Colour Imaging Group, London",CITI:"Citizen",CL00:"Candela",CLIQ:"Color IQ",CMCO:"Chromaco",CMiX:"CHROMiX",COLO:"Colorgraphic Communications",COMP:"Compaq",COMp:"Compeq/Focus Technology",CONR:"Conrac Display Products",CORD:"Cordata Technologies",CPQ:"Compaq",CPRO:"ColorPro",CRN:"Cornerstone",CTX:"CTX International",CVIS:"ColorVision",CWC:"Fujitsu Laboratories",DARI:"Darius Technology",DATA:"Dataproducts",DCP:"Dry Creek Photo",DCRC:"Digital Contents Resource Center, Chung-Ang University",DELL:"Dell Computer",DIC:"Dainippon Ink and Chemicals",DICO:"Diconix",DIGI:"Digital","DL&C":"Digital Light & Color",DPLG:"Doppelganger",DS:"Dainippon Screen",DSOL:"DOOSOL",DUPN:"DuPont",EPSO:"Epson",ESKO:"Esko-Graphics",ETRI:"Electronics and Telecommunications Research Institute",EVER:"Everex Systems",EXAC:"ExactCODE",Eizo:"Eizo",FALC:"Falco Data Products",FF:"Fuji Photo Film",FFEI:"FujiFilm Electronic Imaging",FNRD:"Fnord Software",FORA:"Fora",FORE:"Forefront Technology",FP:"Fujitsu",FPA:"WayTech Development",FUJI:"Fujitsu",FX:"Fuji Xerox",GCC:"GCC Technologies",GGSL:"Global Graphics Software",GMB:"Gretagmacbeth",GMG:"GMG",GOLD:"GoldStar Technology",GOOG:"Google",GPRT:"Giantprint",GTMB:"Gretagmacbeth",GVC:"WayTech Development",GW2K:"Sony",HCI:"HCI",HDM:"Heidelberger Druckmaschinen",HERM:"Hermes",HITA:"Hitachi America",HP:"Hewlett-Packard",HTC:"Hitachi",HiTi:"HiTi Digital",IBM:"IBM",IDNT:"Scitex",IEC:"Hewlett-Packard",IIYA:"Iiyama North America",IKEG:"Ikegami Electronics",IMAG:"Image Systems",IMI:"Ingram Micro",INTC:"Intel",INTL:"N/A (INTL)",INTR:"Intra Electronics",IOCO:"Iocomm International Technology",IPS:"InfoPrint Solutions Company",IRIS:"Scitex",ISL:"Ichikawa Soft Laboratory",ITNL:"N/A (ITNL)",IVM:"IVM",IWAT:"Iwatsu Electric",Idnt:"Scitex",Inca:"Inca Digital Printers",Iris:"Scitex",JPEG:"Joint Photographic Experts Group",JSFT:"Jetsoft Development",JVC:"JVC Information Products",KART:"Scitex",KFC:"KFC Computek Components",KLH:"KLH Computers",KMHD:"Konica Minolta",KNCA:"Konica",KODA:"Kodak",KYOC:"Kyocera",Kart:"Scitex",LCAG:"Leica",LCCD:"Leeds Colour",LDAK:"Left Dakota",LEAD:"Leading Technology",LEXM:"Lexmark International",LINK:"Link Computer",LINO:"Linotronic",LITE:"Lite-On",Leaf:"Leaf",Lino:"Linotronic",MAGC:"Mag Computronic",MAGI:"MAG Innovision",MANN:"Mannesmann",MICN:"Micron Technology",MICR:"Microtek",MICV:"Microvitec",MINO:"Minolta",MITS:"Mitsubishi Electronics America",MITs:"Mitsuba",MNLT:"Minolta",MODG:"Modgraph",MONI:"Monitronix",MONS:"Monaco Systems",MORS:"Morse Technology",MOTI:"Motive Systems",MSFT:"Microsoft",MUTO:"MUTOH INDUSTRIES",Mits:"Mitsubishi Electric",NANA:"NANAO",NEC:"NEC",NEXP:"NexPress Solutions",NISS:"Nissei Sangyo America",NKON:"Nikon",NONE:"none",OCE:"Oce Technologies",OCEC:"OceColor",OKI:"Oki",OKID:"Okidata",OKIP:"Okidata",OLIV:"Olivetti",OLYM:"Olympus",ONYX:"Onyx Graphics",OPTI:"Optiquest",PACK:"Packard Bell",PANA:"Matsushita Electric Industrial",PANT:"Pantone",PBN:"Packard Bell",PFU:"PFU",PHIL:"Philips Consumer Electronics",PNTX:"HOYA",POne:"Phase One A/S",PREM:"Premier Computer Innovations",PRIN:"Princeton Graphic Systems",PRIP:"Princeton Publishing Labs",QLUX:"Hong Kong",QMS:"QMS",QPCD:"QPcard AB",QUAD:"QuadLaser",QUME:"Qume",RADI:"Radius",RDDx:"Integrated Color Solutions",RDG:"Roland DG",REDM:"REDMS Group",RELI:"Relisys",RGMS:"Rolf Gierling Multitools",RICO:"Ricoh",RNLD:"Edmund Ronald",ROYA:"Royal",RPC:"Ricoh Printing Systems",RTL:"Royal Information Electronics",SAMP:"Sampo",SAMS:"Samsung",SANT:"Jaime Santana Pomares",SCIT:"Scitex",SCRN:"Dainippon Screen",SDP:"Scitex",SEC:"Samsung",SEIK:"Seiko Instruments",SEIk:"Seikosha",SGUY:"ScanGuy.com",SHAR:"Sharp Laboratories",SICC:"International Color Consortium",SONY:"Sony",SPCL:"SpectraCal",STAR:"Star",STC:"Sampo Technology",Scit:"Scitex",Sdp:"Scitex",Sony:"Sony",TALO:"Talon Technology",TAND:"Tandy",TATU:"Tatung",TAXA:"TAXAN America",TDS:"Tokyo Denshi Sekei",TECO:"TECO Information Systems",TEGR:"Tegra",TEKT:"Tektronix",TI:"Texas Instruments",TMKR:"TypeMaker",TOSB:"Toshiba",TOSH:"Toshiba",TOTK:"TOTOKU ELECTRIC",TRIU:"Triumph",TSBT:"Toshiba",TTX:"TTX Computer Products",TVM:"TVM Professional Monitor",TW:"TW Casper",ULSX:"Ulead Systems",UNIS:"Unisys",UTZF:"Utz Fehlau & Sohn",VARI:"Varityper",VIEW:"Viewsonic",VISL:"Visual communication",VIVO:"Vivo Mobile Communication",WANG:"Wang",WLBR:"Wilbur Imaging",WTG2:"Ware To Go",WYSE:"WYSE Technology",XERX:"Xerox",XRIT:"X-Rite",ZRAN:"Zoran",Zebr:"Zebra Technologies",appl:"Apple Computer",bICC:"basICColor",berg:"bergdesign",ceyd:"Integrated Color Solutions",clsp:"MacDermid ColorSpan",ds:"Dainippon Screen",dupn:"DuPont",ffei:"FujiFilm Electronic Imaging",flux:"FluxData",iris:"Scitex",kart:"Scitex",lcms:"Little CMS",lino:"Linotronic",none:"none",ob4d:"Erdt Systems",obic:"Medigraph",quby:"Qubyx Sarl",scit:"Scitex",scrn:"Dainippon Screen",sdp:"Scitex",siwi:"SIWI GRAFIKA",yxym:"YxyMaster"},tt={scnr:"Scanner",mntr:"Monitor",prtr:"Printer",link:"Device Link",abst:"Abstract",spac:"Color Space Conversion Profile",nmcl:"Named Color",cenc:"ColorEncodingSpace profile",mid:"MultiplexIdentification profile",mlnk:"MultiplexLink profile",mvis:"MultiplexVisualization profile",nkpf:"Nikon Input Device Profile (NON-STANDARD!)"};B(j,"icc",[[4,te],[12,tt],[40,Object.assign({},te,tt)],[48,te],[80,te],[64,{0:"Perceptual",1:"Relative Colorimetric",2:"Saturation",3:"Absolute Colorimetric"}],["tech",{amd:"Active Matrix Display",crt:"Cathode Ray Tube Display",kpcd:"Photo CD",pmd:"Passive Matrix Display",dcam:"Digital Camera",dcpj:"Digital Cinema Projector",dmpc:"Digital Motion Picture Camera",dsub:"Dye Sublimation Printer",epho:"Electrophotographic Printer",esta:"Electrostatic Printer",flex:"Flexography",fprn:"Film Writer",fscn:"Film Scanner",grav:"Gravure",ijet:"Ink Jet Printer",imgs:"Photo Image Setter",mpfr:"Motion Picture Film Recorder",mpfs:"Motion Picture Film Scanner",offs:"Offset Lithography",pjtv:"Projection Television",rpho:"Photographic Paper Printer",rscn:"Reflective Scanner",silk:"Silkscreen",twax:"Thermal Wax Printer",vidc:"Video Camera",vidm:"Video Monitor"}]]);class ti extends el{static canHandle(e,t,i){return 237===e.getUint8(t+1)&&"Photoshop"===e.getString(t+4,9)&&void 0!==this.containsIptc8bim(e,t,i)}static headerLength(e,t,i){let n,a=this.containsIptc8bim(e,t,i);if(void 0!==a)return(n=e.getUint8(t+a+7))%2!=0&&(n+=1),0===n&&(n=4),a+8+n}static containsIptc8bim(e,t,i){for(let n=0;n<i;n++)if(this.isIptcSegmentHead(e,t+n))return n}static isIptcSegmentHead(e,t){return 56===e.getUint8(t)&&0x3842494d===e.getUint32(t)&&1028===e.getUint16(t+4)}parse(){let{raw:e}=this,t=this.chunk.byteLength-1,i=!1;for(let n=0;n<t;n++)if(28===this.chunk.getUint8(n)&&2===this.chunk.getUint8(n+1)){i=!0;let t=this.chunk.getUint16(n+3),a=this.chunk.getUint8(n+2),r=this.chunk.getLatin1String(n+5,t);e.set(a,this.pluralizeValue(e.get(a),r)),n+=4+t}else if(i)break;return this.translate(),this.output}pluralizeValue(e,t){return void 0!==e?e instanceof Array?(e.push(t),e):[e,t]:t}}m(ti,"type","iptc"),m(ti,"translateValues",!1),m(ti,"reviveValues",!1),D.set("iptc",ti),B(N,"iptc",[[0,"ApplicationRecordVersion"],[3,"ObjectTypeReference"],[4,"ObjectAttributeReference"],[5,"ObjectName"],[7,"EditStatus"],[8,"EditorialUpdate"],[10,"Urgency"],[12,"SubjectReference"],[15,"Category"],[20,"SupplementalCategories"],[22,"FixtureIdentifier"],[25,"Keywords"],[26,"ContentLocationCode"],[27,"ContentLocationName"],[30,"ReleaseDate"],[35,"ReleaseTime"],[37,"ExpirationDate"],[38,"ExpirationTime"],[40,"SpecialInstructions"],[42,"ActionAdvised"],[45,"ReferenceService"],[47,"ReferenceDate"],[50,"ReferenceNumber"],[55,"DateCreated"],[60,"TimeCreated"],[62,"DigitalCreationDate"],[63,"DigitalCreationTime"],[65,"OriginatingProgram"],[70,"ProgramVersion"],[75,"ObjectCycle"],[80,"Byline"],[85,"BylineTitle"],[90,"City"],[92,"Sublocation"],[95,"State"],[100,"CountryCode"],[101,"Country"],[103,"OriginalTransmissionReference"],[105,"Headline"],[110,"Credit"],[115,"Source"],[116,"CopyrightNotice"],[118,"Contact"],[120,"Caption"],[121,"LocalCaption"],[122,"Writer"],[125,"RasterizedCaption"],[130,"ImageType"],[131,"ImageOrientation"],[135,"LanguageIdentifier"],[150,"AudioType"],[151,"AudioSamplingRate"],[152,"AudioSamplingResolution"],[153,"AudioDuration"],[154,"AudioOutcue"],[184,"JobID"],[185,"MasterDocumentID"],[186,"ShortDocumentID"],[187,"UniqueDocumentID"],[188,"OwnerID"],[200,"ObjectPreviewFileFormat"],[201,"ObjectPreviewFileVersion"],[202,"ObjectPreviewData"],[221,"Prefs"],[225,"ClassifyState"],[228,"SimilarityIndex"],[230,"DocumentNotes"],[231,"DocumentHistory"],[232,"ExifCameraInfo"],[255,"CatalogSets"]]),B(j,"iptc",[[10,{0:"0 (reserved)",1:"1 (most urgent)",2:"2",3:"3",4:"4",5:"5 (normal urgency)",6:"6",7:"7",8:"8 (least urgent)",9:"9 (user-defined priority)"}],[75,{a:"Morning",b:"Both Morning and Evening",p:"Evening"}],[131,{L:"Landscape",P:"Portrait",S:"Square"}]])},89348:(e,t,i)=>{"use strict";var n,a,r,s;i.d(t,{i7:()=>w,LK:()=>s,ru:()=>a}),function(e){e.Unimplemented="UNIMPLEMENTED",e.Unavailable="UNAVAILABLE"}(n||(n={}));class o extends Error{constructor(e,t,i){super(e),this.message=e,this.code=t,this.data=i}}let l=e=>{var t,i;return(null==e?void 0:e.androidBridge)?"android":(null==(i=null==(t=null==e?void 0:e.webkit)?void 0:t.messageHandlers)?void 0:i.bridge)?"ios":"web"},h=e=>{let t=e.CapacitorCustomPlatform||null,i=e.Capacitor||{},a=i.Plugins=i.Plugins||{},r=()=>null!==t?t.name:l(e),s=e=>{var t;return null==(t=i.PluginHeaders)?void 0:t.find(t=>t.name===e)},h=new Map;return i.convertFileSrc||(i.convertFileSrc=e=>e),i.getPlatform=r,i.handleError=t=>e.console.error(t),i.isNativePlatform=()=>"web"!==r(),i.isPluginAvailable=e=>{let t=h.get(e);return!!((null==t?void 0:t.platforms.has(r()))||s(e))},i.registerPlugin=(e,l={})=>{let c,u=h.get(e);if(u)return console.warn(`Capacitor plugin "${e}" already registered. Cannot register plugins twice.`),u.proxy;let d=r(),p=s(e),f=async()=>(!c&&d in l?c=c="function"==typeof l[d]?await l[d]():l[d]:null!==t&&!c&&"web"in l&&(c=c="function"==typeof l.web?await l.web():l.web),c),m=(t,a)=>{var r,s;if(p){let n=null==p?void 0:p.methods.find(e=>a===e.name);if(n)if("promise"===n.rtype)return t=>i.nativePromise(e,a.toString(),t);else return(t,n)=>i.nativeCallback(e,a.toString(),t,n);if(t)return null==(r=t[a])?void 0:r.bind(t)}else if(t)return null==(s=t[a])?void 0:s.bind(t);else throw new o(`"${e}" plugin is not implemented on ${d}`,n.Unimplemented)},g=t=>{let i,a=(...a)=>{let r=f().then(r=>{let s=m(r,t);if(s){let e=s(...a);return i=null==e?void 0:e.remove,e}throw new o(`"${e}.${t}()" is not implemented on ${d}`,n.Unimplemented)});return"addListener"===t&&(r.remove=async()=>i()),r};return a.toString=()=>`${t.toString()}() { [capacitor code] }`,Object.defineProperty(a,"name",{value:t,writable:!1,configurable:!1}),a},y=g("addListener"),S=g("removeListener"),C=(e,t)=>{let i=y({eventName:e},t),n=async()=>{S({eventName:e,callbackId:await i},t)},a=new Promise(e=>i.then(()=>e({remove:n})));return a.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await n()},a},P=new Proxy({},{get(e,t){switch(t){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return p?C:y;case"removeListener":return S;default:return g(t)}}});return a[e]=P,h.set(e,{name:e,proxy:P,platforms:new Set([...Object.keys(l),...p?[d]:[]])}),P},i.Exception=o,i.DEBUG=!!i.DEBUG,i.isLoggingEnabled=!!i.isLoggingEnabled,i},c=(e=>e.Capacitor=h(e))("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==i.g?i.g:{}),u=c.registerPlugin;class d{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,t){let i=!1;this.listeners[e]||(this.listeners[e]=[],i=!0),this.listeners[e].push(t);let n=this.windowListeners[e];return n&&!n.registered&&this.addWindowListener(n),i&&this.sendRetainedArgumentsForEvent(e),Promise.resolve({remove:async()=>this.removeListener(e,t)})}async removeAllListeners(){for(let e in this.listeners={},this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,t,i){let n=this.listeners[e];if(!n){if(i){let i=this.retainedEventArguments[e];i||(i=[]),i.push(t),this.retainedEventArguments[e]=i}return}n.forEach(e=>e(t))}hasListeners(e){return!!this.listeners[e].length}registerWindowListener(e,t){this.windowListeners[t]={registered:!1,windowEventName:e,pluginEventName:t,handler:e=>{this.notifyListeners(t,e)}}}unimplemented(e="not implemented"){return new c.Exception(e,n.Unimplemented)}unavailable(e="not available"){return new c.Exception(e,n.Unavailable)}async removeListener(e,t){let i=this.listeners[e];if(!i)return;let n=i.indexOf(t);this.listeners[e].splice(n,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){let t=this.retainedEventArguments[e];t&&(delete this.retainedEventArguments[e],t.forEach(t=>{this.notifyListeners(e,t)}))}}let p=e=>encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),f=e=>e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class m extends d{async getCookies(){let e=document.cookie,t={};return e.split(";").forEach(e=>{if(e.length<=0)return;let[i,n]=e.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");i=f(i).trim(),n=f(n).trim(),t[i]=n}),t}async setCookie(e){try{let t=p(e.key),i=p(e.value),n=`; expires=${(e.expires||"").replace("expires=","")}`,a=(e.path||"/").replace("path=",""),r=null!=e.url&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${t}=${i||""}${n}; path=${a}; ${r};`}catch(e){return Promise.reject(e)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(e){return Promise.reject(e)}}async clearCookies(){try{for(let e of document.cookie.split(";")||[])document.cookie=e.replace(/^ +/,"").replace(/=.*/,`=;expires=${new Date().toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}u("CapacitorCookies",{web:()=>new m});let g=async e=>new Promise((t,i)=>{let n=new FileReader;n.onload=()=>{let e=n.result;t(e.indexOf(",")>=0?e.split(",")[1]:e)},n.onerror=e=>i(e),n.readAsDataURL(e)}),y=(e={})=>{let t=Object.keys(e);return Object.keys(e).map(e=>e.toLocaleLowerCase()).reduce((i,n,a)=>(i[n]=e[t[a]],i),{})},S=(e,t=!0)=>e?Object.entries(e).reduce((e,i)=>{let n,a,[r,s]=i;return Array.isArray(s)?(a="",s.forEach(e=>{n=t?encodeURIComponent(e):e,a+=`${r}=${n}&`}),a.slice(0,-1)):(n=t?encodeURIComponent(s):s,a=`${r}=${n}`),`${e}&${a}`},"").substr(1):null,C=(e,t={})=>{let i=Object.assign({method:e.method||"GET",headers:e.headers},t),n=y(e.headers)["content-type"]||"";if("string"==typeof e.data)i.body=e.data;else if(n.includes("application/x-www-form-urlencoded")){let t=new URLSearchParams;for(let[i,n]of Object.entries(e.data||{}))t.set(i,n);i.body=t.toString()}else if(n.includes("multipart/form-data")||e.data instanceof FormData){let t=new FormData;if(e.data instanceof FormData)e.data.forEach((e,i)=>{t.append(i,e)});else for(let i of Object.keys(e.data))t.append(i,e.data[i]);i.body=t;let n=new Headers(i.headers);n.delete("content-type"),i.headers=n}else(n.includes("application/json")||"object"==typeof e.data)&&(i.body=JSON.stringify(e.data));return i};class P extends d{async request(e){let t,i,n=C(e,e.webFetchExtra),a=S(e.params,e.shouldEncodeUrlParams),r=a?`${e.url}?${a}`:e.url,s=await fetch(r,n),o=s.headers.get("content-type")||"",{responseType:l="text"}=s.ok?e:{};switch(o.includes("application/json")&&(l="json"),l){case"arraybuffer":case"blob":i=await s.blob(),t=await g(i);break;case"json":t=await s.json();break;default:t=await s.text()}let h={};return s.headers.forEach((e,t)=>{h[t]=e}),{data:t,headers:h,status:s.status,url:s.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}u("CapacitorHttp",{web:()=>new P}),function(e){e.Prompt="PROMPT",e.Camera="CAMERA",e.Photos="PHOTOS"}(a||(a={})),function(e){e.Rear="REAR",e.Front="FRONT"}(r||(r={})),function(e){e.Uri="uri",e.Base64="base64",e.DataUrl="dataUrl"}(s||(s={}));class b extends d{async getPhoto(e){return new Promise(async(t,i)=>{if(e.webUseInput||e.source===a.Photos)this.fileInputExperience(e,t,i);else if(e.source===a.Prompt){let n=document.querySelector("pwa-action-sheet");n||(n=document.createElement("pwa-action-sheet"),document.body.appendChild(n)),n.header=e.promptLabelHeader||"Photo",n.cancelable=!1,n.options=[{title:e.promptLabelPhoto||"From Photos"},{title:e.promptLabelPicture||"Take Picture"}],n.addEventListener("onSelection",async n=>{0===n.detail?this.fileInputExperience(e,t,i):this.cameraExperience(e,t,i)})}else this.cameraExperience(e,t,i)})}async pickImages(e){return new Promise(async(e,t)=>{this.multipleFileInputExperience(e,t)})}async cameraExperience(e,t,i){if(customElements.get("pwa-camera-modal")){let n=document.createElement("pwa-camera-modal");n.facingMode=e.direction===r.Front?"user":"environment",document.body.appendChild(n);try{await n.componentOnReady(),n.addEventListener("onPhoto",async a=>{let r=a.detail;null===r?i(new o("User cancelled photos app")):r instanceof Error?i(r):t(await this._getCameraPhoto(r,e)),n.dismiss(),document.body.removeChild(n)}),n.present()}catch(n){this.fileInputExperience(e,t,i)}}else console.error("Unable to load PWA Element 'pwa-camera-modal'. See the docs: https://capacitorjs.com/docs/web/pwa-elements."),this.fileInputExperience(e,t,i)}fileInputExperience(e,t,i){let n=document.querySelector("#_capacitor-camera-input"),s=()=>{var e;null==(e=n.parentNode)||e.removeChild(n)};n||((n=document.createElement("input")).id="_capacitor-camera-input",n.type="file",n.hidden=!0,document.body.appendChild(n),n.addEventListener("change",i=>{let a=n.files[0],r="jpeg";if("image/png"===a.type?r="png":"image/gif"===a.type&&(r="gif"),"dataUrl"===e.resultType||"base64"===e.resultType){let i=new FileReader;i.addEventListener("load",()=>{"dataUrl"===e.resultType?t({dataUrl:i.result,format:r}):"base64"===e.resultType&&t({base64String:i.result.split(",")[1],format:r}),s()}),i.readAsDataURL(a)}else t({webPath:URL.createObjectURL(a),format:r}),s()}),n.addEventListener("cancel",e=>{i(new o("User cancelled photos app")),s()})),n.accept="image/*",n.capture=!0,e.source===a.Photos||e.source===a.Prompt?n.removeAttribute("capture"):e.direction===r.Front?n.capture="user":e.direction===r.Rear&&(n.capture="environment"),n.click()}multipleFileInputExperience(e,t){let i=document.querySelector("#_capacitor-camera-input-multiple"),n=()=>{var e;null==(e=i.parentNode)||e.removeChild(i)};i||((i=document.createElement("input")).id="_capacitor-camera-input-multiple",i.type="file",i.hidden=!0,i.multiple=!0,document.body.appendChild(i),i.addEventListener("change",t=>{let a=[];for(let e=0;e<i.files.length;e++){let t=i.files[e],n="jpeg";"image/png"===t.type?n="png":"image/gif"===t.type&&(n="gif"),a.push({webPath:URL.createObjectURL(t),format:n})}e({photos:a}),n()}),i.addEventListener("cancel",e=>{t(new o("User cancelled photos app")),n()})),i.accept="image/*",i.click()}_getCameraPhoto(e,t){return new Promise((i,n)=>{let a=new FileReader,r=e.type.split("/")[1];"uri"===t.resultType?i({webPath:URL.createObjectURL(e),format:r,saved:!1}):(a.readAsDataURL(e),a.onloadend=()=>{let e=a.result;"dataUrl"===t.resultType?i({dataUrl:e,format:r,saved:!1}):i({base64String:e.split(",")[1],format:r,saved:!1})},a.onerror=e=>{n(e)})})}async checkPermissions(){if("undefined"==typeof navigator||!navigator.permissions)throw this.unavailable("Permissions API not available in this browser");try{return{camera:(await window.navigator.permissions.query({name:"camera"})).state,photos:"granted"}}catch(e){throw this.unavailable("Camera permissions are not available in this browser")}}async requestPermissions(){throw this.unimplemented("Not implemented on web.")}async pickLimitedLibraryPhotos(){throw this.unavailable("Not implemented on web.")}async getLimitedLibraryPhotos(){throw this.unavailable("Not implemented on web.")}}new b;let w=u("Camera",{web:()=>new b})},91867:(e,t,i)=>{"use strict";i.d(t,{A:()=>h});var n=i(29300),a=i.n(n),r=i(12115),s=i(97390),o=i(95155);let l=r.forwardRef((e,t)=>{let{bsPrefix:i,variant:n,animation:r="border",size:l,as:h="div",className:c,...u}=e;i=(0,s.oU)(i,"spinner");let d="".concat(i,"-").concat(r);return(0,o.jsx)(h,{ref:t,...u,className:a()(c,d,l&&"".concat(d,"-").concat(l),n&&"text-".concat(n))})});l.displayName="Spinner";let h=l}}]);