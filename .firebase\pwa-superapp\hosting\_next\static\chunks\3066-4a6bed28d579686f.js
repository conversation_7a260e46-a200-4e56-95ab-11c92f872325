"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3066],{16344:(e,t,a)=>{a.d(t,{A:()=>v});var o=a(95155),n=a(12115),i=a(91867),l=a(16639),s=a(38336),r=a(26957),c=a(15743),d=a.n(c),u=a(68086),m=a(43864),g=a(89348);let p=e=>d().GPSHelper.degToDmsRational(e),f=async()=>{if(!navigator.permissions||!navigator.geolocation)return!1;switch((await navigator.permissions.query({name:"geolocation"})).state){case"granted":return!0;case"prompt":return await new Promise(e=>navigator.geolocation.getCurrentPosition(()=>e(!0),()=>e(!1),{maximumAge:0,timeout:8e3}));default:return!1}},h=async()=>!!navigator.geolocation&&new Promise(e=>{navigator.geolocation.getCurrentPosition(t=>{console.log("Location permission granted, coordinates:",t.coords.latitude,t.coords.longitude),e(!0)},t=>{console.log("Location permission denied or error:",t.message),e(!1)},{enableHighAccuracy:!0,timeout:1e4,maximumAge:0})}),x=()=>new Promise(e=>{if(!navigator.geolocation)return e(null);let t=!1,a=setTimeout(()=>{t||(t=!0,e(null))},8e3);navigator.geolocation.getCurrentPosition(o=>{t||(t=!0,clearTimeout(a),e(o.coords))},()=>{t||(t=!0,clearTimeout(a),e(null))},{enableHighAccuracy:!0,maximumAge:1e4})}),b=async e=>"createImageBitmap"in window?await createImageBitmap(e):new Promise((t,a)=>{let o=new Image;o.onload=()=>t(o),o.onerror=a,o.src=URL.createObjectURL(e)}),w=async(e,t)=>{let a="AIzaSyDNdPSBifICIPY7YumDCko5JX5tTRkYYSk";if(!a)return null;let o=new AbortController,n=setTimeout(()=>o.abort(),4e3);try{let n="https://maps.googleapis.com/maps/api/geocode/json?latlng=".concat(e,",").concat(t,"&key=").concat(a),i=await fetch(n,{signal:o.signal});if(!i.ok)return null;let l=await i.json();if(l.results&&l.results.length>0){for(let e of l.results)if(e.types.includes("street_address")||e.types.includes("premise")||e.types.includes("route"))return e.formatted_address;let e=l.results[0].formatted_address,t=e.split(",");if(t.length>2)return t.slice(0,2).join(",");return e}return null}catch(e){return console.error("Error fetching address:",e),null}finally{clearTimeout(n)}},j=async e=>{if(!/\.jpe?g$/i.test(e.name))return console.log("Not a JPEG file, skipping GPS injection (but will still add footer):",e.name),e;console.log("Getting current coordinates...");let t=await x();if(!t)return console.log("Could not get coordinates, returning original file"),e;console.log("Got coordinates:",t.latitude,t.longitude);let a=await new Promise((t,a)=>{let o=new FileReader;o.onerror=()=>{console.error("FileReader error:",o.error),a(o.error)},o.onload=()=>t(o.result),o.readAsDataURL(e)});try{let o=d().load(a);o.GPS={[d().GPSIFD.GPSLatitudeRef]:t.latitude>=0?"N":"S",[d().GPSIFD.GPSLatitude]:p(Math.abs(t.latitude)),[d().GPSIFD.GPSLongitudeRef]:t.longitude>=0?"E":"W",[d().GPSIFD.GPSLongitude]:p(Math.abs(t.longitude)),[d().GPSIFD.GPSDateStamp]:new Date().toISOString().split("T")[0].replace(/-/g,":"),[d().GPSIFD.GPSVersionID]:[2,3,0,0]},console.log("Inserting GPS EXIF data into image");let n=d().insert(d().dump(o),a),i=atob(n.split(",")[1]);return new File([Uint8Array.from(i,e=>e.charCodeAt(0))],e.name,{type:"image/jpeg"})}catch(t){return console.error("Error injecting GPS data:",t),e}},y=async(e,t)=>{let a=await b(e),o=a.width,n=a.height,i=Math.max(16,.025*n),l=document.createElement("canvas").getContext("2d");l.font="".concat(i,"px sans-serif");let s=((e,t,a)=>{let o=t.split(" "),n=[],i=o[0];for(let t=1;t<o.length;t++){let l=o[t];e.measureText(i+" "+l).width<a?i+=" "+l:(n.push(i),i=l)}return n.push(i),n})(l,t,o-32),r=1.2*i,c=s.length*r+32,d=document.createElement("canvas");d.width=o,d.height=n+c;let u=d.getContext("2d");return u.drawImage(a,0,0),u.fillStyle="#f2f2f2",u.fillRect(0,n,o,c),u.font="".concat(i,"px sans-serif"),u.fillStyle="#000",u.textBaseline="top",s.forEach((e,t)=>{u.fillText(e,16,n+16+t*r)}),u.strokeStyle="#ddd",u.lineWidth=1,u.beginPath(),u.moveTo(0,n),u.lineTo(o,n),u.stroke(),new File([await new Promise(e=>d.toBlob(t=>e(t),"image/jpeg",.92))],e.name,{type:"image/jpeg"})},v=e=>{let{onFilesSelected:t,disabled:a,files:c}=e,[d,p]=(0,n.useState)([]),[b,v]=(0,n.useState)(!1),N=".jpg,.jpeg,.png,.gif,.bmp,.webp,.svg,.pdf,.xls,.xlsx".split(","),k=async e=>{p(t=>[...t,e.name]);try{var a,o,n;let i=new FormData;i.append("file",e);let l=await s.A.post(r.Dp,i,{headers:{"Content-Type":"multipart/form-data"}});(null==l?void 0:l.status)===200&&(null==(n=l.data)||null==(o=n.files)||null==(a=o[0])?void 0:a.originalname)&&t([...c,l.data.files[0].originalname])}finally{p(t=>t.filter(t=>t!==e.name))}},S=async e=>{for(let t of(v(!0),Array.from(e)))try{let e=t.name.substring(t.name.lastIndexOf(".")).toLowerCase();if(!N.includes(e)){alert("File format not allowed: ".concat(t.name));continue}let a=t;if(/\.(jpe?g|png|gif|bmp|webp)$/i.test(a.name))if(console.log("Attempting to inject GPS data for:",a.name),/\.jpe?g$/i.test(a.name)){a=await j(a);let e=await u.NZ(a).catch(e=>(console.error("Error reading GPS data:",e),null));if(e&&e.latitude&&e.longitude){console.log("Valid GPS coordinates found:",e.latitude,e.longitude);let t=await w(e.latitude,e.longitude);if(t)t="Location: ".concat(t);else{let a=(e,t)=>{let a=t?e>=0?"N":"S":e>=0?"E":"W";return"".concat(Math.abs(e).toFixed(6),"\xb0 ").concat(a)};t="Location: ".concat(a(e.latitude,!0),", ").concat(a(e.longitude,!1))}console.log("Adding location label:",t),a=await y(a,t),a=await j(a)}else console.log("No location data available for:",a.name),a=await y(a,"No location data available")}else{let e=await x();if(e){let t=await w(e.latitude,e.longitude);if(t)t="Location: ".concat(t);else{let a=(e,t)=>{let a=t?e>=0?"N":"S":e>=0?"E":"W";return"".concat(Math.abs(e).toFixed(6),"\xb0 ").concat(a)};t="Location: ".concat(a(e.latitude,!0),", ").concat(a(e.longitude,!1))}console.log("Adding location label to non-JPEG image:",t),a=await y(a,t)}else console.log("No location data available for non-JPEG image:",a.name),a=await y(a,"No location data available")}await k(a)}catch(e){console.error("Mobile/PWA upload error:",e),alert("Could not process ".concat(t.name,". Skipping."))}v(!1)},C=async e=>{e.target.files&&(Array.from(e.target.files).some(e=>/\.(jpe?g|png|gif|bmp|webp)$/i.test(e.name))?(console.log("Image files detected, checking location permission"),await f()||(window.confirm("Location access is needed to add location data to your photos.\n\n• Click OK to allow location access now\n• Click Cancel to continue without location data")?(console.log("Requesting location permission..."),await h()?console.log("Location permission granted successfully!"):alert("Location access was denied. To enable location for photos:\n\n1. Click the location icon in your browser's address bar\n2. Select 'Allow' for location access\n3. Try uploading again for location data\n\nContinuing to upload without location data...")):console.log("User chose to continue without location data"))):console.log("No image files detected, skipping location check"),S(e.target.files),e.target.value="")},A=async()=>{try{await f()||(window.confirm("Location access is needed to add location data to your photos.\n\n• Click OK to allow location access now\n• Click Cancel to continue without location data")?(console.log("Requesting location permission..."),await h()?console.log("Location permission granted successfully!"):alert("Location access was denied. To enable location for photos:\n\n1. Click the location icon in your browser's address bar\n2. Select 'Allow' for location access\n3. Try taking the photo again\n\nContinuing without location data...")):console.log("User chose to continue taking photo without location data"));let e=await g.i7.getPhoto({source:g.ru.Camera,resultType:g.LK.Uri,quality:80});if(!e.webPath)return;let t=await fetch(e.webPath).then(e=>e.blob()),a=new File([t],"camera_".concat(Date.now(),".jpg"),{type:"image/jpeg"});v(!0),S([a])}catch(e){console.error("Camera capture error:",e),alert("Could not capture photo. Please try again.")}};return(0,o.jsxs)("div",{className:"mt-3 position-relative",children:[b&&(0,o.jsx)("div",{className:"position-absolute top-0 start-0 w-100 h-100 bg-white bg-opacity-75 d-flex justify-content-center align-items-center",style:{zIndex:10,backdropFilter:"blur(2px)"},children:(0,o.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,o.jsx)(i.A,{animation:"border",variant:"primary"}),(0,o.jsx)("span",{className:"mt-2 text-primary fw-medium",children:"Processing..."})]})}),(0,o.jsxs)(l.A.Label,{className:"fw-medium mb-2",children:["Upload Files ",(0,o.jsx)("span",{className:"text-danger",children:"*"})]}),(0,o.jsxs)("div",{className:"upload-buttons-container p-3 mb-3 bg-light rounded-3 border",style:{backgroundColor:"#f0f1fe"},children:[(0,o.jsxs)("div",{className:"d-flex justify-content-center gap-3",children:[(0,o.jsxs)("label",{className:"btn px-3 py-2 d-flex align-items-center",style:{background:"#5a67d8",borderRadius:"8px",border:"none",color:"white",fontSize:"14px",fontWeight:"500",boxShadow:"0 2px 4px rgba(0,0,0,0.1)",width:"140px",justifyContent:"center"},children:[(0,o.jsx)("i",{className:"bi bi-cloud-arrow-up me-2"}),"Upload Files",(0,o.jsx)("input",{hidden:!0,multiple:!0,disabled:a,type:"file",accept:N.join(","),onChange:C})]}),(0,o.jsxs)("button",{className:"btn px-3 py-2 d-flex align-items-center",style:{background:"#38bdf8",borderRadius:"8px",border:"none",color:"white",fontSize:"14px",fontWeight:"500",boxShadow:"0 2px 4px rgba(0,0,0,0.1)",width:"140px",justifyContent:"center"},disabled:a,onClick:A,children:[(0,o.jsx)("i",{className:"bi bi-camera me-2"}),"Take Photo"]})]}),(0,o.jsx)("div",{className:"text-center mt-2"})]}),d.length>0&&(0,o.jsx)("div",{className:"mt-2 mb-3 p-2 bg-info bg-opacity-10 rounded-3 border border-info border-opacity-25",children:d.map(e=>(0,o.jsxs)("div",{className:"d-flex align-items-center gap-2 p-1",children:[(0,o.jsx)(i.A,{animation:"border",size:"sm",variant:"info"}),(0,o.jsxs)("span",{className:"text-info",children:["Uploading ",e,"..."]})]},e))}),c.length>0&&(0,o.jsxs)("div",{className:"uploaded-files-container mt-3",children:[(0,o.jsxs)("div",{className:"d-flex align-items-center mb-2",children:[(0,o.jsx)("i",{className:"bi bi-images me-2 text-secondary"}),(0,o.jsxs)("span",{className:"text-secondary",children:["Uploaded Files (",c.length,")"]})]}),(0,o.jsx)("div",{className:"row g-2",children:c.map((e,a)=>(0,o.jsx)("div",{className:"col-6",children:(0,o.jsxs)("div",{className:"position-relative border rounded p-2",style:{backgroundColor:"#f8f9fa",height:"100px"},children:[(0,o.jsx)("div",{className:"d-flex justify-content-center align-items-center h-100",style:{position:"relative"},children:(0,o.jsx)(m.A,{size:80,fileName:e,name:!1})}),(0,o.jsx)("button",{className:"btn btn-sm position-absolute top-0 end-0 p-0",style:{backgroundColor:"#f87171",borderRadius:"50%",width:"22px",height:"22px",display:"flex",justifyContent:"center",alignItems:"center",color:"white",border:"none",margin:"2px",zIndex:10},onClick:e=>{e.preventDefault(),e.stopPropagation();let o=[...c];o.splice(a,1),t(o)},children:(0,o.jsx)("i",{className:"bi bi-x",style:{fontSize:"14px"}})}),(0,o.jsx)("div",{className:"small text-truncate mt-1",style:{fontSize:"11px",color:"#666"},children:e.split("/").pop()})]})},a))}),(0,o.jsx)("div",{className:"mt-2 text-secondary",style:{fontSize:"13px"},children:"Upload any relevant photos or documents. You can select multiple files."})]})]})}},27347:(e,t,a)=>{a.d(t,{A:()=>r});var o=a(95155),n=a(12115),i=a(16639),l=a(60902),s=a(56160);let r=e=>{var t;let{title:a,options:r,selectedValue:c,disabled:d,onChange:u,placeholder:m="Select Option",clearable:g=!1,multiSelect:p=!1,selectedValues:f=[],onMultiChange:h,maxSelections:x=2}=e,[b,w]=(0,n.useState)(!1),[j,y]=(0,n.useState)(""),v=r.filter(e=>e.label.toLowerCase().includes(j.toLowerCase())),N=p?f.length>0?f.map(e=>{var t;return null==(t=r.find(t=>t.value===e))?void 0:t.label}).join(", "):m:(null==(t=r.find(e=>e.value===c))?void 0:t.label)||m,k=()=>{w(!1)},S=e=>{if(p&&h){let t=f||[];t.includes(e)?h(t.filter(t=>t!==e)):t.length<x&&h([...t,e])}else null==u||u(e),w(!1)};return(0,o.jsxs)("div",{className:"mb-3",children:[(0,o.jsx)(i.A.Label,{children:a}),(0,o.jsxs)("div",{className:"d-flex gap-2",children:[(0,o.jsx)(l.A,{disabled:d,variant:"outline-secondary",className:"flex-grow-1 text-start",onClick:()=>{w(!0),y("")},children:N}),g&&(p?f.length>0:c)&&(0,o.jsx)(l.A,{variant:"outline-danger",onClick:()=>{p&&h?h([]):null==u||u("")},children:"Clear"})]}),(0,o.jsxs)(s.A,{show:b,onHide:k,centered:!0,children:[(0,o.jsx)(s.A.Header,{closeButton:!0,children:(0,o.jsx)(s.A.Title,{children:a})}),(0,o.jsxs)(s.A.Body,{children:[(0,o.jsx)(i.A.Control,{type:"text",placeholder:"Search...",value:j,onChange:e=>y(e.target.value),className:"mb-3"}),v.length>0?(0,o.jsx)("div",{style:{maxHeight:"300px",overflowY:"auto"},children:v.map(e=>{let t=p?f.includes(e.value):e.value===c,a=p&&!t&&f.length>=x;return(0,o.jsxs)("button",{className:"list-group-item list-group-item-action mb-1 ".concat(t?"active":""," ").concat(a?"disabled":""),onClick:()=>!a&&S(e.value),disabled:a,children:[p&&(0,o.jsx)("input",{type:"checkbox",checked:t,onChange:()=>{},className:"me-2",disabled:a}),e.label]},e.value)})}):(0,o.jsx)("p",{className:"text-muted",children:"No options found."}),p&&(0,o.jsxs)("div",{className:"mt-2 text-muted small",children:["Selected: ",f.length,"/",x]})]}),(0,o.jsxs)(s.A.Footer,{children:[p&&(0,o.jsx)(l.A,{variant:"primary",onClick:k,className:"me-auto",children:"Done"}),(0,o.jsx)(l.A,{variant:"secondary",onClick:k,children:"Close"})]})]})]})}},43864:(e,t,a)=>{a.d(t,{A:()=>p});var o=a(95155),n=a(12115),i=a(26957),l=a(38336);let s=async e=>{try{return(await l.A.get((0,i._i)(e),{headers:{"Content-Type":"application/json"}})).data}catch(e){return console.error("Failed to fetch image URL:",e),null}},r=async e=>{try{let t=(await l.A.post(i.J9,{presignedUrl:e},{responseType:"blob"})).data;return new Promise((e,a)=>{let o=new FileReader;o.onloadend=()=>e(o.result),o.onerror=a,o.readAsDataURL(t)})}catch(e){throw console.error("Error fetching Data URL:",e),e}};var c=a(11518),d=a.n(c),u=a(36209);a(58561);var m=a(4178);let g=e=>{let{imageSrc:t}=e,[a,i]=(0,n.useState)(!1);return(0,o.jsxs)("div",{className:"jsx-15b99a83659358da container",children:[(0,o.jsx)("div",{className:"jsx-15b99a83659358da card",children:(0,o.jsxs)("div",{className:"jsx-15b99a83659358da body-blue text-center",children:[(0,o.jsx)("img",{src:t,alt:"Displayed",onClick:e=>{e.preventDefault(),e.stopPropagation(),i(!0)},style:{cursor:"pointer"},className:"jsx-15b99a83659358da display-image"}),a&&(0,o.jsx)(u.Ay,{open:a,close:()=>i(!1),slides:[{src:t}],plugins:[m.A],carousel:{finite:!0}})]})}),(0,o.jsx)(d(),{id:"15b99a83659358da",children:".display-image.jsx-15b99a83659358da{max-width:80px;max-height:80px;width:auto;height:auto;-o-object-fit:cover;object-fit:cover;cursor:pointer!important;-webkit-transition:-webkit-transform.3s ease-in-out;-moz-transition:-moz-transform.3s ease-in-out;-o-transition:-o-transform.3s ease-in-out;transition:-webkit-transform.3s ease-in-out;transition:-moz-transform.3s ease-in-out;transition:-o-transform.3s ease-in-out;transition:transform.3s ease-in-out;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;border:1px solid#dee2e6;pointer-events:auto;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.display-image.jsx-15b99a83659358da:hover{-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-ms-transform:scale(1.05);-o-transform:scale(1.05);transform:scale(1.05);-webkit-box-shadow:0 2px 8px rgba(0,0,0,.15);-moz-box-shadow:0 2px 8px rgba(0,0,0,.15);box-shadow:0 2px 8px rgba(0,0,0,.15)}.container.jsx-15b99a83659358da{padding:0;margin:0;max-width:none;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.card.jsx-15b99a83659358da{border:none;background:none;margin:0;padding:0;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.body-blue.jsx-15b99a83659358da{padding:0;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}"})]})},p=e=>{let{fileName:t,size:a=100,name:i=!1}=e,[l,c]=(0,n.useState)(null),[d,u]=(0,n.useState)(null);if((0,n.useEffect)(()=>{(async()=>{try{var e;let a=await s(t);c(a);let o=null==(e=t.split(".").pop())?void 0:e.toLowerCase();if(o&&["jpg","jpeg","png","gif","bmp","webp"].includes(o)){let e=await r(a);u(e)}}catch(e){console.error("Error fetching file or data URL:",e)}})()},[t]),!l)return(0,o.jsx)("p",{children:"Loading..."});let m=(e=>{var t;let a=null==(t=e.split(".").pop())?void 0:t.toLowerCase();return a?["jpg","jpeg","png","gif","bmp","webp"].includes(a)?"image":["pdf"].includes(a)?"pdf":["xls","xlsx"].includes(a)?"xls":"other":"other"})(t),p=t.replace(/^\d+[\s-_]*/,"");switch(m){case"image":return(0,o.jsxs)("div",{className:"d-flex flex-column align-items-center",style:{padding:"4px"},children:[d?(0,o.jsx)(g,{imageSrc:d}):(0,o.jsx)("div",{className:"d-flex align-items-center justify-content-center bg-light border rounded",style:{width:a,height:a},children:(0,o.jsx)("div",{className:"spinner-border spinner-border-sm text-primary",role:"status",children:(0,o.jsx)("span",{className:"visually-hidden",children:"Loading..."})})}),i&&(0,o.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center",fontSize:"10px",margin:"2px 0 0 0"},children:p})]});case"pdf":return(0,o.jsxs)("div",{className:"d-flex flex-column align-items-center p-2",children:[(0,o.jsx)("a",{href:l,target:"_blank",rel:"noopener noreferrer",children:(0,o.jsx)("i",{className:"bi bi-file-pdf-fill fs-1 text-danger"})}),i&&(0,o.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center"},children:p})]});case"xls":return(0,o.jsxs)("div",{className:"d-flex flex-column align-items-center p-2",children:[(0,o.jsx)("a",{href:l,target:"_blank",rel:"noopener noreferrer",children:(0,o.jsx)("i",{className:"bi bi-file-excel-fill fs-1 text-success"})}),i&&(0,o.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center"},children:p})]});default:return(0,o.jsxs)("div",{className:"d-flex flex-column align-items-center p-2",children:[(0,o.jsx)("a",{href:l,target:"_blank",rel:"noopener noreferrer",children:(0,o.jsx)("i",{className:"bi bi-file-earmark-fill fs-1 text-secondary"})}),i&&(0,o.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center"},children:p})]})}}}}]);