(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4579],{26957:(e,t,s)=>{"use strict";s.d(t,{AM:()=>n,Dp:()=>d,F4:()=>R,FI:()=>S,H$:()=>r,J9:()=>g,Jo:()=>f,K9:()=>c,M6:()=>m,MO:()=>u,OT:()=>A,P4:()=>j,UR:()=>I,WD:()=>k,WH:()=>v,WU:()=>C,_i:()=>i,bW:()=>N,dG:()=>a,dm:()=>z,iJ:()=>T,mh:()=>D,oo:()=>p,pZ:()=>y,u3:()=>l,x2:()=>b,xE:()=>o,xo:()=>w,yo:()=>x,zP:()=>h});let r="https://client-api.acuizen.com",a=r+"/login-configs",o=r+"/services",i=e=>r+"/files/"+e+"/presigned-url",n=r+"/users/me",c=r+"/dynamic-titles",l=r+"/users/get_users",d=r+"/files",u=r+"/observation-reports",m=r+"/my-observation-reports",p=r+"/dropdowns",g=r+"/get-blob",h=r+"/permit-reports",f=r+"/users",x=r+"/toolbox-talks",y=r+"/my-toolbox-talks",v=e=>r+"/my-assigned-actions/"+e,b=e=>r+"/inspection-checklist-submit/"+e,j=e=>r+"/observation-reports/"+e,N=e=>r+"/inspection-task-submit/"+e,w=e=>r+"/inspections/"+e,C=e=>r+"/permit-report-submit/"+e,k=e=>r+"/permit-reports-acknowledge/"+e,I=e=>r+"/permit-reports-update-status/"+e,S=e=>r+"/observation-action-submit/"+e,z=r+"/risk-assessments",T=e=>r+"/risk-assessments/"+e,R=e=>r+"/ra-team-member-submit-signature/"+e,A=r+"/permit-reports",D=e=>r+"/permit-reports/"+e},35695:(e,t,s)=>{"use strict";var r=s(18999);s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},38336:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(96078),a=s(26957);let o=r.A.create({baseURL:a.H$,headers:{Accept:"application/json","Content-Type":"application/json"},mode:"no-cors"});o.interceptors.request.use(async e=>(e.metadata={requestTime:Date.now()},e),e=>Promise.reject(e)),o.interceptors.response.use(e=>{var t;return e.headers["x-request-time"]=null==(t=e.config.metadata)?void 0:t.requestTime,e},async e=>{let{offlineQueue:t}=await s.e(8836).then(s.bind(s,48836)),{offlineStorage:r}=await s.e(58).then(s.bind(s,60058));if(t.shouldQueue(e)){var a,o,i,n;let s=e.config;if(await t.addRequest(s.url,(null==(a=s.method)?void 0:a.toUpperCase())||"GET",s.data,s.headers),(null==(o=s.method)?void 0:o.toLowerCase())==="get")try{if(null==(i=s.url)?void 0:i.includes("/services")){let e=await r.getServices();if(e.length>0)return console.log("\uD83D\uDCF1 Serving services from offline cache"),{data:e,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:s}}if(null==(n=s.url)?void 0:n.includes("assigned-actions")){let e=new URLSearchParams(s.url.split("?")[1]).get("filter"),t="All";if(e)try{JSON.parse(decodeURIComponent(e))}catch(a){let e=s.url.split("/"),r=e.findIndex(e=>"assigned-actions"===e);-1!==r&&e[r+1]&&(t=e[r+1])}let a=await r.getActions(t);if(a.length>0)return console.log("\uD83D\uDCF1 Serving actions from offline cache"),{data:a,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:s}}}catch(e){console.error("❌ Error serving from cache:",e)}}return Promise.reject(e)});let i=o},41513:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(95155),a=s(46554),o=s(12115),i=s(38336),n=s(26957),c=s(35695);let l="".concat(n.H$,"/my-documents"),d=()=>{let e=(0,c.useRouter)(),[t,s]=(0,o.useState)([]),[d,u]=(0,o.useState)({}),[m,p]=(0,o.useState)(!0),[g,h]=(0,o.useState)(!1),[f,x]=(0,o.useState)("");(0,o.useEffect)(()=>{v(),b()},[]);let y=(t,s)=>{localStorage.setItem("selectedCategoryId",t),localStorage.setItem("selectedCategoryName",s||"Category"),e.push("/doc/category?categoryId=".concat(t))},v=async()=>{try{p(!0),x("");let t="".concat(n.oo,"?filter=").concat(encodeURIComponent(JSON.stringify({where:{maskId:"doc_category"},include:[{relation:"dropdownItems"}]}))),r=await i.A.get(t);if(200===r.status&&r.data&&r.data.length>0){var e;let t=(null==(e=r.data[0])?void 0:e.dropdownItems.map(e=>({label:e.name,value:e.id})))||[];s(t)}else x("No categories found")}catch(e){console.error("Error fetching document categories:",e),x("Failed to load categories")}finally{p(!1)}},b=async()=>{try{h(!0);let e="".concat(l,"?filter=").concat(encodeURIComponent(JSON.stringify({include:[{relation:"creator"},{relation:"documentCategory"},{relation:"reviewer"},{relation:"approver"},{relation:"initiator"}]}))),t=await i.A.get(e);console.log("My documents response:",t);let s=t.data.map(e=>{var t,s;return{id:e.id,name:e.name,type:e.type,size:e.size||"Unknown",uploadedBy:(null==(t=e.creator)?void 0:t.firstName)||e.uploadedBy||e.createdBy||"Not assigned",uploadedDate:e.uploadedDate||e.created||new Date().toISOString(),category:(null==(s=e.documentCategory)?void 0:s.name)||e.category||"Uncategorized",tags:e.keywords?e.keywords.split(",").map(e=>e.trim()):[],description:e.purpose||e.description||"",maskId:e.maskId,scopeApplicability:e.scopeApplicability,purpose:e.purpose,keywords:e.keywords,docId:e.docId,created:e.created,updated:e.updated,creatorTargetDate:e.creatorTargetDate,reviewerTargetDate:e.reviewerTargetDate,approverTargetDate:e.approverTargetDate,initiatorId:e.initiatorId,creatorId:e.creatorId,reviewerId:e.reviewerId,approverId:e.approverId,documentCategoryId:e.documentCategoryId,initiator:e.initiator,creator:e.creator,reviewer:e.reviewer,approver:e.approver,documentCategory:e.documentCategory,files:e.files,status:e.status||"Draft",value:e.value}}).reduce((e,t)=>{let s=t.documentCategoryId||"uncategorized";return e[s]||(e[s]=[]),e[s].push(t),e},{});console.log("Categorized documents:",s),u(s)}catch(e){console.error("Error fetching my documents:",e)}finally{h(!1)}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.default,{heading:"Document Management"}),(0,r.jsx)("div",{className:"page-content-wrapper py-3",children:(0,r.jsx)("div",{className:"container",children:(0,r.jsx)("div",{className:"card shadow-sm",children:(0,r.jsxs)("div",{className:"card-body",children:[(0,r.jsxs)("h5",{className:"card-title mb-3",style:{color:"#0d6efd"},children:[(0,r.jsx)("i",{className:"bi bi-folder2-open me-2"}),"Document Categories"]}),m||g?(0,r.jsx)("div",{className:"d-flex justify-content-center align-items-center py-5",children:(0,r.jsx)("div",{className:"spinner-border text-primary",role:"status",children:(0,r.jsx)("span",{className:"visually-hidden",children:"Loading..."})})}):f?(0,r.jsxs)("div",{className:"alert alert-warning d-flex align-items-center",role:"alert",children:[(0,r.jsx)("i",{className:"bi bi-exclamation-triangle me-2"}),f]}):Object.keys(d).length>0?(0,r.jsxs)("div",{className:"row g-4",children:[t.map(e=>{let t=d[e.value]||[];return 0===t.length?null:(0,r.jsx)("div",{className:"col-12",children:(0,r.jsx)("div",{className:"category-section mb-4",children:(0,r.jsxs)("div",{className:"d-flex align-items-center mb-3 p-3",style:{backgroundColor:"#f8f9fa",borderRadius:"12px",border:"1px solid #e9ecef",cursor:"pointer",transition:"all 0.2s ease"},onClick:()=>y(e.value,e.label),onMouseEnter:e=>{e.currentTarget.style.backgroundColor="#e9ecef"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="#f8f9fa"},children:[(0,r.jsx)("div",{className:"category-icon me-3",style:{width:"48px",height:"48px",backgroundColor:"#e3f2fd",borderRadius:"12px",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,r.jsx)("i",{className:"bi bi-folder2",style:{fontSize:"20px",color:"#0d6efd"}})}),(0,r.jsxs)("div",{className:"flex-grow-1",children:[(0,r.jsx)("h5",{className:"mb-1",style:{color:"#2c3e50",fontWeight:"600"},children:e.label}),(0,r.jsxs)("p",{className:"mb-0 text-muted",style:{fontSize:"14px"},children:[t.length," document",1!==t.length?"s":""]})]}),(0,r.jsx)("div",{className:"ms-2",children:(0,r.jsx)("i",{className:"bi bi-chevron-right",style:{fontSize:"16px",color:"#6c757d"}})})]})})},e.value)}),d.uncategorized&&d.uncategorized.length>0&&(0,r.jsx)("div",{className:"col-12",children:(0,r.jsx)("div",{className:"category-section mb-4",children:(0,r.jsxs)("div",{className:"d-flex align-items-center mb-3 p-3",style:{backgroundColor:"#f8f9fa",borderRadius:"12px",border:"1px solid #e9ecef",cursor:"pointer",transition:"all 0.2s ease"},onClick:()=>y("uncategorized","Uncategorized"),onMouseEnter:e=>{e.currentTarget.style.backgroundColor="#e9ecef"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="#f8f9fa"},children:[(0,r.jsx)("div",{className:"category-icon me-3",style:{width:"48px",height:"48px",backgroundColor:"#fff3cd",borderRadius:"12px",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,r.jsx)("i",{className:"bi bi-question-circle",style:{fontSize:"20px",color:"#856404"}})}),(0,r.jsxs)("div",{className:"flex-grow-1",children:[(0,r.jsx)("h5",{className:"mb-1",style:{color:"#2c3e50",fontWeight:"600"},children:"Uncategorized"}),(0,r.jsxs)("p",{className:"mb-0 text-muted",style:{fontSize:"14px"},children:[d.uncategorized.length," document",1!==d.uncategorized.length?"s":""," without category"]})]}),(0,r.jsx)("div",{className:"ms-2",children:(0,r.jsx)("i",{className:"bi bi-chevron-right",style:{fontSize:"16px",color:"#6c757d"}})})]})})})]}):(0,r.jsxs)("div",{className:"text-center py-5",children:[(0,r.jsx)("i",{className:"bi bi-files",style:{fontSize:"48px",color:"#6c757d"}}),(0,r.jsx)("p",{className:"mt-3 text-muted",children:"No documents found"})]})]})})})})]})}},46554:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var r=s(95155),a=s(12115),o=s(35695),i=s(38336),n=s(26957),c=s(34540),l=s(81359);let d=e=>{let{heading:t}=e,s=(0,o.useRouter)(),[d,u]=(0,a.useState)(""),m=(0,c.wA)();a.useEffect(()=>{p()},[]);let p=async()=>{try{let e=await i.A.get(n.AM);200===e.status?(u(e.data.firstName),m(l.l.setUser(e.data))):s.push("/")}catch(e){console.log(e)}};return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:"header-area",id:"headerArea",children:(0,r.jsx)("div",{className:"container",children:(0,r.jsxs)("div",{className:"header-content header-style-four position-relative d-flex align-items-center justify-content-between",children:[(0,r.jsx)("div",{className:"back-button",children:(0,r.jsx)("button",{onClick:()=>s.back(),className:"border-0 bg-transparent p-0",style:{cursor:"pointer"},children:(0,r.jsx)("i",{className:"bi bi-arrow-left-short fs-3"})})}),(0,r.jsx)("div",{className:"page-heading",children:(0,r.jsx)("h6",{className:"mb-0",children:t})}),(0,r.jsx)("div",{className:"user-profile-wrapper"})]})})})})}},81359:(e,t,s)=>{"use strict";s.d(t,{A:()=>o,l:()=>a});let r=(0,s(51990).Z0)({name:"login",initialState:{isLogin:!1,user:null},reducers:{setLogin(e){e.isLogin=!0},setLogout(e){e.isLogin=!1,e.user=null},setUser(e,t){e.user=t.payload}}}),a=r.actions,o=r},83908:(e,t,s)=>{Promise.resolve().then(s.bind(s,41513))}},e=>{var t=t=>e(e.s=t);e.O(0,[6078,635,8441,1684,7358],()=>t(83908)),_N_E=e.O()}]);