'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import HeaderSeven from '@/layouts/headers/HeaderSeven';
import API from '@/services/API';
import { API_URL } from '@/constant';

interface Document {
  id: string;
  name: string;
  description?: string;
  documentCategoryId: string;
  created: string;
  status?: string;
  creator?: {
    firstName: string;
    lastName: string;
  };
}

function CategoryContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [categoryId, setCategoryId] = useState('');
  const [categoryName, setCategoryName] = useState('');
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const urlCategoryId = searchParams.get('categoryId');
    const storedCategoryId = localStorage.getItem('selectedCategoryId');
    const storedCategoryName = localStorage.getItem('selectedCategoryName');

    if (urlCategoryId) {
      setCategoryId(urlCategoryId);
      localStorage.setItem('selectedCategoryId', urlCategoryId);
    } else if (storedCategoryId) {
      setCategoryId(storedCategoryId);
    }

    if (storedCategoryName) {
      setCategoryName(storedCategoryName);
    }
  }, [searchParams]);

  useEffect(() => {
    if (categoryId) {
      fetchDocuments();
    }
  }, [categoryId]);

  const fetchDocuments = async () => {
    try {
      setLoading(true);

      const uriString = {
        include: [
          { relation: "creator" },
          { relation: "documentCategory" },
          { relation: "reviewer" },
          { relation: "approver" },
          { relation: "initiator" }
        ]
      };

      const MY_DOCUMENTS_API = `${API_URL}/my-documents`;
      const url = `${MY_DOCUMENTS_API}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
      const response = await API.get(url);

      console.log('Category documents response:', response);

      if (response.status === 200 && response.data) {
        // Filter documents by categoryId
        const filteredDocs = response.data.filter((doc: any) => doc.documentCategoryId === categoryId);
        console.log('Filtered documents for category:', categoryId, filteredDocs);
        setDocuments(filteredDocs);
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDocumentClick = (document: Document) => {
    localStorage.setItem('selectedDocument', JSON.stringify(document));
    router.push(`/doc/view?id=${document.id}`);
  };

  const handleBackToCategories = () => {
    router.push('/doc');
  };

  if (loading) {
    return (
      <div className="wrapper">
        <HeaderSeven heading="Loading Documents..." />
        <main>
          <div className="container-fluid">
            <div className="row">
              <div className="col-12">
                <div className="card">
                  <div className="card-body text-center">
                    <div className="spinner-border" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="wrapper">
      <HeaderSeven heading={`Documents in ${categoryName || 'Category'}`} />
      <main className='mt-5'>
        <div className="container-fluid">
          <div className="row">
            <div className="col-12">
              <div className="card">
                <div className="card-header d-flex justify-content-between align-items-center">
                  <h5 className="card-title mb-0">Documents</h5>
                  <button
                    className="btn btn-secondary btn-sm"
                    onClick={handleBackToCategories}
                  >
                    ← Back to Categories
                  </button>
                </div>
                <div className="card-body">
                  {documents.length === 0 ? (
                    <div className="text-center py-4">
                      <p className="text-muted">No documents found in this category.</p>
                    </div>
                  ) : (
                    <div className="row g-3">
                      {documents.map((document) => (
                        <div key={document.id} className="col-12">
                          <div className="card border-0 shadow-sm h-100" style={{ borderRadius: '12px', backgroundColor: '#f8f9fa' }}>
                            <div className="card-body p-3">
                              <div className="d-flex justify-content-between align-items-start mb-2">
                                <div className="flex-grow-1">
                                  <h6 className="card-title mb-1 fw-bold text-dark">{document.name}</h6>
                                  {document.description && (
                                    <p className="card-text text-muted small mb-2" style={{ fontSize: '0.85rem' }}>
                                      {document.description}
                                    </p>
                                  )}
                                </div>
                                <span className={`badge ms-2 ${
                                  document.status === 'active' ? 'bg-success' :
                                  document.status === 'draft' ? 'bg-warning text-dark' : 'bg-secondary'
                                }`} style={{ fontSize: '0.75rem' }}>
                                  {document.status || 'Unknown'}
                                </span>
                              </div>

                              <div className="row g-2 mb-3">
                                <div className="col-6">
                                  <div className="d-flex align-items-center">
                                    <i className="fas fa-user text-primary me-2" style={{ fontSize: '0.85rem' }}></i>
                                    <div>
                                      <small className="text-muted d-block" style={{ fontSize: '0.75rem' }}>Creator</small>
                                      <small className="fw-medium text-dark" style={{ fontSize: '0.8rem' }}>
                                        {document.creator ?
                                          `${document.creator.firstName}` :
                                          'Unknown'
                                        }
                                      </small>
                                    </div>
                                  </div>
                                </div>
                                <div className="col-6">
                                  <div className="d-flex align-items-center">
                                    <i className="fas fa-calendar text-primary me-2" style={{ fontSize: '0.85rem' }}></i>
                                    <div>
                                      <small className="text-muted d-block" style={{ fontSize: '0.75rem' }}>Created</small>
                                      <small className="fw-medium text-dark" style={{ fontSize: '0.8rem' }}>
                                        {new Date(document.created).toLocaleDateString()}
                                      </small>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <div className="d-grid">
                                <button
                                  className="btn btn-primary btn-sm"
                                  onClick={() => handleDocumentClick(document)}
                                  style={{
                                    borderRadius: '8px',
                                    backgroundColor: '#007bff',
                                    border: 'none',
                                    padding: '8px 16px',
                                    fontSize: '0.85rem'
                                  }}
                                >
                                  <i className="fas fa-eye me-2"></i>
                                  View Document
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

function CategoryPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <CategoryContent />
    </Suspense>
  );
}

export default CategoryPage;
