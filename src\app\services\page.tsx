"use client";

import React, { useEffect, useState, useCallback } from "react";
import { useDispatch } from "react-redux";
import { serviceActions } from "@/store/services";
import Link from "next/link";
import HeaderSeven from "@/layouts/headers/HeaderSeven";
import OfflinePageWrapper from "@/components/offline/OfflinePageWrapper";
import OfflineFallback from "@/components/offline/OfflineFallback";
import CachedDataBadge from "@/components/offline/CachedDataBadge";
import { offlineAPI } from "@/services/offlineAPI";

interface ServiceType {
  id: string;
  name: string;
  description: string;
  maskName: string;
  applicability: string;
  status: boolean;
  url: string;
  created: string;
  updated: string;
  mobileShortName: string;
  color: string;
}

const iconMapping: Record<string, string> = {
  "RA": "bi bi-shield-check",
  "EPTW-GEN": "bi bi-file-lock",
  "IR": "bi bi-exclamation-triangle",
  "OBS": "bi bi-eye",
  "INC": "bi bi-clipboard-check",
  "KNOWLEDGE": "bi bi-book",
  "TBT": "bi bi-chat-dots",
  "OTT": "bi bi-list-task",
  "INCINV": "bi bi-briefcase",
  "INS": "bi bi-clipboard-check",
  "DOC": "bi bi-file-earmark-text",
  "GC": "bi bi-hand-thumbs-up",
  "ATM": "bi bi-tools",
  "Change Management": "bi bi-arrow-repeat"
};

const colorMapping: Record<string, string> = {
  "RA": "#FF3B30",
  "EPTW-GEN": "#34C759",
  "IR": "#FF9500",
  "OBS": "#007AFF",
  "INC": "#FF2D92",
  "KNOWLEDGE": "#5856D6",
  "TBT": "#007AFF",
  "OTT": "#AF52DE",
  "INCINV": "#FF6B35",
  "INS": "#32D74B",
  "DOC": "#5856D6",
  "GC": "#30D158",
  "ATM": "#FF6B35",
  "Change Management": "#007AFF"
};

const ServicesPage: React.FC = () => {
  const [services, setServices] = useState<ServiceType[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');
  const [searchQuery, setSearchQuery] = useState('');
  const dispatch = useDispatch();

  // Check if RA service exists in the services array
  const hasRAService = services.some(service => service.maskName === "RA");

  // Filter services based on search query
  const filteredServices = services.filter(service =>
    service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    service.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    service.mobileShortName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const fetchServices = useCallback(async () => {
    try {
      const services = await offlineAPI.getServices();
      setServices(services);
      dispatch(serviceActions.setService(services));
      setLoading(false);
    } catch (error) {
      console.error("Error fetching services:", error);
      setLoading(false);
    }
  }, [dispatch]);

  useEffect(() => {
    fetchServices();
  }, [fetchServices]);

  const removeAppsPrefix = (url: string) => {
    return url.replace(/^\/apps\//, "/");
  };

  const offlineFallback = (
    <OfflineFallback
      title="Services Unavailable Offline"
      message="Services data is not available offline. Please connect to the internet to view all services."
      showHomeLink={true}
    />
  );

  return (
    <>
      <HeaderSeven heading={'Services'} />
      <OfflinePageWrapper
        fallbackContent={offlineFallback}
        showOfflineIndicator={true}
      >
        <div className="page-content-wrapper">
          <div className="container-fluid px-3" style={{ backgroundColor: '#f8f9fa', minHeight: '100vh', padding: '16px' }}>

          {/* Enhanced Header Section - Based on test.tsx */}
          <div className="row">
            <div className="col-12">
              <div style={{ marginBottom: '24px' }}>
                {/* Header Top */}
                <div className="d-flex justify-content-between align-items-start mb-3">
                  <div style={{ flex: 1 }}>
                    {/* <h2 className="fw-bold mb-2 text-dark" style={{ fontSize: '28px', marginBottom: '8px' }}>
                      Services
                    </h2> */}
                    <p className="text-muted mb-0" style={{ opacity: 0.7, fontSize: '16px' }}>
                      Access all safety management services
                    </p>
                  </div>

                  {/* View Toggle */}
                  <div
                    className="d-flex"
                    style={{
                      backgroundColor: '#F2F2F7',
                      borderRadius: '8px',
                      padding: '2px',
                      alignSelf: 'flex-end'
                    }}
                  >
                    <button
                      className={`btn d-flex align-items-center ${viewMode === 'list' ? 'text-white' : 'text-muted'}`}
                      onClick={() => setViewMode('list')}
                      style={{
                        backgroundColor: viewMode === 'list' ? '#007AFF' : 'transparent',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '8px 12px',
                        minWidth: '70px',
                        fontSize: '14px',
                        fontWeight: '500'
                      }}
                    >
                      <i className="bi bi-list me-1" style={{ fontSize: '14px' }}></i>
                      List
                    </button>
                    <button
                      className={`btn d-flex align-items-center ${viewMode === 'grid' ? 'text-white' : 'text-muted'}`}
                      onClick={() => setViewMode('grid')}
                      style={{
                        backgroundColor: viewMode === 'grid' ? '#007AFF' : 'transparent',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '8px 12px',
                        minWidth: '70px',
                        fontSize: '14px',
                        fontWeight: '500'
                      }}
                    >
                      <i className="bi bi-grid me-1" style={{ fontSize: '14px' }}></i>
                      Grid
                    </button>
                  </div>
                </div>

                {/* Search Bar */}
                <div
                  className="d-flex align-items-center mb-3"
                  style={{
                    backgroundColor: '#F2F2F7',
                    borderRadius: '10px',
                    padding: '8px 12px'
                  }}
                >
                  <i className="bi bi-search me-2" style={{ fontSize: '20px', color: '#8E8E93' }}></i>
                  <input
                    type="text"
                    className="form-control border-0 bg-transparent p-0"
                    placeholder="Search services..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    style={{
                      fontSize: '16px',
                      color: '#000000'
                    }}
                  />
                </div>

                {/* Services Count */}
                <div className="d-flex align-items-center justify-content-between">
                  <div className="d-flex align-items-center">
                    <CachedDataBadge type="services" showAge={false} />
                  </div>
                  <div className="text-muted small">
                    {filteredServices.length} {filteredServices.length === 1 ? 'service' : 'services'} available
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Services Content */}
          <div className="row">
            <div className="col-12">
              {loading ? (
                <div className="d-flex justify-content-center align-items-center py-5">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                </div>
              ) : viewMode === 'list' ? (
                /* List View */
                <div style={{ marginBottom: '24px' }}>
                  {filteredServices.map((service) => (
                    <Link key={service.id} href={removeAppsPrefix(service.url)} className="text-decoration-none">
                      <div
                        className="bg-white border rounded-3 mb-3"
                        style={{
                          borderRadius: '12px',
                          borderWidth: '1px',
                          overflow: 'hidden',
                          transition: 'all 0.2s ease',
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'translateY(-2px)';
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = 'none';
                        }}
                      >
                        <div className="d-flex align-items-center p-3">
                          {/* Icon Container */}
                          <div
                            className="d-flex align-items-center justify-content-center me-3"
                            style={{
                              width: '56px',
                              height: '56px',
                              borderRadius: '28px',
                              backgroundColor: (colorMapping[service.maskName] || '#007AFF') + '20'
                            }}
                          >
                            <i
                              className={iconMapping[service.maskName] || "bi bi-file"}
                              style={{
                                fontSize: '32px',
                                color: colorMapping[service.maskName] || '#007AFF'
                              }}
                            ></i>
                          </div>

                          {/* Service Info */}
                          <div style={{ flex: 1 }}>
                            <h6 className="fw-bold text-dark mb-1" style={{ fontSize: '16px', marginBottom: '4px' }}>
                              { service.name}
                            </h6>
                            <p className="text-muted mb-0" style={{ fontSize: '14px', opacity: 0.7 }}>
                              {service.description}
                            </p>
                          </div>

                          {/* Chevron */}
                          <i className="bi bi-chevron-right text-muted" style={{ opacity: 0.5, fontSize: '20px' }}></i>
                        </div>
                      </div>
                    </Link>
                  ))}

                  {/* TBT Service if RA exists */}
                  {hasRAService && (
                    <Link href="/tbt" className="text-decoration-none">
                      <div
                        className="bg-white border rounded-3 mb-3"
                        style={{
                          borderRadius: '12px',
                          borderWidth: '1px',
                          overflow: 'hidden',
                          transition: 'all 0.2s ease',
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'translateY(-2px)';
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = 'none';
                        }}
                      >
                        <div className="d-flex align-items-center p-3">
                          <div
                            className="d-flex align-items-center justify-content-center me-3"
                            style={{
                              width: '56px',
                              height: '56px',
                              borderRadius: '28px',
                              backgroundColor: '#007AFF20'
                            }}
                          >
                            <i
                              className="bi bi-chat-dots"
                              style={{
                                fontSize: '32px',
                                color: '#007AFF'
                              }}
                            ></i>
                          </div>
                          <div style={{ flex: 1 }}>
                            <h6 className="fw-bold text-dark mb-1" style={{ fontSize: '16px', marginBottom: '4px' }}>
                              TBT
                            </h6>
                            <p className="text-muted mb-0" style={{ fontSize: '14px', opacity: 0.7 }}>
                              {/* Time Between Tests - Statistical reporting and analysis */}
                            </p>
                          </div>
                          <i className="bi bi-chevron-right text-muted" style={{ opacity: 0.5, fontSize: '20px' }}></i>
                        </div>
                      </div>
                    </Link>
                  )}
                </div>
              ) : (
                /* Grid View - Redesigned */
                <div className="row g-3 mb-4">
                  {filteredServices.map((service) => (
                    <div key={service.id} className="col-6 col-md-4 col-lg-3">
                      <Link href={removeAppsPrefix(service.url)} className="text-decoration-none">
                        <div
                          className="bg-white border rounded-3 text-center p-3 h-100 service-grid-card"
                          style={{
                            borderRadius: '16px',
                            borderWidth: '1px',
                            borderColor: '#E5E7EB',
                            backgroundColor: '#FFFFFF',
                            boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
                            transition: 'all 0.3s ease',
                            cursor: 'pointer',
                            minHeight: '120px',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'translateY(-4px)';
                            e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.12)';
                            e.currentTarget.style.borderColor = (colorMapping[service.maskName] || '#007AFF') + '40';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';
                            e.currentTarget.style.borderColor = '#E5E7EB';
                          }}
                        >
                          {/* Icon Container */}
                          <div
                            className="d-flex align-items-center justify-content-center mb-3 icon-container"
                            style={{
                              width: '56px',
                              height: '56px',
                              borderRadius: '16px',
                              backgroundColor: (colorMapping[service.maskName] || '#007AFF') + '15',
                              border: `2px solid ${(colorMapping[service.maskName] || '#007AFF')}20`
                            }}
                          >
                            <i
                              className={iconMapping[service.maskName] || "bi bi-file"}
                              style={{
                                fontSize: '28px',
                                color: colorMapping[service.maskName] || '#007AFF'
                              }}
                            ></i>
                          </div>

                          {/* Service Title */}
                          <h6
                            className="fw-bold text-dark mb-1"
                            style={{
                              fontSize: '14px',
                              lineHeight: '18px',
                              fontWeight: '600',
                              marginBottom: '4px',
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              minHeight: '36px'
                            }}
                          >
                            {service.name}
                          </h6>

                          {/* Service Description */}
                          <p
                            className="text-muted small mb-0"
                            style={{
                              fontSize: '11px',
                              lineHeight: '14px',
                              opacity: 0.7,
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              minHeight: '28px'
                            }}
                          >
                            {service.description}
                          </p>
                        </div>
                      </Link>
                    </div>
                  ))}

                  {/* TBT Service if RA exists */}
                  {hasRAService && (
                    <div className="col-6 col-md-4 col-lg-3">
                      <Link href="/tbt" className="text-decoration-none">
                        <div
                          className="bg-white border rounded-3 text-center p-3 h-100 service-grid-card"
                          style={{
                            borderRadius: '16px',
                            borderWidth: '1px',
                            borderColor: '#E5E7EB',
                            backgroundColor: '#FFFFFF',
                            boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
                            transition: 'all 0.3s ease',
                            cursor: 'pointer',
                            minHeight: '120px',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'translateY(-4px)';
                            e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.12)';
                            e.currentTarget.style.borderColor = '#007AFF40';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';
                            e.currentTarget.style.borderColor = '#E5E7EB';
                          }}
                        >
                          <div
                            className="d-flex align-items-center justify-content-center mb-3 icon-container"
                            style={{
                              width: '56px',
                              height: '56px',
                              borderRadius: '16px',
                              backgroundColor: '#007AFF15',
                              border: '2px solid #007AFF20'
                            }}
                          >
                            <i
                              className="bi bi-chat-dots"
                              style={{
                                fontSize: '28px',
                                color: '#007AFF'
                              }}
                            ></i>
                          </div>
                          <h6
                            className="fw-bold text-dark mb-1"
                            style={{
                              fontSize: '14px',
                              lineHeight: '18px',
                              fontWeight: '600',
                              marginBottom: '4px',
                              minHeight: '36px'
                            }}
                          >
                            TBT
                          </h6>
                          <p
                            className="text-muted small mb-0"
                            style={{
                              fontSize: '11px',
                              lineHeight: '14px',
                              opacity: 0.7,
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              minHeight: '28px'
                            }}
                          >
                            {/* Time Between Tests - Statistical reporting and analysis */}
                          </p>
                        </div>
                      </Link>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Footer Section */}
          <div className="row">
            <div className="col-12">
              <div className="text-center py-4" style={{ paddingBottom: '100px' }}>
                <p className="text-muted mb-0" style={{ fontSize: '14px', opacity: 0.6 }}>
                  Select a service to get started with safety management
                </p>
              </div>
            </div>
          </div>

          {/* Footer Navigation */}
          <div className="fixed-bottom bg-white border-top shadow-sm">
            <div className="container-fluid">
              <div className="row text-center py-2">
                <div className="col">
                  <Link href="/dashboard" className="text-decoration-none text-muted">
                    <div className="d-flex flex-column align-items-center">
                      <i className="bi bi-grid-3x3-gap fs-5 mb-1"></i>
                      <small style={{ fontSize: '0.7rem' }}>Dashboard</small>
                    </div>
                  </Link>
                </div>
                <div className="col">
                  <Link href="/services" className="text-decoration-none text-primary">
                    <div className="d-flex flex-column align-items-center">
                      <i className="bi bi-grid-fill fs-5 mb-1"></i>
                      <small style={{ fontSize: '0.7rem' }}>Services</small>
                    </div>
                  </Link>
                </div>
                <div className="col">
                  <Link href="/home" className="text-decoration-none text-muted">
                    <div className="d-flex flex-column align-items-center">
                      <i className="bi bi-house fs-5 mb-1"></i>
                      <small style={{ fontSize: '0.7rem' }}>Home</small>
                    </div>
                  </Link>
                </div>
                <div className="col">
                  <Link href="/history" className="text-decoration-none text-muted">
                    <div className="d-flex flex-column align-items-center">
                      <i className="bi bi-clock-history fs-5 mb-1"></i>
                      <small style={{ fontSize: '0.7rem' }}>History</small>
                    </div>
                  </Link>
                </div>
                <div className="col">
                  <Link href="/profile" className="text-decoration-none text-muted">
                    <div className="d-flex flex-column align-items-center">
                      <i className="bi bi-person fs-5 mb-1"></i>
                      <small style={{ fontSize: '0.7rem' }}>Profile</small>
                    </div>
                  </Link>
                </div>
              </div>
            </div>
          </div>
          </div>
        </div>
      </OfflinePageWrapper>

      <style jsx>{`
        /* Enhanced Grid Styles */
        .service-grid-card {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .service-grid-card:hover {
          transform: translateY(-4px) scale(1.02);
        }

        /* Responsive adjustments */
        @media (max-width: 576px) {
          .service-grid-card {
            min-height: 110px !important;
          }

          .service-grid-card .icon-container {
            width: 48px !important;
            height: 48px !important;
          }

          .service-grid-card .icon-container i {
            font-size: 24px !important;
          }
        }
      `}</style>
    </>
  );
};

export default ServicesPage;
