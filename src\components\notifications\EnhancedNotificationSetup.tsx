"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Badge, Collapse } from 'react-bootstrap';
import unifiedNotificationService from '@/services/unifiedNotificationService';

interface EnhancedNotificationSetupProps {
  onTokenGenerated?: (token: string) => void;
  showTestButton?: boolean;
  autoInitialize?: boolean;
}

const EnhancedNotificationSetup: React.FC<EnhancedNotificationSetupProps> = ({
  onTokenGenerated,
  showTestButton = true,
  autoInitialize = true
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState<NotificationPermission>('default');
  const [deviceInfo, setDeviceInfo] = useState<any>(null);
  const [token, setToken] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [statusMessage, setStatusMessage] = useState<string>('');
  const [showInstructions, setShowInstructions] = useState(false);
  const [setupInstructions, setSetupInstructions] = useState<string[]>([]);

  useEffect(() => {
    if (autoInitialize) {
      initializeService();
    }
  }, [autoInitialize]);

  const initializeService = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const success = await unifiedNotificationService.initialize();
      setIsInitialized(success);

      const info = unifiedNotificationService.getDeviceInfo();
      setDeviceInfo(info);

      const permission = unifiedNotificationService.getPermissionStatus();
      setPermissionStatus(permission);

      const status = unifiedNotificationService.getStatusMessage();
      setStatusMessage(status);

      const instructions = unifiedNotificationService.getSetupInstructions();
      setSetupInstructions(instructions);

      if (!success) {
        setError('Notifications are not supported on this device');
      }
    } catch (error: any) {
      console.error('Error initializing notification service:', error);
      setError(error.message || 'Failed to initialize notification service');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEnableNotifications = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Request permission
      const hasPermission = await unifiedNotificationService.requestPermission();
      
      if (!hasPermission) {
        setError('Notification permission was denied');
        setShowInstructions(true);
        return;
      }

      // Register for notifications
      const generatedToken = await unifiedNotificationService.registerForNotifications();
      
      if (generatedToken) {
        setToken(generatedToken);
        setPermissionStatus('granted');
        onTokenGenerated?.(generatedToken);
        
        // Update status
        const status = unifiedNotificationService.getStatusMessage();
        setStatusMessage(status);
      } else {
        setError('Failed to register for notifications');
      }
    } catch (error: any) {
      console.error('Error enabling notifications:', error);
      setError(error.message || 'Failed to enable notifications');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestNotification = async () => {
    try {
      setIsLoading(true);
      const success = await unifiedNotificationService.showTestNotification();
      
      if (!success) {
        setError('Failed to show test notification');
      }
    } catch (error: any) {
      console.error('Error showing test notification:', error);
      setError('Failed to show test notification');
    } finally {
      setIsLoading(false);
    }
  };

  const getDeviceTypeIcon = () => {
    if (!deviceInfo) return '📱';
    if (deviceInfo.isIOS) return '🍎';
    if (deviceInfo.isAndroid) return '🤖';
    return '💻';
  };

  const getMethodBadgeVariant = () => {
    if (!deviceInfo) return 'secondary';
    switch (deviceInfo.recommendedMethod) {
      case 'fcm': return 'success';
      case 'ios-web-push': return 'info';
      case 'local-only': return 'warning';
      case 'unsupported': return 'danger';
      default: return 'secondary';
    }
  };

  if (!isInitialized && isLoading) {
    return (
      <Card className="text-center p-4">
        <Spinner animation="border" className="mx-auto mb-3" />
        <p>Checking notification support...</p>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm">
      <Card.Header className="d-flex justify-content-between align-items-center">
        <h6 className="mb-0">
          {getDeviceTypeIcon()} Push Notifications
        </h6>
        {deviceInfo && (
          <Badge bg={getMethodBadgeVariant()}>
            {deviceInfo.recommendedMethod.replace('-', ' ').toUpperCase()}
          </Badge>
        )}
      </Card.Header>
      
      <Card.Body>
        {/* Status Message */}
        <div className="mb-3">
          <small className="text-muted">Status: </small>
          <span className={`fw-bold ${permissionStatus === 'granted' ? 'text-success' : 'text-warning'}`}>
            {statusMessage}
          </span>
        </div>

        {/* Device Info */}
        {deviceInfo && (
          <div className="mb-3">
            <small className="text-muted d-block">
              Device: {deviceInfo.isIOS ? 'iOS' : deviceInfo.isAndroid ? 'Android' : 'Desktop'} • 
              Browser: {deviceInfo.browser} • 
              PWA: {deviceInfo.isIOSPWA ? 'Yes' : 'No'}
            </small>
          </div>
        )}

        {/* Error Alert */}
        {error && (
          <Alert variant="danger" className="mb-3">
            <i className="bi bi-exclamation-triangle me-2"></i>
            {error}
          </Alert>
        )}

        {/* Success Alert */}
        {permissionStatus === 'granted' && token && (
          <Alert variant="success" className="mb-3">
            <i className="bi bi-check-circle me-2"></i>
            Notifications are enabled and working!
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="d-flex gap-2 flex-wrap">
          {permissionStatus === 'default' && (
            <Button
              variant="primary"
              onClick={handleEnableNotifications}
              disabled={isLoading || !unifiedNotificationService.isSupported()}
              className="d-flex align-items-center"
            >
              {isLoading ? (
                <>
                  <Spinner animation="border" size="sm" className="me-2" />
                  Enabling...
                </>
              ) : (
                <>
                  <i className="bi bi-bell me-2"></i>
                  Enable Notifications
                </>
              )}
            </Button>
          )}

          {permissionStatus === 'granted' && showTestButton && (
            <Button
              variant="outline-primary"
              onClick={handleTestNotification}
              disabled={isLoading}
              className="d-flex align-items-center"
            >
              {isLoading ? (
                <Spinner animation="border" size="sm" className="me-2" />
              ) : (
                <i className="bi bi-bell-fill me-2"></i>
              )}
              Test Notification
            </Button>
          )}

          {permissionStatus === 'denied' && (
            <Button
              variant="outline-info"
              onClick={() => setShowInstructions(!showInstructions)}
            >
              <i className="bi bi-info-circle me-2"></i>
              Show Setup Instructions
            </Button>
          )}
        </div>

        {/* Setup Instructions */}
        <Collapse in={showInstructions || permissionStatus === 'denied'}>
          <div className="mt-3">
            <Alert variant="info">
              <h6>
                <i className="bi bi-info-circle me-2"></i>
                Setup Instructions
              </h6>
              <ol className="mb-0">
                {setupInstructions.map((instruction, index) => (
                  <li key={index} className="mb-1">
                    {instruction}
                  </li>
                ))}
              </ol>
            </Alert>
          </div>
        </Collapse>

        {/* Unsupported Device */}
        {deviceInfo?.recommendedMethod === 'unsupported' && (
          <Alert variant="warning" className="mt-3">
            <i className="bi bi-exclamation-triangle me-2"></i>
            <strong>Limited Support</strong><br />
            Push notifications are not fully supported on your device. 
            You may still receive notifications when the app is open.
          </Alert>
        )}

        {/* iOS Specific Notice */}
        {deviceInfo?.isIOS && (
          <Alert variant="info" className="mt-3">
            <i className="bi bi-apple me-2"></i>
            <strong>iOS Notice</strong><br />
            For best results on iOS, add this app to your home screen and open it from there.
            Web push notifications on iOS require Safari and iOS 16.4+.
          </Alert>
        )}

        {/* Token Display (for debugging) */}
        {token && process.env.NODE_ENV === 'development' && (
          <div className="mt-3">
            <small className="text-muted d-block">Token (dev only):</small>
            <code className="small text-break">{token.substring(0, 50)}...</code>
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

export default EnhancedNotificationSetup;
