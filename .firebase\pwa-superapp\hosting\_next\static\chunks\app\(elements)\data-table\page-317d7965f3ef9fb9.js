(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3857],{21217:(e,a,s)=>{"use strict";s.d(a,{default:()=>o});var t=s(95155),l=s(9e4),i=s(38808),r=s(12115);let n=e=>{let{handleShowSetting:a,showSetting:s}=e,{theme:r,handleDarkModeToggle:n}=(0,l.D)(),{viewMode:c,handleRTLToggling:d}=(0,i.L)();return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{id:"setting-popup-overlay",className:s?"active":"",onClick:a}),(0,t.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(s?"active":""),id:"settingCard",children:(0,t.jsx)("div",{className:"card-body",children:(0,t.jsxs)("div",{className:"container",children:[(0,t.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,t.jsx)("p",{className:"mb-0",children:"Settings"}),(0,t.jsx)("div",{onClick:a,className:"btn-close",id:"settingCardClose"})]}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,t.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,t.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===r,onChange:n}),(0,t.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===r?"Light":"Dark"," mode"]})]})}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===c,onChange:d}),(0,t.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===c?"LTR":"RTL"," mode"]})]})})]})})})]})};var c=s(6874),d=s.n(c);let o=e=>{let{links:a,title:s}=e,[l,i]=(0,r.useState)(!1),c=()=>i(!l);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"header-area",id:"headerArea",children:(0,t.jsx)("div",{className:"container",children:(0,t.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,t.jsx)("div",{className:"back-button",children:(0,t.jsx)(d(),{href:"/".concat(a),children:(0,t.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,t.jsx)("div",{className:"page-heading",children:(0,t.jsx)("h6",{className:"mb-0",children:s})}),(0,t.jsx)("div",{className:"setting-wrapper",onClick:c,children:(0,t.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,t.jsx)("i",{className:"bi bi-gear"}),(0,t.jsx)("span",{})]})})]})})}),(0,t.jsx)(n,{showSetting:l,handleShowSetting:c})]})}},38808:(e,a,s)=>{"use strict";s.d(a,{L:()=>l});var t=s(12115);let l=()=>{let[e,a]=(0,t.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,t.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let s=()=>{a(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:s,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let a=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(a),e.style.display="none")},20)},1e3))}s()}}}},38983:(e,a,s)=>{"use strict";s.d(a,{default:()=>n});var t=s(95155),l=s(6874),i=s.n(l);s(12115);let r=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],n=()=>(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,t.jsx)("div",{className:"container px-0",children:(0,t.jsx)("div",{className:"footer-nav position-relative",children:(0,t.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:r.map((e,a)=>(0,t.jsx)("li",{children:(0,t.jsxs)(i(),{href:"/".concat(e.link),children:[(0,t.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,t.jsx)("span",{children:e.title})]})},a))})})})})})},9e4:(e,a,s)=>{"use strict";s.d(a,{D:()=>l});var t=s(12115);let l=()=>{let[e,a]=(0,t.useState)("light"),[s,l]=(0,t.useState)(!1);(0,t.useEffect)(()=>{a(localStorage.getItem("theme")||"light"),l(!0)},[]),(0,t.useEffect)(()=>{s&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,s]);let i=(0,t.useCallback)(()=>{a(e=>"dark"===e?"light":"dark")},[]),r=(0,t.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let a=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(a),e.style.display="none")},20)},1e3))}i()},[i]);return{theme:e,toggleTheme:i,handleDarkModeToggle:r}}},91797:(e,a,s)=>{"use strict";s.d(a,{default:()=>i});var t=s(95155),l=s(12115);let i=()=>{let[e,a]=(0,l.useState)([{name:"Tiger Nixon",position:"System Architect",salary:"$320,800"},{name:"Garrett Winters",position:"Accountant",salary:"$170,750"},{name:"Ashton Cox",position:"Junior Technical Author",salary:"$86,000"},{name:"Cedric Kelly",position:"Senior Javascript Developer",salary:"$433,060"},{name:"Airi Satou",position:"Accountant",salary:"$162,700"},{name:"Brielle Williamson",position:"Integration Specialist",salary:"$372,000"},{name:"Herrod Chandler",position:"Sales Assistant",salary:"$137,500"},{name:"Rhona Davidson",position:"Integration Specialist",salary:"$327,900"},{name:"Colleen Hurst",position:"Javascript Developer",salary:"$205,500"},{name:"Sonya Frost",position:"Software Engineer",salary:"$103,600"},{name:"Aaonya Frost 2",position:"Software Engineer",salary:"$34,600"}]),[s,i]=(0,l.useState)(""),r=s=>{a([...e].sort((e,a)=>"salary"===s?parseFloat(e[s].replace(/[\$,]/g,""))-parseFloat(a[s].replace(/[\$,]/g,"")):e[s].localeCompare(a[s])))},n=e.filter(e=>e.name.toLowerCase().includes(s)||e.position.toLowerCase().includes(s)||e.salary.toLowerCase().includes(s));return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("div",{className:"page-content-wrapper py-3 rk_table_2 rk_table",children:(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsx)("div",{className:"card-body",children:(0,t.jsxs)("div",{className:"dataTable-wrapper dataTable-loading no-footer sortable searchable fixed-columns",children:[(0,t.jsxs)("div",{className:"dataTable-top d-flex justify-content-between",children:[(0,t.jsx)("div",{className:"dataTable-dropdown",children:(0,t.jsx)("label",{children:(0,t.jsxs)("select",{className:"dataTable-selector",children:[(0,t.jsx)("option",{value:"10",children:"10"}),(0,t.jsx)("option",{value:"20",children:"20"}),(0,t.jsx)("option",{value:"30",children:"30"}),(0,t.jsx)("option",{value:"40",children:"40"}),(0,t.jsx)("option",{value:"50",children:"50"})]})})}),(0,t.jsx)("div",{className:"dataTable-search",children:(0,t.jsx)("input",{className:"dataTable-input",placeholder:"Search",type:"text",value:s,onChange:e=>{i(e.target.value.toLowerCase())}})})]}),(0,t.jsx)("div",{className:"dataTable-container",children:(0,t.jsxs)("table",{className:"w-100 dataTable-table",id:"dataTable",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{"data-sortable":"",style:{width:"32.1221%",padding:"8px"},onClick:()=>r("name"),children:(0,t.jsx)("a",{href:"#",className:"dataTable-sorter",children:"Name"})}),(0,t.jsx)("th",{"data-sortable":"",style:{width:"48.2558%",padding:"8px"},onClick:()=>r("position"),children:(0,t.jsx)("a",{href:"#",className:"dataTable-sorter",children:"Position"})}),(0,t.jsx)("th",{"data-sortable":"",style:{width:"19.6221%",padding:"8px"},onClick:()=>r("salary"),children:(0,t.jsx)("a",{href:"#",className:"dataTable-sorter",children:"Salary"})})]})}),(0,t.jsx)("tbody",{children:n.map((e,a)=>(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{children:e.name}),(0,t.jsx)("td",{children:e.position}),(0,t.jsx)("td",{children:e.salary})]},a))})]})}),(0,t.jsxs)("div",{className:"dataTable-bottom",children:[(0,t.jsx)("div",{className:"dataTable-info",children:"1 to 10 entries"}),(0,t.jsxs)("div",{className:"dataTable-pagination",children:[(0,t.jsx)("li",{className:"pager",children:(0,t.jsx)("a",{href:"#","data-page":"1",children:(0,t.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,t.jsx)("li",{className:"active",children:(0,t.jsx)("a",{href:"#","data-page":"1",children:"1"})}),(0,t.jsx)("li",{className:"",children:(0,t.jsx)("a",{href:"#","data-page":"2",children:"2"})}),(0,t.jsx)("li",{className:"",children:(0,t.jsx)("a",{href:"#","data-page":"3",children:"3"})}),(0,t.jsx)("li",{className:"",children:(0,t.jsx)("a",{href:"#","data-page":"4",children:"4"})}),(0,t.jsx)("li",{className:"",children:(0,t.jsx)("a",{href:"#","data-page":"5",children:"5"})}),(0,t.jsx)("li",{className:"pager",children:(0,t.jsx)("a",{href:"#","data-page":"2",children:(0,t.jsx)("i",{className:"bi bi-arrow-right-short"})})})]})]})]})})})})})})}},95191:(e,a,s)=>{Promise.resolve().then(s.bind(s,91797)),Promise.resolve().then(s.bind(s,38983)),Promise.resolve().then(s.bind(s,21217))}},e=>{var a=a=>e(e.s=a);e.O(0,[6874,8441,1684,7358],()=>a(95191)),_N_E=e.O()}]);