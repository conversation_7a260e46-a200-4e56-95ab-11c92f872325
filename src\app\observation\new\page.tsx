"use client";

import React, { useEffect, useState, useCallback } from "react";
import AllFilterLocation from "../../../services/AllFilterLocation";
import { Button, Form, FormLabel } from "react-bootstrap";
import FileUploader from "@/services/FileUploader";
import Select from 'react-select'
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import API from "@/services/API";
import { GET_USER_ROLE_BY_MODE, OBSERVATION_REPORT_URL } from "@/constant";
import HeaderSeven from "@/layouts/headers/HeaderSeven";
import Swal from "sweetalert2";
import { useRouter } from "next/navigation";

import ModalSelect, { PickerOption } from "@/services/ModalSelect";

interface ObservationFormData {
    observationCategory: string; // Enum type
    observationType: string; // Enum type
    observationActOrCondition: string; // Enum type
    description: string;
    comments: string;
    dueDate: string; // ISO format string
    rectifiedOnSpot: boolean | null; // Can be true, false, or null
    isQR: boolean;
    actionToBeTaken: string;
    isReviewerRequired: boolean | null; // Can be true, false, or null
    evidence: string[]; // Array of file URLs or names
    uploads: string[]; // Array of file URLs or names
    actionTaken: string;
    locationOneId: string;
    locationTwoId: string;
    locationThreeId: string;
    locationFourId: string;
    locationFiveId: string;
    locationSixId: string;
    actionOwnerId: string;
    multiActionOwnersIds: string[]; // For multiple action owners
    reviewerId: string;
}

const initialFormData: ObservationFormData = {
    observationCategory: "", // Default value
    observationType: "", // Default value
    observationActOrCondition: "", // Default value
    description: "",
    comments: "",
    dueDate: new Date().toISOString(), // Stores date as a string in ISO format
    rectifiedOnSpot: false, // Can be updated to true or false
    isQR: false,
    actionToBeTaken: "",
    isReviewerRequired: false, // Can be updated to true or false
    evidence: [], // Empty array for file uploads
    uploads: [], // Empty array for file uploads
    actionTaken: "",
    locationOneId: "",
    locationTwoId: "",
    locationThreeId: "",
    locationFourId: "",
    locationFiveId: "",
    locationSixId: "",
    actionOwnerId: "",
    multiActionOwnersIds: [], // Empty array for multiple action owners
    reviewerId: ""
};

export default function Page() {
    const [formData, setFormData] = useState(initialFormData);
    const [actionOwner, setActionOwner] = useState([])
    const [reviewer, setReviewer] = useState([])
    const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
    const router = useRouter();
    // Optional: Add any data fetching or logic on mount
    useEffect(() => {
        // e.g., load additional data if needed
        getActionOwner()
        getObsReviewer()
    }, [formData]);

    const getActionOwner = useCallback(async () => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: formData.locationOneId || "",
                locationTwoId: formData.locationTwoId || "",
                locationThreeId: formData.locationThreeId || "",
                locationFourId: formData.locationFourId || "",
                mode: 'obsactionowner'
            });

            if (response.status === 200) {
                const data = response.data.map((item: any) => ({
                    label: item.firstName,
                    value: item.id
                }));
                setActionOwner(data);
            }
        } catch (error) {
            console.error("Error fetching crew list:", error);
        }
    }, [formData.locationOneId, formData.locationTwoId, formData.locationThreeId, formData.locationFourId]);

    const getObsReviewer = useCallback(async () => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: formData.locationOneId || "",
                locationTwoId: formData.locationTwoId || "",
                locationThreeId: formData.locationThreeId || "",
                locationFourId: formData.locationFourId || "",
                mode: 'obsreviewer'
            });

            if (response.status === 200) {
                const data = response.data.map((item: any) => ({
                    label: item.firstName,
                    value: item.id
                }));
                setReviewer(data);
            }
        } catch (error) {
            console.error("Error fetching crew list:", error);
        }
    }, [formData.locationOneId, formData.locationTwoId, formData.locationThreeId, formData.locationFourId]);
    // Called whenever the user selects a new location in AllFilterLocation
    const handleFilter = (
        locationOneId: string,
        locationTwoId: string,
        locationThreeId: string,
        locationFourId: string,
        locationFiveId: string,
        locationSixId: string
    ) => {
        setFormData((prev) => ({
            ...prev,
            locationOneId,
            locationTwoId,
            locationThreeId,
            locationFourId,
            locationFiveId,
            locationSixId
        }));
    };


    const handleApplicantChange = (selectedOption: any) => {
        setFormData({
            ...formData,
            reviewerId: selectedOption ? selectedOption.value : '',
        });
    };

    const handleActionOwnerChange = (selectedOption: any) => {
        setFormData({
            ...formData,
            actionOwnerId: selectedOption ? selectedOption.value : '',
        });
    };

    const validateForm = () => {
        const errors: Record<string, string> = {};

        if (!formData.locationOneId) {
            errors.locationOneId = "Location is required.";
        }
        if (!formData.observationCategory) {
            errors.observationCategory = "Observation Category is required.";
        }
        if (!formData.observationType) {
            errors.observationType = "Observation Type is required.";
        }
        if (!formData.observationActOrCondition) {
            errors.observationActOrCondition = "Observation Act or Condition is required.";
        }
        if (!formData.description.trim()) {
            errors.description = "Description is required.";
        }
        if (formData.uploads.length < 1) {
            errors.uploads = "Please attach at least 1 supporting documents/images.";
        }

        if (formData.observationType === "Unsafe") {
            if (typeof formData.rectifiedOnSpot !== "boolean") {
                errors.rectifiedOnSpot = "Rectified on Spot is required.";
            } else if (formData.rectifiedOnSpot) {
                if (!formData.actionTaken.trim()) {
                    errors.actionTaken = "Action Taken is required.";
                }
                if (formData.evidence.length < 1) {
                    errors.evidence = "Please attach at least 1 evidence files.";
                }
            } else {
                if (typeof formData.isReviewerRequired !== "boolean") {
                    errors.isReviewerRequired = "Please specify if Reviewer is required.";
                } else if (formData.isReviewerRequired && !formData.reviewerId) {
                    errors.reviewerId = "Please select a Reviewer.";
                } else if (!formData.isReviewerRequired) {
                    if (!formData.actionToBeTaken.trim()) {
                        errors.actionToBeTaken = "Action to be Taken is required.";
                    }
                    if (!formData.dueDate) {
                        errors.dueDate = "Due Date is required.";
                    }
                    if (!formData.actionOwnerId && formData.multiActionOwnersIds.length === 0) {
                        errors.actionOwnerId = "Action Owner is required.";
                    }
                }
            }
        }

        setFieldErrors(errors);

        return Object.keys(errors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            Swal.fire({
                title: "Validation Error",
                text: "Please fill all required fields correctly.",
                icon: "warning",
                confirmButtonText: "OK",
            });

            return; // Stop if validation fails
        }
        try {
            // Prepare the data for submission
            const submissionData = { ...formData };

            // Handle action owner logic
            if (formData.multiActionOwnersIds.length === 1) {
                // Single action owner - use actionOwnerId
                submissionData.actionOwnerId = formData.multiActionOwnersIds[0];
                // Remove multiActionOwnersIds from submission
                delete (submissionData as any).multiActionOwnersIds;
            } else if (formData.multiActionOwnersIds.length === 2) {
                // Multiple action owners - use multiActionOwnersIds
                submissionData.multiActionOwnersIds = formData.multiActionOwnersIds;
                // Clear single actionOwnerId
                submissionData.actionOwnerId = "";
            } else {
                // No action owners selected - clear both
                submissionData.actionOwnerId = "";
                delete (submissionData as any).multiActionOwnersIds;
            }

            const response = await API.post(OBSERVATION_REPORT_URL, submissionData);

            if (response.status === 200) {
                Swal.fire({
                    title: "Observation Report",
                    text: "Submitted Successfully",
                    icon: "success",
                    confirmButtonText: "OK",
                }).then(() => {
                    router.back()
                    // Close modal/form after success
                    // window.location.reload(); // Uncomment if you want to reload after submission
                });
            }
        } catch (error) {
            console.error("Error submitting observation report:", error);
            Swal.fire({
                title: "Error",
                text: "Something went wrong. Please try again.",
                icon: "error",
            });
        }

    };
    return (<>



        <HeaderSeven heading={'New Observation'} />
        <div className="page-content-wrapper py-3">
            <div className="container">
                <div className="card">
                    <div className="card-body p-3">
                        <div className="mb-4">
                            <h5 className="mb-3">Location Details</h5>

                            <div className="bg-light shadow-sm p-2">
                                <AllFilterLocation handleFilter={handleFilter} getLocation={formData} disabled={false} />
                                {fieldErrors.locationOneId && (
                                    <div className="text-danger mt-1">{fieldErrors.locationOneId}</div>
                                )}
                            </div>
                        </div>

                        <div className="mb-4">
                            <h5 className="mb-3">Observation Details</h5>

                            <Form.Group controlId="observationCategory" className="mb-3">
                                <Form.Label>Category</Form.Label>
                                <div className="d-flex gap-2">
                                    {["Environment", "HSE", "Social"].map((category) => {
                                        const isActive = formData.observationCategory === category;
                                        const buttonColor = category === "Environment" ? "#dc3545" :
                                            category === "HSE" ? "#fd7e14" :
                                                "#0d6efd";
                                        return (
                                            <button
                                                key={category}
                                                type="button"
                                                className={`btn flex-grow-1 ${isActive ? "text-white" : "btn-outline-secondary"}`}
                                                style={{
                                                    backgroundColor: isActive ? buttonColor : "transparent",
                                                    borderColor: isActive ? buttonColor : "#6c757d",
                                                    transition: "all 0.3s ease-in-out",
                                                }}
                                                onClick={() =>
                                                    setFormData({
                                                        ...formData,
                                                        observationCategory: category,
                                                        observationType: "",
                                                        observationActOrCondition: "",
                                                    })
                                                }
                                            >
                                                {category}
                                            </button>
                                        );
                                    })}
                                </div>
                                {fieldErrors.observationCategory && (
                                    <div className="text-danger mt-1">{fieldErrors.observationCategory}</div>
                                )}
                            </Form.Group>
                            <Form.Group controlId="observationType" className="mb-3">
                                <Form.Label>Observation Type <span className="text-danger">*</span></Form.Label>
                                <div className="d-flex gap-2">
                                    {(formData.observationCategory === "Environment" || formData.observationCategory === "Social")
                                        ? [
                                            { label: "Positive", value: "Safe", color: "#198754" },
                                            { label: "Negative", value: "Unsafe", color: "#dc3545" },
                                        ].map(({ label, value, color }) => {
                                            const isActive = formData.observationType === value;
                                            return (
                                                <button
                                                    key={value}
                                                    type="button"
                                                    className={`btn flex-grow-1 ${isActive ? "text-white" : "btn-outline-secondary"}`}
                                                    style={{
                                                        backgroundColor: isActive ? color : "transparent",
                                                        borderColor: isActive ? color : "#6c757d",
                                                        transition: "all 0.3s ease-in-out",
                                                    }}
                                                    onClick={() =>
                                                        setFormData({
                                                            ...formData,
                                                            observationType: value,
                                                            observationActOrCondition: "", // Reset dependent fields
                                                            rectifiedOnSpot: false,
                                                            actionTaken: "",
                                                            evidence: [],
                                                            isReviewerRequired: false,
                                                            reviewerId: "",
                                                            actionToBeTaken: "",
                                                            dueDate: new Date().toISOString(),
                                                            actionOwnerId: "",
                                                            multiActionOwnersIds: [],
                                                        })
                                                    }
                                                >
                                                    {label}
                                                </button>
                                            );
                                        })
                                        : [
                                            { label: "Safe", value: "Safe", color: "#198754" },
                                            { label: "Unsafe", value: "Unsafe", color: "#dc3545" },
                                        ].map(({ label, value, color }) => {
                                            const isActive = formData.observationType === value;
                                            return (
                                                <button
                                                    key={value}
                                                    type="button"
                                                    className={`btn flex-grow-1 ${isActive ? "text-white" : "btn-outline-secondary"}`}
                                                    style={{
                                                        backgroundColor: isActive ? color : "transparent",
                                                        borderColor: isActive ? color : "#6c757d",
                                                        transition: "all 0.3s ease-in-out",
                                                    }}
                                                    onClick={() =>
                                                        setFormData({
                                                            ...formData,
                                                            observationType: value,
                                                            observationActOrCondition: "", // Reset dependent fields
                                                            rectifiedOnSpot: false,
                                                            actionTaken: "",
                                                            evidence: [],
                                                            isReviewerRequired: false,
                                                            reviewerId: "",
                                                            actionToBeTaken: "",
                                                            dueDate: new Date().toISOString(),
                                                            actionOwnerId: "",
                                                            multiActionOwnersIds: [],
                                                        })
                                                    }
                                                >
                                                    {label}
                                                </button>
                                            );
                                        })
                                    }
                                </div>
                                {fieldErrors.observationType && (
                                    <div className="text-danger mt-1">{fieldErrors.observationType}</div>
                                )}
                            </Form.Group>

                            <Form.Group controlId="observationActOrCondition" className="mb-3">
                                <Form.Label>Observation Act or Condition <span className="text-danger">*</span></Form.Label>
                                <div className="d-flex gap-2">
                                    {[
                                        { label: "Act", value: "Act" },
                                        { label: "Condition", value: "Condition" },
                                    ].map(({ label, value }) => {
                                        const isSelected = formData.observationActOrCondition === value;
                                        const baseColor = formData.observationType === "Safe" ? "#198754" : "#dc3545";
                                        return (
                                            <button
                                                key={value}
                                                type="button"
                                                className={`btn flex-grow-1 ${isSelected ? "text-white" : "btn-outline-secondary"}`}
                                                style={{
                                                    backgroundColor: isSelected ? baseColor : "transparent",
                                                    borderColor: isSelected ? baseColor : "#6c757d",
                                                    transition: "all 0.3s ease-in-out",
                                                }}
                                                onClick={() =>
                                                    setFormData({
                                                        ...formData,
                                                        observationActOrCondition: value,
                                                    })
                                                }
                                            >
                                                {label}
                                            </button>
                                        );
                                    })}
                                </div>
                                {fieldErrors.observationActOrCondition && (
                                    <div className="text-danger mt-1">{fieldErrors.observationActOrCondition}</div>
                                )}
                            </Form.Group>

                            <Form.Group controlId="description" className="mb-3">
                                <Form.Label>Enter a detailed description of the observation</Form.Label>
                                <Form.Control
                                    as="textarea"
                                    rows={3}
                                    placeholder="Provide details about what was observed..."
                                    value={formData.description || ''}
                                    onChange={(e) =>
                                        setFormData((prev) => ({
                                            ...prev,
                                            description: e.target.value,
                                        }))
                                    }
                                />
                                {fieldErrors.description && (
                                    <div className="text-danger mt-1">{fieldErrors.description}</div>
                                )}
                            </Form.Group>

                            <FormLabel>Upload any relevant photos or documents. You can select multiple files.</FormLabel>
                            <Form.Group controlId="attachments" className="mb-3">
                                <div className="mb-2">
                                    <FileUploader
                                        onFilesSelected={(files) => setFormData((prev) => ({
                                            ...prev,
                                            uploads: files,
                                        }))}
                                        files={formData.uploads}
                                        disabled={false}
                                    />
                                </div>
                                <small className="text-muted"></small>
                                {fieldErrors.uploads && (
                                    <div className="text-danger mt-1">{fieldErrors.uploads}</div>
                                )}
                            </Form.Group>




                            {formData.observationType === 'Unsafe' && (<>

                                <Form.Group controlId="rectifiedOnSpot" className="mb-3">
                                    <Form.Label>Was the observation addressed on the spot?</Form.Label>
                                    <div className="d-flex gap-2">
                                        {[
                                            { label: "Yes", value: true, color: "#198754" },
                                            { label: "No", value: false, color: "#dc3545" },
                                        ].map(({ label, value, color }) => {
                                            const isSelected = formData.rectifiedOnSpot === value;
                                            return (
                                                <button
                                                    key={label}
                                                    type="button"
                                                    className={`btn flex-grow-1 ${isSelected ? "text-white" : "btn-outline-secondary"}`}
                                                    style={{
                                                        backgroundColor: isSelected ? color : "transparent",
                                                        borderColor: isSelected ? color : "#6c757d",
                                                        transition: "all 0.3s ease-in-out",
                                                    }}
                                                    onClick={() =>
                                                        setFormData({
                                                            ...formData,
                                                            rectifiedOnSpot: value,
                                                            actionTaken: "",
                                                            evidence: [],
                                                            isReviewerRequired: false,
                                                            reviewerId: "",
                                                            actionToBeTaken: "",
                                                            dueDate: new Date().toISOString(),
                                                            actionOwnerId: "",
                                                            multiActionOwnersIds: [],
                                                        })
                                                    }
                                                >
                                                    {label}
                                                </button>
                                            );
                                        })}
                                    </div>
                                    {fieldErrors.rectifiedOnSpot && (
                                        <div className="text-danger mt-1">{fieldErrors.rectifiedOnSpot}</div>
                                    )}
                                </Form.Group>

                                {formData.rectifiedOnSpot ? (
                                    <>
                                        {/* Action Taken */}
                                        <Form.Group controlId="actionTaken" className="mt-3">
                                            <Form.Label>Action Taken</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={formData.actionTaken || ''}
                                                onChange={(e) =>
                                                    setFormData((prev) => ({
                                                        ...prev,
                                                        actionTaken: e.target.value,
                                                    }))
                                                }
                                            />
                                            {fieldErrors.actionTaken && (
                                                <div className="text-danger mt-1">
                                                    {fieldErrors.actionTaken}
                                                </div>
                                            )}
                                        </Form.Group>

                                        {/* Evidence Upload */}

                                        <Form.Group controlId="evidence" className="mb-3">
                                            <Form.Label>Upload evidence for the actions taken.</Form.Label>
                                            <div className="mb-2">
                                                <FileUploader
                                                    onFilesSelected={(files) => setFormData((prev) => ({
                                                        ...prev,
                                                        evidence: files,
                                                    }))}
                                                    files={formData.evidence}
                                                    disabled={false}
                                                />
                                            </div>
                                            {fieldErrors.evidence && (
                                                <div className="text-danger mt-1">{fieldErrors.evidence}</div>
                                            )}
                                        </Form.Group>


                                    </>
                                ) : (
                                    <>
                                        {/* Need reviewer? */}
                                        <Form.Group controlId="isReviewerRequired" className="mb-3">
                                            <Form.Label>Do you Need to send this Observation to Reviewer?</Form.Label>
                                            <div className="d-flex gap-2">
                                                {[
                                                    { label: "Yes", value: true, color: "#198754" },
                                                    { label: "No", value: false, color: "#dc3545" },
                                                ].map(({ label, value, color }) => {
                                                    const isSelected = formData.isReviewerRequired === value;
                                                    return (
                                                        <button
                                                            key={label}
                                                            type="button"
                                                            className={`btn flex-grow-1 ${isSelected ? "text-white" : "btn-outline-secondary"}`}
                                                            style={{
                                                                backgroundColor: isSelected ? color : "transparent",
                                                                borderColor: isSelected ? color : "#6c757d",
                                                                transition: "all 0.3s ease-in-out",
                                                            }}
                                                            onClick={() =>
                                                                setFormData({
                                                                    ...formData,
                                                                    isReviewerRequired: value,
                                                                    reviewerId: value ? "" : formData.reviewerId,
                                                                    actionToBeTaken: value ? "" : formData.actionToBeTaken,
                                                                    dueDate: value ? new Date().toISOString() : formData.dueDate,
                                                                    actionOwnerId: value ? "" : formData.actionOwnerId,
                                                                    multiActionOwnersIds: value ? [] : formData.multiActionOwnersIds,
                                                                })
                                                            }
                                                        >
                                                            {label}
                                                        </button>
                                                    );
                                                })}
                                            </div>
                                            {fieldErrors.isReviewerRequired && (
                                                <div className="text-danger mt-1">{fieldErrors.isReviewerRequired}</div>
                                            )}
                                        </Form.Group>


                                        {formData.isReviewerRequired ? (
                                            <>
                                                {/* Reviewer is required */}
                                                <Form.Group className="mb-3 mt-3">
                                                    {/* <Form.Label>Reviewer</Form.Label> */}
                                                    {/* <Select
                                                    options={reviewer}
                                                    value={reviewer.find(
                                                        (option: any) => option.value === formData.reviewerId
                                                    )}
                                                    onChange={handleApplicantChange}
                                                    placeholder="Select Reviewer"
                                                    isClearable
                                                /> */}
                                                    <ModalSelect
                                                        title="Reviewer"
                                                        options={reviewer}                // same array of { value, label }
                                                        selectedValue={formData.reviewerId}
                                                        onChange={(newVal) => {
                                                            setFormData((prev) => ({
                                                                ...prev,
                                                                reviewerId: newVal,
                                                            }));
                                                        }}
                                                        placeholder="Select Reviewer"
                                                        clearable
                                                        disabled={false}
                                                    />
                                                    {fieldErrors.reviewerId && (
                                                        <div className="text-danger mt-1">
                                                            {fieldErrors.reviewerId}
                                                        </div>
                                                    )}
                                                </Form.Group>
                                            </>
                                        ) : (
                                            <>
                                                {/* Action fields */}
                                                <Form.Group controlId="actionToBeTaken" className="mb-3 mt-3">
                                                    <Form.Label className="mb-3">Action to be Taken</Form.Label>
                                                    <Form.Control
                                                        type="text"
                                                        value={formData.actionToBeTaken || ''}
                                                        onChange={(e) =>
                                                            setFormData((prev) => ({
                                                                ...prev,
                                                                actionToBeTaken: e.target.value,
                                                            }))
                                                        }
                                                    />
                                                    {fieldErrors.actionToBeTaken && (
                                                        <div className="text-danger mt-1">
                                                            {fieldErrors.actionToBeTaken}
                                                        </div>
                                                    )}
                                                </Form.Group>

                                                <Form.Group controlId="dueDate" className="mb-3">
                                                    <Form.Label className="mb-3">Due Date</Form.Label>
                                                    <div className="d-block d-flex">
                                                        <DatePicker
                                                            selected={formData.dueDate ? new Date(formData.dueDate) : null}
                                                            onChange={(date) =>
                                                                setFormData((prev) => ({
                                                                    ...prev,
                                                                    dueDate: date ? date.toISOString() : "", // Convert date to ISO format
                                                                }))
                                                            }
                                                            dateFormat="yyyy-MM-dd"
                                                            className="form-control d-flex mt-2 w-100" // Added margin to create space
                                                            placeholderText="Select a date"
                                                            minDate={new Date()} // Restrict past dates
                                                        />
                                                    </div>

                                                    {fieldErrors.dueDate && (
                                                        <div className="text-danger mt-1">{fieldErrors.dueDate}</div>
                                                    )}
                                                </Form.Group>

                                                <Form.Group className="mb-3">
                                                    {/* <Form.Label className="mb-3">Action Owner</Form.Label> */}
                                                    {/* <Select
                                                    options={actionOwner}
                                                    value={actionOwner.find(
                                                        (option: any) => option.value === formData.actionOwnerId
                                                    )}
                                                    onChange={handleActionOwnerChange}
                                                    placeholder="Select Action Owner"
                                                    isClearable
                                                /> */}
                                                    <ModalSelect
                                                        title="Action Owner (Select 1 or 2)"
                                                        options={actionOwner}                // same array of { value, label }
                                                        multiSelect={true}
                                                        selectedValues={formData.multiActionOwnersIds}
                                                        onMultiChange={(newValues) => {
                                                            setFormData((prev) => ({
                                                                ...prev,
                                                                multiActionOwnersIds: newValues,
                                                                // Set single actionOwnerId if only one is selected
                                                                actionOwnerId: newValues.length === 1 ? newValues[0] : "",
                                                            }));
                                                        }}
                                                        placeholder="Select Action Owner(s)"
                                                        clearable
                                                        disabled={false}
                                                        maxSelections={2}
                                                    />
                                                    {fieldErrors.actionOwnerId && (
                                                        <div className="text-danger mt-1">
                                                            {fieldErrors.actionOwnerId}
                                                        </div>
                                                    )}
                                                </Form.Group>
                                            </>
                                        )}
                                    </>
                                )}

                            </>)}


                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div className="footer-nav-area" id="footerNav">
            <div className="container px-0">
                <div className="footer-nav position-relative">
                    <div className="h-100 d-flex align-items-center justify-content-between ps-0 w-100">
                        <Button variant="primary" className=" w-100" onClick={handleSubmit}>
                            Submit
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    </>);
}
