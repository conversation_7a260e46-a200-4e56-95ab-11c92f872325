"use client";

import HeaderSeven from "@/layouts/headers/HeaderSeven";
import React, { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import API from "@/services/API";
import { FILE_DOWNLOAD, USERS_URL, PERMIT_REPORT } from "@/constant";
import { Modal, Button } from "react-bootstrap";
import moment from "moment";
import MyLogoComponent from "@/services/MyLogoComponet";
import ViewEptw from "./ViewEPTW";
import { RootState } from "@/store";
import { useSelector } from "react-redux";

const New: React.FC = () => {
    const router = useRouter();
    const [activeTab, setActiveTab] = useState("permit");
    const user = useSelector((state: RootState) => state.login.user);
    const [permitData, setPermitData] = useState<any[]>([]);
    const [archivedPermits, setArchivedPermits] = useState<any[]>([]);
    const [archivedCount, setArchivedCount] = useState<number>(0);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const limit = 10;

    const [loading, setLoading] = useState<boolean>(false);
    const [loadingArchived, setLoadingArchived] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    const [reportData, setReportData] = useState<any | null>(null);
    const [showModal, setShowModal] = useState(false);

    const [users, setUsers] = useState<any[]>([]);
    const [logo, setLogo] = useState<string>("");

    const fetchArchivedPermits = async (page = 1, limit = 10) => {
        setLoadingArchived(true);
        const uriString = {
            include: [
                { relation: 'locationOne' },
                { relation: 'locationTwo' },
                { relation: 'locationThree' },
                { relation: 'locationFour' },
                { relation: 'locationFive' },
                { relation: 'locationSix' },
                { relation: 'applicant' },
                { relation: 'assessor' },
                { relation: 'approver' },
                { relation: 'reviewer' },
            ]
        };

        const archivedUrl = `/archived-permits?page=${page}&limit=${limit}&filter=${encodeURIComponent(JSON.stringify(uriString))}`;

        try {
            const response = await API.get(archivedUrl);
            const { data, total } = response.data;
            setArchivedPermits(data || []);
            setArchivedCount(total || 0);
        } catch (error) {
            console.error('Error fetching archived permits:', error);
        } finally {
            setLoadingArchived(false);
        }
    };

    const fetchAPermitData = async () => {
        setLoading(true);
        setError(null);
        try {
            const uriString = {
                include: [
                    { relation: 'locationOne' },
                    { relation: 'locationTwo' },
                    { relation: 'locationThree' },
                    { relation: 'locationFour' },
                    { relation: 'locationFive' },
                    { relation: 'locationSix' },
                    { relation: 'applicant' },
                    { relation: 'assessor' },
                    { relation: 'approver' },
                    { relation: 'reviewer' },
                ]
            };

            const url = `${PERMIT_REPORT}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
            const response = await API.get(url);
            if (response.status === 200) {
                setPermitData(response.data.reverse());
            }
        } catch (err) {
            setError("Failed to fetch permit data.");
            console.error("Error fetching permit data:", err);
        } finally {
            setLoading(false);
        }
    };

    const getAllUsers = async () => {
        const response = await API.get(USERS_URL);
        setUsers(response.data);
    };

    const getFetchLogo = async () => {
        try {
            const response = await API.get(FILE_DOWNLOAD(localStorage.getItem("logo") || ""), {
                headers: { "Content-Type": "application/json" },
            });
            setLogo(response.data);
        } catch (error) {
            console.error("Error fetching logo:", error);
        }
    };

    useEffect(() => {
        getFetchLogo();
    }, []);

    useEffect(() => {
        if (activeTab === "archived") {
            fetchArchivedPermits(currentPage, limit);
            getAllUsers();
        }
        if (activeTab === "permit") {
            fetchAPermitData();
            getAllUsers();
        }
    }, [activeTab, currentPage]);

    const isEptwApplicant = useMemo(() => {
        return (user as any)?.roles?.some((role: any) => role.maskId === "eptwApplicant");
    }, [user]);

    const handleObservationClick = (obs: any) => {
        setReportData(obs);
        setShowModal(true);
    };
    const activePermitData = permitData.filter(item => item.status === 'Active');
    const pendingPermitData = permitData.filter(item => item.status !== 'Active');
    return (
        <>
            <div className="page-content-wrapper" style={{ backgroundColor: "#ffffff" }}>
                <div className="container-fluid px-3 py-3">
                    {/* Apply Permit Card */}
                    {isEptwApplicant && (
                        <div
                            className="card border-0 p-4 mb-4 d-flex align-items-center flex-row"
                            style={{
                                cursor: "pointer",
                                transition: "transform 0.2s ease, box-shadow 0.2s ease",
                                boxShadow: "0 4px 12px rgba(0,0,0,0.15)"
                            }}
                            onClick={() => router.push("/eptw-gen/new")}
                            onMouseEnter={(e) => {
                                e.currentTarget.style.transform = "translateY(-3px)";
                                e.currentTarget.style.boxShadow = "0 8px 25px rgba(220, 53, 69, 0.2)";
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.transform = "translateY(0)";
                                e.currentTarget.style.boxShadow = "0 4px 12px rgba(0,0,0,0.15)";
                            }}
                        >
                            <div
                                className="rounded-circle d-flex align-items-center justify-content-center me-3"
                                style={{
                                    width: "56px",
                                    height: "56px",
                                    backgroundColor: "#dc3545",
                                    color: "white"
                                }}
                            >
                                <i className="bi bi-file-earmark-plus" style={{ fontSize: "24px" }}></i>
                            </div>
                            <div className="flex-grow-1">
                                <h6 className="mb-1" style={{ fontWeight: "600", color: "#111827" }}>
                                    Apply Permit
                                </h6>
                                <p className="mb-0 text-muted" style={{ fontSize: "14px" }}>
                                    Create and submit a new permit application
                                </p>
                            </div>
                            <i className="bi bi-chevron-right text-muted" style={{ fontSize: "20px" }}></i>
                        </div>
                    )}
                    {/* Simple Tab Navigation */}
                    <div className="mb-4">
                        <div className="d-flex gap-2 p-1 rounded-3" style={{ backgroundColor: "#f8fafc" }}>
                            {[
                                { id: "permit", label: "Under Review" },
                                { id: "active", label: "Active" },
                                { id: "archived", label: "Archived" }
                            ].map((tab) => (
                                <button
                                    key={tab.id}
                                    className={`btn flex-fill py-2 px-3 rounded-2 ${
                                        activeTab === tab.id
                                            ? "btn-danger text-white"
                                            : "btn-light text-muted"
                                    }`}
                                    style={{
                                        border: "none",
                                        fontWeight: "500",
                                        fontSize: "14px",
                                        transition: "all 0.2s ease"
                                    }}
                                    onClick={() => {
                                        setActiveTab(tab.id);
                                        setCurrentPage(1);
                                    }}
                                >
                                    {tab.label}
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Content Area */}
                    <div>
                        {/* Under Review Tab */}
                        {activeTab === "permit" && (
                            <div>
                                {/* Simple Header */}
                                <div className="d-flex justify-content-between align-items-center mb-3">
                                    <h6 className="mb-0" style={{ color: "#374151", fontWeight: "600" }}>
                                        Under Review Permits
                                    </h6>
                                    <span className="badge bg-light text-muted px-3 py-2" style={{ fontSize: "12px" }}>
                                        {pendingPermitData.length} items
                                    </span>
                                </div>

                                {/* Content */}
                                <div style={{ maxHeight: "70vh", overflowY: "auto" }}>
                                    {loading && (
                                        <div className="text-center py-5">
                                            <div className="spinner-border text-danger" role="status">
                                                <span className="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    )}

                                    {error && (
                                        <div className="alert alert-danger" role="alert">
                                            {error}
                                        </div>
                                    )}

                                    {pendingPermitData.length > 0 ? (
                                        <div className="row g-3">
                                            {pendingPermitData.map((obs, index) => (
                                                <div key={index} className="col-12">
                                                    <div
                                                        className="card border-0"
                                                        style={{
                                                            cursor: "pointer",
                                                            transition: "transform 0.2s ease, box-shadow 0.2s ease",
                                                            boxShadow: "0 4px 12px rgba(0,0,0,0.15)"
                                                        }}
                                                        onClick={() => handleObservationClick(obs)}
                                                        onMouseEnter={(e) => {
                                                            e.currentTarget.style.transform = "translateY(-3px)";
                                                            e.currentTarget.style.boxShadow = "0 8px 25px rgba(0,0,0,0.2)";
                                                        }}
                                                        onMouseLeave={(e) => {
                                                            e.currentTarget.style.transform = "translateY(0)";
                                                            e.currentTarget.style.boxShadow = "0 4px 12px rgba(0,0,0,0.15)";
                                                        }}
                                                    >
                                                        <div className="card-body p-3">
                                                            <div className="d-flex justify-content-between align-items-center mb-2">
                                                                <span className="text-muted small">#{obs.maskId}</span>
                                                                <span className="badge bg-warning text-dark" style={{ fontSize: "11px" }}>
                                                                    {obs.status}
                                                                </span>
                                                            </div>

                                                            <h6 className="mb-2" style={{ color: "#111827", lineHeight: "1.4" }}>
                                                                {obs.workDescription}
                                                            </h6>

                                                            <div className="row g-2 mb-2">
                                                                <div className="col-6">
                                                                    <small className="text-muted d-block">Applicant</small>
                                                                    <span style={{ fontSize: "13px", fontWeight: "500" }}>
                                                                        {obs.applicant?.firstName || "N/A"}
                                                                    </span>
                                                                </div>
                                                                <div className="col-6">
                                                                    <small className="text-muted d-block">Assessor</small>
                                                                    <span style={{ fontSize: "13px", fontWeight: "500" }}>
                                                                        {obs.assessor?.firstName || "N/A"}
                                                                    </span>
                                                                </div>
                                                            </div>

                                                            <div className="d-flex justify-content-between align-items-center">
                                                                <small className="text-muted">
                                                                    {new Date(obs.created).toLocaleDateString()}
                                                                </small>
                                                                <small className="text-muted">
                                                                    Reviewer: {obs.reviewer?.firstName || "N/A"}
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        !loading && (
                                            <div className="text-center py-5">
                                                <i className="bi bi-file-earmark-text text-muted mb-3" style={{ fontSize: "3rem" }}></i>
                                                <p className="text-muted">No permits under review</p>
                                            </div>
                                        )
                                    )}
                                </div>
                            </div>
                        )}
                        {/* Active Tab */}
                        {activeTab === "active" && (
                            <div>
                                {/* Simple Header */}
                                <div className="d-flex justify-content-between align-items-center mb-3">
                                    <h6 className="mb-0" style={{ color: "#374151", fontWeight: "600" }}>
                                        Active Permits
                                    </h6>
                                    <span className="badge bg-light text-muted px-3 py-2" style={{ fontSize: "12px" }}>
                                        {activePermitData.length} items
                                    </span>
                                </div>

                                {/* Content */}
                                <div style={{ maxHeight: "70vh", overflowY: "auto" }}>
                                    {loading && (
                                        <div className="text-center py-5">
                                            <div className="spinner-border text-success" role="status">
                                                <span className="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    )}

                                    {error && (
                                        <div className="alert alert-danger" role="alert">
                                            {error}
                                        </div>
                                    )}

                                    {activePermitData.length > 0 ? (
                                        <div className="row g-3">
                                            {activePermitData.map((obs, index) => (
                                                <div key={index} className="col-12">
                                                    <div
                                                        className="card border-0"
                                                        style={{
                                                            cursor: "pointer",
                                                            transition: "transform 0.2s ease, box-shadow 0.2s ease",
                                                            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
                                                            borderLeft: "3px solid #198754"
                                                        }}
                                                        onClick={() => handleObservationClick(obs)}
                                                        onMouseEnter={(e) => {
                                                            e.currentTarget.style.transform = "translateY(-3px)";
                                                            e.currentTarget.style.boxShadow = "0 8px 25px rgba(25, 135, 84, 0.2)";
                                                        }}
                                                        onMouseLeave={(e) => {
                                                            e.currentTarget.style.transform = "translateY(0)";
                                                            e.currentTarget.style.boxShadow = "0 4px 12px rgba(0,0,0,0.15)";
                                                        }}
                                                    >
                                                        <div className="card-body p-3">
                                                            <div className="d-flex justify-content-between align-items-center mb-2">
                                                                <span className="text-muted small">#{obs.maskId}</span>
                                                                <span className="badge bg-success text-white" style={{ fontSize: "11px" }}>
                                                                    {obs.status}
                                                                </span>
                                                            </div>

                                                            <h6 className="mb-2" style={{ color: "#111827", lineHeight: "1.4" }}>
                                                                {obs.workDescription}
                                                            </h6>

                                                            <div className="row g-2 mb-2">
                                                                <div className="col-6">
                                                                    <small className="text-muted d-block">Applicant</small>
                                                                    <span style={{ fontSize: "13px", fontWeight: "500" }}>
                                                                        {obs.applicant?.firstName || "N/A"}
                                                                    </span>
                                                                </div>
                                                                <div className="col-6">
                                                                    <small className="text-muted d-block">Approver</small>
                                                                    <span style={{ fontSize: "13px", fontWeight: "500" }}>
                                                                        {obs.approver?.firstName || "N/A"}
                                                                    </span>
                                                                </div>
                                                            </div>

                                                            <div className="d-flex justify-content-between align-items-center">
                                                                <small className="text-muted">
                                                                    {new Date(obs.created).toLocaleDateString()}
                                                                </small>
                                                                <small className="text-success fw-bold">
                                                                    Active Permit
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        !loading && (
                                            <div className="text-center py-5">
                                                <i className="bi bi-check-circle text-success mb-3" style={{ fontSize: "3rem" }}></i>
                                                <p className="text-muted">No active permits found</p>
                                            </div>
                                        )
                                    )}
                                </div>
                            </div>
                        )}
                        {/* Archived Tab */}
                        {activeTab === "archived" && (
                            <div>
                                {/* Simple Header */}
                                <div className="d-flex justify-content-between align-items-center mb-3">
                                    <h6 className="mb-0" style={{ color: "#374151", fontWeight: "600" }}>
                                        Archived Permits
                                    </h6>
                                    <span className="badge bg-light text-muted px-3 py-2" style={{ fontSize: "12px" }}>
                                        {archivedPermits.length} items
                                    </span>
                                </div>

                                {/* Content */}
                                <div style={{ maxHeight: "70vh", overflowY: "auto" }}>
                                    {loadingArchived && (
                                        <div className="text-center py-5">
                                            <div className="spinner-border text-secondary" role="status">
                                                <span className="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    )}

                                    {error && (
                                        <div className="alert alert-danger" role="alert">
                                            {error}
                                        </div>
                                    )}

                                    {archivedPermits.length > 0 ? (
                                        <>
                                            <div className="row g-3">
                                                {archivedPermits.map((obs, index) => (
                                                    <div key={index} className="col-12">
                                                        <div
                                                            className="card border-0"
                                                            style={{
                                                                cursor: "pointer",
                                                                transition: "transform 0.2s ease, box-shadow 0.2s ease",
                                                                boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
                                                                opacity: "0.8"
                                                            }}
                                                            onClick={() => handleObservationClick(obs)}
                                                            onMouseEnter={(e) => {
                                                                e.currentTarget.style.transform = "translateY(-3px)";
                                                                e.currentTarget.style.boxShadow = "0 8px 25px rgba(0,0,0,0.2)";
                                                                e.currentTarget.style.opacity = "1";
                                                            }}
                                                            onMouseLeave={(e) => {
                                                                e.currentTarget.style.transform = "translateY(0)";
                                                                e.currentTarget.style.boxShadow = "0 4px 12px rgba(0,0,0,0.15)";
                                                                e.currentTarget.style.opacity = "0.8";
                                                            }}
                                                        >
                                                            <div className="card-body p-3">
                                                                <div className="d-flex justify-content-between align-items-center mb-2">
                                                                    <span className="text-muted small">#{obs.maskId}</span>
                                                                    <span className="badge bg-secondary text-white" style={{ fontSize: "11px" }}>
                                                                        {obs.Status || "Archived"}
                                                                    </span>
                                                                </div>

                                                                <h6 className="mb-2" style={{ color: "#111827", lineHeight: "1.4" }}>
                                                                    {obs.workDescription}
                                                                </h6>

                                                                <div className="row g-2 mb-2">
                                                                    <div className="col-6">
                                                                        <small className="text-muted d-block">Applicant</small>
                                                                        <span style={{ fontSize: "13px", fontWeight: "500" }}>
                                                                            {obs.applicant?.firstName || "N/A"}
                                                                        </span>
                                                                    </div>
                                                                    <div className="col-6">
                                                                        <small className="text-muted d-block">Approver</small>
                                                                        <span style={{ fontSize: "13px", fontWeight: "500" }}>
                                                                            {obs.approver?.firstName || "N/A"}
                                                                        </span>
                                                                    </div>
                                                                </div>

                                                                <div className="d-flex justify-content-between align-items-center">
                                                                    <small className="text-muted">
                                                                        {new Date(obs.created).toLocaleDateString()}
                                                                    </small>
                                                                    <small className="text-secondary">
                                                                        Archived
                                                                    </small>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>

                                            {/* Clean Pagination */}
                                            <div className="d-flex justify-content-center align-items-center gap-3 mt-4 pt-3 border-top">
                                                <Button
                                                    variant="outline-secondary"
                                                    size="sm"
                                                    disabled={currentPage === 1}
                                                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                                                    style={{ borderRadius: "8px" }}
                                                >
                                                    <i className="bi bi-chevron-left me-1"></i>
                                                    Previous
                                                </Button>
                                                <span className="badge bg-light text-dark px-3 py-2">
                                                    Page {currentPage}
                                                </span>
                                                <Button
                                                    variant="outline-secondary"
                                                    size="sm"
                                                    disabled={currentPage >= Math.ceil(archivedCount / limit)}
                                                    onClick={() => setCurrentPage(prev => prev + 1)}
                                                    style={{ borderRadius: "8px" }}
                                                >
                                                    Next
                                                    <i className="bi bi-chevron-right ms-1"></i>
                                                </Button>
                                            </div>
                                        </>
                                    ) : (
                                        !loadingArchived && (
                                            <div className="text-center py-5">
                                                <i className="bi bi-archive text-muted mb-3" style={{ fontSize: "3rem" }}></i>
                                                <p className="text-muted">No archived permits found</p>
                                            </div>
                                        )
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Permit Details Modal */}
                <Modal show={showModal} onHide={() => setShowModal(false)} centered>
                    <Modal.Header closeButton>
                        {reportData && (
                            <div className="row w-100">
                                <div className="col-12 d-flex align-items-start gap-3">
                                    <MyLogoComponent logo={logo} />
                                    <div>
                                        <h6>Permit</h6>
                                        <div className="d-flex align-items-center">
                                            <p className="me-2">#{reportData.maskId}</p>
                                            <p className="badge bg-primary text-white">{reportData.status}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </Modal.Header>
                    <Modal.Body>
                        {/* ViewOBS or detail view goes here */}
                        <ViewEptw applicationDetails={reportData} />
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={() => setShowModal(false)}>Close</Button>
                    </Modal.Footer>
                </Modal>

        </>
    );
};

export default New;
