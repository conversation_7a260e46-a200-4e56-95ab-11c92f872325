(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4403],{10255:(e,t,s)=>{"use strict";function n(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return n}}),s(95155),s(47650),s(85744),s(20589)},17828:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return n}});let n=(0,s(64054).createAsyncLocalStorage)()},21217:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var n=s(95155),l=s(9e4),a=s(38808),i=s(12115);let c=e=>{let{handleShowSetting:t,showSetting:s}=e,{theme:i,handleDarkModeToggle:c}=(0,l.D)(),{viewMode:r,handleRTLToggling:d}=(0,a.L)();return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{id:"setting-popup-overlay",className:s?"active":"",onClick:t}),(0,n.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(s?"active":""),id:"settingCard",children:(0,n.jsx)("div",{className:"card-body",children:(0,n.jsxs)("div",{className:"container",children:[(0,n.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,n.jsx)("p",{className:"mb-0",children:"Settings"}),(0,n.jsx)("div",{onClick:t,className:"btn-close",id:"settingCardClose"})]}),(0,n.jsx)("div",{className:"single-setting-panel",children:(0,n.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,n.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,n.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,n.jsx)("div",{className:"single-setting-panel",children:(0,n.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,n.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,n.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,n.jsx)("div",{className:"single-setting-panel",children:(0,n.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,n.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===i,onChange:c}),(0,n.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===i?"Light":"Dark"," mode"]})]})}),(0,n.jsx)("div",{className:"single-setting-panel",children:(0,n.jsxs)("div",{className:"form-check form-switch",children:[(0,n.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===r,onChange:d}),(0,n.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===r?"LTR":"RTL"," mode"]})]})})]})})})]})};var r=s(6874),d=s.n(r);let o=e=>{let{links:t,title:s}=e,[l,a]=(0,i.useState)(!1),r=()=>a(!l);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"header-area",id:"headerArea",children:(0,n.jsx)("div",{className:"container",children:(0,n.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,n.jsx)("div",{className:"back-button",children:(0,n.jsx)(d(),{href:"/".concat(t),children:(0,n.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,n.jsx)("div",{className:"page-heading",children:(0,n.jsx)("h6",{className:"mb-0",children:s})}),(0,n.jsx)("div",{className:"setting-wrapper",onClick:r,children:(0,n.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,n.jsx)("i",{className:"bi bi-gear"}),(0,n.jsx)("span",{})]})})]})})}),(0,n.jsx)(c,{showSetting:l,handleShowSetting:r})]})}},33360:(e,t,s)=>{Promise.resolve().then(s.bind(s,83122)),Promise.resolve().then(s.bind(s,38983)),Promise.resolve().then(s.bind(s,21217))},36645:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let n=s(88229)._(s(67357));function l(e,t){var s;let l={};"function"==typeof e&&(l.loader=e);let a={...l,...t};return(0,n.default)({...a,modules:null==(s=a.loadableGenerated)?void 0:s.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38808:(e,t,s)=>{"use strict";s.d(t,{L:()=>l});var n=s(12115);let l=()=>{let[e,t]=(0,n.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,n.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let s=()=>{t(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:s,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}s()}}}},38983:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var n=s(95155),l=s(6874),a=s.n(l);s(12115);let i=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],c=()=>(0,n.jsx)(n.Fragment,{children:(0,n.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,n.jsx)("div",{className:"container px-0",children:(0,n.jsx)("div",{className:"footer-nav position-relative",children:(0,n.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:i.map((e,t)=>(0,n.jsx)("li",{children:(0,n.jsxs)(a(),{href:"/".concat(e.link),children:[(0,n.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,n.jsx)("span",{children:e.title})]})},t))})})})})})},55028:(e,t,s)=>{"use strict";s.d(t,{default:()=>l.a});var n=s(36645),l=s.n(n)},62146:(e,t,s)=>{"use strict";function n(e){let{reason:t,children:s}=e;return s}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return n}}),s(45262)},64054:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{bindSnapshot:function(){return i},createAsyncLocalStorage:function(){return a},createSnapshot:function(){return c}});let s=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class n{disable(){throw s}getStore(){}run(){throw s}exit(){throw s}enterWith(){throw s}static bind(e){return e}}let l="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function a(){return l?new l:new n}function i(e){return l?l.bind(e):n.bind(e)}function c(){return l?l.snapshot():function(e,...t){return e(...t)}}},67357:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}});let n=s(95155),l=s(12115),a=s(62146);function i(e){return{default:e&&"default"in e?e.default:e}}s(10255);let c={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},r=function(e){let t={...c,...e},s=(0,l.lazy)(()=>t.loader().then(i)),r=t.loading;function d(e){let i=r?(0,n.jsx)(r,{isLoading:!0,pastDelay:!0,error:null}):null,c=!t.ssr||!!t.loading,d=c?l.Suspense:l.Fragment,o=t.ssr?(0,n.jsxs)(n.Fragment,{children:[null,(0,n.jsx)(s,{...e})]}):(0,n.jsx)(a.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(s,{...e})});return(0,n.jsx)(d,{...c?{fallback:i}:{},children:o})}return d.displayName="LoadableComponent",d}},83122:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var n=s(95155);s(12115);let l=(0,s(55028).default)(()=>s.e(9053).then(s.bind(s,39053)),{loadableGenerated:{webpack:()=>[39053]},ssr:!1}),a=()=>(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("div",{className:"page-content-wrapper py-3",children:[(0,n.jsx)("div",{className:"container",children:(0,n.jsx)("div",{className:"element-heading",children:(0,n.jsx)("h6",{children:"Countdown 01"})})}),(0,n.jsx)("div",{className:"container",children:(0,n.jsx)("div",{className:"card shadow-sm bg-img",style:{backgroundImage:"url(/assets/img/core-img/2.png)"},children:(0,n.jsxs)("div",{className:"card-body text-center p-5",children:[(0,n.jsx)("h2",{children:"Coming Soon"}),(0,n.jsx)("p",{children:"It is very nicely designed & coded with the latest technology."}),(0,n.jsx)("div",{className:"countdown1 clockdiv",children:(0,n.jsx)(l,{})})]})})}),(0,n.jsx)("div",{className:"container",children:(0,n.jsx)("div",{className:"element-heading mt-3",children:(0,n.jsx)("h6",{children:"Countdown 02"})})}),(0,n.jsx)("div",{className:"container",children:(0,n.jsx)("div",{className:"card bg-info coming-soon-card text-center bg-img",style:{backgroundImage:"url(/assets/img/core-img/1.png)"},children:(0,n.jsxs)("div",{className:"card-body p-5",children:[(0,n.jsx)("div",{className:"icon-wrap",children:(0,n.jsx)("i",{className:"bi bi-clock text-info"})}),(0,n.jsx)("h2",{className:"text-white",children:"Coming Soon"}),(0,n.jsx)("p",{className:"text-white",children:"It is very nicely designed & coded with the latest technology."}),(0,n.jsx)("div",{className:"countdown3 clockdiv","data-date":"December 22, 2023 21:14:01",children:(0,n.jsx)(l,{})})]})})}),(0,n.jsx)("div",{className:"container",children:(0,n.jsx)("div",{className:"element-heading mt-3",children:(0,n.jsx)("h6",{children:"Countdown 03"})})}),(0,n.jsx)("div",{className:"card bg-img bg-overlay rounded-0 py-5",style:{backgroundImage:"url(/assets/img/bg-img/30.jpg)"},children:(0,n.jsx)("div",{className:"card-body py-2",children:(0,n.jsxs)("div",{className:"container direction-rtl",children:[(0,n.jsxs)("h2",{className:"text-white",children:["New",(0,n.jsx)("span",{className:"text-warning ms-2",children:"update"}),(0,n.jsx)("br",{})," available in:"]}),(0,n.jsx)("div",{className:"countdown2 clockdiv",children:(0,n.jsx)(l,{})})]})})})]})})},85744:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return n.workAsyncStorageInstance}});let n=s(17828)},9e4:(e,t,s)=>{"use strict";s.d(t,{D:()=>l});var n=s(12115);let l=()=>{let[e,t]=(0,n.useState)("light"),[s,l]=(0,n.useState)(!1);(0,n.useEffect)(()=>{t(localStorage.getItem("theme")||"light"),l(!0)},[]),(0,n.useEffect)(()=>{s&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,s]);let a=(0,n.useCallback)(()=>{t(e=>"dark"===e?"light":"dark")},[]),i=(0,n.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}a()},[a]);return{theme:e,toggleTheme:a,handleDarkModeToggle:i}}}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,8441,1684,7358],()=>t(33360)),_N_E=e.O()}]);