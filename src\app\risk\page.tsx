"use client";

import HeaderSeven from "@/layouts/headers/HeaderSeven";
import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import API from "@/services/API";
import { FILE_DOWNLOAD, RISKASSESSMENT_LIST } from "@/constant";
import { Mo<PERSON>, But<PERSON> } from "react-bootstrap"; // Import Bootstrap Modal
import MyLogoComponent from "@/services/MyLogoComponet";
import axios from "axios";
import ViewRisk from "@/components/risk/ViewRisk";
import OfflineAwareLayout from "@/components/offline/OfflineAwareLayout";

const Risk: React.FC = () => {
    const router = useRouter();
    const [activeTab, setActiveTab] = useState("activity");

    // States for risk assessments
    const [risk, setRisk] = useState<any[]>([]);
    const [highRisk, setHighRisk] = useState<any[]>([]);
    const [riskCount, setRiskCount] = useState<number>(0);
    const [highRiskCount, setHighRiskCount] = useState<number>(0);

    const [reportData, setReportData] = useState<any | null>(null);
    const [showModal, setShowModal] = useState(false);

    // Separate loading and error states for each API
    const [loading, setLoading] = useState({ risk: false, highRisk: false });
    const [error, setError] = useState({ risk: null as string | null, highRisk: null as string | null });

    const [logo, setLogo] = useState('')

    // Fetch activity-based risk assessments
    const fetchRisk = async () => {
        try {
            setLoading(prev => ({ ...prev, risk: true }));
            setError(prev => ({ ...prev, risk: null }));

            const uriString = {
                where: {
                    $and: [
                        { $or: [{ status: "Pending" }, { status: "Published" }] },
                        { $or: [{ type: "Routine" }, { type: "Non Routine" }] }
                    ]
                },
                include: [
                    { relation: "department" },
                    { relation: "teamLeader" },
                    { relation: "workActivity" },
                    {
                        relation: "raTeamMembers",
                        scope: { include: [{ relation: "user" }] }
                    }
                ]
            };

            const url = `${RISKASSESSMENT_LIST}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
            const response = await API.get(url);

            if (response.status === 200) {
                setRisk(response.data);
                setRiskCount(response.data.length);
            }
        } catch (err) {
            setError(prev => ({ ...prev, risk: "Failed to fetch risk assessments." }));
            console.error(err);
        } finally {
            setLoading(prev => ({ ...prev, risk: false }));
        }
    };

    // Fetch high-risk hazard assessments
    const fetchHighRisk = async () => {
        try {
            setLoading(prev => ({ ...prev, highRisk: true }));
            setError(prev => ({ ...prev, highRisk: null }));

            const uriString = {
                where: {
                    $and: [
                        { $or: [{ status: "Pending" }, { status: "Published" }] },
                        { type: "High-Risk Hazard" }
                    ]
                },
                include: [
                    { relation: "department" },
                    { relation: "teamLeader" },
                    { relation: "workActivity" },
                    {
                        relation: "raTeamMembers",
                        scope: { include: [{ relation: "user" }] }
                    }
                ]
            };

            const url = `${RISKASSESSMENT_LIST}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
            const response = await API.get(url);

            if (response.status === 200) {
                setHighRisk(response.data);
                setHighRiskCount(response.data.length);
            }
        } catch (err) {
            setError(prev => ({ ...prev, highRisk: "Failed to fetch high-risk hazards." }));
            console.error(err);
        } finally {
            setLoading(prev => ({ ...prev, highRisk: false }));
        }
    };

    useEffect(() => {
        getFetchLogo()
    }, [])

    const getFetchLogo = async () => {
        try {
            const response = await API.get(FILE_DOWNLOAD(localStorage.getItem('logo')), {
                headers: {
                    'Content-Type': 'application/json',
                },
            });
            const data = response.data;
            const logoUrl = data // Assuming the API returns an object with a `url` field
            setLogo(logoUrl);

        } catch (error) {
            console.error('Error fetching logo:', error);
        }
    }

    // Call the appropriate fetch function when the active tab changes
    useEffect(() => {
        if (activeTab === "activity") {
            fetchRisk();
        } else if (activeTab === "hazard") {
            fetchHighRisk();
        }
    }, [activeTab]);

    const handleRAClick = (item: any) => {
        // Example: Navigate to a detail page using the item's id
        setReportData(item);
        setShowModal(true);
    };

    return (
        <>
            <HeaderSeven heading={"Risk Awareness"} />
            <OfflineAwareLayout
                pageTitle="Risk Awareness"
                requiresOnline={true}
            >
                <div className="page-content-wrapper" style={{ backgroundColor: "#f8fafc", minHeight: "100vh" }}>
                <div className="container-fluid px-3 py-4">
                    {/* Enhanced Tab Navigation */}
                    <div className="mb-4">
                        <div className="card border-0 shadow-sm">
                            <div className="card-body p-2">
                                <div className="d-flex gap-1">
                                    {[
                                        { id: "activity", label: "Activity Based", icon: "bi-list-task", count: riskCount },
                                        { id: "hazard", label: "Hazard Based", icon: "bi-exclamation-triangle", count: highRiskCount }
                                    ].map((tab) => (
                                        <button
                                            key={tab.id}
                                            className={`btn flex-fill py-3  rounded-3 d-flex align-items-center justify-content-center gap-2 ${
                                                activeTab === tab.id
                                                    ? "btn-primary text-white shadow-sm"
                                                    : "btn-light text-muted"
                                            }`}
                                            style={{
                                                border: "none",
                                                fontWeight: "600",
                                                fontSize: "14px",
                                                transition: "all 0.3s ease",
                                                position: "relative"
                                            }}
                                            onClick={() => setActiveTab(tab.id)}
                                        >
                                            <i className={`${tab.icon} ${activeTab === tab.id ? 'text-white' : 'text-primary'}`}></i>
                                            <span>{tab.label}</span>
                                            {tab.count > 0 && (
                                                <span className={`badge ${
                                                    activeTab === tab.id ? 'bg-white text-primary' : 'bg-primary text-white'
                                                } ms-1`} style={{ fontSize: "10px" }}>
                                                    {tab.count}
                                                </span>
                                            )}
                                        </button>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Content Area */}
                    <div>
                        {activeTab === "activity" && (
                            <div>
                                {/* Enhanced Header */}
                                <div className="card border-0 shadow-sm mb-4">
                                    <div className="card-body py-3">
                                        <div className="d-flex justify-content-between align-items-center">
                                            <div className="d-flex align-items-center gap-3">
                                                <div className="p-2 rounded-circle" style={{ backgroundColor: "#e0f2fe" }}>
                                                    <i className="bi bi-list-task text-primary" style={{ fontSize: "1.2rem" }}></i>
                                                </div>
                                                <div>
                                                    <h6 className="mb-0" style={{ color: "#1f2937", fontWeight: "600" }}>
                                                        Activity Based Risk Assessments
                                                    </h6>
                                                    <small className="text-muted">Routine and non-routine activities</small>
                                                </div>
                                            </div>
                                            <div className="text-end">
                                                <span className="badge bg-primary px-3 py-2" style={{ fontSize: "12px" }}>
                                                    {riskCount} items
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Content */}
                                <div style={{ maxHeight: "calc(100vh - 300px)", overflowY: "auto" }} className="custom-scrollbar">
                                    {loading.risk && (
                                        <div className="text-center py-5">
                                            <div className="d-flex flex-column align-items-center">
                                                <div className="spinner-border text-primary mb-3" role="status">
                                                    <span className="visually-hidden">Loading...</span>
                                                </div>
                                                <p className="text-muted mb-0">Loading risk assessments...</p>
                                            </div>
                                        </div>
                                    )}

                                    {error.risk && (
                                        <div className="alert alert-danger border-0 shadow-sm" role="alert">
                                            <div className="d-flex align-items-center">
                                                <i className="bi bi-exclamation-triangle-fill me-2"></i>
                                                {error.risk}
                                            </div>
                                        </div>
                                    )}

                                    {risk.length > 0 ? (
                                        <div className="row g-3">
                                            {risk.map((item, index) => (
                                                <div key={index} className="col-12">
                                                    <div
                                                        className="card border-0 h-100"
                                                        style={{
                                                            cursor: "pointer",
                                                            transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                                                            boxShadow: "0 4px 12px rgba(0,0,0,0.08)",
                                                            borderRadius: "16px",
                                                            backgroundColor: "#FFFFFF",
                                                            borderLeft: "4px solid #3b82f6"
                                                        }}
                                                        onClick={() => handleRAClick(item)}
                                                        onMouseEnter={(e) => {
                                                            e.currentTarget.style.transform = "translateY(-4px)";
                                                            e.currentTarget.style.boxShadow = "0 12px 32px rgba(59, 130, 246, 0.15)";
                                                        }}
                                                        onMouseLeave={(e) => {
                                                            e.currentTarget.style.transform = "translateY(0)";
                                                            e.currentTarget.style.boxShadow = "0 4px 12px rgba(0,0,0,0.08)";
                                                        }}
                                                    >
                                                        <div className="card-body p-4">
                                                            <div className="d-flex justify-content-between align-items-start mb-3">
                                                                <div className="d-flex align-items-center gap-2">
                                                                    <div className="p-2 rounded-circle" style={{ backgroundColor: "#eff6ff" }}>
                                                                        <i className="bi bi-clipboard-check text-primary" style={{ fontSize: "0.9rem" }}></i>
                                                                    </div>
                                                                    <span className="text-muted fw-medium" style={{ fontSize: "13px" }}>#{item.maskId}</span>
                                                                </div>
                                                                <span className={`badge px-3 py-1 ${
                                                                    item.status === "Published" ? "bg-success" :
                                                                    item.status === "Pending" ? "bg-warning text-dark" : "bg-secondary"
                                                                }`} style={{ fontSize: "11px", fontWeight: "600" }}>
                                                                    {item.status}
                                                                </span>
                                                            </div>

                                                            <h6 className="mb-3 fw-semibold" style={{ color: "#1f2937", lineHeight: "1.4", fontSize: "15px" }}>
                                                                {item.type === "Routine" ? item.workActivity?.name : item.description}
                                                            </h6>

                                                            <div className="d-flex justify-content-between align-items-center">
                                                                <div className="d-flex align-items-center gap-1 text-muted">
                                                                    <i className="bi bi-calendar3" style={{ fontSize: "12px" }}></i>
                                                                    <small style={{ fontSize: "12px" }}>
                                                                        {new Date(item.created).toLocaleDateString()}
                                                                    </small>
                                                                </div>
                                                                <span className={`badge ${
                                                                    item.type === "Routine" ? "bg-primary" : "bg-info"
                                                                } text-white px-2 py-1`} style={{ fontSize: "10px", fontWeight: "600" }}>
                                                                    {item.type || "N/A"}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        !loading.risk && (
                                            <div className="text-center py-5">
                                                <div className="d-flex flex-column align-items-center">
                                                    <div className="p-4 rounded-circle mb-3" style={{ backgroundColor: "#f3f4f6" }}>
                                                        <i className="bi bi-clipboard-x text-muted" style={{ fontSize: "2.5rem" }}></i>
                                                    </div>
                                                    <h6 className="text-muted mb-2">No Risk Assessments Found</h6>
                                                    <p className="text-muted small mb-0">There are no activity-based risk assessments available at the moment.</p>
                                                </div>
                                            </div>
                                        )
                                    )}
                                </div>
                            </div>
                        )}

                        {activeTab === "hazard" && (
                            <div>
                                {/* Enhanced Header */}
                                <div className="card border-0 shadow-sm mb-4">
                                    <div className="card-body py-3">
                                        <div className="d-flex justify-content-between align-items-center">
                                            <div className="d-flex align-items-center gap-3">
                                                <div className="p-2 rounded-circle" style={{ backgroundColor: "#fef2f2" }}>
                                                    <i className="bi bi-exclamation-triangle text-danger" style={{ fontSize: "1.2rem" }}></i>
                                                </div>
                                                <div>
                                                    <h6 className="mb-0" style={{ color: "#1f2937", fontWeight: "600" }}>
                                                        High-Risk Hazards
                                                    </h6>
                                                    <small className="text-muted">Critical hazards requiring immediate attention</small>
                                                </div>
                                            </div>
                                            <div className="text-end">
                                                <span className="badge bg-danger px-3 py-2" style={{ fontSize: "12px" }}>
                                                    {highRiskCount} items
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Content */}
                                <div style={{ maxHeight: "calc(100vh - 300px)", overflowY: "auto" }} className="custom-scrollbar">
                                    {loading.highRisk && (
                                        <div className="text-center py-5">
                                            <div className="d-flex flex-column align-items-center">
                                                <div className="spinner-border text-danger mb-3" role="status">
                                                    <span className="visually-hidden">Loading...</span>
                                                </div>
                                                <p className="text-muted mb-0">Loading high-risk hazards...</p>
                                            </div>
                                        </div>
                                    )}

                                    {error.highRisk && (
                                        <div className="alert alert-danger border-0 shadow-sm" role="alert">
                                            <div className="d-flex align-items-center">
                                                <i className="bi bi-exclamation-triangle-fill me-2"></i>
                                                {error.highRisk}
                                            </div>
                                        </div>
                                    )}

                                    {highRisk.length > 0 ? (
                                        <div className="row g-3">
                                            {highRisk.map((item, index) => (
                                                <div key={index} className="col-12">
                                                    <div
                                                        className="card border-0 h-100"
                                                        style={{
                                                            cursor: "pointer",
                                                            transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                                                            borderLeft: "4px solid #dc2626",
                                                            boxShadow: "0 4px 12px rgba(220, 38, 38, 0.12)",
                                                            borderRadius: "16px",
                                                            backgroundColor: "#FFFFFF"
                                                        }}
                                                        onClick={() => handleRAClick(item)}
                                                        onMouseEnter={(e) => {
                                                            e.currentTarget.style.transform = "translateY(-4px)";
                                                            e.currentTarget.style.boxShadow = "0 12px 32px rgba(220, 38, 38, 0.25)";
                                                        }}
                                                        onMouseLeave={(e) => {
                                                            e.currentTarget.style.transform = "translateY(0)";
                                                            e.currentTarget.style.boxShadow = "0 4px 12px rgba(220, 38, 38, 0.12)";
                                                        }}
                                                    >
                                                        <div className="card-body p-4">
                                                            <div className="d-flex justify-content-between align-items-start mb-3">
                                                                <div className="d-flex align-items-center gap-2">
                                                                    <div className="p-2 rounded-circle" style={{ backgroundColor: "#fef2f2" }}>
                                                                        <i className="bi bi-exclamation-triangle-fill text-danger" style={{ fontSize: "0.9rem" }}></i>
                                                                    </div>
                                                                    <span className="text-muted fw-medium" style={{ fontSize: "13px" }}>#{item.maskId}</span>
                                                                </div>
                                                                <span className={`badge px-3 py-1 ${
                                                                    item.status === "Published" ? "bg-success" :
                                                                    item.status === "Pending" ? "bg-warning text-dark" : "bg-secondary"
                                                                }`} style={{ fontSize: "11px", fontWeight: "600" }}>
                                                                    {item.status}
                                                                </span>
                                                            </div>

                                                            <h6 className="mb-3 fw-semibold" style={{ color: "#1f2937", lineHeight: "1.4", fontSize: "15px" }}>
                                                                {item.hazardName || "No Description"}
                                                            </h6>

                                                            <div className="d-flex justify-content-between align-items-center">
                                                                <div className="d-flex align-items-center gap-1 text-muted">
                                                                    <i className="bi bi-calendar3" style={{ fontSize: "12px" }}></i>
                                                                    <small style={{ fontSize: "12px" }}>
                                                                        {new Date(item.created).toLocaleDateString()}
                                                                    </small>
                                                                </div>
                                                                <span className="badge bg-danger text-white px-2 py-1" style={{ fontSize: "10px", fontWeight: "600" }}>
                                                                    <i className="bi bi-exclamation-triangle-fill me-1" style={{ fontSize: "8px" }}></i>
                                                                    High Risk
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        !loading.highRisk && (
                                            <div className="text-center py-5">
                                                <div className="d-flex flex-column align-items-center">
                                                    <div className="p-4 rounded-circle mb-3" style={{ backgroundColor: "#f0fdf4" }}>
                                                        <i className="bi bi-shield-check text-success" style={{ fontSize: "2.5rem" }}></i>
                                                    </div>
                                                    <h6 className="text-success mb-2">All Clear!</h6>
                                                    <p className="text-muted small mb-0">No high-risk hazards found. Your workplace is currently safe.</p>
                                                </div>
                                            </div>
                                        )
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            <Modal show={showModal} onHide={() => setShowModal(false)} centered size="lg">
                    <Modal.Header closeButton className="border-0 pb-0">
                        {reportData && (
                            <div className="w-100">
                                <div className="d-flex align-items-center justify-content-between">
                                    <div className="d-flex align-items-center gap-3">
                                        <MyLogoComponent logo={logo} />
                                        <div>
                                            <h5 className="mb-1 fw-semibold">Risk Assessment Details</h5>
                                            <div className="d-flex align-items-center gap-2">
                                                <span className="text-muted small">#{reportData.maskId || ''}</span>
                                                <span className={`badge px-2 py-1 ${
                                                    reportData.status === "Published" ? "bg-success" :
                                                    reportData.status === "Pending" ? "bg-warning text-dark" : "bg-secondary"
                                                }`} style={{ fontSize: "11px" }}>
                                                    {reportData.status}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </Modal.Header>
                    <Modal.Body className="pt-2">
                        <ViewRisk reportData={reportData} />
                    </Modal.Body>
                    <Modal.Footer className="border-0 pt-0">
                        <Button
                            variant="outline-secondary"
                            onClick={() => setShowModal(false)}
                            className="px-4"
                        >
                            <i className="bi bi-x-lg me-2"></i>
                            Close
                        </Button>
                    </Modal.Footer>
                </Modal>

                <style jsx>{`
        .observation-report {
          background: #fff;
          border-radius: 10px;
          padding: 20px;
        }
        .section-title {
          font-size: 1.1rem;
          font-weight: bold;
          color: #333;
        }
        .obs-title {
          font-size: 0.9rem;
          font-weight: bold;
          color: #555;
          margin-bottom: 5px;
        }
        .obs-content {
          font-size: 0.9rem;
          color: #777;
        }
        .image-box {
          border: 1px solid #ddd;
          background: #f8f9fa;
          height: 100px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .custom-scrollbar {
          scrollbar-width: thin;
          scrollbar-color: #cbd5e1 #f1f5f9;
        }
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f1f5f9;
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #cbd5e1;
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #94a3b8;
        }
        @media (max-width: 768px) {
          .card-body {
            padding: 1rem !important;
          }
        }
      `}</style>
            </OfflineAwareLayout>
        </>
    );
};

export default Risk;
