/* FileUploader.tsx ─────────────────────────────────────────────────────── */
"use client";

import React, { useState } from "react";
import { Form, Spinner } from "react-bootstrap";
import API from "@/services/API";
import { FILE_URL } from "@/constant";
import piexif from "piexifjs";
import * as exifr from "exifr";
import ImageComponent from "./FileDownlodS3";
import { Camera, CameraResultType, CameraSource } from "@capacitor/camera";

/* ───────────────────────── helpers ──────────────────────────────────── */

/* deg → rational DMS for EXIF */
const degToDms = (deg: number) =>
  (piexif as any).GPSHelper.degToDmsRational(deg);

const ensureGeoAllowed = async (): Promise<boolean> => {
  if (!navigator.permissions || !navigator.geolocation) return false;

  const status = await navigator.permissions.query({ name: "geolocation" });
  switch (status.state) {
    case "granted":
      return true;                     // already allowed
    case "prompt":
      // will trigger the native prompt once
      return await new Promise((ok) =>
        navigator.geolocation.getCurrentPosition(
          () => ok(true),
          () => ok(false),
          { maximumAge: 0, timeout: 8000 }
        )
      );
    case "denied":
    default:
      return false;
  }
};

// Function to trigger location permission prompt
const requestLocationPermission = async (): Promise<boolean> => {
  if (!navigator.geolocation) return false;

  return new Promise((resolve) => {
    // Always attempt to get current position, which will trigger permission prompt if needed
    navigator.geolocation.getCurrentPosition(
      (position) => {
        console.log("Location permission granted, coordinates:", position.coords.latitude, position.coords.longitude);
        resolve(true);  // Permission granted
      },
      (error) => {
        console.log("Location permission denied or error:", error.message);
        resolve(false); // Permission denied or error
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0  // Always get fresh location to trigger permission prompt
      }
    );
  });
};
/* safe geolocation with 8-second timeout */
const getCoords = (): Promise<GeolocationCoordinates | null> =>
  new Promise((resolve) => {
    if (!navigator.geolocation) return resolve(null);

    let settled = false;
    const timer = setTimeout(() => {
      if (!settled) {
        settled = true;
        resolve(null); // timeout
      }
    }, 8000);

    navigator.geolocation.getCurrentPosition(
      (pos) => {
        if (!settled) {
          settled = true;
          clearTimeout(timer);
          resolve(pos.coords);
        }
      },
      () => {
        if (!settled) {
          settled = true;
          clearTimeout(timer);
          resolve(null); // denied / error
        }
      },
      { enableHighAccuracy: true, maximumAge: 10_000 }
    );
  });

/* polyfill for createImageBitmap (iOS PWA) */
const safeCreateBitmap = async (
  file: File
): Promise<ImageBitmap | HTMLImageElement> => {
  if ("createImageBitmap" in window) {
    // @ts-ignore
    return await createImageBitmap(file);
  }
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
};

/* Google reverse-geocode with 4-second abort */
const fetchAddress = async (
  lat: number,
  lng: number
): Promise<string | null> => {
  const key = 'AIzaSyDNdPSBifICIPY7YumDCko5JX5tTRkYYSk'
  if (!key) return null;

  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), 4000);

  try {
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${key}`;
    const res = await fetch(url, { signal: controller.signal });
    if (!res.ok) return null;
    const json = await res.json();

    // If we have results, try to get a more concise address
    if (json.results && json.results.length > 0) {
      // Try to find a more concise result from the address components
      // Look for the most appropriate result type for a readable address
      for (const result of json.results) {
        if (
          result.types.includes('street_address') ||
          result.types.includes('premise') ||
          result.types.includes('route')
        ) {
          return result.formatted_address;
        }
      }

      // If we couldn't find a more specific result, use the first one
      // but try to make it more concise by removing the country and postal code
      const fullAddress = json.results[0].formatted_address;

      // Extract just the first part of the address (usually the most relevant)
      const parts = fullAddress.split(',');
      if (parts.length > 2) {
        // Return first two parts of the address (usually street and city/area)
        return parts.slice(0, 2).join(',');
      }

      return fullAddress;
    }

    return null;
  } catch (error) {
    console.error("Error fetching address:", error);
    return null;
  } finally {
    clearTimeout(timeout);
  }
};

/* embed EXIF GPS (returns original file if coords null or not a JPEG) */
const injectGPS = async (file: File): Promise<File> => {
  // Only JPEG/JPG files can have EXIF data with GPS
  if (!/\.jpe?g$/i.test(file.name)) {
    console.log("Not a JPEG file, skipping GPS injection (but will still add footer):", file.name);
    return file;
  }

  // Get current coordinates with timeout
  console.log("Getting current coordinates...");
  const coords = await getCoords();
  if (!coords) {
    console.log("Could not get coordinates, returning original file");
    return file;
  }

  console.log("Got coordinates:", coords.latitude, coords.longitude);

  // Convert file to base64
  const base64 = await new Promise<string>((ok, err) => {
    const fr = new FileReader();
    fr.onerror = () => {
      console.error("FileReader error:", fr.error);
      err(fr.error);
    };
    fr.onload = () => ok(fr.result as string);
    fr.readAsDataURL(file);
  });

  try {
    // Load existing EXIF data or create new object
    const exifObj = piexif.load(base64);

    // Set GPS data in EXIF
    exifObj.GPS = {
      [piexif.GPSIFD.GPSLatitudeRef]: coords.latitude >= 0 ? "N" : "S",
      [piexif.GPSIFD.GPSLatitude]: degToDms(Math.abs(coords.latitude)),
      [piexif.GPSIFD.GPSLongitudeRef]: coords.longitude >= 0 ? "E" : "W",
      [piexif.GPSIFD.GPSLongitude]: degToDms(Math.abs(coords.longitude)),
      // Add GPS date/time for better compatibility
      [piexif.GPSIFD.GPSDateStamp]: new Date().toISOString().split('T')[0].replace(/-/g, ':'),
      // Add GPS version for better compatibility
      [piexif.GPSIFD.GPSVersionID]: [2, 3, 0, 0],
    };

    // Insert EXIF data back into image
    console.log("Inserting GPS EXIF data into image");
    const newB64 = piexif.insert(piexif.dump(exifObj), base64);

    // Convert back to File object
    const bin = atob(newB64.split(",")[1]);
    return new File([Uint8Array.from(bin, (c) => c.charCodeAt(0))], file.name, {
      type: "image/jpeg",
    });
  } catch (err) {
    console.error("Error injecting GPS data:", err);
    return file; // Return original file on error
  }
};

/* extend image and append footer strip with text wrapping */
const appendFooter = async (file: File, label: string) => {
  const img = await safeCreateBitmap(file);
  // @ts-ignore width/height presence on HTMLImageElement
  const w = img.width,
    h = img.height;
  const pad = 16;
  const fontSize = Math.max(16, h * 0.025); // Slightly smaller font for more text

  // Function to wrap text and calculate required height
  const wrapText = (ctx: CanvasRenderingContext2D, text: string, maxWidth: number) => {
    const words = text.split(' ');
    const lines = [];
    let currentLine = words[0];

    for (let i = 1; i < words.length; i++) {
      const word = words[i];
      const width = ctx.measureText(currentLine + ' ' + word).width;

      if (width < maxWidth) {
        currentLine += ' ' + word;
      } else {
        lines.push(currentLine);
        currentLine = word;
      }
    }
    lines.push(currentLine);
    return lines;
  };

  // Create temporary canvas to measure text
  const tempCanvas = document.createElement('canvas');
  const tempCtx = tempCanvas.getContext('2d')!;
  tempCtx.font = `${fontSize}px sans-serif`;

  // Calculate wrapped text and required height
  const maxTextWidth = w - (pad * 2);
  const lines = wrapText(tempCtx, label, maxTextWidth);
  const lineHeight = fontSize * 1.2; // Add some line spacing
  const textHeight = lines.length * lineHeight;
  const footerH = textHeight + (pad * 2);

  // Create the actual canvas with the calculated height
  const canvas = document.createElement("canvas");
  canvas.width = w;
  canvas.height = h + footerH;

  const ctx = canvas.getContext("2d")!;
  ctx.drawImage(img as any, 0, 0);

  // Draw footer background
  ctx.fillStyle = "#f2f2f2";
  ctx.fillRect(0, h, w, footerH);

  // Draw text with wrapping
  ctx.font = `${fontSize}px sans-serif`;
  ctx.fillStyle = "#000";
  ctx.textBaseline = "top";

  lines.forEach((line, index) => {
    ctx.fillText(
      line,
      pad,
      h + pad + (index * lineHeight)
    );
  });

  // Add a subtle border at the top of the footer
  ctx.strokeStyle = "#ddd";
  ctx.lineWidth = 1;
  ctx.beginPath();
  ctx.moveTo(0, h);
  ctx.lineTo(w, h);
  ctx.stroke();

  const blob: Blob = await new Promise((ok) =>
    canvas.toBlob((b) => ok(b!), "image/jpeg", 0.92)
  );
  return new File([blob], file.name, { type: "image/jpeg" });
};

/* ───────────────────────── component ────────────────────────────────── */
interface Props {
  onFilesSelected: (l: string[]) => void;
  disabled: boolean;
  files: string[];
}

const FileUploader: React.FC<Props> = ({
  onFilesSelected,
  disabled,
  files,
}) => {
  const [pending, setPending] = useState<string[]>([]);
  const [busy, setBusy] = useState(false);

  const allowed =
    ".jpg,.jpeg,.png,.gif,.bmp,.webp,.svg,.pdf,.xls,.xlsx".split(",");

  const uploadOne = async (file: File) => {
    setPending((p) => [...p, file.name]);
    try {
      const fd = new FormData();
      fd.append("file", file);
      const r = await API.post(FILE_URL, fd, {
        headers: { "Content-Type": "multipart/form-data" },
      });
      if (r?.status === 200 && r.data?.files?.[0]?.originalname) {
        onFilesSelected([...files, r.data.files[0].originalname]);
      }
    } finally {
      setPending((p) => p.filter((n) => n !== file.name));
    }
  };

  const processList = async (list: FileList | File[]) => {
    setBusy(true);

    for (const original of Array.from(list)) {
      try {
        const ext = original.name
          .substring(original.name.lastIndexOf("."))
          .toLowerCase();
        if (!allowed.includes(ext)) {
          alert(`File format not allowed: ${original.name}`);
          continue;
        }

        let file: File = original;

        // Only try to inject GPS for image files (primarily JPG/JPEG, but we'll add a footer to other image types too)
        if (/\.(jpe?g|png|gif|bmp|webp)$/i.test(file.name)) {
          console.log("Attempting to inject GPS data for:", file.name);

          // Check if this is a JPEG file that can have EXIF data
          const isJpeg = /\.jpe?g$/i.test(file.name);

          if (isJpeg) {
            // Try to inject GPS data - this will return the original file if no coords available
            file = await injectGPS(file);

            // Try to read the GPS data we just injected
            const gps = await exifr.gps(file).catch((err) => {
              console.error("Error reading GPS data:", err);
              return null;
            });

            if (gps && gps.latitude && gps.longitude) {
              // We have valid GPS data
              console.log("Valid GPS coordinates found:", gps.latitude, gps.longitude);

              // Try to get a human-readable address
              let label = await fetchAddress(gps.latitude, gps.longitude);

              // If we couldn't get an address, format the coordinates nicely
              if (!label) {
                // Format coordinates in a more readable way
                const formatCoord = (coord: number, isLat: boolean) => {
                  const direction = isLat
                    ? (coord >= 0 ? "N" : "S")
                    : (coord >= 0 ? "E" : "W");
                  return `${Math.abs(coord).toFixed(6)}° ${direction}`;
                };

                label = `Location: ${formatCoord(gps.latitude, true)}, ${formatCoord(gps.longitude, false)}`;
              } else {
                // Prepend "Location: " to the address for clarity
                label = `Location: ${label}`;
              }

              console.log("Adding location label:", label);
              file = await appendFooter(file, label);

              // Re-embed EXIF after canvas export (which strips metadata)
              file = await injectGPS(file);
            } else {
              // No GPS data available - add a footer indicating no location
              console.log("No location data available for:", file.name);
              file = await appendFooter(file, "No location data available");
            }
          } else {
            // For non-JPEG image files, we can't add EXIF data but we can still add a footer
            // Try to get coordinates directly
            const coords = await getCoords();

            if (coords) {
              // We have coordinates, try to get an address
              let label = await fetchAddress(coords.latitude, coords.longitude);

              // If we couldn't get an address, format the coordinates nicely
              if (!label) {
                const formatCoord = (coord: number, isLat: boolean) => {
                  const direction = isLat
                    ? (coord >= 0 ? "N" : "S")
                    : (coord >= 0 ? "E" : "W");
                  return `${Math.abs(coord).toFixed(6)}° ${direction}`;
                };

                label = `Location: ${formatCoord(coords.latitude, true)}, ${formatCoord(coords.longitude, false)}`;
              } else {
                label = `Location: ${label}`;
              }

              console.log("Adding location label to non-JPEG image:", label);
              file = await appendFooter(file, label);
            } else {
              // No coordinates available
              console.log("No location data available for non-JPEG image:", file.name);
              file = await appendFooter(file, "No location data available");
            }
          }
        }

        // Upload the file (with or without location data)
        await uploadOne(file);
      } catch (err) {
        console.error("Mobile/PWA upload error:", err);
        alert(`Could not process ${original.name}. Skipping.`);
      }
    }

    setBusy(false);
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;

    // Check if there are any image files that would benefit from location data
    const hasImageFiles = Array.from(e.target.files).some(file =>
      /\.(jpe?g|png|gif|bmp|webp)$/i.test(file.name)
    );

    // Only ask for location permission if there are image files
    if (hasImageFiles) {
      console.log("Image files detected, checking location permission");
      const geoOk = await ensureGeoAllowed();
      if (!geoOk) {
        const proceed = window.confirm(
          "Location access is needed to add location data to your photos.\n\n" +
            "• Click OK to allow location access now\n" +
            "• Click Cancel to continue without location data"
        );

        if (proceed) {
          // User wants to enable location - trigger the browser prompt
          console.log("Requesting location permission...");
          const permissionGranted = await requestLocationPermission();

          if (!permissionGranted) {
            // If still denied, show instructions and continue
            alert(
              "Location access was denied. To enable location for photos:\n\n" +
              "1. Click the location icon in your browser's address bar\n" +
              "2. Select 'Allow' for location access\n" +
              "3. Try uploading again for location data\n\n" +
              "Continuing to upload without location data..."
            );
          } else {
            console.log("Location permission granted successfully!");
          }
        } else {
          // If user clicks Cancel, continue without location data
          console.log("User chose to continue without location data");
        }
      }
    } else {
      console.log("No image files detected, skipping location check");
    }

    processList(e.target.files);  // Process the files with or without location
    e.target.value = "";  // Reset the input
  };

  /* Camera capture with location handling */
  const captureImage = async () => {
    try {
      /* Check location permissions first */
      const geoOk = await ensureGeoAllowed();
      if (!geoOk) {
        const proceed = window.confirm(
          "Location access is needed to add location data to your photos.\n\n" +
            "• Click OK to allow location access now\n" +
            "• Click Cancel to continue without location data"
        );

        if (proceed) {
          // User wants to enable location - trigger the browser prompt
          console.log("Requesting location permission...");
          const permissionGranted = await requestLocationPermission();

          if (!permissionGranted) {
            // If still denied, show instructions
            alert(
              "Location access was denied. To enable location for photos:\n\n" +
              "1. Click the location icon in your browser's address bar\n" +
              "2. Select 'Allow' for location access\n" +
              "3. Try taking the photo again\n\n" +
              "Continuing without location data..."
            );
          } else {
            console.log("Location permission granted successfully!");
          }
        } else {
          // If user clicks Cancel, continue without location data
          console.log("User chose to continue taking photo without location data");
        }
      }

      // Take the photo regardless of location permission
      const shot = await Camera.getPhoto({
        source: CameraSource.Camera,
        resultType: CameraResultType.Uri,
        quality: 80,
      });

      if (!shot.webPath) return;

      const blob = await fetch(shot.webPath).then((r) => r.blob());
      const camFile = new File([blob], `camera_${Date.now()}.jpg`, {
        type: "image/jpeg",
      });

      setBusy(true);
      processList([camFile]);  // Process with or without location data
    } catch (err) {
      console.error("Camera capture error:", err);
      alert("Could not capture photo. Please try again.");
    }
  };

  /* --------------------- render ------------------------------------ */
  return (
    <div className="mt-3 position-relative">
      {busy && (
        <div className="position-absolute top-0 start-0 w-100 h-100 bg-white bg-opacity-75 d-flex justify-content-center align-items-center"
             style={{ zIndex: 10, backdropFilter: 'blur(2px)' }}>
          <div className="d-flex flex-column align-items-center">
            <Spinner animation="border" variant="primary" />
            <span className="mt-2 text-primary fw-medium">Processing...</span>
          </div>
        </div>
      )}

      <Form.Label className="fw-medium mb-2">
        Upload Files <span className="text-danger">*</span>
      </Form.Label>

      <div className="upload-buttons-container p-3 mb-3 bg-light rounded-3 border" style={{ backgroundColor: '#f0f1fe' }}>
        <div className="d-flex justify-content-center gap-3">
          <label className="btn px-3 py-2 d-flex align-items-center"
                 style={{
                   background: '#5a67d8',
                   borderRadius: '8px',
                   border: 'none',
                   color: 'white',
                   fontSize: '14px',
                   fontWeight: '500',
                   boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                   width: '140px',
                   justifyContent: 'center'
                 }}
          >
            <i className="bi bi-cloud-arrow-up me-2" />
            Upload Files
            <input
              hidden
              multiple
              disabled={disabled}
              type="file"
              accept={allowed.join(",")}
              onChange={handleFileChange}
            />
          </label>

          <button
            className="btn px-3 py-2 d-flex align-items-center"
            style={{
              background: '#38bdf8',
              borderRadius: '8px',
              border: 'none',
              color: 'white',
              fontSize: '14px',
              fontWeight: '500',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              width: '140px',
              justifyContent: 'center'
            }}
            disabled={disabled}
            onClick={captureImage}
          >
            <i className="bi bi-camera me-2" />
            Take Photo
          </button>
        </div>

        <div className="text-center mt-2">
          {/* <small className="text-muted">
            Supported formats: JPG, PNG, PDF, XLS, XLSX
          </small> */}
        </div>
      </div>

      {pending.length > 0 && (
        <div className="mt-2 mb-3 p-2 bg-info bg-opacity-10 rounded-3 border border-info border-opacity-25">
          {pending.map((n) => (
            <div key={n} className="d-flex align-items-center gap-2 p-1">
              <Spinner animation="border" size="sm" variant="info" />
              <span className="text-info">Uploading {n}...</span>
            </div>
          ))}
        </div>
      )}

      {files.length > 0 && (
        <div className="uploaded-files-container mt-3">
          <div className="d-flex align-items-center mb-2">
            <i className="bi bi-images me-2 text-secondary"></i>
            <span className="text-secondary">Uploaded Files ({files.length})</span>
          </div>
          <div className="row g-2">
            {files.map((fn, i) => (
              <div key={i} className="col-6">
                <div
                  className="position-relative border rounded p-2"
                  style={{
                    backgroundColor: '#f8f9fa',
                    height: '100px'
                  }}
                >
                  <div
                    className="d-flex justify-content-center align-items-center h-100"
                    style={{ position: 'relative' }}
                  >
                    <ImageComponent size={80} fileName={fn} name={false} />
                  </div>
                  <button
                    className="btn btn-sm position-absolute top-0 end-0 p-0"
                    style={{
                      backgroundColor: '#f87171',
                      borderRadius: '50%',
                      width: '22px',
                      height: '22px',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      color: 'white',
                      border: 'none',
                      margin: '2px',
                      zIndex: 10
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      const next = [...files];
                      next.splice(i, 1);
                      onFilesSelected(next);
                    }}
                  >
                    <i className="bi bi-x" style={{ fontSize: '14px' }}></i>
                  </button>
                  <div className="small text-truncate mt-1" style={{ fontSize: '11px', color: '#666' }}>
                    {fn.split('/').pop()}
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-2 text-secondary" style={{ fontSize: '13px' }}>
            Upload any relevant photos or documents. You can select multiple files.
          </div>
        </div>
      )}
    </div>
  );
};

export default FileUploader;
