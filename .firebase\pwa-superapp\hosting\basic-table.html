<!DOCTYPE html><html id="previewPage" data-theme="light" lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/57cae671176964c8.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/dd0c9f11e78b6897.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/47e96c737914dbc6.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/f834c3f21e6553d1.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/14831314538c9bba.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/b0288f40f3624e4f.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/143eeed8da4915af.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-05e9b7d736bedb12.js"/><script src="/_next/static/chunks/4bd1b696-760b913b742900c1.js" async=""></script><script src="/_next/static/chunks/1684-27cf822636589fb7.js" async=""></script><script src="/_next/static/chunks/main-app-aee89a841e90a376.js" async=""></script><script src="/_next/static/chunks/6078-824c0e1fb78de585.js" async=""></script><script src="/_next/static/chunks/635-fc754490e8f1c721.js" async=""></script><script src="/_next/static/chunks/7666-5aa1b0bf553cfd31.js" async=""></script><script src="/_next/static/chunks/6222-05ff697aae6edc8e.js" async=""></script><script src="/_next/static/chunks/app/layout-9719ca53aa52e84d.js" async=""></script><script src="/_next/static/chunks/6874-b40c7929c749ad65.js" async=""></script><script src="/_next/static/chunks/1955-be380e4885464387.js" async=""></script><script src="/_next/static/chunks/1531-6e49e700c3d39dad.js" async=""></script><script src="/_next/static/chunks/1727-e51dc7373f0f6346.js" async=""></script><script src="/_next/static/chunks/app/not-found-6f5d8f8dafe4f2c5.js" async=""></script><script src="/_next/static/chunks/app/(elements)/basic-table/page-80eced368194e048.js" async=""></script><meta name="apple-mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"/><meta name="mobile-web-app-capable" content="yes"/><meta name="theme-color" content="#ffffff"/><meta name="theme-color" content="#000000"/><link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&amp;family=Raleway:wght@100;200;300;400;500;600;700;800;900&amp;display=swap" rel="stylesheet"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body><div hidden=""><!--$--><!--/$--></div><div class="header-area" id="headerArea"><div class="container"><div class="header-content position-relative d-flex align-items-center justify-content-between"><div class="back-button"><a href="/pages/"><i class="bi bi-arrow-left-short"></i></a></div><div class="page-heading"><h6 class="mb-0">Basic Table</h6></div><div class="setting-wrapper"><div class="setting-trigger-btn" id="settingTriggerBtn"><i class="bi bi-gear"></i><span></span></div></div></div></div></div><div id="setting-popup-overlay" class=""></div><div class="card setting-popup-card shadow-lg " id="settingCard"><div class="card-body"><div class="container"><div class="setting-heading d-flex align-items-center justify-content-between mb-3"><p class="mb-0">Settings</p><div class="btn-close" id="settingCardClose"></div></div><div class="single-setting-panel"><div class="form-check form-switch mb-2"><input class="form-check-input" type="checkbox" id="availabilityStatus" checked=""/><label class="form-check-label" for="availabilityStatus">Availability status</label></div></div><div class="single-setting-panel"><div class="form-check form-switch mb-2"><input class="form-check-input" type="checkbox" id="sendMeNotifications" checked=""/><label class="form-check-label" for="sendMeNotifications">Send me notifications</label></div></div><div class="single-setting-panel"><div class="form-check form-switch mb-2"><input class="form-check-input" type="checkbox" id="darkSwitch"/><label class="form-check-label" for="darkSwitch">Dark<!-- --> mode</label></div></div><div class="single-setting-panel"><div class="form-check form-switch"><input class="form-check-input" type="checkbox" id="rtlSwitch"/><label class="form-check-label" for="rtlSwitch">RTL<!-- --> mode</label></div></div></div></div></div><div class="page-content-wrapper py-3 rk_table"><div class="container"><div class="element-heading"><h6>Basic Table</h6></div></div><div class="container"><div class="card"><div class="card-body"><table class="table mb-0"><thead><tr><th scope="col">#</th><th scope="col">First</th><th scope="col">Last</th><th scope="col">Handle</th></tr></thead><tbody><tr><th scope="row">1</th><td>Mark</td><td>Otto</td><td>@mdo</td></tr><tr><th scope="row">2</th><td>Jacob</td><td>Thornton</td><td>@fat</td></tr><tr><th scope="row">3</th><td colSpan="2">Larry the Bird</td><td>@twitter</td></tr></tbody></table></div></div></div><div class="container"><div class="element-heading mt-3"><h6>Striped Table</h6></div></div><div class="container"><div class="card"><div class="card-body"><table class="table mb-0 table-striped"><thead><tr><th scope="col">#</th><th scope="col">First</th><th scope="col">Last</th><th scope="col">Handle</th></tr></thead><tbody><tr><th scope="row">1</th><td>Mark</td><td>Otto</td><td>@mdo</td></tr><tr><th scope="row">2</th><td>Jacob</td><td>Thornton</td><td>@fat</td></tr><tr><th scope="row">3</th><td colSpan="2">Larry the Bird</td><td>@twitter</td></tr></tbody></table></div></div></div><div class="container"><div class="element-heading mt-3"><h6>Hoverable Rows Table</h6></div></div><div class="container"><div class="card"><div class="card-body"><table class="table mb-0 table-hover"><thead><tr><th scope="col">#</th><th scope="col">First</th><th scope="col">Last</th><th scope="col">Handle</th></tr></thead><tbody><tr><th scope="row">1</th><td>Mark</td><td>Otto</td><td>@mdo</td></tr><tr><th scope="row">2</th><td>Jacob</td><td>Thornton</td><td>@fat</td></tr><tr><th scope="row">3</th><td colSpan="2">Larry the Bird</td><td>@twitter</td></tr></tbody></table></div></div></div><div class="container"><div class="element-heading mt-3"><h6>Dark Table</h6></div></div><div class="container"><div class="card"><div class="card-body"><table class="table mb-0 table-dark table-striped"><thead><tr><th scope="col">#</th><th scope="col">First</th><th scope="col">Last</th><th scope="col">Handle</th></tr></thead><tbody><tr><th scope="row">1</th><td>Mark</td><td>Otto</td><td>@mdo</td></tr><tr><th scope="row">2</th><td>Jacob</td><td>Thornton</td><td>@fat</td></tr><tr><th scope="row">3</th><td colSpan="2">Larry the Bird</td><td>@twitter</td></tr></tbody></table></div></div></div></div><div class="footer-nav-area" id="footerNav"><div class="container px-0"><div class="footer-nav position-relative"><ul class="h-100 d-flex align-items-center justify-content-between ps-0"><li><a href="/home/"><i class="bi bi-house"></i><span>Home</span></a></li><li><a href="/pages/"><i class="bi bi-collection"></i><span>Pages</span></a></li><li><a href="/elements/"><i class="bi bi-folder2-open"></i><span>Elements</span></a></li><li><a href="/chat-users/"><i class="bi bi-chat-dots"></i><span>Chat</span></a></li><li><a href="/settings/"><i class="bi bi-gear"></i><span>Settings</span></a></li></ul></div></div></div><!--$--><!--/$--> <script src="/_next/static/chunks/webpack-05e9b7d736bedb12.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[79705,[\"6078\",\"static/chunks/6078-824c0e1fb78de585.js\",\"635\",\"static/chunks/635-fc754490e8f1c721.js\",\"7666\",\"static/chunks/7666-5aa1b0bf553cfd31.js\",\"6222\",\"static/chunks/6222-05ff697aae6edc8e.js\",\"7177\",\"static/chunks/app/layout-9719ca53aa52e84d.js\"],\"default\"]\n3:I[87555,[],\"\"]\n4:I[31295,[],\"\"]\n5:I[91727,[\"6874\",\"static/chunks/6874-b40c7929c749ad65.js\",\"1955\",\"static/chunks/1955-be380e4885464387.js\",\"1531\",\"static/chunks/1531-6e49e700c3d39dad.js\",\"1727\",\"static/chunks/1727-e51dc7373f0f6346.js\",\"4345\",\"static/chunks/app/not-found-6f5d8f8dafe4f2c5.js\"],\"default\"]\n6:I[6874,[\"6874\",\"static/chunks/6874-b40c7929c749ad65.js\",\"1955\",\"static/chunks/1955-be380e4885464387.js\",\"1531\",\"static/chunks/1531-6e49e700c3d39dad.js\",\"1727\",\"static/chunks/1727-e51dc7373f0f6346.js\",\"4345\",\"static/chunks/app/not-found-6f5d8f8dafe4f2c5.js\"],\"\"]\n7:I[69243,[\"6078\",\"static/chunks/6078-824c0e1fb78de585.js\",\"635\",\"static/chunks/635-fc754490e8f1c721.js\",\"7666\",\"static/chunks/7666-5aa1b0bf553cfd31.js\",\"6222\",\"static/chunks/6222-05ff697aae6edc8e.js\",\"7177\",\"static/chunks/app/layout-9719ca53aa52e84d.js\"],\"\"]\n9:I[21217,[\"6874\",\"static/chunks/6874-b40c7929c749ad65.js\",\"5691\",\"static/chunks/app/(elements)/basic-table/page-80eced368194e048.js\"],\"default\"]\na:I[38983,[\"6874\",\"static/chunks/6874-b40c7929c749ad65.js\",\"5691\",\"static/chunks/app/(elements)/basic-table/page-80eced368194e048.js\"],\"default\"]\nb:I[59665,[],\"OutletBoundary\"]\ne:I[74911,[],\"AsyncMetadataOutlet\"]\n10:I[59665,[],\"ViewportBoundary\"]\n12:I[59665,[],\"MetadataBoundary\"]\n14:I[26614,[],\"\"]\n:HL[\"/_next/static/css/57cae671176964c8.css\",\"style\"]\n:HL[\"/_next/static/css/dd0c9f11e78b6897.css\",\"style\"]\n:HL[\"/_next/static/css/47e96c737914dbc6.css\",\"style\"]\n:HL[\"/_next/static/css/f834c3f21e6553d1.css\",\"style\"]\n:HL[\"/_next/static/css/14831314538c9bba.css\",\"style\"]\n:HL[\"/_next/static/css/b0288f40f3624e4f.css\",\"style\"]\n:HL[\"/_next/static/css/143eeed8da4915af.css\",\"style\"]\n8:T568,\n         (function() {\n    var hostParts = window.location.hostname.split('.'); \n    var internalNa"])</script><script>self.__next_f.push([1,"me = hostParts.length \u003e 1 ? hostParts[1] : ''; // Extract 'internal'\n\n    console.log(\"Internal Name:\", internalName); // Debugging purpose\n\n     document.title = \"Welcome to \" + internalName.charAt(0).toUpperCase() + internalName.slice(1) + \" Portal\";\n\n    var manifestLink = document.createElement(\"link\");\n    manifestLink.rel = \"manifest\";\n    manifestLink.href = \"/manifest-\" + internalName + \".json\"; // Load client-specific manifest\n    document.head.appendChild(manifestLink);\n\n    var iosIconSizes = [16,20,32, 36, 40, 48, 57, 72, 96, 114, 144, 192, 512];\n\n    // Generate Apple Touch Icons dynamically\n    iosIconSizes.forEach(size =\u003e {\n        var appleIcon = document.createElement(\"link\");\n        appleIcon.rel = \"apple-touch-icon\";\n        appleIcon.sizes = size + \"x\" + size;\n        appleIcon.href = \"/assets/iosicons/\" + internalName + \"/\" + size + \".png\"; // Load client-specific iOS icon\n        document.head.appendChild(appleIcon);\n    });\n\n    // Add Favicon dynamically\n    var faviconLink = document.createElement(\"link\");\n    faviconLink.rel = \"icon\";\n    faviconLink.type = \"image/png\";\n    faviconLink.href = \"/assets/iosicons/\" + internalName + \"/\" + 16+ \".png\"; // Load client-specific favicon\n    document.head.appendChild(faviconLink);\n\n})();\n\n        "])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"umzR2rTgz1T-81H90Y7RI\",\"p\":\"\",\"c\":[\"\",\"basic-table\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"(elements)\",{\"children\":[\"basic-table\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/57cae671176964c8.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/dd0c9f11e78b6897.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/47e96c737914dbc6.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"3\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/f834c3f21e6553d1.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"4\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/14831314538c9bba.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"5\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/b0288f40f3624e4f.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"6\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/143eeed8da4915af.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"id\":\"previewPage\",\"data-theme\":\"light\",\"lang\":\"en\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"meta\",null,{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover\"}],[\"$\",\"meta\",null,{\"name\":\"apple-mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",null,{\"name\":\"apple-mobile-web-app-status-bar-style\",\"content\":\"black-translucent\"}],[\"$\",\"meta\",null,{\"name\":\"mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",null,{\"name\":\"theme-color\",\"content\":\"#ffffff\"}],[\"$\",\"link\",null,{\"href\":\"https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900\u0026family=Raleway:wght@100;200;300;400;500;600;700;800;900\u0026display=swap\",\"rel\":\"stylesheet\"}],[\"$\",\"meta\",null,{\"name\":\"theme-color\",\"content\":\"#000000\"}]]}],[\"$\",\"body\",null,{\"children\":[[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"$L5\",null,{\"links\":\"pages\",\"title\":\"Page Not Found\"}],[\"$\",\"div\",null,{\"className\":\"page-content-wrapper py-3\",\"children\":[\"$\",\"div\",null,{\"className\":\"custom-container\",\"children\":[\"$\",\"div\",null,{\"className\":\"card\",\"children\":[\"$\",\"div\",null,{\"className\":\"card-body px-5 text-center\",\"children\":[[\"$\",\"img\",null,{\"className\":\"mb-4\",\"src\":\"/assets/img/bg-img/39.png\",\"alt\":\"\"}],[\"$\",\"h4\",null,{\"children\":[\"OOPS... \",[\"$\",\"br\",null,{}],\" Page not found!\"]}],[\"$\",\"p\",null,{\"className\":\"mb-4\",\"children\":\"We couldnt find any results for your search. Try again.\"}],[\"$\",\"$L6\",null,{\"className\":\"btn btn-creative btn-danger\",\"href\":\"/home\",\"children\":\"Go to Home\"}]]}]}]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],\" \"]}],[\"$\",\"$L7\",null,{\"id\":\"dynamic-assets\",\"strategy\":\"afterInteractive\",\"children\":\"$8\"}]]}]]}],{\"children\":[\"(elements)\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"basic-table\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"$L9\",null,{\"links\":\"pages\",\"title\":\"Basic Table\"}],[\"$\",\"div\",null,{\"className\":\"page-content-wrapper py-3 rk_table\",\"children\":[[\"$\",\"div\",null,{\"className\":\"container\",\"children\":[\"$\",\"div\",null,{\"className\":\"element-heading\",\"children\":[\"$\",\"h6\",null,{\"children\":\"Basic Table\"}]}]}],[\"$\",\"div\",null,{\"className\":\"container\",\"children\":[\"$\",\"div\",null,{\"className\":\"card\",\"children\":[\"$\",\"div\",null,{\"className\":\"card-body\",\"children\":[\"$\",\"table\",null,{\"className\":\"table mb-0\",\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"scope\":\"col\",\"children\":\"#\"}],[\"$\",\"th\",null,{\"scope\":\"col\",\"children\":\"First\"}],[\"$\",\"th\",null,{\"scope\":\"col\",\"children\":\"Last\"}],[\"$\",\"th\",null,{\"scope\":\"col\",\"children\":\"Handle\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"scope\":\"row\",\"children\":\"1\"}],[\"$\",\"td\",null,{\"children\":\"Mark\"}],[\"$\",\"td\",null,{\"children\":\"Otto\"}],[\"$\",\"td\",null,{\"children\":\"@mdo\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"scope\":\"row\",\"children\":\"2\"}],[\"$\",\"td\",null,{\"children\":\"Jacob\"}],[\"$\",\"td\",null,{\"children\":\"Thornton\"}],[\"$\",\"td\",null,{\"children\":\"@fat\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"scope\":\"row\",\"children\":\"3\"}],[\"$\",\"td\",null,{\"colSpan\":2,\"children\":\"Larry the Bird\"}],[\"$\",\"td\",null,{\"children\":\"@twitter\"}]]}]]}]]}]}]}]}],[\"$\",\"div\",null,{\"className\":\"container\",\"children\":[\"$\",\"div\",null,{\"className\":\"element-heading mt-3\",\"children\":[\"$\",\"h6\",null,{\"children\":\"Striped Table\"}]}]}],[\"$\",\"div\",null,{\"className\":\"container\",\"children\":[\"$\",\"div\",null,{\"className\":\"card\",\"children\":[\"$\",\"div\",null,{\"className\":\"card-body\",\"children\":[\"$\",\"table\",null,{\"className\":\"table mb-0 table-striped\",\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"scope\":\"col\",\"children\":\"#\"}],[\"$\",\"th\",null,{\"scope\":\"col\",\"children\":\"First\"}],[\"$\",\"th\",null,{\"scope\":\"col\",\"children\":\"Last\"}],[\"$\",\"th\",null,{\"scope\":\"col\",\"children\":\"Handle\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"scope\":\"row\",\"children\":\"1\"}],[\"$\",\"td\",null,{\"children\":\"Mark\"}],[\"$\",\"td\",null,{\"children\":\"Otto\"}],[\"$\",\"td\",null,{\"children\":\"@mdo\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"scope\":\"row\",\"children\":\"2\"}],[\"$\",\"td\",null,{\"children\":\"Jacob\"}],[\"$\",\"td\",null,{\"children\":\"Thornton\"}],[\"$\",\"td\",null,{\"children\":\"@fat\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"scope\":\"row\",\"children\":\"3\"}],[\"$\",\"td\",null,{\"colSpan\":2,\"children\":\"Larry the Bird\"}],[\"$\",\"td\",null,{\"children\":\"@twitter\"}]]}]]}]]}]}]}]}],[\"$\",\"div\",null,{\"className\":\"container\",\"children\":[\"$\",\"div\",null,{\"className\":\"element-heading mt-3\",\"children\":[\"$\",\"h6\",null,{\"children\":\"Hoverable Rows Table\"}]}]}],[\"$\",\"div\",null,{\"className\":\"container\",\"children\":[\"$\",\"div\",null,{\"className\":\"card\",\"children\":[\"$\",\"div\",null,{\"className\":\"card-body\",\"children\":[\"$\",\"table\",null,{\"className\":\"table mb-0 table-hover\",\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"scope\":\"col\",\"children\":\"#\"}],[\"$\",\"th\",null,{\"scope\":\"col\",\"children\":\"First\"}],[\"$\",\"th\",null,{\"scope\":\"col\",\"children\":\"Last\"}],[\"$\",\"th\",null,{\"scope\":\"col\",\"children\":\"Handle\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"scope\":\"row\",\"children\":\"1\"}],[\"$\",\"td\",null,{\"children\":\"Mark\"}],[\"$\",\"td\",null,{\"children\":\"Otto\"}],[\"$\",\"td\",null,{\"children\":\"@mdo\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"scope\":\"row\",\"children\":\"2\"}],[\"$\",\"td\",null,{\"children\":\"Jacob\"}],[\"$\",\"td\",null,{\"children\":\"Thornton\"}],[\"$\",\"td\",null,{\"children\":\"@fat\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"scope\":\"row\",\"children\":\"3\"}],[\"$\",\"td\",null,{\"colSpan\":2,\"children\":\"Larry the Bird\"}],[\"$\",\"td\",null,{\"children\":\"@twitter\"}]]}]]}]]}]}]}]}],[\"$\",\"div\",null,{\"className\":\"container\",\"children\":[\"$\",\"div\",null,{\"className\":\"element-heading mt-3\",\"children\":[\"$\",\"h6\",null,{\"children\":\"Dark Table\"}]}]}],[\"$\",\"div\",null,{\"className\":\"container\",\"children\":[\"$\",\"div\",null,{\"className\":\"card\",\"children\":[\"$\",\"div\",null,{\"className\":\"card-body\",\"children\":[\"$\",\"table\",null,{\"className\":\"table mb-0 table-dark table-striped\",\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"scope\":\"col\",\"children\":\"#\"}],[\"$\",\"th\",null,{\"scope\":\"col\",\"children\":\"First\"}],[\"$\",\"th\",null,{\"scope\":\"col\",\"children\":\"Last\"}],[\"$\",\"th\",null,{\"scope\":\"col\",\"children\":\"Handle\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"scope\":\"row\",\"children\":\"1\"}],[\"$\",\"td\",null,{\"children\":\"Mark\"}],[\"$\",\"td\",null,{\"children\":\"Otto\"}],[\"$\",\"td\",null,{\"children\":\"@mdo\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"scope\":\"row\",\"children\":\"2\"}],[\"$\",\"td\",null,{\"children\":\"Jacob\"}],[\"$\",\"td\",null,{\"children\":\"Thornton\"}],[\"$\",\"td\",null,{\"children\":\"@fat\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"scope\":\"row\",\"children\":\"3\"}],[\"$\",\"td\",null,{\"colSpan\":2,\"children\":\"Larry the Bird\"}],[\"$\",\"td\",null,{\"children\":\"@twitter\"}]]}]]}]]}]}]}]}]]}],[\"$\",\"$La\",null,{}]],null,[\"$\",\"$Lb\",null,{\"children\":[\"$Lc\",\"$Ld\",[\"$\",\"$Le\",null,{\"promise\":\"$@f\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"Tez11e7fLFqgiF-b176oXv\",{\"children\":[[\"$\",\"$L10\",null,{\"children\":\"$L11\"}],null]}],[\"$\",\"$L12\",null,{\"children\":\"$L13\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$14\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"15:\"$Sreact.suspense\"\n16:I[74911,[],\"AsyncMetadata\"]\n13:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$15\",null,{\"fallback\":null,\"children\":[\"$\",\"$L16\",null,{\"promise\":\"$@17\"}]}]}]\nd:null\n"])</script><script>self.__next_f.push([1,"11:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nc:null\n"])</script><script>self.__next_f.push([1,"f:{\"metadata\":[[\"$\",\"link\",\"0\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\n17:{\"metadata\":\"$f:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>