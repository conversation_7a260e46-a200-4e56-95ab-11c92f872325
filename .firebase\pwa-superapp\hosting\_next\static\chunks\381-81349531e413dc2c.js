"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[58,381,8836],{17227:(e,t,s)=>{s.d(t,{M:()=>c});var o=s(38336),a=s(60058),r=s(48836),i=s(26957);class n{async getServices(){try{console.log("\uD83C\uDF10 Fetching services from API...");let e=await o.A.get(i.xE);if(200===e.status&&Array.isArray(e.data))return await a.offlineStorage.saveServices(e.data),console.log("✅ Services fetched and cached successfully"),e.data;throw Error("Invalid response format")}catch(t){console.log("❌ Failed to fetch services from API, trying offline cache...");let e=await a.offlineStorage.getServices();if(e.length>0)return console.log("\uD83D\uDCF1 Serving services from offline cache"),e;throw t}}async getActions(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"All";try{console.log("\uD83C\uDF10 Fetching actions from API (filter: ".concat(e,")..."));let t=(0,i.WH)(e)+"?filter=".concat(encodeURIComponent(JSON.stringify({include:[{relation:"submittedBy"}]}))),s=await o.A.get(t);if(200===s.status&&Array.isArray(s.data))return await a.offlineStorage.saveActions(s.data),console.log("✅ Actions fetched and cached successfully"),s.data;throw Error("Invalid response format")}catch(s){console.log("❌ Failed to fetch actions from API, trying offline cache...");let t=await a.offlineStorage.getActions(e);if(t.length>0)return console.log("\uD83D\uDCF1 Serving actions from offline cache"),t;throw s}}async request(e){try{return await o.A.request(e)}catch(s){if(r.offlineQueue.shouldQueue(s)){var t;await r.offlineQueue.addRequest(e.url,(null==(t=e.method)?void 0:t.toUpperCase())||"GET",e.data,e.headers)}throw s}}async isDataFresh(e){let t=await a.offlineStorage.getMetadata("".concat(e,"LastSync"));return!!t&&Date.now()-t<3e5}async forceRefresh(e,t){if(!navigator.onLine)throw Error("Cannot refresh data while offline");return"services"===e?this.getServices():"actions"===e?this.getActions(t||"All"):void 0}async getOfflineStatus(){let e=await a.offlineStorage.getStorageInfo(),t=await a.offlineStorage.getMetadata("servicesLastSync"),s=await a.offlineStorage.getMetadata("actionsLastSync");return{isOnline:navigator.onLine,hasCache:e.services>0||e.actions>0,cacheInfo:e,lastSync:{services:t,actions:s}}}async clearOfflineData(){await a.offlineStorage.clearAllData(),await r.offlineQueue.clearQueue(),console.log("\uD83D\uDDD1️ All offline data cleared")}async syncAll(){if(!navigator.onLine)throw Error("Cannot sync while offline");console.log("\uD83D\uDD04 Starting full sync...");try{await this.getServices();let e=await a.offlineStorage.getServices();for(let t of["All",...e.map(e=>e.maskName)])await this.getActions(t);await r.offlineQueue.processQueue(),console.log("✅ Full sync completed")}catch(e){throw console.error("❌ Sync failed:",e),e}}onConnectionChange(e){let t=()=>e(!0),s=()=>e(!1);return window.addEventListener("online",t),window.addEventListener("offline",s),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",s)}}async getDataAge(e){let t=await a.offlineStorage.getMetadata("".concat(e,"LastSync"));if(!t)return"Never synced";let s=Math.floor((Date.now()-t)/6e4),o=Math.floor(s/60),r=Math.floor(o/24);return r>0?"".concat(r," day").concat(r>1?"s":""," ago"):o>0?"".concat(o," hour").concat(o>1?"s":""," ago"):s>0?"".concat(s," minute").concat(s>1?"s":""," ago"):"Just now"}async hasOfflineData(){let e=await a.offlineStorage.getStorageInfo();return e.services>0||e.actions>0}}let c=new n},26957:(e,t,s)=>{s.d(t,{AM:()=>n,Dp:()=>u,F4:()=>C,FI:()=>R,H$:()=>o,J9:()=>g,Jo:()=>w,K9:()=>c,M6:()=>f,MO:()=>d,OT:()=>L,P4:()=>S,UR:()=>I,WD:()=>E,WH:()=>p,WU:()=>A,_i:()=>i,bW:()=>D,dG:()=>a,dm:()=>x,iJ:()=>Q,mh:()=>P,oo:()=>h,pZ:()=>v,u3:()=>l,x2:()=>b,xE:()=>r,xo:()=>q,yo:()=>y,zP:()=>m});let o="https://client-api.acuizen.com",a=o+"/login-configs",r=o+"/services",i=e=>o+"/files/"+e+"/presigned-url",n=o+"/users/me",c=o+"/dynamic-titles",l=o+"/users/get_users",u=o+"/files",d=o+"/observation-reports",f=o+"/my-observation-reports",h=o+"/dropdowns",g=o+"/get-blob",m=o+"/permit-reports",w=o+"/users",y=o+"/toolbox-talks",v=o+"/my-toolbox-talks",p=e=>o+"/my-assigned-actions/"+e,b=e=>o+"/inspection-checklist-submit/"+e,S=e=>o+"/observation-reports/"+e,D=e=>o+"/inspection-task-submit/"+e,q=e=>o+"/inspections/"+e,A=e=>o+"/permit-report-submit/"+e,E=e=>o+"/permit-reports-acknowledge/"+e,I=e=>o+"/permit-reports-update-status/"+e,R=e=>o+"/observation-action-submit/"+e,x=o+"/risk-assessments",Q=e=>o+"/risk-assessments/"+e,C=e=>o+"/ra-team-member-submit-signature/"+e,L=o+"/permit-reports",P=e=>o+"/permit-reports/"+e},38336:(e,t,s)=>{s.d(t,{A:()=>i});var o=s(96078),a=s(26957);let r=o.A.create({baseURL:a.H$,headers:{Accept:"application/json","Content-Type":"application/json"},mode:"no-cors"});r.interceptors.request.use(async e=>(e.metadata={requestTime:Date.now()},e),e=>Promise.reject(e)),r.interceptors.response.use(e=>{var t;return e.headers["x-request-time"]=null==(t=e.config.metadata)?void 0:t.requestTime,e},async e=>{let{offlineQueue:t}=await s.e(8836).then(s.bind(s,48836)),{offlineStorage:o}=await s.e(58).then(s.bind(s,60058));if(t.shouldQueue(e)){var a,r,i,n;let s=e.config;if(await t.addRequest(s.url,(null==(a=s.method)?void 0:a.toUpperCase())||"GET",s.data,s.headers),(null==(r=s.method)?void 0:r.toLowerCase())==="get")try{if(null==(i=s.url)?void 0:i.includes("/services")){let e=await o.getServices();if(e.length>0)return console.log("\uD83D\uDCF1 Serving services from offline cache"),{data:e,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:s}}if(null==(n=s.url)?void 0:n.includes("assigned-actions")){let e=new URLSearchParams(s.url.split("?")[1]).get("filter"),t="All";if(e)try{JSON.parse(decodeURIComponent(e))}catch(a){let e=s.url.split("/"),o=e.findIndex(e=>"assigned-actions"===e);-1!==o&&e[o+1]&&(t=e[o+1])}let a=await o.getActions(t);if(a.length>0)return console.log("\uD83D\uDCF1 Serving actions from offline cache"),{data:a,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:s}}}catch(e){console.error("❌ Error serving from cache:",e)}}return Promise.reject(e)});let i=r},48836:(e,t,s)=>{s.d(t,{offlineQueue:()=>i});var o=s(60058),a=s(38336);class r{async addRequest(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"GET",s=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0;try{await o.offlineStorage.addToQueue({url:e,method:t,data:s,headers:a,maxRetries:this.maxRetries}),this.notifyListeners(),console.log("\uD83D\uDCE4 Added ".concat(t," request to queue: ").concat(e))}catch(e){console.error("❌ Failed to add request to queue:",e)}}async processQueue(){if(!this.isProcessing&&navigator.onLine){this.isProcessing=!0,this.notifyListeners();try{let e=await o.offlineStorage.getQueueItems();for(let t of(console.log("\uD83D\uDD04 Processing ".concat(e.length," queued requests")),e))await this.processQueueItem(t);console.log("✅ Queue processing completed")}catch(e){console.error("❌ Error processing queue:",e)}finally{this.isProcessing=!1,this.notifyListeners()}}}async processQueueItem(e){try{console.log("\uD83D\uDD04 Processing queued request: ".concat(e.method," ").concat(e.url));let t={method:e.method,url:e.url,headers:e.headers||{}};e.data&&("POST"===e.method||"PUT"===e.method||"PATCH"===e.method)&&(t.data=e.data);let s=await a.A.request(t);if(s.status>=200&&s.status<300)await o.offlineStorage.removeFromQueue(e.id),console.log("✅ Successfully processed: ".concat(e.url));else throw Error("HTTP ".concat(s.status,": ").concat(s.statusText))}catch(t){console.error("❌ Failed to process queued request: ".concat(e.url),t),await this.handleFailedRequest(e)}}async handleFailedRequest(e){let t=e.retryCount+1;t>=e.maxRetries?(await o.offlineStorage.removeFromQueue(e.id),console.log("❌ Max retries reached for: ".concat(e.url,". Removing from queue.")),this.notifyFailedRequest(e)):(await o.offlineStorage.updateQueueItemRetryCount(e.id,t),console.log("\uD83D\uDD04 Retry ".concat(t,"/").concat(e.maxRetries," for: ").concat(e.url)),setTimeout(()=>{navigator.onLine&&this.processQueueItem(e)},this.retryDelay*Math.pow(2,t-1)))}notifyFailedRequest(e){console.warn("\uD83D\uDEA8 Failed to sync request after ".concat(e.maxRetries," attempts: ").concat(e.url)),"Notification"in window&&"granted"===Notification.permission&&new Notification("Sync Failed",{body:"Failed to sync data. Please try again later.",icon:"/assets/icons/Icon-192.png"})}async getStats(){try{let e=await o.offlineStorage.getQueueItems(),t=e.filter(e=>e.retryCount>=e.maxRetries).length;return{pending:e.length-t,failed:t,processing:this.isProcessing}}catch(e){return console.error("❌ Error getting queue stats:",e),{pending:0,failed:0,processing:!1}}}async clearQueue(){try{for(let e of(await o.offlineStorage.getQueueItems()))await o.offlineStorage.removeFromQueue(e.id);console.log("\uD83D\uDDD1️ Cleared offline queue"),this.notifyListeners()}catch(e){console.error("❌ Error clearing queue:",e)}}subscribe(e){return this.listeners.push(e),this.getStats().then(e),()=>{let t=this.listeners.indexOf(e);t>-1&&this.listeners.splice(t,1)}}async notifyListeners(){if(this.listeners.length>0){let e=await this.getStats();this.listeners.forEach(t=>t(e))}}async retryFailedRequests(){try{let e=(await o.offlineStorage.getQueueItems()).filter(e=>e.retryCount>=e.maxRetries);for(let t of e)await o.offlineStorage.updateQueueItemRetryCount(t.id,0);console.log("\uD83D\uDD04 Reset ".concat(e.length," failed requests for retry")),navigator.onLine&&this.processQueue()}catch(e){console.error("❌ Error retrying failed requests:",e)}}shouldQueue(e){var t,s,o,a;return!!(!navigator.onLine||"NETWORK_ERROR"===e.code||(null==(t=e.message)?void 0:t.includes("Network Error")))||(null==(s=e.response)?void 0:s.status)>=500||(null==(o=e.response)?void 0:o.status)>=400&&(null==(a=e.response)?void 0:a.status)<500&&408===e.response.status}constructor(){this.isProcessing=!1,this.maxRetries=3,this.retryDelay=1e3,this.listeners=[],window.addEventListener("online",()=>{console.log("\uD83C\uDF10 Back online! Processing queue..."),this.processQueue()}),window.addEventListener("offline",()=>{console.log("\uD83D\uDCF1 Gone offline. Requests will be queued.")}),navigator.onLine&&setTimeout(()=>this.processQueue(),1e3)}}let i=new r;"serviceWorker"in navigator&&navigator.serviceWorker.ready.then(e=>{"sync"in e&&e.sync.register("background-sync").catch(console.error)})},60058:(e,t,s)=>{s.d(t,{offlineStorage:()=>a});class o{async init(){return new Promise((e,t)=>{let s=indexedDB.open(this.dbName,this.version);s.onerror=()=>{console.error("❌ Failed to open IndexedDB:",s.error),t(s.error)},s.onsuccess=()=>{this.db=s.result,console.log("✅ IndexedDB initialized successfully"),e()},s.onupgradeneeded=e=>{let t=e.target.result;if(console.log("\uD83D\uDD04 Upgrading IndexedDB schema"),!t.objectStoreNames.contains(this.stores.services)){let e=t.createObjectStore(this.stores.services,{keyPath:"id"});e.createIndex("maskName","maskName",{unique:!1}),e.createIndex("lastUpdated","lastUpdated",{unique:!1})}if(!t.objectStoreNames.contains(this.stores.actions)){let e=t.createObjectStore(this.stores.actions,{keyPath:"id"});e.createIndex("application","application",{unique:!1}),e.createIndex("status","status",{unique:!1}),e.createIndex("dueDate","dueDate",{unique:!1}),e.createIndex("lastUpdated","lastUpdated",{unique:!1})}if(!t.objectStoreNames.contains(this.stores.queue)){let e=t.createObjectStore(this.stores.queue,{keyPath:"id"});e.createIndex("timestamp","timestamp",{unique:!1}),e.createIndex("retryCount","retryCount",{unique:!1})}t.objectStoreNames.contains(this.stores.metadata)||t.createObjectStore(this.stores.metadata,{keyPath:"key"})}})}async saveServices(e){if(!this.db)throw Error("Database not initialized");let t=this.db.transaction([this.stores.services],"readwrite").objectStore(this.stores.services),s=Date.now();for(let o of e)await t.put({...o,lastUpdated:s});await this.setMetadata("servicesLastSync",s),console.log("\uD83D\uDCBE Saved ".concat(e.length," services to offline storage"))}async getServices(){if(!this.db)throw Error("Database not initialized");return new Promise((e,t)=>{let s=this.db.transaction([this.stores.services],"readonly").objectStore(this.stores.services).getAll();s.onsuccess=()=>{console.log("\uD83D\uDCF1 Retrieved ".concat(s.result.length," services from offline storage")),e(s.result)},s.onerror=()=>t(s.error)})}async saveActions(e){if(!this.db)throw Error("Database not initialized");let t=this.db.transaction([this.stores.actions],"readwrite").objectStore(this.stores.actions),s=Date.now();for(let o of e)await t.put({...o,lastUpdated:s});await this.setMetadata("actionsLastSync",s),console.log("\uD83D\uDCBE Saved ".concat(e.length," actions to offline storage"))}async getActions(e){if(!this.db)throw Error("Database not initialized");return new Promise((t,s)=>{let o=this.db.transaction([this.stores.actions],"readonly").objectStore(this.stores.actions).getAll();o.onsuccess=()=>{let s=o.result;e&&"All"!==e&&(s=s.filter(t=>t.application===e)),console.log("\uD83D\uDCF1 Retrieved ".concat(s.length," actions from offline storage")),t(s)},o.onerror=()=>s(o.error)})}async addToQueue(e){if(!this.db)throw Error("Database not initialized");let t={...e,id:"queue_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),timestamp:Date.now(),retryCount:0},s=this.db.transaction([this.stores.queue],"readwrite").objectStore(this.stores.queue);await s.add(t),console.log("\uD83D\uDCE4 Added item to offline queue:",t.url)}async getQueueItems(){if(!this.db)throw Error("Database not initialized");return new Promise((e,t)=>{let s=this.db.transaction([this.stores.queue],"readonly").objectStore(this.stores.queue).getAll();s.onsuccess=()=>e(s.result),s.onerror=()=>t(s.error)})}async removeFromQueue(e){if(!this.db)throw Error("Database not initialized");let t=this.db.transaction([this.stores.queue],"readwrite").objectStore(this.stores.queue);await t.delete(e),console.log("✅ Removed item from offline queue:",e)}async updateQueueItemRetryCount(e,t){if(!this.db)throw Error("Database not initialized");let s=this.db.transaction([this.stores.queue],"readwrite").objectStore(this.stores.queue),o=s.get(e);o.onsuccess=()=>{let e=o.result;e&&(e.retryCount=t,s.put(e))}}async setMetadata(e,t){if(!this.db)throw Error("Database not initialized");let s=this.db.transaction([this.stores.metadata],"readwrite").objectStore(this.stores.metadata);await s.put({key:e,value:t,timestamp:Date.now()})}async getMetadata(e){if(!this.db)throw Error("Database not initialized");return new Promise((t,s)=>{let o=this.db.transaction([this.stores.metadata],"readonly").objectStore(this.stores.metadata).get(e);o.onsuccess=()=>{var e;t((null==(e=o.result)?void 0:e.value)||null)},o.onerror=()=>s(o.error)})}async clearAllData(){if(!this.db)throw Error("Database not initialized");let e=Object.values(this.stores),t=this.db.transaction(e,"readwrite");for(let s of e){let e=t.objectStore(s);await e.clear()}console.log("\uD83D\uDDD1️ Cleared all offline data")}async getStorageInfo(){if(!this.db)throw Error("Database not initialized");let e=await this.getServices(),t=await this.getActions(),s=await this.getQueueItems();return{services:e.length,actions:t.length,queue:s.length}}isOnline(){return navigator.onLine}constructor(){this.dbName="AcuiZenOfflineDB",this.version=1,this.db=null,this.stores={services:"services",actions:"actions",queue:"offlineQueue",metadata:"metadata"}}}let a=new o;a.init().catch(console.error)},81359:(e,t,s)=>{s.d(t,{A:()=>r,l:()=>a});let o=(0,s(51990).Z0)({name:"login",initialState:{isLogin:!1,user:null},reducers:{setLogin(e){e.isLogin=!0},setLogout(e){e.isLogin=!1,e.user=null},setUser(e,t){e.user=t.payload}}}),a=o.actions,r=o}}]);