import React, { useState, useEffect } from "react";
import API from "@/services/API";
import { GET_BLOB } from "@/constant";
import Image from "next/image";

interface MyLogoComponentProps {
  logo: string; // The presigned URL
}

const MyLogoComponent: React.FC<MyLogoComponentProps> = ({ logo }) => {
  const [logoDataURL, setLogoDataURL] = useState<string>("");

  useEffect(() => {
    const fetchLogoBlob = async () => {
      try {
        // Send POST request to get the blob
        const response = await API.post(GET_BLOB, { presignedUrl: logo }, {
          responseType: "blob", // Expecting binary data
        });

        // Convert Blob to Data URL (base64)
        const reader = new FileReader();
        reader.onloadend = () => {
          if (typeof reader.result === "string") {
            setLogoDataURL(reader.result);
          }
        };
        reader.readAsDataURL(response.data);
      } catch (error) {
        console.error("Error fetching logo blob:", error);
      }
    };

    if (logo) {
      fetchLogoBlob();
    }
  }, [logo]);

  return (
    <img
      src={logoDataURL || "/default-logo.png"} // Fallback in case of error
      alt="Logo"
      style={{ maxWidth: "125px", height: "auto" }}
    />
  );
};

export default MyLogoComponent;
