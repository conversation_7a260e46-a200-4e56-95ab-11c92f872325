"use client";

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'react-bootstrap';
import fcmService from '@/services/fcmService';

const FCMTester: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<string>('');
  const [error, setError] = useState<string>('');

  const testFCMToken = async () => {
    setIsLoading(true);
    setResult('');
    setError('');

    try {
      console.log('🧪 Starting FCM test...');

      // Clear any existing logs
      console.clear();

      const token = await fcmService.generateFCMToken();

      if (token) {
        setResult(`✅ FCM Token Generated Successfully!\n\nToken: ${token}\n\nLength: ${token.length} characters`);
      } else {
        setError('❌ Failed to generate FCM token. Check console for detailed logs.');
      }
    } catch (error: any) {
      setError(`❌ Error: ${error.message}`);
      console.error('Test error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setResult('');
    setError('');
  };

  return (
    <Card className="mb-3">
      <Card.Header>
        <h6 className="mb-0">🧪 FCM Token Tester</h6>
      </Card.Header>
      <Card.Body>
        <p className="text-muted mb-3">
          Click the button below to test FCM token generation. Check the browser console for detailed logs.
        </p>

        <div className="d-flex gap-2 mb-3">
          <Button
            variant="primary"
            onClick={testFCMToken}
            disabled={isLoading}
          >
            {isLoading ? 'Testing...' : '🧪 Test FCM Token Generation'}
          </Button>

          <Button
            variant="outline-secondary"
            onClick={clearResults}
            disabled={isLoading}
          >
            Clear Results
          </Button>
        </div>

        {result && (
          <Alert variant="success">
            <pre style={{ whiteSpace: 'pre-wrap', fontSize: '0.9em' }}>{result}</pre>
          </Alert>
        )}

        {error && (
          <Alert variant="danger">
            <strong>Error:</strong> {error}
            <hr />
            <small>
              <strong>Common issues:</strong>
              <ul className="mb-0 mt-2">
                <li>Notifications blocked in browser settings</li>
                <li>Not running on HTTPS or localhost</li>
                <li>Service worker not registered</li>
                <li>Invalid Firebase configuration</li>
                <li>Browser doesn&apos;t support FCM</li>
              </ul>
            </small>
          </Alert>
        )}

        <div className="mt-3">
          <small className="text-muted">
            <strong>Instructions:</strong>
            <ol className="mt-1">
              <li>Open browser DevTools (F12)</li>
              <li>Go to Console tab</li>
              <li>Click &quot;Test FCM Token Generation&quot;</li>
              <li>Allow notifications when prompted</li>
              <li>Check console for step-by-step logs</li>
            </ol>
          </small>
        </div>
      </Card.Body>
    </Card>
  );
};

export default FCMTester;
