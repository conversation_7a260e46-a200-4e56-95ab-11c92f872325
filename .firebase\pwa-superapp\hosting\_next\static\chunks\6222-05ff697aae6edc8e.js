"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6222],{15123:(e,o,i)=>{i.d(o,{A:()=>d});var t=i(23915),n=i(20996);let s={apiKey:"AIzaSyCvHAIHZPnzl9xMJu9NpPQ8J-iL8zKDh9k",authDomain:"pwa-superapp.firebaseapp.com",projectId:"pwa-superapp",storageBucket:"pwa-superapp.firebasestorage.app",messagingSenderId:"596753047002",appId:"1:596753047002:web:e809373bd1190e6a8bf4e3"},r=(0,t.Wp)(s),a=null,c=async()=>{try{console.log("\uD83D\uDD04 Checking Firebase messaging support..."),console.log("\uD83D\uDD0D Firebase config check:"),console.log("  - API Key:",s.apiKey?"Set":"Missing"),console.log("  - Project ID:",s.projectId),console.log("  - Sender ID:",s.messagingSenderId),console.log("  - App ID:",s.appId?"Set":"Missing");let e=await (0,n.TT)();if(console.log("\uD83D\uDD0D Firebase messaging supported:",e),!e)return console.log("❌ Firebase messaging is not supported in this browser"),console.log("This could be due to:"),console.log("  - Browser doesn't support service workers"),console.log("  - Browser doesn't support push notifications"),console.log("  - Running in incognito/private mode"),null;if(console.log("\uD83D\uDD04 Initializing Firebase messaging..."),"serviceWorker"in navigator)try{await navigator.serviceWorker.ready,console.log("✅ Service worker is ready for Firebase messaging")}catch(e){console.warn("⚠️ Service worker not ready:",e)}return a=(0,n.dG)(r),console.log("✅ Firebase messaging initialized successfully"),console.log("  - Messaging instance:",!!a),a}catch(e){if(console.error("❌ Error initializing Firebase messaging:",e),console.error("Error details:",{name:e.name,message:e.message,code:e.code}),e.code)switch(e.code){case"app/invalid-api-key":console.error("\uD83D\uDD0D Invalid Firebase API key");break;case"app/invalid-app-id":console.error("\uD83D\uDD0D Invalid Firebase App ID");break;case"messaging/unsupported-browser":console.error("\uD83D\uDD0D Browser not supported for messaging");break;default:console.error("\uD83D\uDD0D Unknown Firebase error:",e.code)}return null}},l="BDukpatijXC4YB_g4W7FvW-UjBfYtMAM7epw7MJFkcb4ICU8udWq7UYAQlk-hFJ9WmZ-EutlgybC5-HIAkaMSnU";class g{async initialize(){if(this.isInitialized)return console.log("✅ FCM Service already initialized"),this.messaging;try{return console.log("\uD83D\uDD04 Initializing FCM Service..."),console.log("Browser environment check:",!0),console.log("Notification support:","Notification"in window),console.log("Service Worker support:","serviceWorker"in navigator),this.messaging=await c(),this.isInitialized=!0,this.messaging?(this.setupForegroundMessageListener(),console.log("✅ FCM Service initialized successfully"),console.log("Messaging instance:",!!this.messaging)):console.log("⚠️ FCM messaging not supported or failed to initialize"),this.messaging}catch(e){return console.error("❌ Error initializing FCM service:",e),console.error("Error details:",{name:e.name,message:e.message,code:e.code}),null}}async requestNotificationPermission(){try{let e=await Notification.requestPermission();if("granted"===e)return console.log("✅ Notification permission granted"),!0;return console.log("❌ Notification permission denied"),!1}catch(e){return console.error("Error requesting notification permission:",e),!1}}async generateFCMToken(){console.log("\uD83D\uDD04 Starting FCM token generation..."),console.log("\uD83D\uDD0D Environment check:"),console.log("  - Window available:",!0),console.log("  - HTTPS/Localhost:","https:"===window.location.protocol||"localhost"===window.location.hostname),console.log("  - Current URL:",window.location.href),console.log("\uD83D\uDD0D Environment variables:"),console.log("  - API Key:","Set"),console.log("  - Project ID:","pwa-superapp"),console.log("  - Sender ID:","596753047002"),console.log("  - VAPID Key:","Set");try{if(this.messaging||(console.log("\uD83D\uDD04 Step 1: Initializing FCM messaging..."),await this.initialize()),!this.messaging)return console.error("❌ Step 1 FAILED: FCM messaging not available"),console.error("This usually means:"),console.error("  - Firebase configuration is incorrect"),console.error("  - Browser doesn't support FCM"),console.error("  - Network connectivity issues"),null;if(console.log("✅ Step 1: FCM messaging initialized"),console.log("\uD83D\uDD04 Step 2: Checking service worker..."),"serviceWorker"in navigator)try{let e=await navigator.serviceWorker.ready;console.log("✅ Step 2: Service Worker is ready"),console.log("  - Scope:",e.scope),console.log("  - Active:",!!e.active)}catch(e){console.warn("⚠️ Step 2: Service Worker not ready:",e)}else console.warn("⚠️ Step 2: Service Worker not supported");if(console.log("\uD83D\uDD04 Step 3: Checking notification permission..."),console.log("  - Current permission:",Notification.permission),!await this.requestNotificationPermission())return console.log("❌ Step 3 FAILED: Cannot generate token without notification permission"),null;console.log("✅ Step 3: Notification permission granted"),console.log("\uD83D\uDD04 Step 4: Generating FCM token..."),console.log("  - Using VAPID key:",l.substring(0,20)+"..."),console.log("  - Messaging instance type:",typeof this.messaging);let e=await (0,n.gf)(this.messaging,{vapidKey:l});if(e)return console.log("✅ Step 4: FCM Token generated successfully!"),console.log("  - Token length:",e.length),console.log("  - Token preview:",e.substring(0,50)+"..."),console.log("  - Full token:",e),localStorage.setItem("fcm_token",e),console.log("✅ Token stored in localStorage"),console.log("\uD83D\uDD04 Step 5: Sending token to server..."),await this.sendTokenToServer(e),e;return console.log("❌ Step 4 FAILED: No registration token available"),console.log("This could be due to:"),console.log("  - Browser blocking notifications"),console.log("  - Invalid VAPID key"),console.log("  - Firebase configuration issues"),console.log("  - Network connectivity issues"),console.log("  - Service worker not properly configured"),null}catch(e){if(console.error("❌ CRITICAL ERROR in FCM token generation:",e),console.error("Error details:",{name:e.name,message:e.message,code:e.code,stack:e.stack}),e.code)switch(e.code){case"messaging/unsupported-browser":console.error("\uD83D\uDD0D Browser not supported for FCM");break;case"messaging/permission-blocked":console.error("\uD83D\uDD0D Notification permission blocked");break;case"messaging/vapid-key-required":console.error("\uD83D\uDD0D VAPID key is required but missing");break;case"messaging/invalid-vapid-key":console.error("\uD83D\uDD0D VAPID key is invalid");break;default:console.error("\uD83D\uDD0D Unknown Firebase error code:",e.code)}return null}}async sendTokenToServer(e){try{let o=localStorage.getItem("access_token")||localStorage.getItem("token")||localStorage.getItem("authToken");if(!o)return void console.log("⚠️ No authorization token found, skipping token registration");let i=await fetch("".concat("https://client-api.acuizen.com","/users/me"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(o)},body:JSON.stringify({deviceToken:e})});if(i.ok){console.log("✅ FCM token sent to server successfully");let e=await i.json();console.log("Server response:",e)}else{let e=await i.text();console.log("⚠️ Failed to send FCM token to server:",i.status,e)}}catch(e){console.error("❌ Error sending token to server:",e)}}setupForegroundMessageListener(){this.messaging&&(0,n.xD)(this.messaging,e=>{console.log("\uD83D\uDCF1 Foreground message received:",e),this.showNotification(e)})}showNotification(e){let{notification:o,data:i}=e;if(o){let e=o.title||"New Notification",t={body:o.body||"",icon:o.icon||"/assets/icons/Icon-192.png",badge:"/assets/icons/Icon-72.png",tag:(null==i?void 0:i.tag)||"default",data:i||{},requireInteraction:!0,actions:[{action:"view",title:"View",icon:"/assets/icons/Icon-72.png"},{action:"dismiss",title:"Dismiss"}]};"serviceWorker"in navigator&&"showNotification"in ServiceWorkerRegistration.prototype?navigator.serviceWorker.ready.then(o=>{o.showNotification(e,t)}):new Notification(e,t)}}async getStoredToken(){return localStorage.getItem("fcm_token")}async refreshToken(){return localStorage.removeItem("fcm_token"),await this.generateFCMToken()}async deleteToken(){try{this.messaging&&(localStorage.removeItem("fcm_token"),console.log("✅ FCM token cleared from local storage"))}catch(e){console.error("Error deleting FCM token:",e)}}constructor(){this.messaging=null,this.isInitialized=!1}}let d=new g},42851:(e,o,i)=>{i.d(o,{A:()=>n});class t{isIOS(){return/iPad|iPhone|iPod/.test(navigator.userAgent)}isIOSPWA(){return this.isIOS()&&!0===window.navigator.standalone}isIOSSafari(){return this.isIOS()&&!this.isIOSPWA()}getIOSVersion(){let e=navigator.userAgent.match(/OS (\d+)_(\d+)_?(\d+)?/);return e?parseInt(e[1],10):null}isSupported(){if(!this.isIOS())return!1;let e=this.getIOSVersion();return e&&e<16?(console.log("⚠️ iOS version too old for web push notifications (requires 16.4+)"),!1):"Notification"in window?"serviceWorker"in navigator||(console.log("⚠️ Service Worker not supported"),!1):(console.log("⚠️ Notification API not supported"),!1)}getPermissionStatus(){return"Notification"in window?Notification.permission:"denied"}async requestPermission(){if(!this.isSupported())return console.log("❌ iOS notifications not supported on this device/version"),!1;try{console.log("\uD83C\uDF4E Requesting iOS notification permission..."),this.isIOSPWA()&&console.log("\uD83D\uDCF1 Requesting permission in iOS PWA context");let e=await Notification.requestPermission();if("granted"===e)return console.log("✅ iOS notification permission granted"),!0;return console.log("❌ iOS notification permission denied"),this.showPermissionGuidance(),!1}catch(e){return console.error("❌ Error requesting iOS notification permission:",e),!1}}showPermissionGuidance(){console.log("\uD83D\uDCF1 iOS Notification Setup Guide:"),console.log("1. Open Safari Settings"),console.log('2. Scroll down to "Websites"'),console.log('3. Tap "Notifications"'),console.log('4. Find your website and set to "Allow"'),this.isIOSPWA()&&(console.log("\uD83D\uDCF1 PWA Additional Steps:"),console.log("5. You may need to re-add the app to home screen"),console.log("6. Ensure the app is opened from home screen icon"))}async showLocalNotification(e){if("granted"!==this.getPermissionStatus())return console.log("❌ Cannot show notification: permission not granted"),!1;try{let o=new Notification(e.title,{body:e.body,icon:e.icon||"/assets/icons/Icon-192.png",badge:e.badge||"/assets/icons/Icon-72.png",tag:e.tag||"ios-notification",data:e.data||{},requireInteraction:!0});return o.onclick=i=>{var t;i.preventDefault(),console.log("\uD83D\uDCF1 iOS notification clicked"),window.focus&&window.focus(),(null==(t=e.data)?void 0:t.url)&&(window.location.href=e.data.url),o.close()},setTimeout(()=>{o.close()},1e4),!0}catch(e){return console.error("❌ Error showing iOS notification:",e),!1}}async registerForPushNotifications(){if(!this.isSupported())return null;try{let e=await navigator.serviceWorker.register("/sw.js");console.log("✅ Service worker registered for iOS push");let o=await e.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:this.urlBase64ToUint8Array("BDukpatijXC4YB_g4W7FvW-UjBfYtMAM7epw7MJFkcb4ICU8udWq7UYAQlk-hFJ9WmZ-EutlgybC5-HIAkaMSnU")});console.log("✅ iOS push subscription created");let i={endpoint:o.endpoint,keys:{p256dh:this.arrayBufferToBase64(o.getKey("p256dh")),auth:this.arrayBufferToBase64(o.getKey("auth"))}};return await this.sendSubscriptionToServer(i),JSON.stringify(i)}catch(e){return console.error("❌ Error registering for iOS push notifications:",e),null}}async sendSubscriptionToServer(e){try{let o=localStorage.getItem("access_token")||localStorage.getItem("token")||localStorage.getItem("authToken");if(!o)return void console.log("⚠️ No auth token found for iOS push subscription");let i=await fetch("".concat("https://client-api.acuizen.com","/users/me/ios-push-subscription"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(o)},body:JSON.stringify({subscription:e,deviceType:"ios-web",userAgent:navigator.userAgent})});i.ok?console.log("✅ iOS push subscription sent to server"):console.log("⚠️ Failed to send iOS push subscription to server:",i.status)}catch(e){console.error("❌ Error sending iOS push subscription to server:",e)}}urlBase64ToUint8Array(e){let o="=".repeat((4-e.length%4)%4),i=(e+o).replace(/-/g,"+").replace(/_/g,"/"),t=window.atob(i),n=new Uint8Array(t.length);for(let e=0;e<t.length;++e)n[e]=t.charCodeAt(e);return n}arrayBufferToBase64(e){let o=new Uint8Array(e),i="";for(let e=0;e<o.byteLength;e++)i+=String.fromCharCode(o[e]);return window.btoa(i)}async testNotification(){console.log("\uD83E\uDDEA Testing iOS notification..."),await this.showLocalNotification({title:"Test Notification",body:"iOS notifications are working!",tag:"test",data:{test:!0}})?console.log("✅ iOS test notification sent successfully"):console.log("❌ iOS test notification failed")}}let n=new t},46222:(e,o,i)=>{i.d(o,{A:()=>r});var t=i(15123),n=i(42851);class s{async initialize(){console.log("\uD83D\uDD04 Initializing Unified Notification Service..."),this.deviceInfo=this.detectDevice(),console.log("\uD83D\uDCF1 Device Info:",this.deviceInfo);try{switch(this.deviceInfo.recommendedMethod){case"fcm":return console.log("\uD83D\uDE80 Using FCM for notifications"),await t.A.initialize(),!0;case"ios-web-push":return console.log("\uD83C\uDF4E Using iOS Web Push for notifications"),n.A.isSupported();case"local-only":return console.log("\uD83D\uDCF1 Using local notifications only"),!0;case"unsupported":return console.log("❌ Push notifications not supported on this device"),!1;default:return console.log("⚠️ Unknown notification method, falling back to FCM"),await t.A.initialize(),!0}}catch(e){return console.error("❌ Error initializing unified notification service:",e),!1}}detectDevice(){let e=navigator.userAgent.toLowerCase(),o=/ipad|iphone|ipod/.test(e),i=o&&!0===window.navigator.standalone,t=/android/.test(e),n="unknown";e.includes("chrome")?n="chrome":e.includes("firefox")?n="firefox":e.includes("safari")?n="safari":e.includes("edge")&&(n="edge");let s="serviceWorker"in navigator&&"PushManager"in window&&"Notification"in window,r="unsupported";if(o){let o=e.match(/os (\d+)_(\d+)_?(\d+)?/);r=(o?parseInt(o[1],10):0)>=16&&s?"ios-web-push":s?"local-only":"unsupported"}else t||"chrome"===n||"firefox"===n||"edge"===n?r="fcm":s&&(r="fcm");return{isIOS:o,isIOSPWA:i,isAndroid:t,browser:n,supportsWebPush:s,recommendedMethod:r}}async requestPermission(){var e,o;this.deviceInfo||await this.initialize(),console.log("\uD83D\uDD14 Requesting permission using: ".concat(null==(e=this.deviceInfo)?void 0:e.recommendedMethod));try{switch(null==(o=this.deviceInfo)?void 0:o.recommendedMethod){case"fcm":return await t.A.requestNotificationPermission();case"ios-web-push":return await n.A.requestPermission();case"local-only":if("Notification"in window){let e=await Notification.requestPermission();return"granted"===e}return!1;case"unsupported":return console.log("❌ Notifications not supported on this device"),this.showUnsupportedMessage(),!1;default:return!1}}catch(e){return console.error("❌ Error requesting notification permission:",e),!1}}async registerForNotifications(){this.deviceInfo||await this.initialize();try{var e;switch(null==(e=this.deviceInfo)?void 0:e.recommendedMethod){case"fcm":return await t.A.generateFCMToken();case"ios-web-push":return await n.A.registerForPushNotifications();case"local-only":if("granted"===Notification.permission)return"local-notifications-enabled";return null;default:return null}}catch(e){return console.error("❌ Error registering for notifications:",e),null}}async showTestNotification(){return await this.showLocalNotification({title:"Test Notification",body:"Your notifications are working correctly!",tag:"test-notification",data:{test:!0}})}async showLocalNotification(e){if(this.deviceInfo||await this.initialize(),"granted"!==Notification.permission)return console.log("❌ Cannot show notification: permission not granted"),!1;try{var o;switch(null==(o=this.deviceInfo)?void 0:o.recommendedMethod){case"ios-web-push":case"local-only":return await n.A.showLocalNotification(e);case"fcm":return t.A.showNotification({notification:{title:e.title,body:e.body,icon:e.icon},data:e.data}),!0;default:let i=new Notification(e.title,{body:e.body,icon:e.icon||"/assets/icons/Icon-192.png",tag:e.tag});return i.onclick=()=>{e.url&&window.open(e.url,"_blank"),i.close()},!0}}catch(e){return console.error("❌ Error showing local notification:",e),!1}}getPermissionStatus(){return"Notification"in window?Notification.permission:"denied"}isSupported(){var e;return(null==(e=this.deviceInfo)?void 0:e.recommendedMethod)!=="unsupported"}getDeviceInfo(){return this.deviceInfo}showUnsupportedMessage(){console.log("\uD83D\uDCF1 Notification Support Information:"),console.log("Your device/browser combination does not support push notifications."),console.log("Supported combinations:"),console.log("• Android: Chrome, Firefox, Edge"),console.log("• iOS: Safari 16.4+ (limited support)"),console.log("• Desktop: Chrome, Firefox, Edge, Safari")}getStatusMessage(){if(!this.deviceInfo)return"Checking notification support...";let e=this.getPermissionStatus();return"granted"===e?"Notifications enabled (".concat(this.deviceInfo.recommendedMethod,")"):"denied"===e?"Notifications blocked - please enable in browser settings":"unsupported"===this.deviceInfo.recommendedMethod?"Notifications not supported on this device":"Click to enable notifications"}getSetupInstructions(){if(!this.deviceInfo)return["Checking device compatibility..."];let e=[];return this.deviceInfo.isIOS?(e.push("iOS Setup:"),e.push("1. Ensure you have iOS 16.4 or later"),e.push("2. Open this site in Safari (not Chrome or other browsers)"),e.push('3. Tap the share button and "Add to Home Screen"'),e.push("4. Open the app from your home screen"),e.push("5. Allow notifications when prompted")):this.deviceInfo.isAndroid?(e.push("Android Setup:"),e.push("1. Allow notifications when prompted"),e.push("2. If blocked, go to browser settings > Site settings > Notifications"),e.push('3. Find this site and set to "Allow"')):(e.push("Desktop Setup:"),e.push("1. Allow notifications when prompted"),e.push("2. If blocked, click the lock icon in address bar"),e.push('3. Set notifications to "Allow"')),e}constructor(){this.deviceInfo=null}}let r=new s}}]);