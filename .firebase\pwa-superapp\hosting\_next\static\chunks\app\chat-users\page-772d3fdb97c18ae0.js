(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3376],{38983:(e,s,i)=>{"use strict";i.d(s,{default:()=>m});var a=i(95155),n=i(6874),r=i.n(n);i(12115);let t=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],m=()=>(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,a.jsx)("div",{className:"container px-0",children:(0,a.jsx)("div",{className:"footer-nav position-relative",children:(0,a.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:t.map((e,s)=>(0,a.jsx)("li",{children:(0,a.jsxs)(r(),{href:"/".concat(e.link),children:[(0,a.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,a.jsx)("span",{children:e.title})]})},s))})})})})})},64267:(e,s,i)=>{"use strict";i.d(s,{default:()=>n});var a=i(95155);i(12115);let n=()=>(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"card mb-2",children:(0,a.jsx)("div",{className:"card-body p-2",children:(0,a.jsx)("div",{className:"chat-search-box",children:(0,a.jsx)("form",{onSubmit:e=>e.preventDefault(),children:(0,a.jsxs)("div",{className:"input-group",children:[(0,a.jsx)("span",{className:"input-group-text",id:"searchbox",children:(0,a.jsx)("i",{className:"bi bi-search"})}),(0,a.jsx)("input",{className:"form-control",type:"search",placeholder:"Search users or messages","aria-describedby":"searchbox"})]})})})})})})},86638:(e,s,i)=>{"use strict";i.d(s,{default:()=>t});var a=i(95155);i(12115);var n=i(27677);let r=[{id:1,img:"/assets/img/bg-img/user1.png",name:"Aynal"},{id:2,img:"/assets/img/bg-img/user2.png",name:"Afrin"},{id:3,img:"/assets/img/bg-img/user3.png",name:"Fairoze"},{id:4,img:"/assets/img/bg-img/user4.png",name:"ReFresh"},{id:5,img:"/assets/img/bg-img/user1.png",name:"Robin"},{id:6,img:"/assets/img/bg-img/user2.png",name:"Hasnain"},{id:7,img:"/assets/img/bg-img/user1.png",name:"Aynal"},{id:8,img:"/assets/img/bg-img/user2.png",name:"Afrin"},{id:9,img:"/assets/img/bg-img/user3.png",name:"Fairoze"},{id:10,img:"/assets/img/bg-img/user4.png",name:"ReFresh"},{id:11,img:"/assets/img/bg-img/user1.png",name:"রবিন"},{id:12,img:"/assets/img/bg-img/user2.png",name:"Hasnain"}],t=()=>(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"chat-user-status-slides-wrapper",children:(0,a.jsx)(n.RC,{spaceBetween:10,slidesPerView:10,breakpoints:{0:{slidesPerView:5},480:{slidesPerView:5},768:{slidesPerView:7},1200:{slidesPerView:10}},className:"chat-user-status-slides mb-2",children:r.map((e,s)=>(0,a.jsx)(n.qr,{children:(0,a.jsx)("div",{className:"user-status-slide",children:(0,a.jsxs)("a",{href:"#",children:[(0,a.jsx)("img",{src:e.img,alt:""}),(0,a.jsx)("div",{className:"active-status"}),(0,a.jsx)("p",{className:"mb-0 mt-1 text-truncate",children:e.name})]})})},s))})})})},88207:(e,s,i)=>{Promise.resolve().then(i.t.bind(i,6874,23)),Promise.resolve().then(i.bind(i,64267)),Promise.resolve().then(i.bind(i,86638)),Promise.resolve().then(i.bind(i,38983)),Promise.resolve().then(i.bind(i,91727))}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,1955,1531,7677,1727,8441,1684,7358],()=>s(88207)),_N_E=e.O()}]);