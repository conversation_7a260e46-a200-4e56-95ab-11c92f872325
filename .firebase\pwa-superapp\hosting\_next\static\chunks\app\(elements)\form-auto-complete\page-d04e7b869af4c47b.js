(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4302],{21217:(e,a,i)=>{"use strict";i.d(a,{default:()=>d});var t=i(95155),n=i(9e4),s=i(38808),l=i(12115);let r=e=>{let{handleShowSetting:a,showSetting:i}=e,{theme:l,handleDarkModeToggle:r}=(0,n.D)(),{viewMode:o,handleRTLToggling:c}=(0,s.L)();return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{id:"setting-popup-overlay",className:i?"active":"",onClick:a}),(0,t.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(i?"active":""),id:"settingCard",children:(0,t.jsx)("div",{className:"card-body",children:(0,t.jsxs)("div",{className:"container",children:[(0,t.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,t.jsx)("p",{className:"mb-0",children:"Settings"}),(0,t.jsx)("div",{onClick:a,className:"btn-close",id:"settingCardClose"})]}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,t.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,t.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===l,onChange:r}),(0,t.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===l?"Light":"Dark"," mode"]})]})}),(0,t.jsx)("div",{className:"single-setting-panel",children:(0,t.jsxs)("div",{className:"form-check form-switch",children:[(0,t.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===o,onChange:c}),(0,t.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===o?"LTR":"RTL"," mode"]})]})})]})})})]})};var o=i(6874),c=i.n(o);let d=e=>{let{links:a,title:i}=e,[n,s]=(0,l.useState)(!1),o=()=>s(!n);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"header-area",id:"headerArea",children:(0,t.jsx)("div",{className:"container",children:(0,t.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,t.jsx)("div",{className:"back-button",children:(0,t.jsx)(c(),{href:"/".concat(a),children:(0,t.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,t.jsx)("div",{className:"page-heading",children:(0,t.jsx)("h6",{className:"mb-0",children:i})}),(0,t.jsx)("div",{className:"setting-wrapper",onClick:o,children:(0,t.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,t.jsx)("i",{className:"bi bi-gear"}),(0,t.jsx)("span",{})]})})]})})}),(0,t.jsx)(r,{showSetting:n,handleShowSetting:o})]})}},38808:(e,a,i)=>{"use strict";i.d(a,{L:()=>n});var t=i(12115);let n=()=>{let[e,a]=(0,t.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,t.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let i=()=>{a(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:i,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let a=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(a),e.style.display="none")},20)},1e3))}i()}}}},38983:(e,a,i)=>{"use strict";i.d(a,{default:()=>r});var t=i(95155),n=i(6874),s=i.n(n);i(12115);let l=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],r=()=>(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,t.jsx)("div",{className:"container px-0",children:(0,t.jsx)("div",{className:"footer-nav position-relative",children:(0,t.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:l.map((e,a)=>(0,t.jsx)("li",{children:(0,t.jsxs)(s(),{href:"/".concat(e.link),children:[(0,t.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,t.jsx)("span",{children:e.title})]})},a))})})})})})},54755:(e,a,i)=>{"use strict";i.d(a,{default:()=>r});var t=i(95155),n=i(38983),s=i(21217),l=i(12115);let r=()=>{let e=["Afghanistan","Albania","Algeria","Andorra","Angola","Anguilla","Antigua & Barbuda","Argentina","Armenia","Aruba","Australia","Austria","Azerbaijan","Bahamas","Bahrain","Bangladesh","Barbados","Belarus","Belgium","Belize","Benin","Bermuda","Bhutan","Bolivia","Bosnia & Herzegovina","Botswana","Brazil","British Virgin Islands","Brunei","Bulgaria","Burkina Faso","Burundi","Cambodia","Cameroon","Canada","Cape Verde","Cayman Islands","Central Arfrican Republic","Chad","Chile","China","Colombia","Congo","Cook Islands","Costa Rica","Cote D Ivoire","Croatia","Cuba","Curacao","Cyprus","Czech Republic","Denmark","Djibouti","Dominica","Dominican Republic","Ecuador","Egypt","El Salvador","Equatorial Guinea","Eritrea","Estonia","Ethiopia","Falkland Islands","Faroe Islands","Fiji","Finland","France","French Polynesia","French West Indies","Gabon","Gambia","Georgia","Germany","Ghana","Gibraltar","Greece","Greenland","Grenada","Guam","Guatemala","Guernsey","Guinea","Guinea Bissau","Guyana","Haiti","Honduras","Hong Kong","Hungary","Iceland","India","Indonesia","Iran","Iraq","Ireland","Isle of Man","Israel","Italy","Jamaica","Japan","Jersey","Jordan","Kazakhstan","Kenya","Kiribati","Kosovo","Kuwait","Kyrgyzstan","Laos","Latvia","Lebanon","Lesotho","Liberia","Libya","Liechtenstein","Lithuania","Luxembourg","Macau","Macedonia","Madagascar","Malawi","Malaysia","Maldives","Mali","Malta","Marshall Islands","Mauritania","Mauritius","Mexico","Micronesia","Moldova","Monaco","Mongolia","Montenegro","Montserrat","Morocco","Mozambique","Myanmar","Namibia","Nauro","Nepal","Netherlands","Netherlands Antilles","New Caledonia","New Zealand","Nicaragua","Niger","Nigeria","North Korea","Norway","Oman","Pakistan","Palau","Palestine","Panama","Papua New Guinea","Paraguay","Peru","Philippines","Poland","Portugal","Puerto Rico","Qatar","Reunion","Romania","Russia","Rwanda","Saint Pierre & Miquelon","Samoa","San Marino","Sao Tome and Principe","Saudi Arabia","Senegal","Serbia","Seychelles","Sierra Leone","Singapore","Slovakia","Slovenia","Solomon Islands","Somalia","South Africa","South Korea","South Sudan","Spain","Sri Lanka","St Kitts & Nevis","St Lucia","St Vincent","Sudan","Suriname","Swaziland","Sweden","Switzerland","Syria","Taiwan","Tajikistan","Tanzania","Thailand","Timor LEste","Togo","Tonga","Trinidad & Tobago","Tunisia","Turkey","Turkmenistan","Turks & Caicos","Tuvalu","Uganda","Ukraine","United Arab Emirates","United Kingdom","United States of America","Uruguay","Uzbekistan","Vanuatu","Vatican City","Venezuela","Vietnam","Virgin Islands (US)","Yemen","Zambia","Zimbabwe"],a=["Kabul","Tirana","Algiers","Luanda","Saint","Johns","Buenos","Aires","Yerevan","Canberra","Vienna","Baku","Nassau","Manama","Dhaka","Bridgetown","Minsk","Brussels","Belmopan","Porto","Novo","Thimphu","Sarajevo","Gaborone","Brasilia","Sofia","Ouagadougou","Gitega","Praia","Phnom","Penh","Yaounde","Ottawa","Bangui","Djamena","Santiago","Beijing","Bogot\xe1","Moroni","Kinshasa","Brazzaville","San","Jose","Yamoussoukro","Zagreb","Havana","Nicosia","Prague","Suva","Helsinki","Paris","Libreville","Banjul","Tbilisi","Berlin","Accra","Athens","Saint","Georges","Guatemala","City","Conakry","Bissau","Georgetown","Reykjavik","New Delhi","Jakarta","Tehran","Baghdad","Dublin","Jerusalem","Rome","Kingston","Tokyo","Amman","NurSultan","Nairobi","Tarawa","Pristina","Kuwait City","Bishkek","Vientiane","Riga","Beirut","Maseru","Monrovia","Tripoli","Vaduz","Vilnius","Luxembourg"],[i,r]=(0,l.useState)(""),[o,c]=(0,l.useState)([]),d=a=>{if(""===a.trim())return void c([]);c(e.filter(e=>e.toLowerCase().includes(a.toLowerCase())))},u=e=>{r(e),c([]);let a=document.getElementById("autoCompleteCountries");a&&(a.value=e,a.focus())},[m,h]=(0,l.useState)(""),[g,p]=(0,l.useState)([]),v=e=>{if(""===e.trim())return void p([]);p(a.filter(a=>a.toLowerCase().includes(e.toLowerCase())))},b=e=>{h(e),p([]);let a=document.getElementById("autoCompletePlace");a&&(a.value=e,a.focus())};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(s.default,{links:"elements",title:"Auto Complete"}),(0,t.jsxs)("div",{className:"page-content-wrapper py-3",children:[(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("div",{className:"element-heading",children:(0,t.jsx)("h6",{children:"Auto complete countries"})})}),(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("form",{action:"#",autoComplete:"off",children:(0,t.jsx)("div",{className:"form-group mb-0",children:(0,t.jsx)("input",{className:"form-control",id:"autoCompleteCountries",type:"text",name:"myCountry",placeholder:"Type a country name",onChange:e=>{let a=e.target.value;r(a),d(a)},value:i})})}),o.length>0&&(0,t.jsx)("div",{className:"suggestions-container",children:o.map((e,a)=>(0,t.jsx)("div",{className:"suggestion-item",onMouseDown:()=>u(e),style:{cursor:"pointer",padding:"8px",borderBottom:"1px solid #ddd"},children:e},a))})]})})}),(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("div",{className:"element-heading mt-3",children:(0,t.jsx)("h6",{children:"Auto complete place"})})}),(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("form",{action:"#",autoComplete:"off",children:(0,t.jsx)("div",{className:"form-group mb-0",children:(0,t.jsx)("input",{className:"form-control",id:"autoCompletePlace",type:"text",name:"place",placeholder:"Type a place name",value:m,onChange:e=>{let a=e.target.value;h(a),v(a)}})})}),g.length>0&&(0,t.jsx)("div",{children:g.map((e,a)=>(0,t.jsx)("div",{onMouseDown:()=>b(e),children:e},a))})]})})})]}),(0,t.jsx)(n.default,{})]})}},89489:(e,a,i)=>{Promise.resolve().then(i.bind(i,54755))},9e4:(e,a,i)=>{"use strict";i.d(a,{D:()=>n});var t=i(12115);let n=()=>{let[e,a]=(0,t.useState)("light"),[i,n]=(0,t.useState)(!1);(0,t.useEffect)(()=>{a(localStorage.getItem("theme")||"light"),n(!0)},[]),(0,t.useEffect)(()=>{i&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,i]);let s=(0,t.useCallback)(()=>{a(e=>"dark"===e?"light":"dark")},[]),l=(0,t.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let a=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(a),e.style.display="none")},20)},1e3))}s()},[s]);return{theme:e,toggleTheme:s,handleDarkModeToggle:l}}}},e=>{var a=a=>e(e.s=a);e.O(0,[6874,8441,1684,7358],()=>a(89489)),_N_E=e.O()}]);