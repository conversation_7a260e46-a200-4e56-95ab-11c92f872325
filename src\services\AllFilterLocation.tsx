import React, { useState, useEffect, useRef } from "react";
import API from "../services/API";
import { DYNAMIC_TITLES_URL, API_URL } from "../constant";
import { Form } from "react-bootstrap";
import LocationModalSelect, { OptionType } from "./LocationModalSelect";

interface Location {
  id: string;
  name: string;
}

interface TitlesState {
  tier1: string;
  tier2: string;
  tier3: string;
  tier4: string;
  tier5: string;
  tier6: string;
}

interface AllFilterLocationProps {
  getLocation?: {
    locationOneId: string;
    locationTwoId: string;
    locationThreeId: string;
    locationFourId: string;
    locationFiveId: string;
    locationSixId: string;
  };
  handleFilter: (
    loc1: string,
    loc2: string,
    loc3: string,
    loc4: string,
    loc5: string,
    loc6: string
  ) => void;
  disabled:boolean;
}

const AllFilterLocation: React.FC<AllFilterLocationProps> = (props) => {
  const BASE_URL = `${API_URL}/`;

  const [locationOne, setLocationOne] = useState<Location[]>([]);
  const [locationTwo, setLocationTwo] = useState<Location[]>([]);
  const [locationThree, setLocationThree] = useState<Location[]>([]);
  const [locationFour, setLocationFour] = useState<Location[]>([]);
  const [locationFive, setLocationFive] = useState<Location[]>([]);
  const [locationSix, setLocationSix] = useState<Location[]>([]);

  const [selectedLocationOne, setSelectedLocationOne] = useState<string>("");
  const [selectedLocationTwo, setSelectedLocationTwo] = useState<string>("");
  const [selectedLocationThree, setSelectedLocationThree] = useState<string>("");
  const [selectedLocationFour, setSelectedLocationFour] = useState<string>("");
  const [selectedLocationFive, setSelectedLocationFive] = useState<string>("");
  const [selectedLocationSix, setSelectedLocationSix] = useState<string>("");

  // Refs to avoid re-fetching if user sets same selection
  const prevSelectedLocationOne = useRef<string>("");
  const prevSelectedLocationTwo = useRef<string>("");
  const prevSelectedLocationThree = useRef<string>("");
  const prevSelectedLocationFour = useRef<string>("");
  const prevSelectedLocationFive = useRef<string>("");
  const prevSelectedLocationSix = useRef<string>("");

  const [title, setTitles] = useState<TitlesState>({
    tier1: "Tier I",
    tier2: "Tier II",
    tier3: "Tier III",
    tier4: "Tier IV",
    tier5: "Tier V",
    tier6: "Tier VI",
  });

  // Utility to map to { value, label }
  const formatOptions = (locations: Location[]): OptionType[] => {
    return locations.map((location) => ({
      value: location.id,
      label: location.name,
    }));
  };

  // On mount, fetch Tier1
  useEffect(() => {
    (async () => {
      try {
        const response = await API.get(`${BASE_URL}location-ones`);
        if (response.status === 200) {
          setLocationOne(response.data);
          // Auto-select if only one option available
          if (response.data.length === 1 && !selectedLocationOne) {
            setSelectedLocationOne(response.data[0].id);
          }
        }
      } catch (error) {
        console.error("Error fetching locationOne:", error);
      }
    })();

    // Also fetch dynamic titles
    getLocationConfigs();
  }, []);

  // Fetch dynamic titles
  const getLocationConfigs = async () => {
    try {
      const response = await API.get(DYNAMIC_TITLES_URL);
      if (response.status === 200 && response.data && response.data.length > 0) {
        const titles = response.data;
        const locationsObject: Record<string, string> = titles.reduce(
          (obj: Record<string, string>, item: any) => {
            obj[item.title] = item.altTitle;
            return obj;
          },
          {}
        );
        setTitles({
          tier1: locationsObject.LocationOne || "Tier I",
          tier2: locationsObject.LocationTwo || "Tier II",
          tier3: locationsObject.LocationThree || "Tier III",
          tier4: locationsObject.LocationFour || "Tier IV",
          tier5: locationsObject.LocationFive || "Tier V",
          tier6: locationsObject.LocationSix || "Tier VI",
        });
      }
    } catch (error) {
      console.error("Error fetching location configs:", error);
      // fallback to defaults
    }
  };

  // Sync with parent getLocation prop
  useEffect(() => {
    if (props.getLocation) {
      const { locationOneId, locationTwoId, locationThreeId, locationFourId, locationFiveId, locationSixId } =
        props.getLocation;

      if (
        locationOneId !== selectedLocationOne ||
        locationTwoId !== selectedLocationTwo ||
        locationThreeId !== selectedLocationThree ||
        locationFourId !== selectedLocationFour ||
        locationFiveId !== selectedLocationFive ||
        locationSixId !== selectedLocationSix
      ) {
        setSelectedLocationOne(locationOneId);
        setSelectedLocationTwo(locationTwoId);
        setSelectedLocationThree(locationThreeId);
        setSelectedLocationFour(locationFourId);
        setSelectedLocationFive(locationFiveId);
        setSelectedLocationSix(locationSixId);
      }
    }
  }, [props.getLocation]);

  // TIER 1 changes
  useEffect(() => {
    if (!selectedLocationOne || selectedLocationOne === prevSelectedLocationOne.current) return;
    prevSelectedLocationOne.current = selectedLocationOne;

    (async () => {
      try {
        const response = await API.get(`${BASE_URL}location-ones/${selectedLocationOne}/location-twos`);
        if (response.status === 200) {
          setLocationTwo(response.data);
          // Reset lower-level
          setSelectedLocationTwo("");
          setSelectedLocationThree("");
          setSelectedLocationFour("");
          setSelectedLocationFive("");
          setSelectedLocationSix("");
          setLocationThree([]);
          setLocationFour([]);
          setLocationFive([]);
          setLocationSix([]);

          // Auto-select if only one option available
          if (response.data.length === 1) {
            setSelectedLocationTwo(response.data[0].id);
          }
        }
      } catch (error) {
        console.error("Error fetching locationTwo:", error);
      }
    })();

    props.handleFilter(selectedLocationOne, "", "", "", "", "");
  }, [selectedLocationOne]);

  // TIER 2 changes
  useEffect(() => {
    if (!selectedLocationTwo || selectedLocationTwo === prevSelectedLocationTwo.current) return;
    prevSelectedLocationTwo.current = selectedLocationTwo;

    (async () => {
      try {
        const response = await API.get(`${BASE_URL}location-twos/${selectedLocationTwo}/location-threes`);
        if (response.status === 200) {
          setLocationThree(response.data);
          // Reset
          setSelectedLocationThree("");
          setSelectedLocationFour("");
          setSelectedLocationFive("");
          setSelectedLocationSix("");
          setLocationFour([]);
          setLocationFive([]);
          setLocationSix([]);

          // Auto-select if only one option available
          if (response.data.length === 1) {
            setSelectedLocationThree(response.data[0].id);
          }
        }
      } catch (error) {
        console.error("Error fetching locationThree:", error);
      }
    })();

    props.handleFilter(selectedLocationOne, selectedLocationTwo, "", "", "", "");
  }, [selectedLocationTwo]);

  // TIER 3 changes
  useEffect(() => {
    if (!selectedLocationThree || selectedLocationThree === prevSelectedLocationThree.current) return;
    prevSelectedLocationThree.current = selectedLocationThree;

    (async () => {
      try {
        const response = await API.get(`${BASE_URL}location-threes/${selectedLocationThree}/location-fours`);
        if (response.status === 200) {
          setLocationFour(response.data);
          // Reset
          setSelectedLocationFour("");
          setSelectedLocationFive("");
          setSelectedLocationSix("");
          setLocationFive([]);
          setLocationSix([]);

          // Auto-select if only one option available
          if (response.data.length === 1) {
            setSelectedLocationFour(response.data[0].id);
          }
        }
      } catch (error) {
        console.error("Error fetching locationFour:", error);
      }
    })();

    props.handleFilter(selectedLocationOne, selectedLocationTwo, selectedLocationThree, "", "", "");
  }, [selectedLocationThree]);

  // TIER 4 changes
  useEffect(() => {
    if (!selectedLocationFour || selectedLocationFour === prevSelectedLocationFour.current) return;
    prevSelectedLocationFour.current = selectedLocationFour;

    (async () => {
      try {
        const response = await API.get(`${BASE_URL}location-fours/${selectedLocationFour}/location-fives`);
        if (response.status === 200) {
          setLocationFive(response.data);
          // Reset
          setSelectedLocationFive("");
          setSelectedLocationSix("");
          setLocationSix([]);

          // Auto-select if only one option available
          if (response.data.length === 1) {
            setSelectedLocationFive(response.data[0].id);
          }
        }
      } catch (error) {
        console.error("Error fetching locationFive:", error);
      }
    })();

    props.handleFilter(selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, "", "");
  }, [selectedLocationFour]);

  // TIER 5 changes
  useEffect(() => {
    if (!selectedLocationFive || selectedLocationFive === prevSelectedLocationFive.current) return;
    prevSelectedLocationFive.current = selectedLocationFive;

    (async () => {
      try {
        const response = await API.get(`${BASE_URL}location-fives/${selectedLocationFive}/location-sixes`);
        if (response.status === 200) {
          setLocationSix(response.data);
          // Reset
          setSelectedLocationSix("");

          // Auto-select if only one option available
          if (response.data.length === 1) {
            setSelectedLocationSix(response.data[0].id);
          }
        }
      } catch (error) {
        console.error("Error fetching locationSix:", error);
      }
    })();

    props.handleFilter(
      selectedLocationOne,
      selectedLocationTwo,
      selectedLocationThree,
      selectedLocationFour,
      selectedLocationFive,
      ""
    );
  }, [selectedLocationFive]);

  // TIER 6 changes
  useEffect(() => {
    if (!selectedLocationSix || selectedLocationSix === prevSelectedLocationSix.current) return;
    prevSelectedLocationSix.current = selectedLocationSix;

    props.handleFilter(
      selectedLocationOne,
      selectedLocationTwo,
      selectedLocationThree,
      selectedLocationFour,
      selectedLocationFive,
      selectedLocationSix
    );
  }, [selectedLocationSix]);

  return (
    <div className="d-flex flex-column gap-3">
      {/* Tier 1 */}
      <LocationModalSelect
        title={title.tier1}
        options={formatOptions(locationOne)}
        selectedValue={selectedLocationOne}
        onChange={(val) => setSelectedLocationOne(val)}
        disabled={props.disabled}
      />

      {/* Tier 2 */}
      {selectedLocationOne && locationTwo.length > 0 && (
        <LocationModalSelect
          title={title.tier2}
          options={formatOptions(locationTwo)}
          selectedValue={selectedLocationTwo}
          onChange={(val) => setSelectedLocationTwo(val)}
          disabled={props.disabled}
        />
      )}

      {/* Tier 3 */}
      {selectedLocationTwo && locationThree.length > 0 && (
        <LocationModalSelect
          title={title.tier3}
          options={formatOptions(locationThree)}
          selectedValue={selectedLocationThree}
          onChange={(val) => setSelectedLocationThree(val)}
          disabled={props.disabled}
        />
      )}

      {/* Tier 4 */}
      {selectedLocationThree && locationFour.length > 0 && (
        <LocationModalSelect
          title={title.tier4}
          options={formatOptions(locationFour)}
          selectedValue={selectedLocationFour}
          onChange={(val) => setSelectedLocationFour(val)}
          disabled={props.disabled}
        />
      )}

      {/* Tier 5 */}
      {selectedLocationFour && locationFive.length > 0 && (
        <LocationModalSelect
          title={title.tier5}
          options={formatOptions(locationFive)}
          selectedValue={selectedLocationFive}
          onChange={(val) => setSelectedLocationFive(val)}
          disabled={props.disabled}
        />
      )}

      {/* Tier 6 */}
      {selectedLocationFive && locationSix.length > 0 && (
        <LocationModalSelect
          title={title.tier6}
          options={formatOptions(locationSix)}
          selectedValue={selectedLocationSix}
          onChange={(val) => setSelectedLocationSix(val)}
          disabled={props.disabled}
        />
      )}
    </div>
  );
};

export default AllFilterLocation;
