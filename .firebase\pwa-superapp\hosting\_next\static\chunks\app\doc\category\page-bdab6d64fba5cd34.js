(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9156],{26957:(e,s,t)=>{"use strict";t.d(s,{AM:()=>c,Dp:()=>d,F4:()=>L,FI:()=>R,H$:()=>a,J9:()=>x,Jo:()=>g,K9:()=>l,M6:()=>u,MO:()=>m,OT:()=>z,P4:()=>N,UR:()=>C,WD:()=>k,WH:()=>v,WU:()=>S,_i:()=>n,bW:()=>y,dG:()=>r,dm:()=>A,iJ:()=>I,mh:()=>U,oo:()=>h,pZ:()=>j,u3:()=>o,x2:()=>b,xE:()=>i,xo:()=>w,yo:()=>p,zP:()=>f});let a="https://client-api.acuizen.com",r=a+"/login-configs",i=a+"/services",n=e=>a+"/files/"+e+"/presigned-url",c=a+"/users/me",l=a+"/dynamic-titles",o=a+"/users/get_users",d=a+"/files",m=a+"/observation-reports",u=a+"/my-observation-reports",h=a+"/dropdowns",x=a+"/get-blob",f=a+"/permit-reports",g=a+"/users",p=a+"/toolbox-talks",j=a+"/my-toolbox-talks",v=e=>a+"/my-assigned-actions/"+e,b=e=>a+"/inspection-checklist-submit/"+e,N=e=>a+"/observation-reports/"+e,y=e=>a+"/inspection-task-submit/"+e,w=e=>a+"/inspections/"+e,S=e=>a+"/permit-report-submit/"+e,k=e=>a+"/permit-reports-acknowledge/"+e,C=e=>a+"/permit-reports-update-status/"+e,R=e=>a+"/observation-action-submit/"+e,A=a+"/risk-assessments",I=e=>a+"/risk-assessments/"+e,L=e=>a+"/ra-team-member-submit-signature/"+e,z=a+"/permit-reports",U=e=>a+"/permit-reports/"+e},35695:(e,s,t)=>{"use strict";var a=t(18999);t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},37894:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var a=t(95155),r=t(12115),i=t(35695),n=t(46554),c=t(38336),l=t(26957);function o(){let e=(0,i.useSearchParams)(),s=(0,i.useRouter)(),[t,o]=(0,r.useState)(""),[d,m]=(0,r.useState)(""),[u,h]=(0,r.useState)([]),[x,f]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let s=e.get("categoryId"),t=localStorage.getItem("selectedCategoryId"),a=localStorage.getItem("selectedCategoryName");s?(o(s),localStorage.setItem("selectedCategoryId",s)):t&&o(t),a&&m(a)},[e]),(0,r.useEffect)(()=>{t&&g()},[t]);let g=async()=>{try{f(!0);let e="".concat(l.H$,"/my-documents"),s="".concat(e,"?filter=").concat(encodeURIComponent(JSON.stringify({include:[{relation:"creator"},{relation:"documentCategory"},{relation:"reviewer"},{relation:"approver"},{relation:"initiator"}]}))),a=await c.A.get(s);if(console.log("Category documents response:",a),200===a.status&&a.data){let e=a.data.filter(e=>e.documentCategoryId===t);console.log("Filtered documents for category:",t,e),h(e)}}catch(e){console.error("Error fetching documents:",e)}finally{f(!1)}},p=e=>{localStorage.setItem("selectedDocument",JSON.stringify(e)),s.push("/doc/view?id=".concat(e.id))};return x?(0,a.jsxs)("div",{className:"wrapper",children:[(0,a.jsx)(n.default,{heading:"Loading Documents..."}),(0,a.jsx)("main",{children:(0,a.jsx)("div",{className:"container-fluid",children:(0,a.jsx)("div",{className:"row",children:(0,a.jsx)("div",{className:"col-12",children:(0,a.jsx)("div",{className:"card",children:(0,a.jsx)("div",{className:"card-body text-center",children:(0,a.jsx)("div",{className:"spinner-border",role:"status",children:(0,a.jsx)("span",{className:"visually-hidden",children:"Loading..."})})})})})})})})]}):(0,a.jsxs)("div",{className:"wrapper",children:[(0,a.jsx)(n.default,{heading:"Documents in ".concat(d||"Category")}),(0,a.jsx)("main",{className:"mt-5",children:(0,a.jsx)("div",{className:"container-fluid",children:(0,a.jsx)("div",{className:"row",children:(0,a.jsx)("div",{className:"col-12",children:(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("div",{className:"card-header d-flex justify-content-between align-items-center",children:[(0,a.jsx)("h5",{className:"card-title mb-0",children:"Documents"}),(0,a.jsx)("button",{className:"btn btn-secondary btn-sm",onClick:()=>{s.push("/doc")},children:"← Back to Categories"})]}),(0,a.jsx)("div",{className:"card-body",children:0===u.length?(0,a.jsx)("div",{className:"text-center py-4",children:(0,a.jsx)("p",{className:"text-muted",children:"No documents found in this category."})}):(0,a.jsx)("div",{className:"row g-3",children:u.map(e=>(0,a.jsx)("div",{className:"col-12",children:(0,a.jsx)("div",{className:"card border-0 shadow-sm h-100",style:{borderRadius:"12px",backgroundColor:"#f8f9fa"},children:(0,a.jsxs)("div",{className:"card-body p-3",children:[(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-start mb-2",children:[(0,a.jsxs)("div",{className:"flex-grow-1",children:[(0,a.jsx)("h6",{className:"card-title mb-1 fw-bold text-dark",children:e.name}),e.description&&(0,a.jsx)("p",{className:"card-text text-muted small mb-2",style:{fontSize:"0.85rem"},children:e.description})]}),(0,a.jsx)("span",{className:"badge ms-2 ".concat("active"===e.status?"bg-success":"draft"===e.status?"bg-warning text-dark":"bg-secondary"),style:{fontSize:"0.75rem"},children:e.status||"Unknown"})]}),(0,a.jsxs)("div",{className:"row g-2 mb-3",children:[(0,a.jsx)("div",{className:"col-6",children:(0,a.jsxs)("div",{className:"d-flex align-items-center",children:[(0,a.jsx)("i",{className:"fas fa-user text-primary me-2",style:{fontSize:"0.85rem"}}),(0,a.jsxs)("div",{children:[(0,a.jsx)("small",{className:"text-muted d-block",style:{fontSize:"0.75rem"},children:"Creator"}),(0,a.jsx)("small",{className:"fw-medium text-dark",style:{fontSize:"0.8rem"},children:e.creator?"".concat(e.creator.firstName):"Unknown"})]})]})}),(0,a.jsx)("div",{className:"col-6",children:(0,a.jsxs)("div",{className:"d-flex align-items-center",children:[(0,a.jsx)("i",{className:"fas fa-calendar text-primary me-2",style:{fontSize:"0.85rem"}}),(0,a.jsxs)("div",{children:[(0,a.jsx)("small",{className:"text-muted d-block",style:{fontSize:"0.75rem"},children:"Created"}),(0,a.jsx)("small",{className:"fw-medium text-dark",style:{fontSize:"0.8rem"},children:new Date(e.created).toLocaleDateString()})]})]})})]}),(0,a.jsx)("div",{className:"d-grid",children:(0,a.jsxs)("button",{className:"btn btn-primary btn-sm",onClick:()=>p(e),style:{borderRadius:"8px",backgroundColor:"#007bff",border:"none",padding:"8px 16px",fontSize:"0.85rem"},children:[(0,a.jsx)("i",{className:"fas fa-eye me-2"}),"View Document"]})})]})})},e.id))})})]})})})})})]})}let d=function(){return(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{children:"Loading..."}),children:(0,a.jsx)(o,{})})}},38336:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var a=t(96078),r=t(26957);let i=a.A.create({baseURL:r.H$,headers:{Accept:"application/json","Content-Type":"application/json"},mode:"no-cors"});i.interceptors.request.use(async e=>(e.metadata={requestTime:Date.now()},e),e=>Promise.reject(e)),i.interceptors.response.use(e=>{var s;return e.headers["x-request-time"]=null==(s=e.config.metadata)?void 0:s.requestTime,e},async e=>{let{offlineQueue:s}=await t.e(8836).then(t.bind(t,48836)),{offlineStorage:a}=await t.e(58).then(t.bind(t,60058));if(s.shouldQueue(e)){var r,i,n,c;let t=e.config;if(await s.addRequest(t.url,(null==(r=t.method)?void 0:r.toUpperCase())||"GET",t.data,t.headers),(null==(i=t.method)?void 0:i.toLowerCase())==="get")try{if(null==(n=t.url)?void 0:n.includes("/services")){let e=await a.getServices();if(e.length>0)return console.log("\uD83D\uDCF1 Serving services from offline cache"),{data:e,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:t}}if(null==(c=t.url)?void 0:c.includes("assigned-actions")){let e=new URLSearchParams(t.url.split("?")[1]).get("filter"),s="All";if(e)try{JSON.parse(decodeURIComponent(e))}catch(r){let e=t.url.split("/"),a=e.findIndex(e=>"assigned-actions"===e);-1!==a&&e[a+1]&&(s=e[a+1])}let r=await a.getActions(s);if(r.length>0)return console.log("\uD83D\uDCF1 Serving actions from offline cache"),{data:r,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:t}}}catch(e){console.error("❌ Error serving from cache:",e)}}return Promise.reject(e)});let n=i},46554:(e,s,t)=>{"use strict";t.d(s,{default:()=>d});var a=t(95155),r=t(12115),i=t(35695),n=t(38336),c=t(26957),l=t(34540),o=t(81359);let d=e=>{let{heading:s}=e,t=(0,i.useRouter)(),[d,m]=(0,r.useState)(""),u=(0,l.wA)();r.useEffect(()=>{h()},[]);let h=async()=>{try{let e=await n.A.get(c.AM);200===e.status?(m(e.data.firstName),u(o.l.setUser(e.data))):t.push("/")}catch(e){console.log(e)}};return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"header-area",id:"headerArea",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"header-content header-style-four position-relative d-flex align-items-center justify-content-between",children:[(0,a.jsx)("div",{className:"back-button",children:(0,a.jsx)("button",{onClick:()=>t.back(),className:"border-0 bg-transparent p-0",style:{cursor:"pointer"},children:(0,a.jsx)("i",{className:"bi bi-arrow-left-short fs-3"})})}),(0,a.jsx)("div",{className:"page-heading",children:(0,a.jsx)("h6",{className:"mb-0",children:s})}),(0,a.jsx)("div",{className:"user-profile-wrapper"})]})})})})}},65574:(e,s,t)=>{Promise.resolve().then(t.bind(t,37894))},81359:(e,s,t)=>{"use strict";t.d(s,{A:()=>i,l:()=>r});let a=(0,t(51990).Z0)({name:"login",initialState:{isLogin:!1,user:null},reducers:{setLogin(e){e.isLogin=!0},setLogout(e){e.isLogin=!1,e.user=null},setUser(e,s){e.user=s.payload}}}),r=a.actions,i=a}},e=>{var s=s=>e(e.s=s);e.O(0,[6078,635,8441,1684,7358],()=>s(65574)),_N_E=e.O()}]);