"use client";

import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import fcmService from '@/services/fcmService';
import unifiedNotificationService from '@/services/unifiedNotificationService';
import { Toast, ToastContainer } from 'react-bootstrap';

const PostLoginFCMInitializer: React.FC = () => {
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastVariant, setToastVariant] = useState<'success' | 'warning' | 'danger'>('success');
  const [isInitialized, setIsInitialized] = useState(false);

  // Keys for localStorage to track notification states
  const NOTIFICATION_DISMISSED_KEY = 'fcm_notification_dismissed';
  const NOTIFICATION_ENABLED_SHOWN_KEY = 'fcm_enabled_notification_shown';
  const NOTIFICATION_PROMPT_SHOWN_KEY = 'fcm_prompt_shown_today';

  // Get login state from Redux
  const isLoggedIn = useSelector((state: RootState) => state.login.isLogin);

  // Helper functions to manage notification state
  const hasUserDismissedNotification = () => {
    return localStorage.getItem(NOTIFICATION_DISMISSED_KEY) === 'true';
  };

  const hasShownEnabledNotificationToday = () => {
    const lastShown = localStorage.getItem(NOTIFICATION_ENABLED_SHOWN_KEY);
    if (!lastShown) return false;

    const today = new Date().toDateString();
    return lastShown === today;
  };

  const hasShownPromptToday = () => {
    const lastShown = localStorage.getItem(NOTIFICATION_PROMPT_SHOWN_KEY);
    if (!lastShown) return false;

    const today = new Date().toDateString();
    return lastShown === today;
  };

  const markNotificationDismissed = () => {
    localStorage.setItem(NOTIFICATION_DISMISSED_KEY, 'true');
  };

  const markEnabledNotificationShown = () => {
    const today = new Date().toDateString();
    localStorage.setItem(NOTIFICATION_ENABLED_SHOWN_KEY, today);
  };

  const markPromptShown = () => {
    const today = new Date().toDateString();
    localStorage.setItem(NOTIFICATION_PROMPT_SHOWN_KEY, today);
  };

  // Helper function to reset notification preferences (for debugging)
  const resetNotificationPreferences = () => {
    localStorage.removeItem(NOTIFICATION_DISMISSED_KEY);
    localStorage.removeItem(NOTIFICATION_ENABLED_SHOWN_KEY);
    localStorage.removeItem(NOTIFICATION_PROMPT_SHOWN_KEY);
    console.log('🔄 Notification preferences reset. Reload the page to see prompts again.');
  };

  // Make reset function available globally for debugging
  React.useEffect(() => {
    (window as any).resetFCMNotifications = resetNotificationPreferences;
  }, []);

  useEffect(() => {
    console.log('🔍 PostLoginFCMInitializer - Login state check:');
    console.log('  - isLoggedIn (Redux):', isLoggedIn);
    console.log('  - isInitialized:', isInitialized);
    console.log('  - access_token exists:', !!localStorage.getItem('access_token'));

    // Check both Redux state and localStorage for login status
    const hasAccessToken = !!localStorage.getItem('access_token');
    const shouldInitialize = (isLoggedIn || hasAccessToken) && !isInitialized;

    console.log('  - shouldInitialize:', shouldInitialize);

    if (shouldInitialize) {
      console.log('🚀 Triggering FCM initialization after login...');
      initializeFCMAfterLogin();
      setIsInitialized(true);
    }
  }, [isLoggedIn, isInitialized]); // eslint-disable-line react-hooks/exhaustive-deps

  // Also check on component mount in case user is already logged in
  useEffect(() => {
    const checkInitialLoginState = () => {
      const hasAccessToken = !!localStorage.getItem('access_token');
      console.log('🔍 Initial login state check on mount:');
      console.log('  - access_token exists:', hasAccessToken);
      console.log('  - Redux isLoggedIn:', isLoggedIn);

      if (hasAccessToken && !isInitialized) {
        console.log('🚀 User already logged in, initializing FCM...');
        setTimeout(() => {
          initializeFCMAfterLogin();
          setIsInitialized(true);
        }, 1000); // Small delay to ensure everything is loaded
      }
    };

    checkInitialLoginState();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const initializeFCMAfterLogin = async () => {
    try {
      console.log('🚀 Initializing FCM after successful login...');

      // Check if user has access token (confirming they're authenticated)
      const accessToken = localStorage.getItem('access_token');
      if (!accessToken) {
        console.log('⚠️ No access token found, skipping FCM initialization');
        return;
      }

      // Initialize FCM service
      await fcmService.initialize();

      // Check if we already have a stored FCM token
      const existingToken = await fcmService.getStoredToken();

      if (existingToken) {
        console.log('✅ Existing FCM token found:', existingToken.substring(0, 50) + '...');

        // Send existing token to server (in case it wasn't sent before)
        await fcmService.sendTokenToServer(existingToken);

        // Only show "already enabled" notification if we haven't shown it today
        if (!hasShownEnabledNotificationToday()) {
          showNotification('Push notifications are already enabled!', 'success');
          markEnabledNotificationShown();
        }
      } else {
        // Check if notifications are supported
        if (!('Notification' in window)) {
          console.log('⚠️ Push notifications not supported in this browser');
          return;
        }

        // Check current permission status
        const permission = Notification.permission;

        if (permission === 'granted') {
          // User has already granted permission, generate token
          await generateTokenSilently();
        } else if (permission === 'default') {
          // Only show prompt if user hasn't dismissed it and we haven't shown it today
          if (!hasUserDismissedNotification() && !hasShownPromptToday()) {
            // Show a friendly prompt to enable notifications after a short delay
            setTimeout(() => {
              showNotificationPrompt();
              markPromptShown();
            }, 2000); // Wait 2 seconds after login before prompting
          }
        } else {
          // Permission denied
          console.log('⚠️ Notification permission denied');
          showNotification('Push notifications are disabled. You can enable them in Settings.', 'warning');
        }
      }
    } catch (error) {
      console.error('❌ Error initializing FCM after login:', error);
    }
  };

  const generateTokenSilently = async () => {
    try {
      console.log('🔄 Generating FCM token silently...');
      const token = await fcmService.generateFCMToken();

      if (token) {
        // Only show success message if we haven't shown it today
        if (!hasShownEnabledNotificationToday()) {
          showNotification('Push notifications enabled successfully!', 'success');
          markEnabledNotificationShown();
        }
      }
    } catch (error) {
      console.error('❌ Error generating FCM token:', error);
    }
  };

  const showNotificationPrompt = () => {
    // Show a user-friendly toast asking if they want to enable notifications
    setToastMessage('Welcome! Would you like to enable push notifications to stay updated with important alerts and messages?');
    setToastVariant('warning');
    setShowToast(true);

    // Auto-hide after 15 seconds to give user time to read
    setTimeout(() => {
      setShowToast(false);
    }, 15000);
  };

  const handleEnableNotifications = async () => {
    try {
      setShowToast(false);
      console.log('🔄 User requested to enable notifications...');

      const token = await fcmService.generateFCMToken();

      if (token) {
        showNotification('Push notifications enabled successfully!', 'success');
        markEnabledNotificationShown();
        // Clear the dismissed flag since user successfully enabled notifications
        localStorage.removeItem(NOTIFICATION_DISMISSED_KEY);
      } else {
        showNotification('Failed to enable push notifications. Please try again.', 'danger');
      }
    } catch (error) {
      console.error('❌ Error enabling notifications:', error);
      showNotification('Failed to enable push notifications. Please try again.', 'danger');
    }
  };

  const showNotification = (message: string, variant: 'success' | 'warning' | 'danger') => {
    setToastMessage(message);
    setToastVariant(variant);
    setShowToast(true);

    // Auto-hide success messages after 5 seconds
    if (variant === 'success') {
      setTimeout(() => {
        setShowToast(false);
      }, 5000);
    }
  };

  // This component doesn't render anything visible by default
  // It only shows toasts when needed
  return (
    <>
      <ToastContainer
        position="top-center"
        className="modern-notification-toast"
        style={{
          position: 'fixed',
          top: '20px',
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 1060,
          width: '100%',
          maxWidth: '420px',
          padding: '0 16px'
        }}
      >
        <Toast
          show={showToast}
          onClose={() => setShowToast(false)}
          className="modern-toast-card"
          style={{
            width: '100%',
            border: 'none',
            borderRadius: '16px',
            boxShadow: '0 10px 40px rgba(0, 0, 0, 0.1), 0 4px 16px rgba(0, 0, 0, 0.05)',
            background: '#ffffff',
            color: '#1f2937',
            overflow: 'hidden'
          }}
        >
          <Toast.Header
            closeButton={true}
            style={{
              background: toastVariant === 'success'
                ? '#f0fdf4'
                : toastVariant === 'warning'
                ? '#fffbeb'
                : toastVariant === 'danger'
                ? '#fef2f2'
                : '#f0f9ff',
              border: 'none',
              borderRadius: '16px 16px 0 0',
              padding: '16px 20px 12px 20px',
              borderBottom: `1px solid ${
                toastVariant === 'success'
                  ? '#dcfce7'
                  : toastVariant === 'warning'
                  ? '#fef3c7'
                  : toastVariant === 'danger'
                  ? '#fecaca'
                  : '#dbeafe'
              }`
            }}
            className="modern-toast-header"
          >
            <div className="d-flex align-items-center">
              <div
                className="toast-icon-wrapper me-3"
                style={{
                  width: '40px',
                  height: '40px',
                  borderRadius: '12px',
                  background: toastVariant === 'success'
                    ? '#22c55e'
                    : toastVariant === 'warning'
                    ? '#f59e0b'
                    : toastVariant === 'danger'
                    ? '#ef4444'
                    : '#3b82f6',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <i
                  className={`bi ${
                    toastVariant === 'success' ? 'bi-check-circle-fill' :
                    toastVariant === 'warning' ? 'bi-bell-fill' :
                    toastVariant === 'danger' ? 'bi-exclamation-triangle-fill' :
                    'bi-bell-fill'
                  }`}
                  style={{ fontSize: '20px', color: 'white' }}
                ></i>
              </div>
              <div>
                <strong className="toast-title" style={{
                  fontSize: '16px',
                  fontWeight: '600',
                  color: toastVariant === 'success'
                    ? '#166534'
                    : toastVariant === 'warning'
                    ? '#92400e'
                    : toastVariant === 'danger'
                    ? '#991b1b'
                    : '#1e40af'
                }}>
                  {toastVariant === 'success' ? 'Success!' :
                   toastVariant === 'warning' ? 'Enable Notifications' :
                   toastVariant === 'danger' ? 'Error' :
                   'Notifications'}
                </strong>
              </div>
            </div>
          </Toast.Header>
          <Toast.Body style={{
            padding: '20px',
            background: '#ffffff'
          }}>
            <p className="mb-0" style={{
              fontSize: '15px',
              lineHeight: '1.6',
              color: '#6b7280',
              marginBottom: toastVariant === 'warning' ? '16px' : '0'
            }}>
              {toastMessage}
            </p>
            {toastVariant === 'warning' && (
              <div className="mt-3 d-flex flex-column gap-2">
                <button
                  className="btn modern-primary-btn"
                  onClick={handleEnableNotifications}
                  style={{
                    background: '#3b82f6',
                    border: '1px solid #3b82f6',
                    color: 'white',
                    borderRadius: '12px',
                    padding: '12px 20px',
                    fontSize: '15px',
                    fontWeight: '600',
                    transition: 'all 0.2s ease',
                    boxShadow: '0 2px 8px rgba(59, 130, 246, 0.2)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = '#2563eb';
                    e.currentTarget.style.borderColor = '#2563eb';
                    e.currentTarget.style.transform = 'translateY(-1px)';
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.3)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = '#3b82f6';
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(59, 130, 246, 0.2)';
                  }}
                >
                  <i className="bi bi-bell me-2"></i>
                  Enable Notifications
                </button>
                <button
                  className="btn modern-secondary-btn"
                  onClick={() => {
                    setShowToast(false);
                    markNotificationDismissed();
                  }}
                  style={{
                    background: '#f9fafb',
                    border: '1px solid #e5e7eb',
                    color: '#6b7280',
                    borderRadius: '12px',
                    padding: '12px 20px',
                    fontSize: '14px',
                    fontWeight: '500',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = '#f3f4f6';
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.color = '#374151';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = '#f9fafb';
                    e.currentTarget.style.borderColor = '#e5e7eb';
                    e.currentTarget.style.color = '#6b7280';
                  }}
                >
                  Maybe Later
                </button>
              </div>
            )}
          </Toast.Body>
        </Toast>
      </ToastContainer>


    </>
  );
};

export default PostLoginFCMInitializer;
