"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2545],{45299:(e,t,n)=>{n.d(t,{ie:()=>b,we:()=>N});var r,l=n(12115),u=n(95155),o=n(86301);n(47650);var i=n(84945);let s={...r||(r=n.t(l,2))},a=s.useInsertionEffect||(e=>e()),c="ArrowUp",f="ArrowDown",d="ArrowLeft",g="ArrowRight";function h(e,t){let{startingIndex:n=-1,decrement:r=!1,disabledIndices:l,amount:u=1}=void 0===t?{}:t,o=e.current,i=n;do i+=r?-u:u;while(i>=0&&i<=o.length-1&&function(e,t,n){if(n)return n.includes(t);let r=e[t];return null==r||r.hasAttribute("disabled")||"true"===r.getAttribute("aria-disabled")}(o,i,l));return i}var v="undefined"!=typeof document?l.useLayoutEffect:l.useEffect;let m=!1,p=0,x=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+p++,w=s.useId||function(){let[e,t]=l.useState(()=>m?x():void 0);return v(()=>{null==e&&t(x())},[]),l.useEffect(()=>{m=!0},[]),e},b=l.forwardRef(function(e,t){let{context:{placement:n,elements:{floating:r},middlewareData:{arrow:i,shift:s}},width:a=14,height:c=7,tipRadius:f=0,strokeWidth:d=0,staticOffset:g,stroke:h,d:m,style:{transform:p,...x}={},...b}=e,R=w(),[C,k]=l.useState(!1);if(v(()=>{r&&"rtl"===(0,o.L9)(r).direction&&k(!0)},[r]),!r)return null;let[y,M]=n.split("-"),E="top"===y||"bottom"===y,S=g;(E&&null!=s&&s.x||!E&&null!=s&&s.y)&&(S=null);let A=2*d,j=A/2,I=a/2*(-(f/8)+1),q=c/2*f/4,D=!!m,L=S&&"end"===M?"bottom":"top",O=S&&"end"===M?"right":"left";S&&C&&(O="end"===M?"left":"right");let _=(null==i?void 0:i.x)!=null?S||i.x:"",z=(null==i?void 0:i.y)!=null?S||i.y:"",B=m||"M0,0 H"+a+(" L"+(a-I))+","+(c-q)+(" Q"+a/2+","+c+" "+I)+","+(c-q)+" Z",N={top:D?"rotate(180deg)":"",left:D?"rotate(90deg)":"rotate(-90deg)",bottom:D?"":"rotate(180deg)",right:D?"rotate(-90deg)":"rotate(90deg)"}[y];return(0,u.jsxs)("svg",{...b,"aria-hidden":!0,ref:t,width:D?a:a+A,height:a,viewBox:"0 0 "+a+" "+(c>a?c:a),style:{position:"absolute",pointerEvents:"none",[O]:_,[L]:z,[y]:E||D?"100%":"calc(100% - "+A/2+"px)",transform:[N,p].filter(e=>!!e).join(" "),...x},children:[A>0&&(0,u.jsx)("path",{clipPath:"url(#"+R+")",fill:"none",stroke:h,strokeWidth:A+ +!m,d:B}),(0,u.jsx)("path",{stroke:A&&!m?b.fill:"none",d:B}),(0,u.jsx)("clipPath",{id:R,children:(0,u.jsx)("rect",{x:-j,y:j*(D?-1:1),width:a+A,height:a})})]})}),R=l.createContext(null),C=l.createContext(null),k=()=>{var e;return(null==(e=l.useContext(R))?void 0:e.id)||null},y=()=>l.useContext(C),M=()=>{},E=l.createContext({delay:0,initialDelay:0,timeoutMs:0,currentId:null,setCurrentId:M,setState:M,isInstantPhase:!1}),S=0,A=new WeakMap,j=new WeakSet,I={},q=0,D=e=>e&&(e.host||D(e.parentNode)),L=(e,t)=>t.map(t=>{if(e.contains(t))return t;let n=D(t);return e.contains(n)?n:null}).filter(e=>null!=e),O=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function _(e,t){let n=tabbable(e,O());"prev"===t&&n.reverse();let r=n.indexOf(activeElement(getDocument(e)));return n.slice(r+1)[0]}let z="data-floating-ui-focusable",B=null;function N(e){void 0===e&&(e={});let{nodeId:t}=e,n=function(e){let{open:t=!1,onOpenChange:n,elements:r}=e,u=w(),o=l.useRef({}),[i]=l.useState(()=>(function(){let e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach(e=>e(n))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,(null==(r=e.get(t))?void 0:r.filter(e=>e!==n))||[])}}})()),s=null!=k(),[c,f]=l.useState(r.reference),d=function(e){let t=l.useRef(()=>{});return a(()=>{t.current=e}),l.useCallback(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}((e,t,r)=>{o.current.openEvent=e?t:void 0,i.emit("openchange",{open:e,event:t,reason:r,nested:s}),null==n||n(e,t,r)}),g=l.useMemo(()=>({setPositionReference:f}),[]),h=l.useMemo(()=>({reference:c||r.reference||null,floating:r.floating||null,domReference:r.reference}),[c,r.reference,r.floating]);return l.useMemo(()=>({dataRef:o,open:t,onOpenChange:d,elements:h,events:i,floatingId:u,refs:g}),[t,d,h,i,u,g])}({...e,elements:{reference:null,floating:null,...e.elements}}),r=e.rootContext||n,u=r.elements,[s,c]=l.useState(null),[f,d]=l.useState(null),g=(null==u?void 0:u.domReference)||s,h=l.useRef(null),m=y();v(()=>{g&&(h.current=g)},[g]);let p=(0,i.we)({...e,elements:{...u,...f&&{reference:f}}}),x=l.useCallback(e=>{let t=(0,o.vq)(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;d(t),p.refs.setReference(t)},[p.refs]),b=l.useCallback(e=>{((0,o.vq)(e)||null===e)&&(h.current=e,c(e)),((0,o.vq)(p.refs.reference.current)||null===p.refs.reference.current||null!==e&&!(0,o.vq)(e))&&p.refs.setReference(e)},[p.refs]),R=l.useMemo(()=>({...p.refs,setReference:b,setPositionReference:x,domReference:h}),[p.refs,b,x]),C=l.useMemo(()=>({...p.elements,domReference:g}),[p.elements,g]),M=l.useMemo(()=>({...p,...r,refs:R,elements:C,nodeId:t}),[p,R,C,t,r]);return v(()=>{r.dataRef.current.floatingContext=M;let e=null==m?void 0:m.nodesRef.current.find(e=>e.id===t);e&&(e.context=M)}),l.useMemo(()=>({...p,context:M,refs:R,elements:C}),[p,R,C,M])}let P="active",W="selected";function H(e,t,n){switch(e){case"vertical":return t;case"horizontal":return n;default:return t||n}}}}]);