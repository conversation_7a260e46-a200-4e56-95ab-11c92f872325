"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9314],{23327:(e,t,a)=>{a.d(t,{A:()=>u});var s=a(95155),r=a(12115),i=a(38336),o=a(26957),n=a(16639),l=a(60902),c=a(56160);let d=e=>{var t;let{title:a,options:i,selectedValue:o,onChange:d,disabled:u}=e,[h,f]=(0,r.useState)(!1),[g,m]=(0,r.useState)(""),p=i.filter(e=>e.label.toLowerCase().includes(g.toLowerCase())),x=()=>{f(!1)},b=e=>{d(e),f(!1)},v=(null==(t=i.find(e=>e.value===o))?void 0:t.label)||"Select";return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.A.Label,{children:a}),(0,s.jsx)(l.A,{variant:"outline-secondary",className:"w-100 text-start",onClick:()=>{m(""),f(!0)},disabled:u,children:v}),(0,s.jsxs)(c.A,{show:h,onHide:x,centered:!0,children:[(0,s.jsx)(c.A.Header,{closeButton:!0,children:(0,s.jsx)(c.A.Title,{children:a})}),(0,s.jsxs)(c.A.Body,{children:[(0,s.jsx)(n.A.Control,{type:"text",placeholder:"Search...",value:g,onChange:e=>m(e.target.value),className:"mb-3"}),p.length>0?(0,s.jsx)("div",{style:{maxHeight:"300px",overflowY:"auto"},className:"list-group",children:p.map(e=>(0,s.jsx)("button",{type:"button",className:"list-group-item list-group-item-action ".concat(e.value===o?"active":""),onClick:()=>b(e.value),children:e.label},e.value))}):(0,s.jsx)("p",{className:"text-muted",children:"No options found."})]}),(0,s.jsx)(c.A.Footer,{children:(0,s.jsx)(l.A,{variant:"secondary",onClick:x,children:"Close"})})]})]})},u=e=>{let t="".concat(o.H$,"/"),[a,n]=(0,r.useState)([]),[l,c]=(0,r.useState)([]),[u,h]=(0,r.useState)([]),[f,g]=(0,r.useState)([]),[m,p]=(0,r.useState)([]),[x,b]=(0,r.useState)([]),[v,y]=(0,r.useState)(""),[j,w]=(0,r.useState)(""),[A,S]=(0,r.useState)(""),[T,C]=(0,r.useState)(""),[E,L]=(0,r.useState)(""),[N,I]=(0,r.useState)(""),k=(0,r.useRef)(""),F=(0,r.useRef)(""),R=(0,r.useRef)(""),V=(0,r.useRef)(""),U=(0,r.useRef)(""),H=(0,r.useRef)(""),[O,_]=(0,r.useState)({tier1:"Tier I",tier2:"Tier II",tier3:"Tier III",tier4:"Tier IV",tier5:"Tier V",tier6:"Tier VI"}),q=e=>e.map(e=>({value:e.id,label:e.name}));(0,r.useEffect)(()=>{(async()=>{try{let e=await i.A.get("".concat(t,"location-ones"));200===e.status&&(n(e.data),1!==e.data.length||v||y(e.data[0].id))}catch(e){console.error("Error fetching locationOne:",e)}})(),P()},[]);let P=async()=>{try{let e=await i.A.get(o.K9);if(200===e.status&&e.data&&e.data.length>0){let t=e.data.reduce((e,t)=>(e[t.title]=t.altTitle,e),{});_({tier1:t.LocationOne||"Tier I",tier2:t.LocationTwo||"Tier II",tier3:t.LocationThree||"Tier III",tier4:t.LocationFour||"Tier IV",tier5:t.LocationFive||"Tier V",tier6:t.LocationSix||"Tier VI"})}}catch(e){console.error("Error fetching location configs:",e)}};return(0,r.useEffect)(()=>{if(e.getLocation){let{locationOneId:t,locationTwoId:a,locationThreeId:s,locationFourId:r,locationFiveId:i,locationSixId:o}=e.getLocation;(t!==v||a!==j||s!==A||r!==T||i!==E||o!==N)&&(y(t),w(a),S(s),C(r),L(i),I(o))}},[e.getLocation]),(0,r.useEffect)(()=>{v&&v!==k.current&&(k.current=v,(async()=>{try{let e=await i.A.get("".concat(t,"location-ones/").concat(v,"/location-twos"));200===e.status&&(c(e.data),w(""),S(""),C(""),L(""),I(""),h([]),g([]),p([]),b([]),1===e.data.length&&w(e.data[0].id))}catch(e){console.error("Error fetching locationTwo:",e)}})(),e.handleFilter(v,"","","","",""))},[v]),(0,r.useEffect)(()=>{j&&j!==F.current&&(F.current=j,(async()=>{try{let e=await i.A.get("".concat(t,"location-twos/").concat(j,"/location-threes"));200===e.status&&(h(e.data),S(""),C(""),L(""),I(""),g([]),p([]),b([]),1===e.data.length&&S(e.data[0].id))}catch(e){console.error("Error fetching locationThree:",e)}})(),e.handleFilter(v,j,"","","",""))},[j]),(0,r.useEffect)(()=>{A&&A!==R.current&&(R.current=A,(async()=>{try{let e=await i.A.get("".concat(t,"location-threes/").concat(A,"/location-fours"));200===e.status&&(g(e.data),C(""),L(""),I(""),p([]),b([]),1===e.data.length&&C(e.data[0].id))}catch(e){console.error("Error fetching locationFour:",e)}})(),e.handleFilter(v,j,A,"","",""))},[A]),(0,r.useEffect)(()=>{T&&T!==V.current&&(V.current=T,(async()=>{try{let e=await i.A.get("".concat(t,"location-fours/").concat(T,"/location-fives"));200===e.status&&(p(e.data),L(""),I(""),b([]),1===e.data.length&&L(e.data[0].id))}catch(e){console.error("Error fetching locationFive:",e)}})(),e.handleFilter(v,j,A,T,"",""))},[T]),(0,r.useEffect)(()=>{E&&E!==U.current&&(U.current=E,(async()=>{try{let e=await i.A.get("".concat(t,"location-fives/").concat(E,"/location-sixes"));200===e.status&&(b(e.data),I(""),1===e.data.length&&I(e.data[0].id))}catch(e){console.error("Error fetching locationSix:",e)}})(),e.handleFilter(v,j,A,T,E,""))},[E]),(0,r.useEffect)(()=>{N&&N!==H.current&&(H.current=N,e.handleFilter(v,j,A,T,E,N))},[N]),(0,s.jsxs)("div",{className:"d-flex flex-column gap-3",children:[(0,s.jsx)(d,{title:O.tier1,options:q(a),selectedValue:v,onChange:e=>y(e),disabled:e.disabled}),v&&l.length>0&&(0,s.jsx)(d,{title:O.tier2,options:q(l),selectedValue:j,onChange:e=>w(e),disabled:e.disabled}),j&&u.length>0&&(0,s.jsx)(d,{title:O.tier3,options:q(u),selectedValue:A,onChange:e=>S(e),disabled:e.disabled}),A&&f.length>0&&(0,s.jsx)(d,{title:O.tier4,options:q(f),selectedValue:T,onChange:e=>C(e),disabled:e.disabled}),T&&m.length>0&&(0,s.jsx)(d,{title:O.tier5,options:q(m),selectedValue:E,onChange:e=>L(e),disabled:e.disabled}),E&&x.length>0&&(0,s.jsx)(d,{title:O.tier6,options:q(x),selectedValue:N,onChange:e=>I(e),disabled:e.disabled})]})}},26957:(e,t,a)=>{a.d(t,{AM:()=>n,Dp:()=>d,F4:()=>I,FI:()=>E,H$:()=>s,J9:()=>g,Jo:()=>p,K9:()=>l,M6:()=>h,MO:()=>u,OT:()=>k,P4:()=>j,UR:()=>C,WD:()=>T,WH:()=>v,WU:()=>S,_i:()=>o,bW:()=>w,dG:()=>r,dm:()=>L,iJ:()=>N,mh:()=>F,oo:()=>f,pZ:()=>b,u3:()=>c,x2:()=>y,xE:()=>i,xo:()=>A,yo:()=>x,zP:()=>m});let s="https://client-api.acuizen.com",r=s+"/login-configs",i=s+"/services",o=e=>s+"/files/"+e+"/presigned-url",n=s+"/users/me",l=s+"/dynamic-titles",c=s+"/users/get_users",d=s+"/files",u=s+"/observation-reports",h=s+"/my-observation-reports",f=s+"/dropdowns",g=s+"/get-blob",m=s+"/permit-reports",p=s+"/users",x=s+"/toolbox-talks",b=s+"/my-toolbox-talks",v=e=>s+"/my-assigned-actions/"+e,y=e=>s+"/inspection-checklist-submit/"+e,j=e=>s+"/observation-reports/"+e,w=e=>s+"/inspection-task-submit/"+e,A=e=>s+"/inspections/"+e,S=e=>s+"/permit-report-submit/"+e,T=e=>s+"/permit-reports-acknowledge/"+e,C=e=>s+"/permit-reports-update-status/"+e,E=e=>s+"/observation-action-submit/"+e,L=s+"/risk-assessments",N=e=>s+"/risk-assessments/"+e,I=e=>s+"/ra-team-member-submit-signature/"+e,k=s+"/permit-reports",F=e=>s+"/permit-reports/"+e},38336:(e,t,a)=>{a.d(t,{A:()=>o});var s=a(96078),r=a(26957);let i=s.A.create({baseURL:r.H$,headers:{Accept:"application/json","Content-Type":"application/json"},mode:"no-cors"});i.interceptors.request.use(async e=>(e.metadata={requestTime:Date.now()},e),e=>Promise.reject(e)),i.interceptors.response.use(e=>{var t;return e.headers["x-request-time"]=null==(t=e.config.metadata)?void 0:t.requestTime,e},async e=>{let{offlineQueue:t}=await a.e(8836).then(a.bind(a,48836)),{offlineStorage:s}=await a.e(58).then(a.bind(a,60058));if(t.shouldQueue(e)){var r,i,o,n;let a=e.config;if(await t.addRequest(a.url,(null==(r=a.method)?void 0:r.toUpperCase())||"GET",a.data,a.headers),(null==(i=a.method)?void 0:i.toLowerCase())==="get")try{if(null==(o=a.url)?void 0:o.includes("/services")){let e=await s.getServices();if(e.length>0)return console.log("\uD83D\uDCF1 Serving services from offline cache"),{data:e,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:a}}if(null==(n=a.url)?void 0:n.includes("assigned-actions")){let e=new URLSearchParams(a.url.split("?")[1]).get("filter"),t="All";if(e)try{JSON.parse(decodeURIComponent(e))}catch(r){let e=a.url.split("/"),s=e.findIndex(e=>"assigned-actions"===e);-1!==s&&e[s+1]&&(t=e[s+1])}let r=await s.getActions(t);if(r.length>0)return console.log("\uD83D\uDCF1 Serving actions from offline cache"),{data:r,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:a}}}catch(e){console.error("❌ Error serving from cache:",e)}}return Promise.reject(e)});let o=i},46554:(e,t,a)=>{a.d(t,{default:()=>d});var s=a(95155),r=a(12115),i=a(35695),o=a(38336),n=a(26957),l=a(34540),c=a(81359);let d=e=>{let{heading:t}=e,a=(0,i.useRouter)(),[d,u]=(0,r.useState)(""),h=(0,l.wA)();r.useEffect(()=>{f()},[]);let f=async()=>{try{let e=await o.A.get(n.AM);200===e.status?(u(e.data.firstName),h(c.l.setUser(e.data))):a.push("/")}catch(e){console.log(e)}};return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"header-area",id:"headerArea",children:(0,s.jsx)("div",{className:"container",children:(0,s.jsxs)("div",{className:"header-content header-style-four position-relative d-flex align-items-center justify-content-between",children:[(0,s.jsx)("div",{className:"back-button",children:(0,s.jsx)("button",{onClick:()=>a.back(),className:"border-0 bg-transparent p-0",style:{cursor:"pointer"},children:(0,s.jsx)("i",{className:"bi bi-arrow-left-short fs-3"})})}),(0,s.jsx)("div",{className:"page-heading",children:(0,s.jsx)("h6",{className:"mb-0",children:t})}),(0,s.jsx)("div",{className:"user-profile-wrapper"})]})})})})}},81359:(e,t,a)=>{a.d(t,{A:()=>i,l:()=>r});let s=(0,a(51990).Z0)({name:"login",initialState:{isLogin:!1,user:null},reducers:{setLogin(e){e.isLogin=!0},setLogout(e){e.isLogin=!1,e.user=null},setUser(e,t){e.user=t.payload}}}),r=s.actions,i=s}}]);