(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2637],{26957:(e,s,t)=>{"use strict";t.d(s,{AM:()=>l,Dp:()=>o,F4:()=>z,FI:()=>S,H$:()=>a,J9:()=>h,Jo:()=>b,K9:()=>c,M6:()=>u,MO:()=>m,OT:()=>R,P4:()=>N,UR:()=>L,WD:()=>A,WH:()=>j,WU:()=>k,_i:()=>n,bW:()=>w,dG:()=>r,dm:()=>D,iJ:()=>C,mh:()=>U,oo:()=>x,pZ:()=>f,u3:()=>d,x2:()=>v,xE:()=>i,xo:()=>y,yo:()=>g,zP:()=>p});let a="https://client-api.acuizen.com",r=a+"/login-configs",i=a+"/services",n=e=>a+"/files/"+e+"/presigned-url",l=a+"/users/me",c=a+"/dynamic-titles",d=a+"/users/get_users",o=a+"/files",m=a+"/observation-reports",u=a+"/my-observation-reports",x=a+"/dropdowns",h=a+"/get-blob",p=a+"/permit-reports",b=a+"/users",g=a+"/toolbox-talks",f=a+"/my-toolbox-talks",j=e=>a+"/my-assigned-actions/"+e,v=e=>a+"/inspection-checklist-submit/"+e,N=e=>a+"/observation-reports/"+e,w=e=>a+"/inspection-task-submit/"+e,y=e=>a+"/inspections/"+e,k=e=>a+"/permit-report-submit/"+e,A=e=>a+"/permit-reports-acknowledge/"+e,L=e=>a+"/permit-reports-update-status/"+e,S=e=>a+"/observation-action-submit/"+e,D=a+"/risk-assessments",C=e=>a+"/risk-assessments/"+e,z=e=>a+"/ra-team-member-submit-signature/"+e,R=a+"/permit-reports",U=e=>a+"/permit-reports/"+e},32210:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var a=t(95155),r=t(12115),i=t(35695),n=t(46554),l=t(43864);let c=e=>{var s;let{document:t}=e;console.log("document",t);let r=e=>{let{content:s}=e;switch(e.type){case"document-header":return(0,a.jsxs)("div",{className:"mb-4 text-center border-bottom pb-3",children:[(0,a.jsx)("h1",{className:"h2 fw-bold text-dark mb-2",children:(null==s?void 0:s.text)||"Document Title"}),(0,a.jsxs)("div",{className:"text-muted small",children:["Document ID: ",t.maskId||t.docId||"N/A"]})]});case"section-header":return(0,a.jsx)("div",{className:"mb-3 mt-4",children:(0,a.jsx)("h2",{className:"h4 fw-semibold text-dark border-bottom pb-2",children:(null==s?void 0:s.text)||"Section Header"})});case"paragraph":return(0,a.jsx)("div",{className:"mb-3",children:(0,a.jsx)("p",{className:"text-dark lh-lg text-justify",children:(null==s?void 0:s.text)||"This is a paragraph of text content."})});case"bullet-list":return(0,a.jsx)("div",{className:"mb-3",children:(0,a.jsx)("ul",{className:"list-unstyled",children:(null==s?void 0:s.items)&&Array.isArray(s.items)?s.items.map((e,s)=>(0,a.jsxs)("li",{className:"mb-2",children:[(0,a.jsx)("i",{className:"bi bi-dot text-primary me-2"}),e]},s)):(0,a.jsxs)("li",{children:[(0,a.jsx)("i",{className:"bi bi-dot text-primary me-2"}),"List item"]})})});case"numbered-list":return(0,a.jsx)("div",{className:"mb-3",children:(0,a.jsx)("ol",{className:"list-group list-group-numbered",children:(null==s?void 0:s.items)&&Array.isArray(s.items)?s.items.map((e,s)=>(0,a.jsx)("li",{className:"list-group-item border-0 ps-0 lh-lg",children:e},s)):(0,a.jsx)("li",{className:"list-group-item border-0 ps-0",children:"List item"})})});case"quote":return(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)("blockquote",{className:"blockquote border-start border-primary border-4 ps-3 py-2 bg-light fst-italic",children:(null==s?void 0:s.text)||"This is a quote block."})});case"separator":return(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)("hr",{className:"border-secondary"})});case"image":case"video":case"file-attachment":return(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center p-3 bg-light rounded border",children:[(0,a.jsx)(l.A,{fileName:null==s?void 0:s.filename,size:200,name:!0}),(null==s?void 0:s.caption)&&(0,a.jsx)("p",{className:"text-muted fst-italic mt-2 text-center small",children:s.caption})]})});case"link":return(0,a.jsx)("div",{className:"mb-3",children:(0,a.jsx)("a",{href:null==s?void 0:s.url,target:"_blank",rel:"noopener noreferrer",className:"text-primary text-decoration-underline",children:(null==s?void 0:s.text)||(null==s?void 0:s.url)||"Link"})});default:return(0,a.jsx)("div",{className:"mb-3 p-3 bg-light rounded border-start border-secondary border-4",children:(0,a.jsxs)("div",{className:"text-muted small",children:[e.type.replace("-"," ").replace(/\b\w/g,e=>e.toUpperCase())," Component"]})})}};return(0,a.jsx)("div",{className:"bg-light min-vh-100",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"row justify-content-center",children:(0,a.jsx)("div",{className:"col-12 col-lg-10 col-xl-8",children:(0,a.jsxs)("div",{className:"bg-white shadow rounded overflow-hidden",style:{minHeight:"80vh"},children:[(0,a.jsx)("div",{className:"bg-gradient p-4 border-bottom",style:{background:"linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%)"},children:(0,a.jsx)("div",{className:"d-flex align-items-start justify-content-between",children:(0,a.jsxs)("div",{className:"flex-grow-1",children:[(0,a.jsx)("h1",{className:"h3 fw-bold text-dark mb-2",children:t.name}),(0,a.jsxs)("div",{className:"d-flex align-items-center gap-3 text-muted small",children:[(0,a.jsxs)("div",{className:"d-flex align-items-center",children:[(0,a.jsx)("i",{className:"bi bi-file-earmark-text me-1"}),(0,a.jsx)("span",{children:t.maskId||t.docId||"N/A"})]}),t.created&&(0,a.jsxs)("div",{className:"d-flex align-items-center",children:[(0,a.jsx)("i",{className:"bi bi-calendar me-1"}),(0,a.jsx)("span",{children:new Date(t.created).toLocaleDateString()})]}),(null==(s=t.creator)?void 0:s.firstName)&&(0,a.jsxs)("div",{className:"d-flex align-items-center",children:[(0,a.jsx)("i",{className:"bi bi-person me-1"}),(0,a.jsx)("span",{children:t.creator.firstName})]})]}),(0,a.jsxs)("div",{className:"d-flex align-items-center gap-2 mt-3",children:[(0,a.jsx)("span",{className:"badge ".concat((e=>{switch(null==e?void 0:e.toLowerCase()){case"approved":return"bg-success text-white";case"under review":case"review":case"pending":return"bg-warning text-dark";case"rejected":return"bg-danger text-white";default:return"bg-secondary text-white"}})(t.status||"Draft")),children:t.status||"Draft"}),t.category&&(0,a.jsxs)("span",{className:"badge bg-light text-dark border",children:[(0,a.jsx)("i",{className:"bi bi-tag me-1"}),t.category]})]})]})})}),(0,a.jsxs)("div",{className:"p-4",children:[t.value&&Array.isArray(t.value)&&t.value.length>0?(0,a.jsx)("div",{children:t.value.sort((e,s)=>e.position-s.position).map((e,s)=>(0,a.jsx)("div",{children:r(e)},e.id||s))}):"Existing"===t.type&&t.files?(0,a.jsx)("div",{className:"text-center py-5",children:(0,a.jsxs)("div",{className:"p-4 bg-warning bg-opacity-10 rounded border border-warning d-inline-block",children:[(0,a.jsx)("h5",{className:"fw-semibold text-dark mb-3",children:"Attached Document"}),(0,a.jsx)(l.A,{fileName:t.files,size:200,name:!0})]})}):(0,a.jsxs)("div",{className:"text-center py-5",children:[(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)("i",{className:"bi bi-file-earmark-text display-1 text-muted"})}),(0,a.jsx)("h5",{className:"fw-semibold text-dark mb-2",children:"No Content Available"}),(0,a.jsx)("p",{className:"text-muted",children:"This document does not have any content components to display."})]}),(0,a.jsx)("div",{className:"mt-5 pt-4 border-top text-center text-muted small",children:(0,a.jsxs)("p",{className:"mb-0",children:["Document generated on ",new Date().toLocaleDateString()," •",t.category&&" Category: ".concat(t.category," • "),"Status: ",t.status||"Draft"]})})]})]})})})})})},d=()=>{let e=(0,i.useRouter)(),s=(0,i.useSearchParams)(),[t,l]=(0,r.useState)(null);return((0,r.useEffect)(()=>{let t=s.get("id");if(t){let s=localStorage.getItem("selectedDocument");if(s){let e=JSON.parse(s);if(e.id===t)return void l(e)}e.push("/doc")}else{let s=localStorage.getItem("selectedDocument");s?l(JSON.parse(s)):e.push("/doc")}},[e,s]),t)?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.default,{heading:t.name}),(0,a.jsx)("div",{className:"page-content-wrapper py-3",children:(0,a.jsx)("div",{className:"container-fluid",children:(0,a.jsx)(c,{document:t})})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.default,{heading:"Document Details"}),(0,a.jsx)("div",{className:"page-content-wrapper py-3",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"text-center py-5",children:[(0,a.jsx)("div",{className:"spinner-border text-primary",role:"status",children:(0,a.jsx)("span",{className:"visually-hidden",children:"Loading..."})}),(0,a.jsx)("p",{className:"mt-3",children:"Loading document..."})]})})})]})},o=()=>(0,a.jsx)(r.Suspense,{fallback:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.default,{heading:"Document Details"}),(0,a.jsx)("div",{className:"page-content-wrapper py-3",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"text-center py-5",children:[(0,a.jsx)("div",{className:"spinner-border text-primary",role:"status",children:(0,a.jsx)("span",{className:"visually-hidden",children:"Loading..."})}),(0,a.jsx)("p",{className:"mt-3",children:"Loading document..."})]})})})]}),children:(0,a.jsx)(d,{})})},35695:(e,s,t)=>{"use strict";var a=t(18999);t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},38336:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var a=t(96078),r=t(26957);let i=a.A.create({baseURL:r.H$,headers:{Accept:"application/json","Content-Type":"application/json"},mode:"no-cors"});i.interceptors.request.use(async e=>(e.metadata={requestTime:Date.now()},e),e=>Promise.reject(e)),i.interceptors.response.use(e=>{var s;return e.headers["x-request-time"]=null==(s=e.config.metadata)?void 0:s.requestTime,e},async e=>{let{offlineQueue:s}=await t.e(8836).then(t.bind(t,48836)),{offlineStorage:a}=await t.e(58).then(t.bind(t,60058));if(s.shouldQueue(e)){var r,i,n,l;let t=e.config;if(await s.addRequest(t.url,(null==(r=t.method)?void 0:r.toUpperCase())||"GET",t.data,t.headers),(null==(i=t.method)?void 0:i.toLowerCase())==="get")try{if(null==(n=t.url)?void 0:n.includes("/services")){let e=await a.getServices();if(e.length>0)return console.log("\uD83D\uDCF1 Serving services from offline cache"),{data:e,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:t}}if(null==(l=t.url)?void 0:l.includes("assigned-actions")){let e=new URLSearchParams(t.url.split("?")[1]).get("filter"),s="All";if(e)try{JSON.parse(decodeURIComponent(e))}catch(r){let e=t.url.split("/"),a=e.findIndex(e=>"assigned-actions"===e);-1!==a&&e[a+1]&&(s=e[a+1])}let r=await a.getActions(s);if(r.length>0)return console.log("\uD83D\uDCF1 Serving actions from offline cache"),{data:r,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:t}}}catch(e){console.error("❌ Error serving from cache:",e)}}return Promise.reject(e)});let n=i},43864:(e,s,t)=>{"use strict";t.d(s,{A:()=>h});var a=t(95155),r=t(12115),i=t(26957),n=t(38336);let l=async e=>{try{return(await n.A.get((0,i._i)(e),{headers:{"Content-Type":"application/json"}})).data}catch(e){return console.error("Failed to fetch image URL:",e),null}},c=async e=>{try{let s=(await n.A.post(i.J9,{presignedUrl:e},{responseType:"blob"})).data;return new Promise((e,t)=>{let a=new FileReader;a.onloadend=()=>e(a.result),a.onerror=t,a.readAsDataURL(s)})}catch(e){throw console.error("Error fetching Data URL:",e),e}};var d=t(11518),o=t.n(d),m=t(36209);t(58561);var u=t(4178);let x=e=>{let{imageSrc:s}=e,[t,i]=(0,r.useState)(!1);return(0,a.jsxs)("div",{className:"jsx-15b99a83659358da container",children:[(0,a.jsx)("div",{className:"jsx-15b99a83659358da card",children:(0,a.jsxs)("div",{className:"jsx-15b99a83659358da body-blue text-center",children:[(0,a.jsx)("img",{src:s,alt:"Displayed",onClick:e=>{e.preventDefault(),e.stopPropagation(),i(!0)},style:{cursor:"pointer"},className:"jsx-15b99a83659358da display-image"}),t&&(0,a.jsx)(m.Ay,{open:t,close:()=>i(!1),slides:[{src:s}],plugins:[u.A],carousel:{finite:!0}})]})}),(0,a.jsx)(o(),{id:"15b99a83659358da",children:".display-image.jsx-15b99a83659358da{max-width:80px;max-height:80px;width:auto;height:auto;-o-object-fit:cover;object-fit:cover;cursor:pointer!important;-webkit-transition:-webkit-transform.3s ease-in-out;-moz-transition:-moz-transform.3s ease-in-out;-o-transition:-o-transform.3s ease-in-out;transition:-webkit-transform.3s ease-in-out;transition:-moz-transform.3s ease-in-out;transition:-o-transform.3s ease-in-out;transition:transform.3s ease-in-out;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;border:1px solid#dee2e6;pointer-events:auto;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.display-image.jsx-15b99a83659358da:hover{-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-ms-transform:scale(1.05);-o-transform:scale(1.05);transform:scale(1.05);-webkit-box-shadow:0 2px 8px rgba(0,0,0,.15);-moz-box-shadow:0 2px 8px rgba(0,0,0,.15);box-shadow:0 2px 8px rgba(0,0,0,.15)}.container.jsx-15b99a83659358da{padding:0;margin:0;max-width:none;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.card.jsx-15b99a83659358da{border:none;background:none;margin:0;padding:0;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.body-blue.jsx-15b99a83659358da{padding:0;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}"})]})},h=e=>{let{fileName:s,size:t=100,name:i=!1}=e,[n,d]=(0,r.useState)(null),[o,m]=(0,r.useState)(null);if((0,r.useEffect)(()=>{(async()=>{try{var e;let t=await l(s);d(t);let a=null==(e=s.split(".").pop())?void 0:e.toLowerCase();if(a&&["jpg","jpeg","png","gif","bmp","webp"].includes(a)){let e=await c(t);m(e)}}catch(e){console.error("Error fetching file or data URL:",e)}})()},[s]),!n)return(0,a.jsx)("p",{children:"Loading..."});let u=(e=>{var s;let t=null==(s=e.split(".").pop())?void 0:s.toLowerCase();return t?["jpg","jpeg","png","gif","bmp","webp"].includes(t)?"image":["pdf"].includes(t)?"pdf":["xls","xlsx"].includes(t)?"xls":"other":"other"})(s),h=s.replace(/^\d+[\s-_]*/,"");switch(u){case"image":return(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center",style:{padding:"4px"},children:[o?(0,a.jsx)(x,{imageSrc:o}):(0,a.jsx)("div",{className:"d-flex align-items-center justify-content-center bg-light border rounded",style:{width:t,height:t},children:(0,a.jsx)("div",{className:"spinner-border spinner-border-sm text-primary",role:"status",children:(0,a.jsx)("span",{className:"visually-hidden",children:"Loading..."})})}),i&&(0,a.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center",fontSize:"10px",margin:"2px 0 0 0"},children:h})]});case"pdf":return(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center p-2",children:[(0,a.jsx)("a",{href:n,target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)("i",{className:"bi bi-file-pdf-fill fs-1 text-danger"})}),i&&(0,a.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center"},children:h})]});case"xls":return(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center p-2",children:[(0,a.jsx)("a",{href:n,target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)("i",{className:"bi bi-file-excel-fill fs-1 text-success"})}),i&&(0,a.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center"},children:h})]});default:return(0,a.jsxs)("div",{className:"d-flex flex-column align-items-center p-2",children:[(0,a.jsx)("a",{href:n,target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)("i",{className:"bi bi-file-earmark-fill fs-1 text-secondary"})}),i&&(0,a.jsx)("p",{style:{wordBreak:"break-word",textAlign:"center"},children:h})]})}}},46554:(e,s,t)=>{"use strict";t.d(s,{default:()=>o});var a=t(95155),r=t(12115),i=t(35695),n=t(38336),l=t(26957),c=t(34540),d=t(81359);let o=e=>{let{heading:s}=e,t=(0,i.useRouter)(),[o,m]=(0,r.useState)(""),u=(0,c.wA)();r.useEffect(()=>{x()},[]);let x=async()=>{try{let e=await n.A.get(l.AM);200===e.status?(m(e.data.firstName),u(d.l.setUser(e.data))):t.push("/")}catch(e){console.log(e)}};return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"header-area",id:"headerArea",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"header-content header-style-four position-relative d-flex align-items-center justify-content-between",children:[(0,a.jsx)("div",{className:"back-button",children:(0,a.jsx)("button",{onClick:()=>t.back(),className:"border-0 bg-transparent p-0",style:{cursor:"pointer"},children:(0,a.jsx)("i",{className:"bi bi-arrow-left-short fs-3"})})}),(0,a.jsx)("div",{className:"page-heading",children:(0,a.jsx)("h6",{className:"mb-0",children:s})}),(0,a.jsx)("div",{className:"user-profile-wrapper"})]})})})})}},69387:(e,s,t)=>{Promise.resolve().then(t.bind(t,32210))},81359:(e,s,t)=>{"use strict";t.d(s,{A:()=>i,l:()=>r});let a=(0,t(51990).Z0)({name:"login",initialState:{isLogin:!1,user:null},reducers:{setLogin(e){e.isLogin=!0},setLogout(e){e.isLogin=!1,e.user=null},setUser(e,s){e.user=s.payload}}}),r=a.actions,i=a}},e=>{var s=s=>e(e.s=s);e.O(0,[3496,6078,635,1205,8441,1684,7358],()=>s(69387)),_N_E=e.O()}]);