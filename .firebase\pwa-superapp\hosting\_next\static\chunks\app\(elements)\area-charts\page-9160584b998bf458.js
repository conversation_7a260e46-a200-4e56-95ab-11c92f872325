(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9269],{10255:(e,t,s)=>{"use strict";function a(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return a}}),s(95155),s(47650),s(85744),s(20589)},17828:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return a}});let a=(0,s(64054).createAsyncLocalStorage)()},21217:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var a=s(95155),l=s(9e4),i=s(38808),n=s(12115);let r=e=>{let{handleShowSetting:t,showSetting:s}=e,{theme:n,handleDarkModeToggle:r}=(0,l.D)(),{viewMode:o,handleRTLToggling:c}=(0,i.L)();return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{id:"setting-popup-overlay",className:s?"active":"",onClick:t}),(0,a.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(s?"active":""),id:"settingCard",children:(0,a.jsx)("div",{className:"card-body",children:(0,a.jsxs)("div",{className:"container",children:[(0,a.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,a.jsx)("p",{className:"mb-0",children:"Settings"}),(0,a.jsx)("div",{onClick:t,className:"btn-close",id:"settingCardClose"})]}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,a.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,a.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===n,onChange:r}),(0,a.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===n?"Light":"Dark"," mode"]})]})}),(0,a.jsx)("div",{className:"single-setting-panel",children:(0,a.jsxs)("div",{className:"form-check form-switch",children:[(0,a.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===o,onChange:c}),(0,a.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===o?"LTR":"RTL"," mode"]})]})})]})})})]})};var o=s(6874),c=s.n(o);let d=e=>{let{links:t,title:s}=e,[l,i]=(0,n.useState)(!1),o=()=>i(!l);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"header-area",id:"headerArea",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,a.jsx)("div",{className:"back-button",children:(0,a.jsx)(c(),{href:"/".concat(t),children:(0,a.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,a.jsx)("div",{className:"page-heading",children:(0,a.jsx)("h6",{className:"mb-0",children:s})}),(0,a.jsx)("div",{className:"setting-wrapper",onClick:o,children:(0,a.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,a.jsx)("i",{className:"bi bi-gear"}),(0,a.jsx)("span",{})]})})]})})}),(0,a.jsx)(r,{showSetting:l,handleShowSetting:o})]})}},36441:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});var a=s(95155);s(12115);let l=(0,s(55028).default)(()=>Promise.all([s.e(3104),s.e(8699)]).then(s.bind(s,78699)),{loadableGenerated:{webpack:()=>[78699]},ssr:!1}),i={chart:{height:240,type:"area",animations:{enabled:!0,easing:"easeinout",speed:1e3},dropShadow:{enabled:!0,opacity:.1,blur:1,left:-5,top:18},zoom:{enabled:!1},toolbar:{show:!1}},colors:["#0134d4","#ea4c62"],dataLabels:{enabled:!1},fill:{type:"gradient",gradient:{type:"vertical",shadeIntensity:1,inverseColors:!0,opacityFrom:.15,opacityTo:.02,stops:[40,100]}},grid:{borderColor:"#dbeaea",strokeDashArray:4,xaxis:{lines:{show:!0}},yaxis:{lines:{show:!1}},padding:{top:0,right:0,bottom:0,left:0}},legend:{position:"bottom",horizontalAlign:"center",offsetY:4,fontSize:"14px",markers:{strokeWidth:0,radius:20},itemMargin:{horizontal:5,vertical:0}},title:{text:"$5,394",align:"left",margin:0,offsetX:0,offsetY:20,floating:!1,style:{fontSize:"16px",color:"#8480ae"}},tooltip:{theme:"dark",marker:{show:!0},x:{show:!1}},subtitle:{text:"This week sales",align:"left",margin:0,offsetX:0,offsetY:0,floating:!1,style:{fontSize:"14px",color:"#8480ae"}},stroke:{show:!0,curve:"smooth",width:3},labels:["S","S","M","T","W","T","F"],series:[{name:"Affan",data:[320,420,395,350,410,355,360]},{name:"Suha",data:[400,395,350,395,430,385,374]}],xaxis:{crosshairs:{show:!0},labels:{offsetX:0,offsetY:0,style:{colors:"#8480ae",fontSize:"12px"}},tooltip:{enabled:!1}},yaxis:{labels:{offsetX:-10,offsetY:0,style:{colors:"#8480ae",fontSize:"12px"}}}},n={chart:{height:220,type:"area",animations:{enabled:!0,easing:"easeinout",speed:1e3},dropShadow:{enabled:!0,opacity:.1,blur:1,left:-5,top:5},zoom:{enabled:!1},toolbar:{show:!1}},colors:["#0134d4"],dataLabels:{enabled:!1},fill:{type:"gradient",gradient:{type:"vertical",shadeIntensity:1,inverseColors:!0,opacityFrom:.15,opacityTo:.05,stops:[40,100]}},grid:{borderColor:"#dbeaea",strokeDashArray:4,xaxis:{lines:{show:!0}},yaxis:{lines:{show:!1}},padding:{top:0,right:0,bottom:0,left:0}},legend:{position:"top",horizontalAlign:"right",offsetY:-60,fontSize:"14px",markers:{strokeWidth:0,radius:20},itemMargin:{horizontal:5,vertical:0}},title:{text:"",align:"left",margin:0,offsetX:0,offsetY:0,floating:!0,style:{fontSize:"16px",color:"#8480ae"}},subtitle:{text:"",align:"left",margin:0,offsetX:0,offsetY:20,floating:!0,style:{fontSize:"14px",color:"#8480ae"}},tooltip:{theme:"dark",marker:{show:!0},x:{show:!1}},stroke:{show:!0,curve:"smooth",width:3},labels:["Jul","Aug","Sep","Oct","Nov","Dec"],series:[{name:"Sales",data:[1420,14602,24741,24119,48224,40635]}],xaxis:{crosshairs:{show:!0},labels:{offsetX:0,offsetY:0,style:{colors:"#8480ae",fontSize:"12px"}},tooltip:{enabled:!1}},yaxis:{labels:{offsetX:-10,offsetY:0,style:{colors:"#8480ae",fontSize:"12px"}}}},r=()=>(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"page-content-wrapper py-3",children:[(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"element-heading",children:(0,a.jsx)("h6",{children:"Area One"})})}),(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"card shadow-sm",children:(0,a.jsx)("div",{className:"card-body pb-2",children:(0,a.jsx)("div",{className:"chart-wrapper",children:(0,a.jsx)(l,{options:i,series:i.series,type:"area",height:240})})})})}),(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"element-heading mt-3",children:(0,a.jsx)("h6",{children:"Area Two"})})}),(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"card shadow-sm",children:(0,a.jsx)("div",{className:"card-body pb-2",children:(0,a.jsx)("div",{className:"chart-wrapper",children:(0,a.jsx)(l,{options:n,series:n.series,type:"area",height:220})})})})})]})})},36645:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let a=s(88229)._(s(67357));function l(e,t){var s;let l={};"function"==typeof e&&(l.loader=e);let i={...l,...t};return(0,a.default)({...i,modules:null==(s=i.loadableGenerated)?void 0:s.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38808:(e,t,s)=>{"use strict";s.d(t,{L:()=>l});var a=s(12115);let l=()=>{let[e,t]=(0,a.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,a.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let s=()=>{t(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:s,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}s()}}}},38983:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});var a=s(95155),l=s(6874),i=s.n(l);s(12115);let n=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],r=()=>(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,a.jsx)("div",{className:"container px-0",children:(0,a.jsx)("div",{className:"footer-nav position-relative",children:(0,a.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:n.map((e,t)=>(0,a.jsx)("li",{children:(0,a.jsxs)(i(),{href:"/".concat(e.link),children:[(0,a.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,a.jsx)("span",{children:e.title})]})},t))})})})})})},39949:(e,t,s)=>{Promise.resolve().then(s.bind(s,36441)),Promise.resolve().then(s.bind(s,38983)),Promise.resolve().then(s.bind(s,21217))},55028:(e,t,s)=>{"use strict";s.d(t,{default:()=>l.a});var a=s(36645),l=s.n(a)},62146:(e,t,s)=>{"use strict";function a(e){let{reason:t,children:s}=e;return s}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return a}}),s(45262)},64054:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{bindSnapshot:function(){return n},createAsyncLocalStorage:function(){return i},createSnapshot:function(){return r}});let s=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class a{disable(){throw s}getStore(){}run(){throw s}exit(){throw s}enterWith(){throw s}static bind(e){return e}}let l="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function i(){return l?new l:new a}function n(e){return l?l.bind(e):a.bind(e)}function r(){return l?l.snapshot():function(e,...t){return e(...t)}}},67357:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let a=s(95155),l=s(12115),i=s(62146);function n(e){return{default:e&&"default"in e?e.default:e}}s(10255);let r={loader:()=>Promise.resolve(n(()=>null)),loading:null,ssr:!0},o=function(e){let t={...r,...e},s=(0,l.lazy)(()=>t.loader().then(n)),o=t.loading;function c(e){let n=o?(0,a.jsx)(o,{isLoading:!0,pastDelay:!0,error:null}):null,r=!t.ssr||!!t.loading,c=r?l.Suspense:l.Fragment,d=t.ssr?(0,a.jsxs)(a.Fragment,{children:[null,(0,a.jsx)(s,{...e})]}):(0,a.jsx)(i.BailoutToCSR,{reason:"next/dynamic",children:(0,a.jsx)(s,{...e})});return(0,a.jsx)(c,{...r?{fallback:n}:{},children:d})}return c.displayName="LoadableComponent",c}},85744:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return a.workAsyncStorageInstance}});let a=s(17828)},9e4:(e,t,s)=>{"use strict";s.d(t,{D:()=>l});var a=s(12115);let l=()=>{let[e,t]=(0,a.useState)("light"),[s,l]=(0,a.useState)(!1);(0,a.useEffect)(()=>{t(localStorage.getItem("theme")||"light"),l(!0)},[]),(0,a.useEffect)(()=>{s&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,s]);let i=(0,a.useCallback)(()=>{t(e=>"dark"===e?"light":"dark")},[]),n=(0,a.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let t=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(t),e.style.display="none")},20)},1e3))}i()},[i]);return{theme:e,toggleTheme:i,handleDarkModeToggle:n}}}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,8441,1684,7358],()=>t(39949)),_N_E=e.O()}]);