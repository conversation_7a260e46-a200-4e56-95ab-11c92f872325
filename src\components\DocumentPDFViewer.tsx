import React from 'react';
import ImageComponent from '@/services/FileDownlodS3';

interface DocumentComponent {
  id: string;
  type: string;
  content: any;
  position: number;
}

interface Document {
  id: string;
  name: string;
  type: string;
  size?: string;
  uploadedBy?: string;
  uploadedDate?: string;
  category?: string;
  tags?: string[];
  description?: string;
  maskId?: string;
  scopeApplicability?: string;
  purpose?: string;
  keywords?: string;
  docId?: string;
  created?: string;
  updated?: string;
  creatorTargetDate?: string;
  reviewerTargetDate?: string;
  approverTargetDate?: string;
  initiatorId?: string;
  creatorId?: string;
  reviewerId?: string;
  approverId?: string;
  documentCategoryId?: string;
  initiator?: any;
  creator?: any;
  reviewer?: any;
  approver?: any;
  documentCategory?: any;
  files?: any;
  status?: string;
  docStatus?: string;
  value?: DocumentComponent[];
}

interface DocumentPDFViewerProps {
  document: Document;
}

const DocumentPDFViewer: React.FC<DocumentPDFViewerProps> = ({ document }) => {

  console.log('document', document);

  // Get status color using Bootstrap classes
  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'bg-success text-white';
      case 'under review':
      case 'review':
        return 'bg-warning text-dark';
      case 'rejected':
        return 'bg-danger text-white';
      case 'draft':
        return 'bg-secondary text-white';
      case 'pending':
        return 'bg-warning text-dark';
      default:
        return 'bg-secondary text-white';
    }
  };

  const renderDocumentComponent = (component: DocumentComponent) => {
    const { content } = component;

    switch (component.type) {
      case 'document-header':
        return (
          <div className="mb-4 text-center border-bottom pb-3">
            <h1 className="h2 fw-bold text-dark mb-2">
              {content?.text || 'Document Title'}
            </h1>
            <div className="text-muted small">
              Document ID: {document.maskId || document.docId || 'N/A'}
            </div>
          </div>
        );

      case 'section-header':
        return (
          <div className="mb-3 mt-4">
            <h2 className="h4 fw-semibold text-dark border-bottom pb-2">
              {content?.text || 'Section Header'}
            </h2>
          </div>
        );

      case 'paragraph':
        return (
          <div className="mb-3">
            <p className="text-dark lh-lg text-justify">
              {content?.text || 'This is a paragraph of text content.'}
            </p>
          </div>
        );

      case 'bullet-list':
        return (
          <div className="mb-3">
            <ul className="list-unstyled">
              {content?.items && Array.isArray(content.items) ? (
                content.items.map((item: string, idx: number) => (
                  <li key={idx} className="mb-2">
                    <i className="bi bi-dot text-primary me-2"></i>
                    {item}
                  </li>
                ))
              ) : (
                <li><i className="bi bi-dot text-primary me-2"></i>List item</li>
              )}
            </ul>
          </div>
        );

      case 'numbered-list':
        return (
          <div className="mb-3">
            <ol className="list-group list-group-numbered">
              {content?.items && Array.isArray(content.items) ? (
                content.items.map((item: string, idx: number) => (
                  <li key={idx} className="list-group-item border-0 ps-0 lh-lg">
                    {item}
                  </li>
                ))
              ) : (
                <li className="list-group-item border-0 ps-0">List item</li>
              )}
            </ol>
          </div>
        );

      case 'quote':
        return (
          <div className="mb-4">
            <blockquote className="blockquote border-start border-primary border-4 ps-3 py-2 bg-light fst-italic">
              {content?.text || 'This is a quote block.'}
            </blockquote>
          </div>
        );

      case 'separator':
        return (
          <div className="mb-4">
            <hr className="border-secondary" />
          </div>
        );

      case 'image':
      case 'video':
      case 'file-attachment':
        return (
          <div className="mb-4">
            <div className="d-flex flex-column align-items-center p-3 bg-light rounded border">
              <ImageComponent 
                fileName={content?.filename} 
                size={200} 
                name={true} 
              />
              {content?.caption && (
                <p className="text-muted fst-italic mt-2 text-center small">
                  {content.caption}
                </p>
              )}
            </div>
          </div>
        );

      case 'link':
        return (
          <div className="mb-3">
            <a
              href={content?.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary text-decoration-underline"
            >
              {content?.text || content?.url || 'Link'}
            </a>
          </div>
        );

      default:
        return (
          <div className="mb-3 p-3 bg-light rounded border-start border-secondary border-4">
            <div className="text-muted small">
              {component.type.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())} Component
            </div>
          </div>
        );
    }
  };

  return (
    <div className="bg-light min-vh-100">
      {/* PDF-like Document Container */}
      <div className="container">
        <div className="row justify-content-center">
          <div className="col-12 col-lg-10 col-xl-8">
            <div className="bg-white shadow rounded overflow-hidden" style={{ minHeight: '80vh' }}>
              {/* Document Header */}
              <div className="bg-gradient p-4 border-bottom" style={{ background: 'linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%)' }}>
                <div className="d-flex align-items-start justify-content-between">
                  <div className="flex-grow-1">
                    <h1 className="h3 fw-bold text-dark mb-2">
                      {document.name}
                    </h1>
                    <div className="d-flex align-items-center gap-3 text-muted small">
                      <div className="d-flex align-items-center">
                        <i className="bi bi-file-earmark-text me-1"></i>
                        <span>{document.maskId || document.docId || 'N/A'}</span>
                      </div>
                      {document.created && (
                        <div className="d-flex align-items-center">
                          <i className="bi bi-calendar me-1"></i>
                          <span>{new Date(document.created).toLocaleDateString()}</span>
                        </div>
                      )}
                      {document.creator?.firstName && (
                        <div className="d-flex align-items-center">
                          <i className="bi bi-person me-1"></i>
                          <span>{document.creator.firstName}</span>
                        </div>
                      )}
                    </div>
                    <div className="d-flex align-items-center gap-2 mt-3">
                      <span className={`badge ${getStatusColor(document.status || 'Draft')}`}>
                        {document.status || 'Draft'}
                      </span>
                      {document.category && (
                        <span className="badge bg-light text-dark border">
                          <i className="bi bi-tag me-1"></i>
                          {document.category}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Document Content */}
              <div className="p-4">
                {/* Document Components */}
                {document.value && Array.isArray(document.value) && document.value.length > 0 ? (
                  <div>
                    {document.value
                      .sort((a, b) => a.position - b.position)
                      .map((component, index) => (
                        <div key={component.id || index}>
                          {renderDocumentComponent(component)}
                        </div>
                      ))}
                  </div>
                ) : (
                  /* Show attached file for Existing documents */
                  document.type === 'Existing' && document.files ? (
                    <div className="text-center py-5">
                      <div className="p-4 bg-warning bg-opacity-10 rounded border border-warning d-inline-block">
                        <h5 className="fw-semibold text-dark mb-3">Attached Document</h5>
                        <ImageComponent 
                          fileName={document.files} 
                          size={200} 
                          name={true} 
                        />
                      </div>
                    </div>
                  ) : (
                    /* Empty state */
                    <div className="text-center py-5">
                      <div className="mb-4">
                        <i className="bi bi-file-earmark-text display-1 text-muted"></i>
                      </div>
                      <h5 className="fw-semibold text-dark mb-2">
                        No Content Available
                      </h5>
                      <p className="text-muted">
                        This document does not have any content components to display.
                      </p>
                    </div>
                  )
                )}

                {/* Document Footer */}
                <div className="mt-5 pt-4 border-top text-center text-muted small">
                  <p className="mb-0">
                    Document generated on {new Date().toLocaleDateString()} •  
                    {document.category && ` Category: ${document.category} • `}
                    Status: {document.status || 'Draft'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentPDFViewer;
