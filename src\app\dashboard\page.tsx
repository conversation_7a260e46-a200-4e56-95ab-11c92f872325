"use client";

import React, { useState } from "react";
import HeaderSeven from "@/layouts/headers/HeaderSeven";
import Link from "next/link";

export default function DashboardPage() {
  // Dashboard data - similar to test.tsx
  const dashboardCards = [
    {
      title: 'Active Reports',
      value: '12',
      icon: 'bi bi-file-text',
      color: '#007AFF',
    },
    {
      title: 'Pending Permits',
      value: '5',
      icon: 'bi bi-shield-check',
      color: '#FF9500',
    },
    {
      title: 'Risk Assessments',
      value: '8',
      icon: 'bi bi-shield-fill-exclamation',
      color: '#FF3B30',
    },
    {
      title: 'Completed Audits',
      value: '23',
      icon: 'bi bi-list-check',
      color: '#34C759',
    },
  ];

  // Chart data for visualization
  const moduleUsageData = [
    { name: 'Observation', value: 12, color: '#007AFF' },
    { name: 'Risk Assess.', value: 8, color: '#FF3B30' },
    { name: 'Incidents', value: 6, color: '#FF9500' },
    { name: 'Audits', value: 23, color: '#34C759' },
  ];

  const monthlyTrendsData = [
    { month: 'Jan', value: 20 },
    { month: 'Feb', value: 45 },
    { month: 'Mar', value: 28 },
    { month: 'Apr', value: 80 },
    { month: 'May', value: 99 },
    { month: 'Jun', value: 43 },
  ];

  const riskDistributionData = [
    { name: 'Low', value: 15, color: '#34C759' },
    { name: 'Medium', value: 8, color: '#FF9500' },
    { name: 'High', value: 3, color: '#FF3B30' },
  ];

  return (
    <>
      <HeaderSeven heading="Dashboard" />

      <div className="page-content-wrapper" style={{ minHeight: "100vh", backgroundColor: "#f8f9fa" }}>
        <div className="container-fluid px-3" style={{ padding: '16px' }}>

          {/* Header Section - Based on test.tsx */}
          <div style={{ marginBottom: '20px' }}>
            {/* <h2 className="fw-bold mb-2 text-dark" style={{ fontSize: '28px', marginBottom: '4px' }}>
              Dashboard
            </h2> */}
            <p className="text-muted mb-0" style={{ fontSize: '16px', opacity: 0.7 }}>
              Welcome back! Here&apos;s your overview
            </p>
          </div>

          {/* Dashboard Cards - Based on test.tsx */}
          <div className="row g-3 mb-4">
            {dashboardCards.map((card, index) => (
              <div key={index} className="col-6 col-md-3">
                <div
                  className="bg-white border rounded-3 p-3"
                  style={{
                    borderRadius: '12px',
                    borderWidth: '1px',
                    borderColor: '#E5E7EB',
                    backgroundColor: '#FFFFFF',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
                    transition: 'all 0.3s ease',
                    cursor: 'pointer'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.12)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';
                  }}
                >
                  {/* Card Header */}
                  <div className="d-flex align-items-center justify-content-between mb-2">
                    <div
                      className="d-flex align-items-center justify-content-center"
                      style={{
                        width: '40px',
                        height: '40px',
                        borderRadius: '20px',
                        backgroundColor: card.color + '20'
                      }}
                    >
                      <i
                        className={card.icon}
                        style={{
                          fontSize: '24px',
                          color: card.color
                        }}
                      ></i>
                    </div>
                    <h3
                      className="fw-bold mb-0"
                      style={{
                        fontSize: '24px',
                        fontWeight: 'bold',
                        color: '#1A1A1A'
                      }}
                    >
                      {card.value}
                    </h3>
                  </div>
                  <p
                    className="mb-0"
                    style={{
                      fontSize: '14px',
                      opacity: 0.8,
                      color: '#666666'
                    }}
                  >
                    {card.title}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Module Usage Chart Section */}
          <div className="row mb-4">
            <div className="col-12">
              <div
                className="bg-white border rounded-3 p-4"
                style={{
                  borderRadius: '12px',
                  borderWidth: '1px',
                  borderColor: '#E5E7EB',
                  backgroundColor: '#FFFFFF',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
                }}
              >
                <h5 className="fw-semibold mb-3" style={{ fontSize: '18px', fontWeight: '600' }}>
                  Module Usage
                </h5>
                <div className="row g-2">
                  {moduleUsageData.map((item, index) => (
                    <div key={index} className="col-6 col-md-3">
                      <div className="d-flex align-items-center p-2">
                        <div
                          style={{
                            width: '12px',
                            height: '12px',
                            borderRadius: '6px',
                            backgroundColor: item.color,
                            marginRight: '8px'
                          }}
                        ></div>
                        <div>
                          <div className="fw-bold" style={{ fontSize: '16px' }}>{item.value}</div>
                          <div className="text-muted" style={{ fontSize: '12px' }}>{item.name}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Simple Bar Chart Visualization */}
                <div className="mt-3">
                  {moduleUsageData.map((item, index) => (
                    <div key={index} className="mb-2">
                      <div className="d-flex justify-content-between align-items-center mb-1">
                        <span style={{ fontSize: '14px', fontWeight: '500' }}>{item.name}</span>
                        <span style={{ fontSize: '14px', color: '#666' }}>{item.value}</span>
                      </div>
                      <div
                        className="progress"
                        style={{ height: '8px', borderRadius: '4px', backgroundColor: '#F3F4F6' }}
                      >
                        <div
                          className="progress-bar"
                          style={{
                            width: `${(item.value / 25) * 100}%`,
                            backgroundColor: item.color,
                            borderRadius: '4px'
                          }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Monthly Trends and Risk Distribution */}
          <div className="row g-3 mb-4">
            {/* Monthly Trends */}
            <div className="col-12 col-lg-6">
              <div
                className="bg-white border rounded-3 p-4"
                style={{
                  borderRadius: '12px',
                  borderWidth: '1px',
                  borderColor: '#E5E7EB',
                  backgroundColor: '#FFFFFF',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
                }}
              >
                <h5 className="fw-semibold mb-3" style={{ fontSize: '18px', fontWeight: '600' }}>
                  Monthly Trends
                </h5>
                <div className="d-flex align-items-end justify-content-between" style={{ height: '120px' }}>
                  {monthlyTrendsData.map((item, index) => (
                    <div key={index} className="d-flex flex-column align-items-center">
                      <div
                        style={{
                          width: '20px',
                          height: `${(item.value / 100) * 80}px`,
                          backgroundColor: '#34C759',
                          borderRadius: '2px',
                          marginBottom: '8px'
                        }}
                      ></div>
                      <span style={{ fontSize: '12px', color: '#666' }}>{item.month}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Risk Distribution */}
            <div className="col-12 col-lg-6">
              <div
                className="bg-white border rounded-3 p-4"
                style={{
                  borderRadius: '12px',
                  borderWidth: '1px',
                  borderColor: '#E5E7EB',
                  backgroundColor: '#FFFFFF',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
                }}
              >
                <h5 className="fw-semibold mb-3" style={{ fontSize: '18px', fontWeight: '600' }}>
                  Risk Distribution
                </h5>
                <div className="d-flex flex-column gap-3">
                  {riskDistributionData.map((item, index) => (
                    <div key={index} className="d-flex align-items-center justify-content-between">
                      <div className="d-flex align-items-center">
                        <div
                          style={{
                            width: '16px',
                            height: '16px',
                            borderRadius: '8px',
                            backgroundColor: item.color,
                            marginRight: '12px'
                          }}
                        ></div>
                        <span style={{ fontSize: '14px', fontWeight: '500' }}>{item.name}</span>
                      </div>
                      <div className="d-flex align-items-center">
                        <span style={{ fontSize: '16px', fontWeight: 'bold', marginRight: '8px' }}>{item.value}</span>
                        <div
                          className="progress"
                          style={{ width: '60px', height: '6px', borderRadius: '3px', backgroundColor: '#F3F4F6' }}
                        >
                          <div
                            className="progress-bar"
                            style={{
                              width: `${(item.value / 20) * 100}%`,
                              backgroundColor: item.color,
                              borderRadius: '3px'
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Footer Section */}
          <div className="row">
            <div className="col-12">
              <div className="text-center py-4" style={{ paddingBottom: '100px' }}>
                <p className="text-muted mb-0" style={{ fontSize: '14px', opacity: 0.6 }}>
                  Dashboard data updates in real-time
                </p>
              </div>
            </div>
          </div>

        </div>
      </div>

      {/* Footer Navigation */}
      <div className="fixed-bottom bg-white border-top shadow-sm">
        <div className="container-fluid">
          <div className="row text-center py-2">
            <div className="col">
              <Link href="/dashboard" className="text-decoration-none text-primary">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-grid-3x3-gap-fill fs-5 mb-1"></i>
                  <small style={{ fontSize: '0.7rem' }}>Dashboard</small>
                </div>
              </Link>
            </div>
            <div className="col">
              <Link href="/services" className="text-decoration-none text-muted">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-grid fs-5 mb-1"></i>
                  <small style={{ fontSize: '0.7rem' }}>Services</small>
                </div>
              </Link>
            </div>
            <div className="col">
              <Link href="/home" className="text-decoration-none text-muted">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-house fs-5 mb-1"></i>
                  <small style={{ fontSize: '0.7rem' }}>Home</small>
                </div>
              </Link>
            </div>
            <div className="col">
              <Link href="/history" className="text-decoration-none text-muted">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-clock-history fs-5 mb-1"></i>
                  <small style={{ fontSize: '0.7rem' }}>History</small>
                </div>
              </Link>
            </div>
            <div className="col">
              <Link href="/profile" className="text-decoration-none text-muted">
                <div className="d-flex flex-column align-items-center">
                  <i className="bi bi-person fs-5 mb-1"></i>
                  <small style={{ fontSize: '0.7rem' }}>Profile</small>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
