import React from 'react';
import moment from 'moment';
import { Accordion } from "react-bootstrap";

interface TaskItem {
    name?: string;
}

interface HazardImage {
    image: string;
    name: string;
}

interface OptionItem {
    current_type: string;
    value: string;
}

interface TaskData {
    // For non–High-Risk Hazard tasks:
    // item[0]: TaskItem (header)
    // item[1]: { selected: HazardImage[] }
    // item[2]: { option: OptionItem[] } => Consequence
    // item[3]: { option: OptionItem[] } => Controls

    // For High-Risk Hazard tasks:
    // item[0]: { option: OptionItem[] } => Consequence
    // item[1]: { option: OptionItem[] } => Controls
    0: any;
    1: any;
    2?: any;
    3?: any;
}

interface ReportData {
    type: string;
    workActivity?: { name?: string };
    description: string;
    department?: { name?: string };
    created?: string | Date;
    tasks: TaskData[];
    hazardName: string;
}

interface ViewRiskProps {
    reportData: ReportData;
}

const ViewRisk: React.FC<ViewRiskProps> = ({ reportData }) => {
    return (
        <div className="observation-report" style={{ background: 'transparent', padding: '0' }}>
            {/* Work Activity / Hazard Name */}
            {reportData.type !== "High-Risk Hazard" ? (
                <>
                    <div className="card border-0 mb-3" style={{ backgroundColor: "#f8fafc" }}>
                        <div className="card-body p-3">
                            <div className="d-flex align-items-center gap-2 mb-2">
                                <i className="bi bi-briefcase text-primary"></i>
                                <h6 className="section-title mb-0">Work Activity</h6>
                            </div>
                            <p className="obs-dec text-muted mb-0">
                                {reportData.type === "Routine"
                                    ? reportData.workActivity?.name
                                    : reportData.description}
                            </p>
                        </div>
                    </div>
                    <div className="card border-0 mb-3" style={{ backgroundColor: "#f8fafc" }}>
                        <div className="card-body p-3">
                            <div className="d-flex align-items-center gap-2 mb-2">
                                <i className="bi bi-building text-primary"></i>
                                <h6 className="section-title mb-0">Department</h6>
                            </div>
                            <p className="obs-dec text-muted mb-0">
                                {reportData.type === "Routine"
                                    ? reportData.department?.name
                                    : reportData.description}
                            </p>
                        </div>
                    </div>
                </>
            ) : (
                <div className="card border-0 mb-3" style={{ backgroundColor: "#fef2f2" }}>
                    <div className="card-body p-3">
                        <div className="d-flex align-items-center gap-2 mb-2">
                            <i className="bi bi-exclamation-triangle text-danger"></i>
                            <h6 className="section-title mb-0">Hazard Name</h6>
                        </div>
                        <p className="obs-dec text-muted mb-0">{reportData.hazardName}</p>
                    </div>
                </div>
            )}

            {/* Basic Info */}
            <div className="row g-3 mb-3">
                <div className="col-md-6">
                    <div className="card border-0" style={{ backgroundColor: "#f0f9ff" }}>
                        <div className="card-body p-3">
                            <div className="d-flex align-items-center gap-2 mb-2">
                                <i className="bi bi-tag text-info"></i>
                                <h6 className="obs-title mb-0">Type</h6>
                            </div>
                            <p className="obs-content mb-0">{reportData.type}</p>
                        </div>
                    </div>
                </div>
                <div className="col-md-6">
                    <div className="card border-0" style={{ backgroundColor: "#f0f9ff" }}>
                        <div className="card-body p-3">
                            <div className="d-flex align-items-center gap-2 mb-2">
                                <i className="bi bi-calendar3 text-info"></i>
                                <h6 className="obs-title mb-0">Reported Date</h6>
                            </div>
                            <p className="obs-content mb-0">
                                {reportData.created
                                    ? moment(reportData.created).format('Do MMM YYYY, hh:mm:ss a')
                                    : 'N/A'}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {reportData.type !== "High-Risk Hazard" ? (
                // Non High-Risk Hazard: Render Tasks Accordion
                <div className="">
                    <div className="d-flex align-items-center gap-2 mb-3">
                        <i className="bi bi-list-check text-primary"></i>
                        <h5 className="mb-0 fw-semibold">Risk Assessment Tasks</h5>
                    </div>
                    <Accordion>
                        {reportData.tasks.map((item, i) => (
                            <Accordion.Item eventKey={i.toString()} key={i} className="mb-3 border-0 shadow-sm" style={{ borderRadius: "12px", overflow: "hidden" }}>
                                <Accordion.Header style={{ borderRadius: "12px 12px 0 0" }}>
                                    <div className="d-flex align-items-center gap-2">
                                        <span className="badge bg-primary text-white" style={{ fontSize: "10px" }}>
                                            {i + 1}
                                        </span>
                                        {item[0].name || `Sub Activity ${i + 1}`}
                                    </div>
                                </Accordion.Header>
                                <Accordion.Body style={{ backgroundColor: "#fafbfc" }}>
                                    <div className="mb-4">
                                        <div className="card border-0 mb-3" style={{ backgroundColor: "#fff7ed" }}>
                                            <div className="card-body p-3">
                                                <div className="d-flex align-items-center gap-2 mb-3">
                                                    <i className="bi bi-exclamation-triangle text-warning"></i>
                                                    <h6 className="obs-title mb-0">Identified Hazards</h6>
                                                </div>
                                                {item[1].selected.map((img: HazardImage, j: number) => (
                                                    <div key={j} className="card border-0 mb-2 shadow-sm">
                                                        <div className="card-body p-3">
                                                            <div className="d-flex align-items-center gap-3">
                                                                <div className="p-2 rounded" style={{ backgroundColor: "#f3f4f6" }}>
                                                                    <img
                                                                        src={`https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/${img.image}`}
                                                                        style={{ height: 32, width: 32, objectFit: "contain" }}
                                                                        alt="hazard"
                                                                    />
                                                                </div>
                                                                <div className="flex-grow-1">
                                                                    <p className="mb-0 fw-medium">{img.name}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                    <div className="row g-3">
                                        {/* Consequence */}
                                        <div className="col-md-6">
                                            <div className="card border-0" style={{ backgroundColor: "#fef2f2" }}>
                                                <div className="card-body p-3">
                                                    <div className="d-flex align-items-center gap-2 mb-3">
                                                        <i className="bi bi-exclamation-circle text-danger"></i>
                                                        <h6 className="obs-title mb-0">Consequences</h6>
                                                    </div>
                                                    {item[2].option.map((opt: OptionItem, j: number) => (
                                                        <div key={j} className="card border-0 mb-2" style={{ backgroundColor: "rgba(255,255,255,0.7)" }}>
                                                            <div className="card-body p-2">
                                                                <div className="d-flex align-items-center gap-2">
                                                                    <span className="badge bg-danger text-white" style={{ fontSize: "9px" }}>
                                                                        {opt.current_type}
                                                                    </span>
                                                                    <span className="small">{opt.value}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        </div>
                                        {/* Controls */}
                                        <div className="col-md-6">
                                            <div className="card border-0" style={{ backgroundColor: "#f0fdf4" }}>
                                                <div className="card-body p-3">
                                                    <div className="d-flex align-items-center gap-2 mb-3">
                                                        <i className="bi bi-shield-check text-success"></i>
                                                        <h6 className="obs-title mb-0">Controls</h6>
                                                    </div>
                                                    {item[3].option.map((opt: OptionItem, j: number) => (
                                                        <div key={j} className="card border-0 mb-2" style={{ backgroundColor: "rgba(255,255,255,0.7)" }}>
                                                            <div className="card-body p-2">
                                                                <div className="d-flex align-items-center gap-2">
                                                                    <span className="badge bg-success text-white" style={{ fontSize: "9px" }}>
                                                                        {opt.current_type}
                                                                    </span>
                                                                    <span className="small">{opt.value}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </Accordion.Body>
                            </Accordion.Item>
                        ))}
                    </Accordion>
                </div>
            ) : (
                // High-Risk Hazard: Render Tasks without Accordion
                <div className="">
                    <div className="d-flex align-items-center gap-2 mb-3">
                        <i className="bi bi-exclamation-triangle-fill text-danger"></i>
                        <h5 className="mb-0 fw-semibold text-danger">High-Risk Assessment</h5>
                    </div>
                    {reportData.tasks.map((item, i) => (
                        <div className="mb-4" key={i}>
                            <div className="row g-3">
                                {/* Consequence */}
                                <div className="col-md-6">
                                    <div className="card border-0" style={{ backgroundColor: "#fef2f2" }}>
                                        <div className="card-body p-3">
                                            <div className="d-flex align-items-center gap-2 mb-3">
                                                <i className="bi bi-exclamation-circle text-danger"></i>
                                                <h6 className="obs-title mb-0">Consequences</h6>
                                            </div>
                                            {item[0].option.map((opt: OptionItem, j: number) => (
                                                <div key={j} className="card border-0 mb-2" style={{ backgroundColor: "rgba(255,255,255,0.7)" }}>
                                                    <div className="card-body p-2">
                                                        <div className="d-flex align-items-center gap-2">
                                                            <span className="badge bg-danger text-white" style={{ fontSize: "9px" }}>
                                                                {opt.current_type}
                                                            </span>
                                                            <span className="small">{opt.value}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                                {/* Controls */}
                                <div className="col-md-6">
                                    <div className="card border-0" style={{ backgroundColor: "#f0fdf4" }}>
                                        <div className="card-body p-3">
                                            <div className="d-flex align-items-center gap-2 mb-3">
                                                <i className="bi bi-shield-check text-success"></i>
                                                <h6 className="obs-title mb-0">Controls</h6>
                                            </div>
                                            {item[1].option.map((opt: OptionItem, j: number) => (
                                                <div key={j} className="card border-0 mb-2" style={{ backgroundColor: "rgba(255,255,255,0.7)" }}>
                                                    <div className="card-body p-2">
                                                        <div className="d-flex align-items-center gap-2">
                                                            <span className="badge bg-success text-white" style={{ fontSize: "9px" }}>
                                                                {opt.current_type}
                                                            </span>
                                                            <span className="small">{opt.value}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default ViewRisk;
