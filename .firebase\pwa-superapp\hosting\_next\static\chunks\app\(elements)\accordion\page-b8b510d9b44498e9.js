(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[802],{10614:function(e,a,i){e.exports=function(e,a,i,s){"use strict";let c=".bs.collapse",l=`show${c}`,t=`shown${c}`,d=`hide${c}`,o=`hidden${c}`,r=`click${c}.data-api`,n="show",m="collapse",h="collapsing",p=`:scope .${m} .${m}`,x='[data-bs-toggle="collapse"]',g={parent:null,toggle:!0},b={parent:"(null|element)",toggle:"boolean"};class j extends e{constructor(e,a){for(let s of(super(e,a),this._isTransitioning=!1,this._triggerArray=[],i.find(x))){let e=i.getSelectorFromElement(s),a=i.find(e).filter(e=>e===this._element);null!==e&&a.length&&this._triggerArray.push(s)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return g}static get DefaultType(){return b}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let e=[];if(this._config.parent&&(e=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter(e=>e!==this._element).map(e=>j.getOrCreateInstance(e,{toggle:!1}))),e.length&&e[0]._isTransitioning||a.trigger(this._element,l).defaultPrevented)return;for(let a of e)a.hide();let i=this._getDimension();this._element.classList.remove(m),this._element.classList.add(h),this._element.style[i]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;let s=i[0].toUpperCase()+i.slice(1),c=`scroll${s}`;this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(h),this._element.classList.add(m,n),this._element.style[i]="",a.trigger(this._element,t)},this._element,!0),this._element.style[i]=`${this._element[c]}px`}hide(){if(this._isTransitioning||!this._isShown()||a.trigger(this._element,d).defaultPrevented)return;let e=this._getDimension();for(let a of(this._element.style[e]=`${this._element.getBoundingClientRect()[e]}px`,s.reflow(this._element),this._element.classList.add(h),this._element.classList.remove(m,n),this._triggerArray)){let e=i.getElementFromSelector(a);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([a],!1)}this._isTransitioning=!0,this._element.style[e]="",this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(h),this._element.classList.add(m),a.trigger(this._element,o)},this._element,!0)}_isShown(e=this._element){return e.classList.contains(n)}_configAfterMerge(e){return e.toggle=!!e.toggle,e.parent=s.getElement(e.parent),e}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(this._config.parent)for(let e of this._getFirstLevelChildren(x)){let a=i.getElementFromSelector(e);a&&this._addAriaAndCollapsedClass([e],this._isShown(a))}}_getFirstLevelChildren(e){let a=i.find(p,this._config.parent);return i.find(e,this._config.parent).filter(e=>!a.includes(e))}_addAriaAndCollapsedClass(e,a){if(e.length)for(let i of e)i.classList.toggle("collapsed",!a),i.setAttribute("aria-expanded",a)}static jQueryInterface(e){let a={};return"string"==typeof e&&/show|hide/.test(e)&&(a.toggle=!1),this.each(function(){let i=j.getOrCreateInstance(this,a);if("string"==typeof e){if(void 0===i[e])throw TypeError(`No method named "${e}"`);i[e]()}})}}return a.on(document,r,x,function(e){for(let a of(("A"===e.target.tagName||e.delegateTarget&&"A"===e.delegateTarget.tagName)&&e.preventDefault(),i.getMultipleElementsFromSelector(this)))j.getOrCreateInstance(a,{toggle:!1}).toggle()}),s.defineJQueryPlugin(j),j}(i(19962),i(65613),i(27346),i(60250))},21217:(e,a,i)=>{"use strict";i.d(a,{default:()=>n});var s=i(95155),c=i(9e4),l=i(38808),t=i(12115);let d=e=>{let{handleShowSetting:a,showSetting:i}=e,{theme:t,handleDarkModeToggle:d}=(0,c.D)(),{viewMode:o,handleRTLToggling:r}=(0,l.L)();return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{id:"setting-popup-overlay",className:i?"active":"",onClick:a}),(0,s.jsx)("div",{className:"card setting-popup-card shadow-lg ".concat(i?"active":""),id:"settingCard",children:(0,s.jsx)("div",{className:"card-body",children:(0,s.jsxs)("div",{className:"container",children:[(0,s.jsxs)("div",{className:"setting-heading d-flex align-items-center justify-content-between mb-3",children:[(0,s.jsx)("p",{className:"mb-0",children:"Settings"}),(0,s.jsx)("div",{onClick:a,className:"btn-close",id:"settingCardClose"})]}),(0,s.jsx)("div",{className:"single-setting-panel",children:(0,s.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,s.jsx)("input",{className:"form-check-input",type:"checkbox",id:"availabilityStatus",defaultChecked:!0}),(0,s.jsx)("label",{className:"form-check-label",htmlFor:"availabilityStatus",children:"Availability status"})]})}),(0,s.jsx)("div",{className:"single-setting-panel",children:(0,s.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,s.jsx)("input",{className:"form-check-input",type:"checkbox",id:"sendMeNotifications",defaultChecked:!0}),(0,s.jsx)("label",{className:"form-check-label",htmlFor:"sendMeNotifications",children:"Send me notifications"})]})}),(0,s.jsx)("div",{className:"single-setting-panel",children:(0,s.jsxs)("div",{className:"form-check form-switch mb-2",children:[(0,s.jsx)("input",{className:"form-check-input",type:"checkbox",id:"darkSwitch",checked:"dark"===t,onChange:d}),(0,s.jsxs)("label",{className:"form-check-label",htmlFor:"darkSwitch",children:["dark"===t?"Light":"Dark"," mode"]})]})}),(0,s.jsx)("div",{className:"single-setting-panel",children:(0,s.jsxs)("div",{className:"form-check form-switch",children:[(0,s.jsx)("input",{className:"form-check-input",type:"checkbox",id:"rtlSwitch",checked:"rtl"===o,onChange:r}),(0,s.jsxs)("label",{className:"form-check-label",htmlFor:"rtlSwitch",children:["rtl"===o?"LTR":"RTL"," mode"]})]})})]})})})]})};var o=i(6874),r=i.n(o);let n=e=>{let{links:a,title:i}=e,[c,l]=(0,t.useState)(!1),o=()=>l(!c);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"header-area",id:"headerArea",children:(0,s.jsx)("div",{className:"container",children:(0,s.jsxs)("div",{className:"header-content position-relative d-flex align-items-center justify-content-between",children:[(0,s.jsx)("div",{className:"back-button",children:(0,s.jsx)(r(),{href:"/".concat(a),children:(0,s.jsx)("i",{className:"bi bi-arrow-left-short"})})}),(0,s.jsx)("div",{className:"page-heading",children:(0,s.jsx)("h6",{className:"mb-0",children:i})}),(0,s.jsx)("div",{className:"setting-wrapper",onClick:o,children:(0,s.jsxs)("div",{className:"setting-trigger-btn",id:"settingTriggerBtn",children:[(0,s.jsx)("i",{className:"bi bi-gear"}),(0,s.jsx)("span",{})]})})]})})}),(0,s.jsx)(d,{showSetting:c,handleShowSetting:o})]})}},38808:(e,a,i)=>{"use strict";i.d(a,{L:()=>c});var s=i(12115);let c=()=>{let[e,a]=(0,s.useState)(()=>localStorage.getItem("rtl")||"ltr");(0,s.useEffect)(()=>{document.documentElement.setAttribute("view-mode",e),localStorage.setItem("rtl",e)},[e]);let i=()=>{a(e=>"rtl"===e?"ltr":"rtl")};return{viewMode:e,toggleViewMode:i,handleRTLToggling:()=>{{let e=document.querySelector(".rtl-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let a=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(a),e.style.display="none")},20)},1e3))}i()}}}},38983:(e,a,i)=>{"use strict";i.d(a,{default:()=>d});var s=i(95155),c=i(6874),l=i.n(c);i(12115);let t=[{id:1,icon:"house",title:"Home",link:"home"},{id:2,icon:"collection",title:"Pages",link:"pages"},{id:3,icon:"folder2-open",title:"Elements",link:"elements"},{id:4,icon:"chat-dots",title:"Chat",link:"chat-users"},{id:5,icon:"gear",title:"Settings",link:"settings"}],d=()=>(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"footer-nav-area",id:"footerNav",children:(0,s.jsx)("div",{className:"container px-0",children:(0,s.jsx)("div",{className:"footer-nav position-relative",children:(0,s.jsx)("ul",{className:"h-100 d-flex align-items-center justify-content-between ps-0",children:t.map((e,a)=>(0,s.jsx)("li",{children:(0,s.jsxs)(l(),{href:"/".concat(e.link),children:[(0,s.jsx)("i",{className:"bi bi-".concat(e.icon)}),(0,s.jsx)("span",{children:e.title})]})},a))})})})})})},64250:(e,a,i)=>{Promise.resolve().then(i.bind(i,95706)),Promise.resolve().then(i.bind(i,38983)),Promise.resolve().then(i.bind(i,21217))},9e4:(e,a,i)=>{"use strict";i.d(a,{D:()=>c});var s=i(12115);let c=()=>{let[e,a]=(0,s.useState)("light"),[i,c]=(0,s.useState)(!1);(0,s.useEffect)(()=>{a(localStorage.getItem("theme")||"light"),c(!0)},[]),(0,s.useEffect)(()=>{i&&(document.documentElement.setAttribute("data-theme",e),localStorage.setItem("theme",e))},[e,i]);let l=(0,s.useCallback)(()=>{a(e=>"dark"===e?"light":"dark")},[]),t=(0,s.useCallback)(()=>{{let e=document.querySelector(".dark-mode-switching");e&&(e.style.display="block",e.style.opacity="1",setTimeout(()=>{let a=setInterval(()=>{parseFloat(e.style.opacity)>0?e.style.opacity=(parseFloat(e.style.opacity)-.1).toString():(clearInterval(a),e.style.display="none")},20)},1e3))}l()},[l]);return{theme:e,toggleTheme:l,handleDarkModeToggle:t}}},95706:(e,a,i)=>{"use strict";i.d(a,{default:()=>c});var s=i(95155);i(12115);let c=()=>(i(10614),(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"page-content-wrapper py-3",children:[(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"element-heading",children:(0,s.jsx)("h6",{children:"Accordion with Image"})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"card",children:(0,s.jsx)("div",{className:"card-body",children:(0,s.jsxs)("div",{className:"accordion accordion-style-six",id:"accordionStyle6",children:[(0,s.jsx)("div",{className:"accordion-item",style:{backgroundImage:"url(/assets/img/bg-img/1.jpg)"},children:(0,s.jsxs)("div",{className:"accordion-header",id:"accordionSix1",children:[(0,s.jsx)("h6",{"data-bs-toggle":"collapse","data-bs-target":"#accordionStyleSix1","aria-expanded":"true","aria-controls":"accordionStyleSix1",children:"What is refund policy?"}),(0,s.jsx)("div",{className:"accordion-collapse collapse show",id:"accordionStyleSix1","aria-labelledby":"accordionSix1","data-bs-parent":"#accordionStyle6",children:(0,s.jsx)("p",{className:"mb-0 mt-2 text-white",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Rerum, velit?"})})]})}),(0,s.jsxs)("div",{className:"accordion-item",style:{backgroundImage:"url(/assets/img/bg-img/2.jpg)"},children:[(0,s.jsx)("div",{className:"accordion-header",id:"accordionSix2",children:(0,s.jsx)("h6",{className:"collapsed","data-bs-toggle":"collapse","data-bs-target":"#accordionStyleSix2","aria-expanded":"false","aria-controls":"accordionStyleSix2",children:"Can it accept Paypal?"})}),(0,s.jsx)("div",{className:"accordion-collapse collapse",id:"accordionStyleSix2","aria-labelledby":"accordionSix2","data-bs-parent":"#accordionStyle6",children:(0,s.jsx)("p",{className:"mb-0 mt-2 text-white",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate."})})]}),(0,s.jsxs)("div",{className:"accordion-item",style:{backgroundImage:"url(/assets/img/bg-img/3.jpg)"},children:[(0,s.jsx)("div",{className:"accordion-header",id:"accordionSix3",children:(0,s.jsx)("h6",{className:"collapsed","data-bs-toggle":"collapse","data-bs-target":"#accordionStyleSix3","aria-expanded":"false","aria-controls":"accordionStyleSix3",children:"What is PWA ready?"})}),(0,s.jsx)("div",{className:"accordion-collapse collapse",id:"accordionStyleSix3","aria-labelledby":"accordionSix3","data-bs-parent":"#accordionStyle6",children:(0,s.jsx)("p",{className:"mb-0 mt-2 text-white",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate."})})]})]})})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"element-heading mt-3",children:(0,s.jsx)("h6",{children:"Bordered Accordion"})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"card",children:(0,s.jsx)("div",{className:"card-body",children:(0,s.jsxs)("div",{className:"accordion accordion-style-five",id:"accordionStyle5",children:[(0,s.jsx)("div",{className:"accordion-item accordion-bg-primary",children:(0,s.jsxs)("div",{className:"accordion-header",id:"accordionFive1",children:[(0,s.jsxs)("h6",{"data-bs-toggle":"collapse","data-bs-target":"#accordionStyleFive1","aria-expanded":"true","aria-controls":"accordionStyleFive1",children:[(0,s.jsx)("i",{className:"bi bi-plus-lg"}),"What is refund policy?"]}),(0,s.jsx)("div",{className:"accordion-collapse collapse show",id:"accordionStyleFive1","aria-labelledby":"accordionFive1","data-bs-parent":"#accordionStyle5",children:(0,s.jsx)("p",{className:"mb-0 mt-2",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Rerum, velit?"})})]})}),(0,s.jsxs)("div",{className:"accordion-item accordion-bg-warning",children:[(0,s.jsx)("div",{className:"accordion-header",id:"accordionFive2",children:(0,s.jsxs)("h6",{className:"collapsed","data-bs-toggle":"collapse","data-bs-target":"#accordionStyleFive2","aria-expanded":"false","aria-controls":"accordionStyleFive2",children:[(0,s.jsx)("i",{className:"bi bi-plus-lg"}),"Can it accept Paypal?"]})}),(0,s.jsx)("div",{className:"accordion-collapse collapse",id:"accordionStyleFive2","aria-labelledby":"accordionFive2","data-bs-parent":"#accordionStyle5",children:(0,s.jsx)("p",{className:"mb-0 mt-2",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate."})})]}),(0,s.jsxs)("div",{className:"accordion-item accordion-bg-success",children:[(0,s.jsx)("div",{className:"accordion-header",id:"accordionFive3",children:(0,s.jsxs)("h6",{className:"collapsed","data-bs-toggle":"collapse","data-bs-target":"#accordionStyleFive3","aria-expanded":"false","aria-controls":"accordionStyleFive3",children:[(0,s.jsx)("i",{className:"bi bi-plus-lg"}),"What is PWA ready?"]})}),(0,s.jsx)("div",{className:"accordion-collapse collapse",id:"accordionStyleFive3","aria-labelledby":"accordionFive3","data-bs-parent":"#accordionStyle5",children:(0,s.jsx)("p",{className:"mb-0 mt-2",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate."})})]}),(0,s.jsxs)("div",{className:"accordion-item accordion-bg-info",children:[(0,s.jsx)("div",{className:"accordion-header",id:"accordionFive4",children:(0,s.jsxs)("h6",{className:"collapsed","data-bs-toggle":"collapse","data-bs-target":"#accordionStyleFive4","aria-expanded":"false","aria-controls":"accordionStyleFive4",children:[(0,s.jsx)("i",{className:"bi bi-plus-lg"}),"What is the single license?"]})}),(0,s.jsx)("div",{className:"accordion-collapse collapse",id:"accordionStyleFive4","aria-labelledby":"accordionFive4","data-bs-parent":"#accordionStyle5",children:(0,s.jsx)("p",{className:"mb-0 mt-2",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate."})})]}),(0,s.jsxs)("div",{className:"accordion-item accordion-bg-danger",children:[(0,s.jsx)("div",{className:"accordion-header",id:"accordionFive5",children:(0,s.jsxs)("h6",{className:"collapsed","data-bs-toggle":"collapse","data-bs-target":"#accordionStyleFive5","aria-expanded":"false","aria-controls":"accordionStyleFive5",children:[(0,s.jsx)("i",{className:"bi bi-plus-lg"}),"Whats new in 2.0?"]})}),(0,s.jsx)("div",{className:"accordion-collapse collapse",id:"accordionStyleFive5","aria-labelledby":"accordionFive5","data-bs-parent":"#accordionStyle5",children:(0,s.jsx)("p",{className:"mb-0 mt-2",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate."})})]})]})})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"element-heading mt-3",children:(0,s.jsx)("h6",{children:"Cozy Accordion"})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"card",children:(0,s.jsx)("div",{className:"card-body",children:(0,s.jsxs)("div",{className:"accordion accordion-flush accordion-style-one",id:"accordionStyle1",children:[(0,s.jsxs)("div",{className:"accordion-item",children:[(0,s.jsx)("div",{className:"accordion-header",id:"accordionOne",children:(0,s.jsxs)("h6",{"data-bs-toggle":"collapse","data-bs-target":"#accordionStyleOne","aria-expanded":"true","aria-controls":"accordionStyleOne",children:["What is refund policy?",(0,s.jsx)("i",{className:"bi bi-chevron-down"})]})}),(0,s.jsx)("div",{className:"accordion-collapse collapse show",id:"accordionStyleOne","aria-labelledby":"accordionOne","data-bs-parent":"#accordionStyle1",children:(0,s.jsx)("div",{className:"accordion-body",children:(0,s.jsx)("p",{className:"mb-0",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Rerum, velit?"})})})]}),(0,s.jsxs)("div",{className:"accordion-item",children:[(0,s.jsx)("div",{className:"accordion-header",id:"accordionTwo",children:(0,s.jsxs)("h6",{className:"collapsed","data-bs-toggle":"collapse","data-bs-target":"#accordionStyleTwo","aria-expanded":"false","aria-controls":"accordionStyleTwo",children:["Can it accept Paypal?",(0,s.jsx)("i",{className:"bi bi-chevron-down"})]})}),(0,s.jsx)("div",{className:"accordion-collapse collapse",id:"accordionStyleTwo","aria-labelledby":"accordionTwo","data-bs-parent":"#accordionStyle1",children:(0,s.jsx)("div",{className:"accordion-body",children:(0,s.jsx)("p",{className:"mb-0",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate."})})})]}),(0,s.jsxs)("div",{className:"accordion-item",children:[(0,s.jsx)("div",{className:"accordion-header",id:"accordionThree",children:(0,s.jsxs)("h6",{className:"collapsed","data-bs-toggle":"collapse","data-bs-target":"#accordionStyleThree","aria-expanded":"false","aria-controls":"accordionStyleThree",children:["What is PWA ready?",(0,s.jsx)("i",{className:"bi bi-chevron-down"})]})}),(0,s.jsx)("div",{className:"accordion-collapse collapse",id:"accordionStyleThree","aria-labelledby":"accordionThree","data-bs-parent":"#accordionStyle1",children:(0,s.jsx)("div",{className:"accordion-body",children:(0,s.jsx)("p",{className:"mb-0",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate."})})})]})]})})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"element-heading mt-3",children:(0,s.jsx)("h6",{children:"Accordion with Plus Sign"})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"card",children:(0,s.jsx)("div",{className:"card-body",children:(0,s.jsxs)("div",{className:"accordion accordion-flush accordion-style-two",id:"accordionStyle2",children:[(0,s.jsxs)("div",{className:"accordion-item",children:[(0,s.jsx)("div",{className:"accordion-header",id:"accordionFour",children:(0,s.jsxs)("h6",{"data-bs-toggle":"collapse","data-bs-target":"#accordionStyleFour","aria-expanded":"true","aria-controls":"accordionStyleFour",children:[(0,s.jsx)("i",{className:"bi bi-plus-lg"}),"What is refund policy?"]})}),(0,s.jsx)("div",{className:"accordion-collapse collapse show",id:"accordionStyleFour","aria-labelledby":"accordionFour","data-bs-parent":"#accordionStyle2",children:(0,s.jsx)("div",{className:"accordion-body",children:(0,s.jsx)("p",{className:"mb-0",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Rerum, velit?"})})})]}),(0,s.jsxs)("div",{className:"accordion-item",children:[(0,s.jsx)("div",{className:"accordion-header",id:"accordionFive",children:(0,s.jsxs)("h6",{className:"collapsed","data-bs-toggle":"collapse","data-bs-target":"#accordionStyleFive","aria-expanded":"false","aria-controls":"accordionStyleFive",children:[(0,s.jsx)("i",{className:"bi bi-plus-lg"}),"Can it accept Paypal?"]})}),(0,s.jsx)("div",{className:"accordion-collapse collapse",id:"accordionStyleFive","aria-labelledby":"accordionFive","data-bs-parent":"#accordionStyle2",children:(0,s.jsx)("div",{className:"accordion-body",children:(0,s.jsx)("p",{className:"mb-0",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate."})})})]}),(0,s.jsxs)("div",{className:"accordion-item",children:[(0,s.jsx)("div",{className:"accordion-header",id:"accordionSix",children:(0,s.jsxs)("h6",{className:"collapsed","data-bs-toggle":"collapse","data-bs-target":"#accordionStyleSix","aria-expanded":"false","aria-controls":"accordionStyleSix",children:[(0,s.jsx)("i",{className:"bi bi-plus-lg"}),"What is PWA ready?"]})}),(0,s.jsx)("div",{className:"accordion-collapse collapse",id:"accordionStyleSix","aria-labelledby":"accordionSix","data-bs-parent":"#accordionStyle2",children:(0,s.jsx)("div",{className:"accordion-body",children:(0,s.jsx)("p",{className:"mb-0",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate."})})})]})]})})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"element-heading mt-3",children:(0,s.jsx)("h6",{children:"Dark Accordion"})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"card bg-dark",children:(0,s.jsx)("div",{className:"card-body",children:(0,s.jsxs)("div",{className:"accordion accordion-style-four",id:"accordionStyle4",children:[(0,s.jsxs)("div",{className:"accordion-item bg-transparent",children:[(0,s.jsx)("div",{className:"accordion-header",id:"accordionTen",children:(0,s.jsxs)("h6",{"data-bs-toggle":"collapse","data-bs-target":"#accordionStyleTen","aria-expanded":"true","aria-controls":"accordionStyleTen",children:["# What is refund policy?",(0,s.jsx)("i",{className:"bi bi-caret-down"})]})}),(0,s.jsx)("div",{className:"accordion-collapse collapse show",id:"accordionStyleTen","aria-labelledby":"accordionTen","data-bs-parent":"#accordionStyle4",children:(0,s.jsx)("div",{className:"accordion-body",children:(0,s.jsx)("p",{className:"mb-0",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Rerum, velit?"})})})]}),(0,s.jsxs)("div",{className:"accordion-item bg-transparent",children:[(0,s.jsx)("div",{className:"accordion-header",id:"accordion11",children:(0,s.jsxs)("h6",{className:"collapsed","data-bs-toggle":"collapse","data-bs-target":"#accordionStyle11","aria-expanded":"false","aria-controls":"accordionStyle11",children:["# Can it accept Paypal?",(0,s.jsx)("i",{className:"bi bi-caret-down"})]})}),(0,s.jsx)("div",{className:"accordion-collapse collapse",id:"accordionStyle11","aria-labelledby":"accordion11","data-bs-parent":"#accordionStyle4",children:(0,s.jsx)("div",{className:"accordion-body",children:(0,s.jsx)("p",{className:"mb-0",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate."})})})]}),(0,s.jsxs)("div",{className:"accordion-item bg-transparent",children:[(0,s.jsx)("div",{className:"accordion-header",id:"accordion12",children:(0,s.jsxs)("h6",{className:"collapsed","data-bs-toggle":"collapse","data-bs-target":"#accordionStyle12","aria-expanded":"false","aria-controls":"accordionStyle12",children:["# What is PWA ready?",(0,s.jsx)("i",{className:"bi bi-caret-down"})]})}),(0,s.jsx)("div",{className:"accordion-collapse collapse",id:"accordionStyle12","aria-labelledby":"accordion12","data-bs-parent":"#accordionStyle4",children:(0,s.jsx)("div",{className:"accordion-body",children:(0,s.jsx)("p",{className:"mb-0",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate."})})})]})]})})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"element-heading mt-3",children:(0,s.jsx)("h6",{children:"Compact Accordion"})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"card",children:(0,s.jsx)("div",{className:"card-body",children:(0,s.jsxs)("div",{className:"accordion accordion-style-three",id:"accordionStyle3",children:[(0,s.jsxs)("div",{className:"accordion-item",children:[(0,s.jsx)("div",{className:"accordion-header",id:"accordionSeven",children:(0,s.jsxs)("h6",{className:"shadow-sm rounded border","data-bs-toggle":"collapse","data-bs-target":"#accordionStyleSeven","aria-expanded":"true","aria-controls":"accordionStyleSeven",children:["What is refund policy?",(0,s.jsx)("i",{className:"bi bi-caret-down"})]})}),(0,s.jsx)("div",{className:"accordion-collapse collapse show",id:"accordionStyleSeven","aria-labelledby":"accordionSeven","data-bs-parent":"#accordionStyle3",children:(0,s.jsx)("div",{className:"accordion-body",children:(0,s.jsx)("p",{className:"mb-0",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Rerum, velit?"})})})]}),(0,s.jsxs)("div",{className:"accordion-item",children:[(0,s.jsx)("div",{className:"accordion-header",id:"accordionEight",children:(0,s.jsxs)("h6",{className:"shadow-sm rounded collapsed border","data-bs-toggle":"collapse","data-bs-target":"#accordionStyleEight","aria-expanded":"false","aria-controls":"accordionStyleEight",children:["Can it accept Paypal?",(0,s.jsx)("i",{className:"bi bi-caret-down"})]})}),(0,s.jsx)("div",{className:"accordion-collapse collapse",id:"accordionStyleEight","aria-labelledby":"accordionEight","data-bs-parent":"#accordionStyle3",children:(0,s.jsx)("div",{className:"accordion-body",children:(0,s.jsx)("p",{className:"mb-0",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate."})})})]}),(0,s.jsxs)("div",{className:"accordion-item",children:[(0,s.jsx)("div",{className:"accordion-header",id:"accordionNine",children:(0,s.jsxs)("h6",{className:"shadow-sm rounded collapsed border","data-bs-toggle":"collapse","data-bs-target":"#accordionStyleNine","aria-expanded":"false","aria-controls":"accordionStyleNine",children:["What is PWA ready?",(0,s.jsx)("i",{className:"bi bi-caret-down"})]})}),(0,s.jsx)("div",{className:"accordion-collapse collapse",id:"accordionStyleNine","aria-labelledby":"accordionNine","data-bs-parent":"#accordionStyle3",children:(0,s.jsx)("div",{className:"accordion-body",children:(0,s.jsx)("p",{className:"mb-0",children:"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate."})})})]})]})})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"element-heading mt-3",children:(0,s.jsx)("h6",{children:"Bootstrap Accordion"})})}),(0,s.jsx)("div",{className:"container",children:(0,s.jsx)("div",{className:"card",children:(0,s.jsx)("div",{className:"card-body",children:(0,s.jsxs)("div",{className:"accordion",id:"basicaccordion",children:[(0,s.jsxs)("div",{className:"accordion-item",children:[(0,s.jsx)("div",{className:"accordion-header",id:"headingOne",children:(0,s.jsx)("button",{className:"accordion-button",type:"button","data-bs-toggle":"collapse","data-bs-target":"#collapseOne","aria-expanded":"true","aria-controls":"collapseOne",children:"What is PWA ready?"})}),(0,s.jsx)("div",{className:"accordion-collapse collapse show",id:"collapseOne","aria-labelledby":"headingOne","data-bs-parent":"#basicaccordion",children:(0,s.jsx)("div",{className:"accordion-body",children:(0,s.jsx)("p",{className:"mb-0",children:"Hello, I am bootstrap 5 accordion. I am number one."})})})]}),(0,s.jsxs)("div",{className:"accordion-item",children:[(0,s.jsx)("div",{className:"accordion-header",id:"headingTwo",children:(0,s.jsx)("button",{className:"accordion-button collapsed",type:"button","data-bs-toggle":"collapse","data-bs-target":"#collapseTwo","aria-expanded":"false","aria-controls":"collapseTwo",children:"What is refund policy?"})}),(0,s.jsx)("div",{className:"accordion-collapse collapse",id:"collapseTwo","aria-labelledby":"headingTwo","data-bs-parent":"#basicaccordion",children:(0,s.jsx)("div",{className:"accordion-body",children:(0,s.jsx)("p",{className:"mb-0",children:"Hello, I am bootstrap 5 accordion. I am number two."})})})]}),(0,s.jsxs)("div",{className:"accordion-item",children:[(0,s.jsx)("div",{className:"accordion-header",id:"headingThree",children:(0,s.jsx)("button",{className:"accordion-button collapsed",type:"button","data-bs-toggle":"collapse","data-bs-target":"#collapseThree","aria-expanded":"false","aria-controls":"collapseThree",children:"Can it accept Paypal?"})}),(0,s.jsx)("div",{className:"accordion-collapse collapse",id:"collapseThree","aria-labelledby":"headingThree","data-bs-parent":"#basicaccordion",children:(0,s.jsx)("div",{className:"accordion-body",children:(0,s.jsx)("p",{className:"mb-0",children:"Hello, I am bootstrap 5 accordion. I am number three."})})})]})]})})})})]})}))}},e=>{var a=a=>e(e.s=a);e.O(0,[6874,2703,8441,1684,7358],()=>a(64250)),_N_E=e.O()}]);