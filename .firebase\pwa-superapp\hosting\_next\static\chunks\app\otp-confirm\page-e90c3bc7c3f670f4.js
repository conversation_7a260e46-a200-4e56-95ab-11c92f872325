(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8205],{31899:(e,t,s)=>{Promise.resolve().then(s.bind(s,78125))},78093:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var n=s(95155),l=s(6874),a=s.n(l);s(12115);let c=e=>{let{links:t}=e;return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)("div",{className:"login-back-button",children:(0,n.jsx)(a(),{href:"/".concat(t),children:(0,n.jsx)("i",{className:"bi bi-arrow-left-short"})})})})}},78125:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var n=s(95155),l=s(12115),a=s(78093);let c=()=>{let[e,t]=(0,l.useState)([,,,,].fill("")),s=(s,n)=>{let l=s.value.replace(/[^0-9]/g,"");if(1===l.length){let s=[...e];if(s[n]=l,t(s),n<e.length-1){let e=document.getElementById("otp-input-".concat(n+1));null==e||e.focus()}}},c=(t,s)=>{if("Backspace"===t.key&&""===e[s]&&s>0){let e=document.getElementById("otp-input-".concat(s-1));null==e||e.focus()}},[r,i]=(0,l.useState)(60),[o,d]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{let e;return r>0?e=setInterval(()=>{i(e=>e-1)},1e3):d(!0),()=>clearInterval(e)},[r]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(a.A,{links:"otp"}),(0,n.jsx)("div",{className:"login-wrapper d-flex align-items-center justify-content-center",children:(0,n.jsxs)("div",{className:"custom-container",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("img",{className:"mx-auto mb-4 d-block",src:"/assets/img/bg-img/38.png",alt:""}),(0,n.jsx)("h3",{children:"Verify Phone Number"}),(0,n.jsxs)("p",{className:"mb-4",children:["Enter the OTP code sent to ",(0,n.jsx)("strong",{children:"012 3456 7890"})]})]}),(0,n.jsx)("div",{className:"otp-verify-form mt-4",children:(0,n.jsxs)("form",{action:"/home",onSubmit:e=>e.preventDefault(),children:[(0,n.jsx)("div",{className:"input-group mb-3 otp-input-group",children:e.map((e,t)=>(0,n.jsx)("input",{id:"otp-input-".concat(t),className:"single-otp-input form-control",type:"text",value:e,placeholder:"-",maxLength:1,onChange:e=>s(e.target,t),onKeyDown:e=>c(e,t)},t))}),(0,n.jsx)("button",{className:"btn btn-primary w-100",children:"Verify & Proceed"})]})}),(0,n.jsx)("div",{className:"login-meta-data text-center",children:(0,n.jsxs)("p",{className:"mt-3 mb-0",children:["Dont received the OTP? "," ",(0,n.jsx)("span",{className:"otp-sec",id:"resendOTP",style:{color:r<=10?"red":"black"},children:o?(0,n.jsx)("a",{className:"resendOTP",href:"",children:"Resend OTP"}):"Wait ".concat(r," secs")})]})})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,8441,1684,7358],()=>t(31899)),_N_E=e.O()}]);