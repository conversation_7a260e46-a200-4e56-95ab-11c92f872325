(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{26957:(e,s,t)=>{"use strict";t.d(s,{AM:()=>o,Dp:()=>d,F4:()=>A,FI:()=>C,H$:()=>i,J9:()=>u,Jo:()=>b,K9:()=>n,M6:()=>x,MO:()=>m,OT:()=>E,P4:()=>N,UR:()=>k,WD:()=>S,WH:()=>v,WU:()=>F,_i:()=>l,bW:()=>y,dG:()=>r,dm:()=>R,iJ:()=>z,mh:()=>W,oo:()=>h,pZ:()=>g,u3:()=>c,x2:()=>j,xE:()=>a,xo:()=>w,yo:()=>f,zP:()=>p});let i="https://client-api.acuizen.com",r=i+"/login-configs",a=i+"/services",l=e=>i+"/files/"+e+"/presigned-url",o=i+"/users/me",n=i+"/dynamic-titles",c=i+"/users/get_users",d=i+"/files",m=i+"/observation-reports",x=i+"/my-observation-reports",h=i+"/dropdowns",u=i+"/get-blob",p=i+"/permit-reports",b=i+"/users",f=i+"/toolbox-talks",g=i+"/my-toolbox-talks",v=e=>i+"/my-assigned-actions/"+e,j=e=>i+"/inspection-checklist-submit/"+e,N=e=>i+"/observation-reports/"+e,y=e=>i+"/inspection-task-submit/"+e,w=e=>i+"/inspections/"+e,F=e=>i+"/permit-report-submit/"+e,S=e=>i+"/permit-reports-acknowledge/"+e,k=e=>i+"/permit-reports-update-status/"+e,C=e=>i+"/observation-action-submit/"+e,R=i+"/risk-assessments",z=e=>i+"/risk-assessments/"+e,A=e=>i+"/ra-team-member-submit-signature/"+e,E=i+"/permit-reports",W=e=>i+"/permit-reports/"+e},35695:(e,s,t)=>{"use strict";var i=t(18999);t.o(i,"useRouter")&&t.d(s,{useRouter:function(){return i.useRouter}}),t.o(i,"useSearchParams")&&t.d(s,{useSearchParams:function(){return i.useSearchParams}})},38336:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var i=t(96078),r=t(26957);let a=i.A.create({baseURL:r.H$,headers:{Accept:"application/json","Content-Type":"application/json"},mode:"no-cors"});a.interceptors.request.use(async e=>(e.metadata={requestTime:Date.now()},e),e=>Promise.reject(e)),a.interceptors.response.use(e=>{var s;return e.headers["x-request-time"]=null==(s=e.config.metadata)?void 0:s.requestTime,e},async e=>{let{offlineQueue:s}=await t.e(8836).then(t.bind(t,48836)),{offlineStorage:i}=await t.e(58).then(t.bind(t,60058));if(s.shouldQueue(e)){var r,a,l,o;let t=e.config;if(await s.addRequest(t.url,(null==(r=t.method)?void 0:r.toUpperCase())||"GET",t.data,t.headers),(null==(a=t.method)?void 0:a.toLowerCase())==="get")try{if(null==(l=t.url)?void 0:l.includes("/services")){let e=await i.getServices();if(e.length>0)return console.log("\uD83D\uDCF1 Serving services from offline cache"),{data:e,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:t}}if(null==(o=t.url)?void 0:o.includes("assigned-actions")){let e=new URLSearchParams(t.url.split("?")[1]).get("filter"),s="All";if(e)try{JSON.parse(decodeURIComponent(e))}catch(r){let e=t.url.split("/"),i=e.findIndex(e=>"assigned-actions"===e);-1!==i&&e[i+1]&&(s=e[i+1])}let r=await i.getActions(s);if(r.length>0)return console.log("\uD83D\uDCF1 Serving actions from offline cache"),{data:r,status:200,statusText:"OK (Cached)",headers:{"x-served-from":"offline-cache"},config:t}}}catch(e){console.error("❌ Error serving from cache:",e)}}return Promise.reject(e)});let l=a},44879:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var i=t(95155);t(12115);var r=t(46554),a=t(6874),l=t.n(a);function o(){let e=[{name:"Observation",value:12,color:"#007AFF"},{name:"Risk Assess.",value:8,color:"#FF3B30"},{name:"Incidents",value:6,color:"#FF9500"},{name:"Audits",value:23,color:"#34C759"}];return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(r.default,{heading:"Dashboard"}),(0,i.jsx)("div",{className:"page-content-wrapper",style:{minHeight:"100vh",backgroundColor:"#f8f9fa"},children:(0,i.jsxs)("div",{className:"container-fluid px-3",style:{padding:"16px"},children:[(0,i.jsx)("div",{style:{marginBottom:"20px"},children:(0,i.jsx)("p",{className:"text-muted mb-0",style:{fontSize:"16px",opacity:.7},children:"Welcome back! Here's your overview"})}),(0,i.jsx)("div",{className:"row g-3 mb-4",children:[{title:"Active Reports",value:"12",icon:"bi bi-file-text",color:"#007AFF"},{title:"Pending Permits",value:"5",icon:"bi bi-shield-check",color:"#FF9500"},{title:"Risk Assessments",value:"8",icon:"bi bi-shield-fill-exclamation",color:"#FF3B30"},{title:"Completed Audits",value:"23",icon:"bi bi-list-check",color:"#34C759"}].map((e,s)=>(0,i.jsx)("div",{className:"col-6 col-md-3",children:(0,i.jsxs)("div",{className:"bg-white border rounded-3 p-3",style:{borderRadius:"12px",borderWidth:"1px",borderColor:"#E5E7EB",backgroundColor:"#FFFFFF",boxShadow:"0 2px 8px rgba(0,0,0,0.06)",transition:"all 0.3s ease",cursor:"pointer"},onMouseEnter:e=>{e.currentTarget.style.transform="translateY(-2px)",e.currentTarget.style.boxShadow="0 8px 25px rgba(0,0,0,0.12)"},onMouseLeave:e=>{e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="0 2px 8px rgba(0,0,0,0.06)"},children:[(0,i.jsxs)("div",{className:"d-flex align-items-center justify-content-between mb-2",children:[(0,i.jsx)("div",{className:"d-flex align-items-center justify-content-center",style:{width:"40px",height:"40px",borderRadius:"20px",backgroundColor:e.color+"20"},children:(0,i.jsx)("i",{className:e.icon,style:{fontSize:"24px",color:e.color}})}),(0,i.jsx)("h3",{className:"fw-bold mb-0",style:{fontSize:"24px",fontWeight:"bold",color:"#1A1A1A"},children:e.value})]}),(0,i.jsx)("p",{className:"mb-0",style:{fontSize:"14px",opacity:.8,color:"#666666"},children:e.title})]})},s))}),(0,i.jsx)("div",{className:"row mb-4",children:(0,i.jsx)("div",{className:"col-12",children:(0,i.jsxs)("div",{className:"bg-white border rounded-3 p-4",style:{borderRadius:"12px",borderWidth:"1px",borderColor:"#E5E7EB",backgroundColor:"#FFFFFF",boxShadow:"0 2px 8px rgba(0,0,0,0.06)"},children:[(0,i.jsx)("h5",{className:"fw-semibold mb-3",style:{fontSize:"18px",fontWeight:"600"},children:"Module Usage"}),(0,i.jsx)("div",{className:"row g-2",children:e.map((e,s)=>(0,i.jsx)("div",{className:"col-6 col-md-3",children:(0,i.jsxs)("div",{className:"d-flex align-items-center p-2",children:[(0,i.jsx)("div",{style:{width:"12px",height:"12px",borderRadius:"6px",backgroundColor:e.color,marginRight:"8px"}}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"fw-bold",style:{fontSize:"16px"},children:e.value}),(0,i.jsx)("div",{className:"text-muted",style:{fontSize:"12px"},children:e.name})]})]})},s))}),(0,i.jsx)("div",{className:"mt-3",children:e.map((e,s)=>(0,i.jsxs)("div",{className:"mb-2",children:[(0,i.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-1",children:[(0,i.jsx)("span",{style:{fontSize:"14px",fontWeight:"500"},children:e.name}),(0,i.jsx)("span",{style:{fontSize:"14px",color:"#666"},children:e.value})]}),(0,i.jsx)("div",{className:"progress",style:{height:"8px",borderRadius:"4px",backgroundColor:"#F3F4F6"},children:(0,i.jsx)("div",{className:"progress-bar",style:{width:"".concat(e.value/25*100,"%"),backgroundColor:e.color,borderRadius:"4px"}})})]},s))})]})})}),(0,i.jsxs)("div",{className:"row g-3 mb-4",children:[(0,i.jsx)("div",{className:"col-12 col-lg-6",children:(0,i.jsxs)("div",{className:"bg-white border rounded-3 p-4",style:{borderRadius:"12px",borderWidth:"1px",borderColor:"#E5E7EB",backgroundColor:"#FFFFFF",boxShadow:"0 2px 8px rgba(0,0,0,0.06)"},children:[(0,i.jsx)("h5",{className:"fw-semibold mb-3",style:{fontSize:"18px",fontWeight:"600"},children:"Monthly Trends"}),(0,i.jsx)("div",{className:"d-flex align-items-end justify-content-between",style:{height:"120px"},children:[{month:"Jan",value:20},{month:"Feb",value:45},{month:"Mar",value:28},{month:"Apr",value:80},{month:"May",value:99},{month:"Jun",value:43}].map((e,s)=>(0,i.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,i.jsx)("div",{style:{width:"20px",height:"".concat(e.value/100*80,"px"),backgroundColor:"#34C759",borderRadius:"2px",marginBottom:"8px"}}),(0,i.jsx)("span",{style:{fontSize:"12px",color:"#666"},children:e.month})]},s))})]})}),(0,i.jsx)("div",{className:"col-12 col-lg-6",children:(0,i.jsxs)("div",{className:"bg-white border rounded-3 p-4",style:{borderRadius:"12px",borderWidth:"1px",borderColor:"#E5E7EB",backgroundColor:"#FFFFFF",boxShadow:"0 2px 8px rgba(0,0,0,0.06)"},children:[(0,i.jsx)("h5",{className:"fw-semibold mb-3",style:{fontSize:"18px",fontWeight:"600"},children:"Risk Distribution"}),(0,i.jsx)("div",{className:"d-flex flex-column gap-3",children:[{name:"Low",value:15,color:"#34C759"},{name:"Medium",value:8,color:"#FF9500"},{name:"High",value:3,color:"#FF3B30"}].map((e,s)=>(0,i.jsxs)("div",{className:"d-flex align-items-center justify-content-between",children:[(0,i.jsxs)("div",{className:"d-flex align-items-center",children:[(0,i.jsx)("div",{style:{width:"16px",height:"16px",borderRadius:"8px",backgroundColor:e.color,marginRight:"12px"}}),(0,i.jsx)("span",{style:{fontSize:"14px",fontWeight:"500"},children:e.name})]}),(0,i.jsxs)("div",{className:"d-flex align-items-center",children:[(0,i.jsx)("span",{style:{fontSize:"16px",fontWeight:"bold",marginRight:"8px"},children:e.value}),(0,i.jsx)("div",{className:"progress",style:{width:"60px",height:"6px",borderRadius:"3px",backgroundColor:"#F3F4F6"},children:(0,i.jsx)("div",{className:"progress-bar",style:{width:"".concat(e.value/20*100,"%"),backgroundColor:e.color,borderRadius:"3px"}})})]})]},s))})]})})]}),(0,i.jsx)("div",{className:"row",children:(0,i.jsx)("div",{className:"col-12",children:(0,i.jsx)("div",{className:"text-center py-4",style:{paddingBottom:"100px"},children:(0,i.jsx)("p",{className:"text-muted mb-0",style:{fontSize:"14px",opacity:.6},children:"Dashboard data updates in real-time"})})})})]})}),(0,i.jsx)("div",{className:"fixed-bottom bg-white border-top shadow-sm",children:(0,i.jsx)("div",{className:"container-fluid",children:(0,i.jsxs)("div",{className:"row text-center py-2",children:[(0,i.jsx)("div",{className:"col",children:(0,i.jsx)(l(),{href:"/dashboard",className:"text-decoration-none text-primary",children:(0,i.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,i.jsx)("i",{className:"bi bi-grid-3x3-gap-fill fs-5 mb-1"}),(0,i.jsx)("small",{style:{fontSize:"0.7rem"},children:"Dashboard"})]})})}),(0,i.jsx)("div",{className:"col",children:(0,i.jsx)(l(),{href:"/services",className:"text-decoration-none text-muted",children:(0,i.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,i.jsx)("i",{className:"bi bi-grid fs-5 mb-1"}),(0,i.jsx)("small",{style:{fontSize:"0.7rem"},children:"Services"})]})})}),(0,i.jsx)("div",{className:"col",children:(0,i.jsx)(l(),{href:"/home",className:"text-decoration-none text-muted",children:(0,i.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,i.jsx)("i",{className:"bi bi-house fs-5 mb-1"}),(0,i.jsx)("small",{style:{fontSize:"0.7rem"},children:"Home"})]})})}),(0,i.jsx)("div",{className:"col",children:(0,i.jsx)(l(),{href:"/history",className:"text-decoration-none text-muted",children:(0,i.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,i.jsx)("i",{className:"bi bi-clock-history fs-5 mb-1"}),(0,i.jsx)("small",{style:{fontSize:"0.7rem"},children:"History"})]})})}),(0,i.jsx)("div",{className:"col",children:(0,i.jsx)(l(),{href:"/profile",className:"text-decoration-none text-muted",children:(0,i.jsxs)("div",{className:"d-flex flex-column align-items-center",children:[(0,i.jsx)("i",{className:"bi bi-person fs-5 mb-1"}),(0,i.jsx)("small",{style:{fontSize:"0.7rem"},children:"Profile"})]})})})]})})})]})}},46554:(e,s,t)=>{"use strict";t.d(s,{default:()=>d});var i=t(95155),r=t(12115),a=t(35695),l=t(38336),o=t(26957),n=t(34540),c=t(81359);let d=e=>{let{heading:s}=e,t=(0,a.useRouter)(),[d,m]=(0,r.useState)(""),x=(0,n.wA)();r.useEffect(()=>{h()},[]);let h=async()=>{try{let e=await l.A.get(o.AM);200===e.status?(m(e.data.firstName),x(c.l.setUser(e.data))):t.push("/")}catch(e){console.log(e)}};return(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("div",{className:"header-area",id:"headerArea",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"header-content header-style-four position-relative d-flex align-items-center justify-content-between",children:[(0,i.jsx)("div",{className:"back-button",children:(0,i.jsx)("button",{onClick:()=>t.back(),className:"border-0 bg-transparent p-0",style:{cursor:"pointer"},children:(0,i.jsx)("i",{className:"bi bi-arrow-left-short fs-3"})})}),(0,i.jsx)("div",{className:"page-heading",children:(0,i.jsx)("h6",{className:"mb-0",children:s})}),(0,i.jsx)("div",{className:"user-profile-wrapper"})]})})})})}},73146:(e,s,t)=>{Promise.resolve().then(t.bind(t,44879))},81359:(e,s,t)=>{"use strict";t.d(s,{A:()=>a,l:()=>r});let i=(0,t(51990).Z0)({name:"login",initialState:{isLogin:!1,user:null},reducers:{setLogin(e){e.isLogin=!0},setLogout(e){e.isLogin=!1,e.user=null},setUser(e,s){e.user=s.payload}}}),r=i.actions,a=i}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,6078,635,8441,1684,7358],()=>s(73146)),_N_E=e.O()}]);